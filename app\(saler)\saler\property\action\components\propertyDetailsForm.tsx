import React from 'react';
import { Home, Info, Building, Ruler, Home as HomeIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  PropertyDetail,
  ApartmentOrientation,
  PropertyType,
} from '@/lib/api/services/fetchProperty';

interface PropertyDetailsFormProps {
  formData: {
    type: PropertyType;
    propertyDetails: PropertyDetail;
  };
  handleSelectChange: (id: string, value: string) => void;
  handleCheckboxChange: (id: string, checked: boolean) => void;
  handleNext: () => void;
}

// Utility function to determine if room-related fields should be hidden
const shouldHideRoomFields = (propertyType: PropertyType): boolean => {
  const landAndCommercialTypes = [
    PropertyType.LAND_PLOT,
    PropertyType.PROJECT_LAND,
    PropertyType.OFFICE,
    PropertyType.WAREHOUSE,
    PropertyType.FACTORY,
    PropertyType.INDUSTRIAL,
    PropertyType.NEW_URBAN_AREA,
    PropertyType.ECO_RESORT,
    PropertyType.OTHER,
  ];

  return landAndCommercialTypes.includes(propertyType);
};

// Utility function to determine if building structure fields should be hidden
const shouldHideBuildingStructureFields = (propertyType: PropertyType): boolean => {
  const landTypes = [
    PropertyType.LAND_PLOT,
    PropertyType.PROJECT_LAND,
    PropertyType.NEW_URBAN_AREA,
    PropertyType.ECO_RESORT,
  ];

  return landTypes.includes(propertyType);
};

// Utility function to determine area field types based on property type
const getAreaFieldType = (
  propertyType: PropertyType
): 'residential' | 'land' | 'commercial' | 'house' => {
  const apartmentTypes = [
    PropertyType.APARTMENT,
    PropertyType.MINI_SERVICE_APARTMENT,
    PropertyType.SOCIAL_HOUSING,
    PropertyType.MOTEL,
    PropertyType.AIRBNB,
    PropertyType.HOTEL,
  ];

  const houseTypes = [
    PropertyType.HOUSE,
    PropertyType.TOWNHOUSE,
    PropertyType.VILLA,
    PropertyType.SHOP_HOUSE,
    PropertyType.COMMERCIAL_TOWNHOUSE,
  ];

  const landTypes = [
    PropertyType.LAND_PLOT,
    PropertyType.PROJECT_LAND,
    PropertyType.NEW_URBAN_AREA,
    PropertyType.ECO_RESORT,
  ];

  if (apartmentTypes.includes(propertyType)) {
    return 'residential';
  } else if (houseTypes.includes(propertyType)) {
    return 'house';
  } else if (landTypes.includes(propertyType)) {
    return 'land';
  } else {
    return 'commercial';
  }
};

const PropertyDetailsForm: React.FC<PropertyDetailsFormProps> = ({
  formData,
  handleSelectChange,
  handleCheckboxChange,
  handleNext,
}) => {
  const hideRoomFields = shouldHideRoomFields(formData.type);
  const hideBuildingStructureFields = shouldHideBuildingStructureFields(formData.type);
  const areaFieldType = getAreaFieldType(formData.type);

  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Header Section */}
      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Home className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">Thông tin chi tiết</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Nhập thông tin chi tiết về bất động sản
            </p>
          </div>
        </div>
      </div>

      {/* Room Configuration Card - Only show for residential properties */}
      {!hideRoomFields && (
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4 md:pb-6">
            <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
              <HomeIcon className="h-4 w-4 md:h-5 md:w-5" />
              Thông tin phòng
            </CardTitle>
            <CardDescription className="text-sm">
              Nhập số lượng phòng và không gian sống trong bất động sản
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 md:space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.bedrooms" className="text-sm font-medium">
                    Số phòng ngủ
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Input
                  id="propertyDetails.bedrooms"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Nhập số phòng ngủ"
                  value={formData.propertyDetails.bedrooms ?? ''}
                  onChange={e => handleSelectChange('propertyDetails.bedrooms', e.target.value)}
                  className="h-11"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.bathrooms" className="text-sm font-medium">
                    Số phòng tắm
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Input
                  id="propertyDetails.bathrooms"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Nhập số phòng tắm"
                  value={formData.propertyDetails.bathrooms ?? ''}
                  onChange={e => handleSelectChange('propertyDetails.bathrooms', e.target.value)}
                  className="h-11"
                />
              </div>

              <div className="space-y-3 sm:col-span-2 lg:col-span-1">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.livingRooms" className="text-sm font-medium">
                    Số phòng khách
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Input
                  id="propertyDetails.livingRooms"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Nhập số phòng khách"
                  value={formData.propertyDetails.livingRooms ?? ''}
                  onChange={e => handleSelectChange('propertyDetails.livingRooms', e.target.value)}
                  className="h-11"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Building Structure Card - Only show for properties with buildings */}
      {!hideBuildingStructureFields && (
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4 md:pb-6">
            <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
              <Building className="h-4 w-4 md:h-5 md:w-5" />
              Thông tin cấu trúc nhà
            </CardTitle>
            <CardDescription className="text-sm">
              Nhập các thông tin cấu trúc nhà và số tầng của bất động sản
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 md:space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.floorNumber" className="text-sm font-medium">
                    Số tầng
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Input
                  id="propertyDetails.floorNumber"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Nhập số tầng"
                  value={formData.propertyDetails.floorNumber ?? ''}
                  onChange={e => handleSelectChange('propertyDetails.floorNumber', e.target.value)}
                  className="h-11"
                />
                <p className="text-xs text-muted-foreground">Số Tầng của bất động sản</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.numberOfFloors" className="text-sm font-medium">
                    Tầng của bất động sản
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Input
                  id="propertyDetails.numberOfFloors"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Nhập tầng của bất động sản"
                  value={formData.propertyDetails.numberOfFloors ?? ''}
                  onChange={e =>
                    handleSelectChange('propertyDetails.numberOfFloors', e.target.value)
                  }
                  className="h-11"
                />
                <p className="text-xs text-muted-foreground">Tầng của bất động sản</p>
              </div>

              <div className="space-y-3 sm:col-span-2 lg:col-span-1">
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor="propertyDetails.apartmentOrientation"
                    className="text-sm font-medium"
                  >
                    Hướng của bất động sản
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <Select
                  value={formData.propertyDetails.apartmentOrientation}
                  onValueChange={value =>
                    handleSelectChange('propertyDetails.apartmentOrientation', value)
                  }
                >
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Chọn hướng của bất động sản" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ApartmentOrientation.NORTH}>Bắc</SelectItem>
                    <SelectItem value={ApartmentOrientation.SOUTH}>Nam</SelectItem>
                    <SelectItem value={ApartmentOrientation.EAST}>Đông</SelectItem>
                    <SelectItem value={ApartmentOrientation.WEST}>Tây</SelectItem>
                    <SelectItem value={ApartmentOrientation.NORTHEAST}>Đông Bắc</SelectItem>
                    <SelectItem value={ApartmentOrientation.NORTHWEST}>Tây Bắc</SelectItem>
                    <SelectItem value={ApartmentOrientation.SOUTHEAST}>Đông Nam</SelectItem>
                    <SelectItem value={ApartmentOrientation.SOUTHWEST}>Tây Nam</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Hướng chính của bất động sản</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Area Measurements Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-base md:text-lg font-medium flex items-center gap-2">
            <Ruler className="h-4 w-4 md:h-5 md:w-5" />
            Đo đạc diện tích
          </CardTitle>
          <CardDescription className="text-sm">
            Nhập các thông tin về diện tích và kích thước của bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {/* Land Area - Show for land properties and commercial properties */}
            {(areaFieldType === 'land' || areaFieldType === 'commercial') && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.landArea" className="text-sm font-medium">
                    Diện tích đất
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <div className="relative">
                  <Input
                    id="propertyDetails.landArea"
                    type="number"
                    min="0"
                    step="100"
                    placeholder="Nhập diện tích đất"
                    value={formData.propertyDetails.landArea ?? ''}
                    onChange={e => handleSelectChange('propertyDetails.landArea', e.target.value)}
                    className="h-11 pr-12"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                    m²
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">Diện tích đất của bất động sản</p>
              </div>
            )}

            {/* Apartment Area Fields - Only thông thủy and tim tường */}
            {areaFieldType === 'residential' && (
              <>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="propertyDetails.buildingArea" className="text-sm font-medium">
                      Diện tích thông thủy
                    </Label>
                    <span className="text-destructive text-sm">*</span>
                  </div>
                  <div className="relative">
                    <Input
                      id="propertyDetails.buildingArea"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập diện tích thông thủy"
                      value={formData.propertyDetails.buildingArea ?? ''}
                      onChange={e =>
                        handleSelectChange('propertyDetails.buildingArea', e.target.value)
                      }
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m²
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">Diện tích thông thủy</p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="propertyDetails.landArea" className="text-sm font-medium">
                      Diện tích tim tường
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      id="propertyDetails.landArea"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập diện tích tim tường"
                      value={formData.propertyDetails.landArea ?? ''}
                      onChange={e => handleSelectChange('propertyDetails.landArea', e.target.value)}
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m²
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">Diện tích tính từ tim tường</p>
                </div>
              </>
            )}

            {/* House Area Fields - Land area + thông thủy and tim tường */}
            {areaFieldType === 'house' && (
              <>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="propertyDetails.landArea" className="text-sm font-medium">
                      Diện tích đất
                    </Label>
                    <span className="text-destructive text-sm">*</span>
                  </div>
                  <div className="relative">
                    <Input
                      id="propertyDetails.landArea"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập diện tích đất"
                      value={formData.propertyDetails.landArea ?? ''}
                      onChange={e => handleSelectChange('propertyDetails.landArea', e.target.value)}
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m²
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">Diện tích đất của bất động sản</p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="propertyDetails.buildingArea" className="text-sm font-medium">
                      Diện tích thông thủy
                    </Label>
                    <span className="text-destructive text-sm">*</span>
                  </div>
                  <div className="relative">
                    <Input
                      id="propertyDetails.buildingArea"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập diện tích thông thủy"
                      value={formData.propertyDetails.buildingArea ?? ''}
                      onChange={e =>
                        handleSelectChange('propertyDetails.buildingArea', e.target.value)
                      }
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m²
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">Diện tích thông thủy</p>
                </div>
              </>
            )}

            {/* Building Area - Show for commercial properties */}
            {areaFieldType === 'commercial' && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label htmlFor="propertyDetails.buildingArea" className="text-sm font-medium">
                    Diện tích nhà
                  </Label>
                  <span className="text-destructive text-sm">*</span>
                </div>
                <div className="relative">
                  <Input
                    id="propertyDetails.buildingArea"
                    type="number"
                    min="0"
                    step="100"
                    placeholder="Nhập diện tích nhà"
                    value={formData.propertyDetails.buildingArea ?? ''}
                    onChange={e =>
                      handleSelectChange('propertyDetails.buildingArea', e.target.value)
                    }
                    className="h-11 pr-12"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                    m²
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">Diện tích nhà của bất động sản</p>
              </div>
            )}
          </div>

          {/* Land dimensions - Show for land properties, commercial properties, and house types */}
          {(areaFieldType === 'land' ||
            areaFieldType === 'commercial' ||
            areaFieldType === 'house') && (
            <>
              <Separator />

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                <div className="space-y-3">
                  <Label htmlFor="propertyDetails.landLength" className="text-sm font-medium">
                    Chiều dài đất
                  </Label>
                  <div className="relative">
                    <Input
                      id="propertyDetails.landLength"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập chiều dài đất"
                      value={formData.propertyDetails.landLength ?? ''}
                      onChange={e =>
                        handleSelectChange('propertyDetails.landLength', e.target.value)
                      }
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m
                    </span>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="propertyDetails.landWidth" className="text-sm font-medium">
                    Chiều rộng đất
                  </Label>
                  <div className="relative">
                    <Input
                      id="propertyDetails.landWidth"
                      type="number"
                      min="0"
                      step="100"
                      placeholder="Nhập chiều rộng đất"
                      value={formData.propertyDetails.landWidth ?? ''}
                      onChange={e =>
                        handleSelectChange('propertyDetails.landWidth', e.target.value)
                      }
                      className="h-11 pr-12"
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                      m
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Property Features Card - Only show for properties with buildings */}
      {!hideBuildingStructureFields && (
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4 md:pb-6">
            <CardTitle className="text-base md:text-lg font-medium">
              Tính năng bất động sản
            </CardTitle>
            <CardDescription className="text-sm">
              Chọn các tính năng và đặc điểm khác của bất động sản
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 md:space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="propertyDetails.hasBasement"
                    checked={formData.propertyDetails.hasBasement}
                    onCheckedChange={checked =>
                      handleCheckboxChange('propertyDetails.hasBasement', !!checked)
                    }
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="propertyDetails.hasBasement"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Có hầm
                    </Label>
                    <p className="text-xs text-muted-foreground">Bất động sản có hầm</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="propertyDetails.furnished"
                    checked={formData.propertyDetails.furnished}
                    onCheckedChange={checked =>
                      handleCheckboxChange('propertyDetails.furnished', !!checked)
                    }
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="propertyDetails.furnished"
                      className="text-sm font-medium cursor-pointer"
                    >
                      Có đồ nội thất
                    </Label>
                    <p className="text-xs text-muted-foreground">Bất động sản có đồ nội thất</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Card */}
      <Card className="border-dashed bg-muted/30">
        <CardContent className="p-4 md:p-6">
          <div className="flex items-start gap-3">
            <Info className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Mẹo đo đạc</p>
              <p className="text-xs text-muted-foreground">
                Đo đạc chính xác giúp khách hàng hiểu rõ hơn về bất động sản. Nếu bạn không chắc
                chắn về bất kỳ thông số nào, hãy thuê chuyên gia đo đạc hoặc để trống các trường
                thay vì đoán.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-4 md:pt-6 border-t">
        <Button onClick={handleNext} size="lg" className="px-6 md:px-8">
          Tiếp theo
        </Button>
      </div>
    </div>
  );
};

export default PropertyDetailsForm;
