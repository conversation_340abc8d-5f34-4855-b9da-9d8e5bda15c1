import { z } from 'zod';

// Phone number validation regex for Vietnam
const phoneRegex = /^(\+84|84|0)([3|5|7|8|9])+([0-9]{8})$/;

// ID number validation regex (12 digits for new ID, 9 digits for old ID)
const idNumberRegex = /^(\d{9}|\d{12})$/;

export const partyASchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  address: z.string().min(1, 'Địa chỉ không được để trống'),
  phone: z.string().regex(phoneRegex, 'Số điện thoại không hợp lệ'),
  fax: z.string().optional(),
  email: z.string().email('Email không hợp lệ'),
  taxCode: z.string().optional(),
  accountNumber: z.string().optional(),
  representative: z.string().min(1, 'Tên người đại diện không được để trống'),
  birthYear: z.string().min(1, '<PERSON><PERSON><PERSON> sinh không được để trống'),
  position: z.string().min(1, 'Chức vụ không được để trống'),
  idNumber: z.string().regex(idNumberRegex, 'Số CMND/CCCD không hợp lệ'),
  idIssuedDate: z.string().min(1, 'Ngày cấp không được để trống'),
  idIssuedPlace: z.string().min(1, 'Nơi cấp không được để trống'),
  personalName: z.string().optional(),
  personalBirthYear: z.string().optional(),
  personalIdNumber: z.string().optional(),
  personalIdIssuedDate: z.string().optional(),
  personalIdIssuedPlace: z.string().optional(),
  personalAddress: z.string().optional(),
  personalPhone: z.string().optional(),
  ownershipType: z.string().optional(),
});

export const partyBSchema = z
  .object({
    name: z.string().min(1, 'Tên không được để trống'),
    idNumber: z.string().regex(idNumberRegex, 'Số CMND/CCCD không hợp lệ'),
    phone: z.string().regex(phoneRegex, 'Số điện thoại không hợp lệ'),
    birthDate: z.string().min(1, 'Ngày sinh không được để trống'),
    address: z.string().min(1, 'Địa chỉ không được để trống'),
    email: z.string().email('Email không hợp lệ'),
    idVerification: z.array(z.string()).min(1, 'Phải upload ít nhất 1 ảnh CCCD'),
    leaseStartDate: z.string().min(1, 'Ngày bắt đầu thuê không được để trống'),
    leaseEndDate: z.string().min(1, 'Ngày kết thúc thuê không được để trống'),
  })
  .refine(
    data => {
      if (data.leaseStartDate && data.leaseEndDate) {
        const startDate = new Date(data.leaseStartDate);
        const endDate = new Date(data.leaseEndDate);
        return endDate > startDate;
      }
      return true;
    },
    {
      message: 'Ngày kết thúc thuê phải sau ngày bắt đầu thuê',
      path: ['leaseEndDate'],
    }
  );

export const contractTermsSchema = z
  .object({
    monthlyRent: z.number().min(1, 'Tiền thuê phải lớn hơn 0'),
    deposit: z.number().min(0, 'Tiền cọc không được âm'),
    paymentMethod: z.string().min(1, 'Phương thức thanh toán không được để trống'),
    paymentDay: z
      .number()
      .min(1, 'Ngày thanh toán phải từ 1-31')
      .max(31, 'Ngày thanh toán phải từ 1-31'),
    propertyAddress: z.string().min(1, 'Địa chỉ căn hộ không được để trống'),
    propertyPurpose: z.string().optional(),
    propertyType: z.string().optional(),
    apartmentNumber: z.string().optional(),
    floor: z.string().optional(),
    area: z.number().positive('Diện tích phải lớn hơn 0').optional(),
    landArea: z.number().positive('Diện tích đất phải lớn hơn 0').optional(),
    sharedArea: z.number().positive('Diện tích sử dụng chung phải lớn hơn 0').optional(),
    privateArea: z.number().positive('Diện tích sử dụng riêng phải lớn hơn 0').optional(),
    ownershipOrigin: z.string().optional(),
    ownershipRestrictions: z.string().optional(),
    facilities: z.string().optional(),
    leaseStartDate: z.string().min(1, 'Ngày bắt đầu thuê không được để trống'),
    leaseEndDate: z.string().min(1, 'Ngày kết thúc thuê không được để trống'),
  })
  .refine(
    data => {
      if (data.leaseStartDate && data.leaseEndDate) {
        const startDate = new Date(data.leaseStartDate);
        const endDate = new Date(data.leaseEndDate);
        return endDate > startDate;
      }
      return true;
    },
    {
      message: 'Ngày kết thúc thuê phải sau ngày bắt đầu thuê',
      path: ['leaseEndDate'],
    }
  );

export const contractClausesarticle4to6Schema = z.object({
  article4to6: z.string().min(1, 'Nội dung điều khoản 4-6 không được để trống'),
});

export const contractClausesarticle7to10Schema = z.object({
  article7to10: z.string().min(1, 'Nội dung điều khoản 7-10 không được để trống'),
});

export const formDataSchema = z.object({
  partyA: partyASchema,
  partyB: partyBSchema,
  terms: contractTermsSchema,
  clausesArticle4to6: contractClausesarticle4to6Schema,
  clausesarticle7to10: contractClausesarticle7to10Schema,
});

export type PartyAInput = z.infer<typeof partyASchema>;
export type PartyBInput = z.infer<typeof partyBSchema>;
export type ContractTermsInput = z.infer<typeof contractTermsSchema>;
export type FormDataInput = z.infer<typeof formDataSchema>;
export type ContractClausesArticle4to6Input = z.infer<typeof contractClausesarticle4to6Schema>;
export type ContractClausesarticle7to10Input = z.infer<typeof contractClausesarticle7to10Schema>;
