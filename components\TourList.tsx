'use client';

import type React from 'react';
import { useState, useMemo, useEffect } from 'react';
import debounce from 'lodash.debounce';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useUserTours, useDeleteTour } from '@/hooks/useTour';
import type { Tour } from '@/lib/api/services/fetchTour';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
} from '@/components/ui/pagination';
import { Card, CardContent } from '@/components/ui/card'; // For skeleton loading
import { TourCard } from './TourCard';

interface TourListProps {
  disabled: boolean;
  isDialog?: boolean;
  onEditTour?: (tour: Tour) => void;
}

export function TourList({ disabled, onEditTour, isDialog }: TourListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 5; // Number of tours per page
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce function using lodash.debounce
  const debouncedUpdate = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedSearchTerm(value);
        setPageNumber(1); // Reset page on search change
      }, 500), // 500ms debounce delay
    []
  );

  // Update searchTerm immediately, but debounce API call
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedUpdate(value);
  };

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedUpdate.cancel();
    };
  }, [debouncedUpdate]);

  // Use the useUserTours hook with the debounced search term
  const { tours, isLoading, isError, error, totalPages, refetch } = useUserTours({
    searchTerm: debouncedSearchTerm,
    pageNumber,
    pageSize,
  });

  // Use the delete tour hook
  const { mutate: deleteTour, isPending: isDeletingTour } = useDeleteTour();

  // Adjust page number if it exceeds total pages after a search/filter
  useEffect(() => {
    if (pageNumber > totalPages && totalPages > 0) {
      setPageNumber(totalPages);
    }
  }, [totalPages, pageNumber]);

  const isEmpty = !isLoading && tours.length === 0;

  const handleDeleteTour: (tourId: string) => void = (tourId: string) => {
    deleteTour(tourId, {
      onSuccess: () => {
        refetch();
        console.log('Tour deleted successfully');
      },
      onError: error => {
        console.error('Failed to delete tour:', error);
      },
    });
  };

  const isTableDisabled = disabled || isDeletingTour;

  return (
    <div className="space-y-4">
      <Input
        placeholder="Tìm kiếm lịch trình theo tên..."
        value={searchTerm}
        onChange={handleSearchChange}
        disabled={isTableDisabled}
      />
      {isLoading ? (
        // Skeleton loading state for the cards
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-8">
          {Array.from({ length: pageSize }).map((_, index) => (
            <Card
              key={index}
              className="w-full max-w-md mx-auto shadow-lg rounded-lg overflow-hidden animate-pulse"
            >
              <div className="w-full h-48 bg-gray-200" />
              <CardContent className="p-6 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-3/4" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="h-4 bg-gray-200 rounded w-full" />
                <div className="space-y-2 mt-4">
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                  <div className="h-4 bg-gray-200 rounded w-1/3" />
                  <div className="flex items-center gap-2">
                    <div className="h-12 w-16 bg-gray-200 rounded-md" />
                    <div className="space-y-1">
                      <div className="h-4 bg-gray-200 rounded w-24" />
                      <div className="h-4 bg-gray-200 rounded w-20" />
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-6">
                  <div className="h-8 w-24 bg-gray-200 rounded" />
                  <div className="h-8 w-16 bg-gray-200 rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : isError ? (
        <div className="text-center text-red-500 py-8">
          <p>{error?.message || 'Không thể tải danh sách lịch trình.'}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Thử lại
          </Button>
        </div>
      ) : isEmpty ? (
        <div className="text-center py-8 text-muted-foreground">
          {searchTerm ? 'Không tìm thấy lịch trình phù hợp.' : 'Bắt đầu tìm kiếm lịch trình.'}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tours.map(tour => (
              <TourCard
                key={tour.id}
                tour={tour}
                onEditTour={onEditTour ?? (() => {})}
                onDeleteTour={handleDeleteTour}
                isDeletingTour={isDeletingTour}
                disabled={isTableDisabled}
                hideActions={isDialog}
              />
            ))}
          </div>
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setPageNumber(p => Math.max(1, p - 1))}
                      className={pageNumber === 1 ? 'pointer-events-none text-gray-400' : ''}
                    >
                      Trước
                    </PaginationPrevious>
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      if (totalPages <= 5) return true;
                      return (
                        page === 1 ||
                        page === totalPages ||
                        (page >= pageNumber - 1 && page <= pageNumber + 1)
                      );
                    })
                    .reduce<number[]>((acc, page, i, arr) => {
                      if (i > 0 && page - arr[i - 1] > 1) {
                        acc.push(-1); // Dấu ...
                      }
                      acc.push(page);
                      return acc;
                    }, [])
                    .map((page, i) =>
                      page === -1 ? (
                        <PaginationItem key={`ellipsis-${i}`}>
                          <span className="px-2 text-muted-foreground">...</span>
                        </PaginationItem>
                      ) : (
                        <PaginationItem key={page}>
                          <Button
                            variant={pageNumber === page ? 'outline' : 'ghost'}
                            className={`px-3 py-1 text-sm ${pageNumber === page ? 'border border-gray-300' : ''}`}
                            onClick={() => setPageNumber(page)}
                          >
                            {page}
                          </Button>
                        </PaginationItem>
                      )
                    )}
                  {/* Nút Tiếp */}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setPageNumber(p => Math.min(totalPages, p + 1))}
                      className={
                        pageNumber === totalPages ? 'pointer-events-none text-gray-400' : ''
                      }
                    >
                      Tiếp
                    </PaginationNext>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
}
