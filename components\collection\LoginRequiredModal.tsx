'use client';

import { LogIn, User<PERSON><PERSON>, Heart } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface LoginRequiredModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function LoginRequiredModal({ isOpen, onClose }: LoginRequiredModalProps) {
  const router = useRouter();

  const handleLogin = () => {
    onClose();
    router.push('/login');
  };

  const handleRegister = () => {
    onClose();
    router.push('/register');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90vw] max-w-sm sm:max-w-md p-0 gap-0 bg-white mx-auto">
        {/* Header - Mobile optimized */}
        <DialogHeader className="px-4 sm:px-6 py-4 sm:py-5 border-b border-gray-100">
          <div className="pr-6 sm:pr-0">
            <DialogTitle className="text-base sm:text-lg font-medium text-gray-900 leading-tight">
              Yêu cầu đăng nhập
            </DialogTitle>
          </div>
        </DialogHeader>

        {/* Content - Mobile-first responsive */}
        <div className="px-4 sm:px-6 py-5 sm:py-6 text-center space-y-5 sm:space-y-6">
          {/* Icon - Mobile optimized */}
          <div className="mx-auto w-16 h-16 sm:w-18 sm:h-18 bg-red-50 rounded-full flex items-center justify-center">
            <Heart className="h-7 w-7 sm:h-8 sm:w-8 text-red-500" />
          </div>

          {/* Message - Responsive typography */}
          <div className="space-y-3 sm:space-y-2">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 leading-tight">
              Đăng nhập để lưu bất động sản yêu thích
            </h3>
            <p className="text-gray-600 text-sm sm:text-base leading-relaxed px-2 sm:px-0">
              Bạn cần đăng nhập để có thể tạo bộ sưu tập và lưu các bất động sản yêu thích. Điều này
              giúp bạn dễ dàng quản lý và theo dõi những bất động sản quan tâm.
            </p>
          </div>

          {/* Features - Mobile-optimized layout */}
          <div className="bg-blue-50 rounded-lg p-4 sm:p-5 text-left">
            <h4 className="font-medium text-gray-900 mb-3 text-sm sm:text-base">
              Lợi ích khi đăng nhập:
            </h4>
            <ul className="text-sm sm:text-sm text-gray-600 space-y-2 sm:space-y-1.5">
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <span className="leading-relaxed">Tạo và quản lý bộ sưu tập bất động sản</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <span className="leading-relaxed">Lưu và theo dõi bất động sản yêu thích</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <span className="leading-relaxed">Nhận thông báo về giá và cập nhật mới</span>
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <span className="leading-relaxed">Liên hệ trực tiếp với chuyên viên tư vấn</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Action Buttons - Mobile-first responsive layout */}
        <div className="px-4 sm:px-6 py-4 sm:py-5 border-t border-gray-100 space-y-3 sm:space-y-4">
          {/* Primary action - Login */}
          <Button
            onClick={handleLogin}
            className="w-full h-12 sm:h-11 bg-red-600 hover:bg-red-700 text-white font-medium text-base sm:text-sm"
          >
            <LogIn className="w-4 h-4 mr-2" />
            Đăng nhập ngay
          </Button>

          {/* Secondary actions - Mobile stacked, desktop inline */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-3">
            <Button
              variant="outline"
              onClick={handleRegister}
              className="flex-1 h-11 sm:h-10 border-gray-300 text-gray-700 hover:bg-gray-50 font-medium text-sm"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              <span className="hidden xs:inline">Đăng ký tài khoản</span>
              <span className="xs:hidden">Đăng ký</span>
            </Button>

            <Button
              variant="ghost"
              onClick={onClose}
              className="flex-1 h-11 sm:h-10 text-gray-600 hover:text-gray-800 hover:bg-gray-100 font-medium text-sm"
            >
              Để sau
            </Button>
          </div>

          {/* Footer text - Mobile-optimized */}
          <p className="text-xs text-gray-500 text-center mt-4 leading-relaxed px-2 sm:px-0">
            Miễn phí tạo tài khoản và sử dụng tất cả tính năng
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
