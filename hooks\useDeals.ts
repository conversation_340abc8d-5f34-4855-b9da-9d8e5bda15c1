import {
  ActionDealResponse,
  CreateDealRequest,
  DealDetailsResponse,
  DealLogResponse,
  DealLogSearchParams,
  DealNoteUpdateRequest,
  DealResponse,
  DealSearchParams,
  dealService,
  DealUpdateRequest,
} from '@/lib/api/services/fetchDeal';
import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

/**
 * Hook to fetch deals with filters
 */
export function useDeals(filters?: DealSearchParams) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['deals', 'list', filters ? JSON.stringify(filters) : 'all'],
    queryFn: () => dealService.getDeals(filters),
    select: (data: DealResponse) => ({
      deals: data.data?.data || [],
      count: data.data?.totalCount || 0,
      limit: data.data?.pageSize || 10,
      page: data.data?.pageNumber || 1,
      status: data.status,
      message: data.message,
    }),
  });
  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    deals: data?.deals || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    status: data?.status,
    message: data?.message,
  };
}

/**
 * Hook to fetch a single deal by ID
 */
export function useDeal(id: string) {
  return useQuery({
    queryKey: ['deals', 'detail', id],
    queryFn: () => dealService.getDeal(id),
    select: (data: DealDetailsResponse) => ({
      deal: data.data,
      status: data.status,
      message: data.message,
    }),
  });
}

/**
 * Hook to create a new deal with automatic toast notifications
 */
export function useCreateDeal() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (newDeal: Partial<CreateDealRequest>) => dealService.createDeal(newDeal),
    onSuccess: (data: ActionDealResponse) => {
      if (data.status) {
        toast.success(data.message || 'Deal created successfully');
        queryClient.invalidateQueries({ queryKey: ['deals', 'list'] });
      } else {
        toast.error(data.message || 'Failed to create deal');
      }
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Something went wrong while creating the deal';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to update an existing deal with automatic toast notifications
 */
export function useUpdateDeal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ...updateData }: Partial<DealUpdateRequest> & { id: string }) =>
      dealService.updateDeal(id, updateData),
    onSuccess: (data: ActionDealResponse, variables) => {
      if (data.status) {
        // Show success toast
        toast.success(data.message || 'Deal updated successfully');

        if (variables.status) {
          // For status updates, invalidate all related queries
          queryClient.invalidateQueries({
            queryKey: ['deals', 'list'],
            refetchType: 'active', // This forces an immediate refetch
          });

          // Invalidate detail queries
          queryClient.invalidateQueries({ queryKey: ['deals', 'detail'] });
          // Invalidate deal logs for the specific deal to get the newest status change log
          queryClient.invalidateQueries({
            queryKey: ['deals', 'logs', variables.id],
            refetchType: 'active',
          });

          // Also invalidate infinite scroll logs if you're using them
          queryClient.invalidateQueries({
            queryKey: ['deals', 'logs', 'infinite', variables.id],
          });
        } else {
          // For other updates, invalidate all deal-related queries
          queryClient.invalidateQueries({ queryKey: ['deals'] });
        }
      } else {
        // Show error toast if status is false
        toast.error(data.message || 'Failed to update deal');
      }
    },
    onError: (error: Error) => {
      // Show error toast for network/request errors
      const errorMessage = error.message || 'Something went wrong while updating the deal';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to delete a deal with automatic toast notifications
 */
export function useDeleteDeal() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => dealService.deleteDeal(id),
    onSuccess: (data: ActionDealResponse, id: string) => {
      if (data.status) {
        // Show success toast
        toast.success(data.message || 'Deal deleted successfully');
        // Remove the specific deal from cache and refresh list
        queryClient.removeQueries({ queryKey: ['deals', 'detail', 'deleted', id] });
        queryClient.invalidateQueries({ queryKey: ['deals', 'list'] });
      } else {
        // Show error toast if status is false
        toast.error(data.message || 'Failed to delete deal');
      }
    },
    onError: (error: Error) => {
      // Show error toast for network/request errors
      const errorMessage = error.message || 'Something went wrong while deleting the deal';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to update a specific deal note with automatic toast notifications
 */
export function useUpdateDealNote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      dealId,
      dealNoteId,
      ...noteData
    }: Partial<DealNoteUpdateRequest> & {
      dealId: string;
      dealNoteId: string;
    }) => dealService.updateDealNote(dealId, dealNoteId, noteData),
    onSuccess: (data: ActionDealResponse) => {
      if (data.status) {
        toast.success(data.message || 'Note updated successfully');
        queryClient.invalidateQueries({ queryKey: ['deals', 'detail'] });
        queryClient.invalidateQueries({ queryKey: ['deals', 'list'] });
      } else {
        toast.error(data.message || 'Failed to update note');
      }
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Something went wrong while updating the note';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to fetch deal logs with pagination
 */
export function useDealLogs(dealId: string, params?: DealLogSearchParams) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['deals', 'logs', dealId, params ? JSON.stringify(params) : 'all'],
    queryFn: () => dealService.getDealLogs(dealId, params),
    enabled: !!dealId,
    select: (data: DealLogResponse) => ({
      logs: data.data?.items || [],
      count: data.data?.totalCount || 0,
      limit: data.data?.pageSize || 10,
      page: data.data?.pageNumber || 1,
      totalPages: data.data?.totalPages || 1,
      status: data.status,
      message: data.message,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    logs: data?.logs || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
  };
}

/**
 * Hook to fetch deal logs with infinite scroll
 */
export function useDealLogsInfinite(dealId: string, pageSize: number = 10) {
  return useInfiniteQuery({
    queryKey: ['deals', 'logs', 'infinite', dealId, pageSize],
    queryFn: ({ pageParam = 1 }) =>
      dealService.getDealLogs(dealId, { pageNumber: pageParam, pageSize }),
    initialPageParam: 1,
    getNextPageParam: (lastPage: DealLogResponse) => {
      const { pageNumber, totalPages } = lastPage.data;
      return pageNumber < totalPages ? pageNumber + 1 : undefined;
    },
    select: (data: InfiniteData<DealLogResponse>) => ({
      pages: data.pages,
      pageParams: data.pageParams,
      logs: data.pages.flatMap(page => page.data.items),
      totalCount: data.pages[0]?.data.totalCount || 0,
    }),
  });
}
