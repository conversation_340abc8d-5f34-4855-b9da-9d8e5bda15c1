'use client';

import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { appointmentService, AppointmentResponse } from '@/lib/api/services/fetchAppointment';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, User, Phone, Mail, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function ManageTours() {
  const [appointments, setAppointments] = useState<NonNullable<AppointmentResponse['data']>[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAppointments = async () => {
      try {
        const response = await appointmentService.getUserAppointments();
        if (response.status) {
          setAppointments(
            response.data.filter(
              (appointment): appointment is NonNullable<AppointmentResponse['data']> =>
                appointment !== null
            )
          );
        } else {
          throw new Error(response.message);
        }
      } catch (error) {
        console.error('Không thể tải danh sách lịch hẹn');
      } finally {
        setIsLoading(false);
      }
    };

    loadAppointments();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'Đang mở';
      case 'completed':
        return 'Hoàn thành';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map(i => (
          <Card key={i} className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[1, 2, 3, 4, 5].map(j => (
                <div key={j} className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4 rounded" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-32" />
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Chưa có lịch hẹn nào</h3>
        <p className="text-muted-foreground mb-6">
          Bạn chưa có lịch hẹn xem nhà nào. Hãy tìm kiếm bất động sản phù hợp!
        </p>
        <Button asChild>
          <Link href="/properties">Tìm bất động sản</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {appointments.map(appointment => (
        <Card key={appointment.id} className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-lg font-medium">
              Lịch hẹn #{appointment.id.slice(-6)}
            </CardTitle>
            <Badge className={`${getStatusColor(appointment.status)} text-white`}>
              {getStatusText(appointment.status)}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Ngày hẹn:</span>
                <span>{format(new Date(appointment.date), 'dd/MM/yyyy', { locale: vi })}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Giờ hẹn:</span>
                <span>{format(new Date(appointment.date), 'HH:mm', { locale: vi })}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Môi giới:</span>
                <span>{appointment.saler.fullName}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">SĐT:</span>
                <span>{appointment.saler.phoneNumber}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Email:</span>
                <span className="truncate">{appointment.saler.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm pt-2 border-t">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Bất động sản:</span>
                <Link
                  href={`/properties/${appointment.propertyId}`}
                  className="text-primary hover:underline font-medium"
                >
                  #{appointment.propertyId.slice(-8)}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
