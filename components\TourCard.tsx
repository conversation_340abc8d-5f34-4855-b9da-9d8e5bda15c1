'use client';

import { useState, type ReactNode } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Loader2,
  Trash2,
  Eye,
  CalendarDays,
  CheckCircle,
  XCircle,
  Clock,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatTourTime, getTourStatusDisplay } from '@/utils/tour';
import { Tour } from '@/lib/api/services/fetchTour';
import { formatDateWithLocale } from '@/utils/dates/formatDate';
import { PropertyCard } from './PropertyCard';

interface TourCardProps {
  tour: Tour;
  onEditTour: (tour: Tour) => void;
  onDeleteTour: (tourId: string) => void;
  isDeletingTour: boolean;
  disabled: boolean;
  hideActions?: boolean;
}

const MAX_PROPERTIES_DISPLAY = 1; // Number of properties to show initially

export function TourCard({
  tour,
  onEditTour,
  onDeleteTour,
  isDeletingTour,
  disabled,
  hideActions,
}: TourCardProps) {
  const [showAllProperties, setShowAllProperties] = useState(false);

  const firstPropertyImage =
    tour.tourProperties?.[0]?.response?.imageUrls?.[0] || '/placeholder.svg?height=200&width=400';

  const getPropertyStatusIcon = (status: string): ReactNode => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'Approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'Rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'Pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const displayedProperties = showAllProperties
    ? tour.tourProperties
    : tour.tourProperties.slice(0, MAX_PROPERTIES_DISPLAY);

  const hasMoreProperties = tour.tourProperties.length > MAX_PROPERTIES_DISPLAY;

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg rounded-2xl overflow-hidden">
      <CardHeader className="p-4">
        <div className="h-[180px] flex gap-4 p-2 bg-slate-200/70 rounded-2xl shadow-md">
          <Image
            src={firstPropertyImage || '/placeholder.svg'}
            alt={tour.name || 'Tour Image'}
            width={400}
            height={200}
            className="w-28 h-40 object-cover rounded-2xl shadow-md"
          />
          <div className="flex-1 min-w-0 h-full">
            <div className="flex flex-col">
              <Badge
                variant="outline"
                className="flex items-center gap-1 text-sm text-muted-foreground mt-1 w-fit"
              >
                <CalendarDays className="h-4 w-4 flex-shrink-0" />
                <span className="whitespace-nowrap">
                  {formatDateWithLocale(tour.startDateTime, 'vi')}
                </span>
              </Badge>
              <div className="flex-1 mt-2 p-2">
                <p className=" font-bold break-words line-clamp-2 capitalize">{tour.name}</p>
                <p className="text-muted-foreground text-sm break-words line-clamp-2">
                  {tour.note || 'Không có ghi chú'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-6 space-y-4">
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Lịch trình chi tiết:</h3>
          {tour.tourProperties.length === 0 ? (
            <p className="text-muted-foreground text-sm">
              Không có bất động sản nào trong lịch trình này.
            </p>
          ) : (
            <div className="relative pl-6 ">
              <div className="absolute left-3 top-0 bottom-0 w-0.5 bg-gray-200" />
              {displayedProperties.map(prop => {
                const propertyStatus = getTourStatusDisplay(prop.status || '');
                return (
                  <div key={prop.id} className="relative mb-6 last:mb-0">
                    <div className="absolute -left-3 top-0 -translate-x-1/2 bg-white p-1 rounded-full z-10">
                      {getPropertyStatusIcon(prop.status ?? '')}
                    </div>
                    <div className="ml-4 space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        {formatTourTime(prop.visitTimeStart)} - {formatTourTime(prop.visitTimeEnd)}
                      </div>
                      {prop.response ? (
                        <PropertyCard
                          property={prop.response} // Cast to Property type
                          size="sm" // Use small size for compactness
                          priority={false}
                          onHover={() => {}}
                        />
                      ) : (
                        <p className="text-muted-foreground text-sm">
                          Thông tin bất động sản không khả dụng.
                        </p>
                      )}
                      <Badge variant="outline" className={cn(propertyStatus.colorClass)}>
                        {propertyStatus.text}
                      </Badge>
                    </div>
                  </div>
                );
              })}
              {hasMoreProperties && (
                <div className="flex justify-center mt-4">
                  <Button
                    variant="ghost"
                    onClick={() => setShowAllProperties(!showAllProperties)}
                    className="flex items-center gap-1 text-sm"
                  >
                    {showAllProperties ? (
                      <>
                        <ChevronUp className="h-4 w-4" /> Thu gọn
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-4 w-4" /> Xem thêm (
                        {tour.tourProperties.length - MAX_PROPERTIES_DISPLAY} nữa)
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
        {!hideActions && (
          <div className="flex justify-end gap-2 mt-6">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onEditTour(tour)}
              disabled={disabled}
              className="flex items-center gap-1"
            >
              <Eye className="h-4 w-4" />
              Xem chi tiết
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  disabled={disabled || isDeletingTour}
                  className="flex items-center gap-1"
                >
                  {isDeletingTour ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  Xóa
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Xác nhận xóa lịch trình</AlertDialogTitle>
                  <AlertDialogDescription>
                    Bạn có chắc chắn muốn xóa lịch trình {tour.name}? Hành động này không thể hoàn
                    tác.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeletingTour}>Hủy</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => onDeleteTour(tour.id)}
                    disabled={isDeletingTour}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isDeletingTour ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Đang xóa...
                      </>
                    ) : (
                      'Xóa'
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
