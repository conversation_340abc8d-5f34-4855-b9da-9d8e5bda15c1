import { AdvancedMarker, InfoWindow, useMap, useMapsLibrary } from '@vis.gl/react-google-maps';
import React, { useEffect, useRef, useState } from 'react';
import { Route, Hospital, Store, School, Utensils, Leaf, Coffee } from 'lucide-react';

export function NearbyPlaces({
  position,
  radius = 1200,
  types = ['school', 'restaurant'],
  onPlacesUpdate,
  onNavigateTo,
}: {
  position: google.maps.LatLngLiteral;
  radius?: number;
  types?: string[];
  onPlacesUpdate?: (places: Array<google.maps.places.PlaceResult & { _type?: string }>) => void;
  onNavigateTo?: (place: google.maps.places.PlaceResult & { _type?: string }) => void;
}) {
  const map = useMap();
  const placesLib = useMapsLibrary('places');
  const [places, setPlaces] = useState<Array<google.maps.places.PlaceResult & { _type?: string }>>(
    []
  );
  const [openPlaceId, setOpenPlaceId] = useState<string | null>(null);
  const [placeDetailsById, setPlaceDetailsById] = useState<
    Record<string, google.maps.places.PlaceResult>
  >({});
  const serviceRef = useRef<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    if (!map || !placesLib) return;

    let mounted = true;
    const service = new (placesLib as any).PlacesService(map as unknown as google.maps.Map);
    serviceRef.current = service as unknown as google.maps.places.PlacesService;

    setPlaces([]);
    types.forEach(t => {
      const request: google.maps.places.PlaceSearchRequest = {
        location: position,
        radius,
        type: t as any,
      };

      service.nearbySearch(
        request,
        (results: google.maps.places.PlaceResult[] | null, status: any) => {
          if (!mounted) return;
          if (status === 'OK' && results) {
            const mapped = results.map(r => ({ ...r, _type: t }));
            setPlaces(prev => {
              const merged = [...prev, ...mapped];
              const seen = new Set<string>();
              const deduped = merged.filter(p => {
                const id = (p.place_id ?? p.name ?? JSON.stringify(p)).toString();
                if (seen.has(id)) return false;
                seen.add(id);
                return true;
              });
              if (onPlacesUpdate) onPlacesUpdate(deduped);
              return deduped;
            });
          }
        }
      );
    });

    return () => {
      mounted = false;
    };
  }, [map, placesLib, position.lat, position.lng, radius, JSON.stringify(types)]);

  if (!map || !placesLib) return null;

  function latLngToLiteral(loc: google.maps.LatLng): google.maps.LatLngLiteral {
    return { lat: loc.lat(), lng: loc.lng() };
  }

  function getMarkerConfigForType(placeType?: string) {
    switch (placeType) {
      case 'school':
        return { color: '#3B82F6', Icon: School };
      case 'restaurant':
        return { color: '#059669', Icon: Utensils };
      case 'hospital':
        return { color: '#E11D48', Icon: Hospital };
      case 'supermarket':
        return { color: '#F59E0B', Icon: Store };
      case 'park':
        return { color: '#65A30D', Icon: Leaf };
      case 'cafe':
        return { color: '#CA8A04', Icon: Coffee };
      default:
        return { color: '#4B5563', Icon: Store };
    }
  }

  function requestPlaceDetails(placeId: string) {
    if (!serviceRef.current || !placeId) return;
    if (placeDetailsById[placeId]) return;

    const request: google.maps.places.PlaceDetailsRequest = {
      placeId,
      fields: [
        'name',
        'rating',
        'user_ratings_total',
        'formatted_address',
        'formatted_phone_number',
        'photos',
        'website',
        'reviews',
        'url',
        'opening_hours',
        'price_level',
        'types',
      ] as unknown as Array<keyof google.maps.places.PlaceResult>,
    };

    serviceRef.current.getDetails(
      request,
      (
        details: google.maps.places.PlaceResult | null,
        status: google.maps.places.PlacesServiceStatus
      ) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && details && details.place_id) {
          setPlaceDetailsById(prev => ({ ...prev, [details.place_id as string]: details }));
        }
      }
    );
  }

  function handleMarkerClick(
    id: string,
    place: google.maps.places.PlaceResult & { _type?: string }
  ) {
    setOpenPlaceId(prev => {
      const next = prev === id ? null : id;
      const placeId = place.place_id ?? null;
      if (next && placeId) requestPlaceDetails(placeId);
      return next;
    });
  }

  return (
    <>
      {places.map(p => {
        const loc = p.geometry?.location;
        if (!loc) return null;
        const pos = latLngToLiteral(loc);
        const id = p.place_id ?? p.name ?? Math.random().toString(36).slice(2);
        const { color, Icon } = getMarkerConfigForType(p._type);

        return (
          <React.Fragment key={id}>
            <AdvancedMarker position={pos} onClick={() => handleMarkerClick(id, p)}>
              <div className="relative" style={{ width: 28, height: 36 }}>
                <div
                  className="absolute left-1/2 top-0 -translate-x-1/2 w-7 h-7 rounded-full flex items-center justify-center text-white shadow-md"
                  style={{ backgroundColor: color }}
                >
                  <Icon className="w-3.5 h-3.5 text-white" />
                </div>
                <div
                  className="absolute left-1/2 -translate-x-1/2"
                  style={{
                    top: 26,
                    width: 0,
                    height: 0,
                    borderLeft: '6px solid transparent',
                    borderRight: '6px solid transparent',
                    borderTop: `8px solid ${color}`,
                  }}
                />
              </div>
            </AdvancedMarker>

            {openPlaceId === id && (
              <InfoWindow
                position={pos}
                onCloseClick={() => setOpenPlaceId(null)}
                headerDisabled
                shouldFocus={false}
                disableAutoPan
                pixelOffset={[0, -44]}
              >
                <div className="max-w-sm">
                  {(() => {
                    const details = (p.place_id && placeDetailsById[p.place_id]) || null;
                    const photoUrl =
                      details?.photos?.[0]?.getUrl({ maxWidth: 320, maxHeight: 200 }) ||
                      p.photos?.[0]?.getUrl({ maxWidth: 320, maxHeight: 200 });
                    const rating = details?.rating ?? p.rating;
                    const total = details?.user_ratings_total ?? p.user_ratings_total;
                    const address = details?.formatted_address ?? p.formatted_address ?? p.vicinity;
                    const website = details?.website;
                    const googleUrl = details?.url;
                    const priceLevel = details?.price_level;
                    const phone = details?.formatted_phone_number;
                    const firstReview =
                      details?.reviews && details.reviews.length > 0 ? details.reviews[0] : null;

                    return (
                      <div className="space-y-2">
                        {photoUrl && (
                          <div className="w-full h-40 overflow-hidden rounded-md">
                            <img
                              src={photoUrl}
                              alt={details?.name ?? p.name ?? 'photo'}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex items-start justify-between gap-3">
                          <div className="min-w-0">
                            <h4 className="font-semibold text-sm truncate">
                              {details?.name ?? p.name}
                            </h4>
                            {address && <p className="text-xs text-gray-600 truncate">{address}</p>}
                            {phone && <p className="text-xs text-gray-600">{phone}</p>}
                            <div className="flex items-center gap-2 text-xs text-gray-700 mt-1">
                              {rating !== undefined && (
                                <span className="font-medium">
                                  {rating.toFixed ? rating.toFixed(1) : rating}
                                </span>
                              )}
                              {total !== undefined && <span>({total})</span>}
                              {priceLevel !== undefined && (
                                <span className="ml-1">
                                  {'$'.repeat(Math.max(0, Math.min(4, priceLevel)))}
                                </span>
                              )}
                            </div>
                          </div>
                          <button
                            type="button"
                            aria-label="Get directions"
                            title="Get directions"
                            className="shrink-0 inline-flex items-center gap-1 px-2 py-1 rounded bg-red-600 text-white text-xs hover:bg-red-700"
                            onClick={() => {
                              if (onNavigateTo) onNavigateTo(p);
                              setOpenPlaceId(null);
                            }}
                          >
                            <Route className="w-3 h-3" />
                            Chỉ đường
                          </button>
                        </div>

                        {(website || googleUrl) && (
                          <div className="flex gap-3 text-xs">
                            {website && (
                              <a
                                href={website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 underline"
                              >
                                Website
                              </a>
                            )}
                            {googleUrl && (
                              <a
                                href={googleUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 underline"
                              >
                                View on Google Maps
                              </a>
                            )}
                          </div>
                        )}

                        {firstReview && (
                          <div className="border-t pt-2">
                            <p className="text-xs text-gray-800 line-clamp-4">
                              “{firstReview.text}”
                            </p>
                            <div className="text-[10px] text-gray-500 mt-1">
                              — {firstReview.author_name}{' '}
                              {firstReview.rating !== undefined ? `· ${firstReview.rating}★` : ''}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              </InfoWindow>
            )}
          </React.Fragment>
        );
      })}
    </>
  );
}
