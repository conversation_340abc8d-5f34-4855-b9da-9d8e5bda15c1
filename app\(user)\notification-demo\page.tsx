'use client';

import { useAuth } from '@/hooks/useAuth'; // Giả sử bạn có auth context
import { useNotifications } from '@/hooks/useNotification';

export default function Page() {
  const { token } = useAuth();

  // Auto-connect và nhận notifications
  // Ensure token is string | undefined, not null
  const { isConnected, notifications } = useNotifications(token ?? undefined);

  return (
    <div>
      <p>Connection Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}</p>
      <p>Total Notifications: {notifications.length}</p>
    </div>
  );
}
