import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { clause3To6Schema, Clause3To6Input } from '../schemas';

const DEFAULT_CLAUSE_3_TO_6_CONTENT = `Điều 3. Thời hạn giao nhận nhà ở

1. <PERSON>ên bán có trách nhiệm bàn giao nhà ở kèm theo các trang thiết bị gắn với nhà ở đó và giấy tờ pháp lý về nhà ở nêu tại Điều 1 của hợp đồng này cho Bên mua trong thời hạn là...............ngày, kể từ ngày Bên mua thanh toán đủ số tiền mua nhà ở (hoặc kể từ ngày hợp đồng này được ký kết). Việc bàn giao nhà ở phải lập thành biên bản có chữ ký xác nhận của hai bên.

2. Các trường hợp thỏa thuận khác...............................

Điều 4. Bảo hành nhà ở

1. Bên bán có trách nhiệm bảo hành nhà ở theo đúng quy định tại Điều 85 của Luật Nhà ở năm 2014.

2. Bên mua phải kịp thời thông báo bằng văn bản cho Bên bán khi nhà ở có các hư hỏng thuộc diện được bảo hành. Trong thời hạn...........ngày, kể từ ngày nhận được thông báo của Bên mua, Bên bán có trách nhiệm thực hiện việc bảo hành các hư hỏng theo đúng quy định. Nếu Bên bán chậm thực hiện bảo hành mà gây thiệt hại cho Bên mua thì phải chịu trách nhiệm bồi thường.

3. Không thực hiện việc bảo hành trong các trường hợp nhà ở bị hư hỏng do thiên tai, địch họa hoặc do lỗi của người sử dụng gây ra.

4. Sau thời hạn bảo hành theo quy định của Luật Nhà ở, việc sửa chữa những hư hỏng thuộc trách nhiệm của Bên mua.

Điều 5. Quyền và nghĩa vụ của Bên bán

1. Quyền của Bên bán:

a) Yêu cầu Bên mua trả đủ tiền mua nhà theo đúng thỏa thuận nêu tại Điều 2 của hợp đồng này;

b) Yêu cầu Bên mua nhận bàn giao nhà ở theo đúng thỏa thuận nêu tại Điều 3 của hợp đồng này;

c) Yêu cầu Bên mua nộp đầy đủ các nghĩa vụ tài chính liên quan đến việc mua bán nhà ở theo quy định của pháp luật;

d) Yêu cầu Bên mua đảm bảo quyền lợi hợp pháp của người thứ ba (trong trường hợp nhà ở mua bán đang cho thuê, cho mượn).

2. Nghĩa vụ của Bên bán:

a) Bàn giao nhà ở kèm theo hồ sơ cho Bên mua theo đúng thỏa thuận tại Điều 3 của hợp đồng này;

b) Bảo quản nhà ở trong thời gian chưa bàn giao nhà cho Bên mua;

c) Nộp tiền sử dụng đất và các khoản phí, lệ phí khác liên quan đến mua bán nhà ở theo quy định của pháp luật;

d) Thông báo cho Bên mua các hạn chế về quyền sở hữu đối với nhà ở (nếu mua bán nhà ở là căn hộ nhà chung cư thì Bên bán phải thông báo cho Bên mua biết rõ các quyền và nghĩa vụ đối với phần diện tích sở hữu chung, sở hữu riêng của căn hộ mua bán và quy chế quản lý sử dụng nhà chung cư đó);

đ) Có trách nhiệm làm thủ tục đề nghị cấp giấy chứng nhận quyền sở hữu đối với nhà ở cho Bên mua (trừ trường hợp các bên có thỏa thuận Bên mua đi làm thủ tục).

Điều 6. Quyền và nghĩa vụ của Bên mua

1. Quyền của Bên mua:

a) Yêu cầu Bên bán bàn giao nhà kèm theo giấy tờ về nhà ở theo đúng thỏa thuận tại Điều 3 của Hợp đồng này;

b) Yêu cầu Bên bán phối hợp, cung cấp các giấy tờ có liên quan để làm thủ tục đề nghị cấp giấy chứng nhận quyền sở hữu đối với nhà ở (nếu Bên mua đi làm thủ tục này);

c) Yêu cầu Bên bán nộp đầy đủ các khoản phí, lệ phí liên quan đến việc mua bán nhà ở theo quy định của pháp luật.

2. Nghĩa vụ của Bên mua:

a) Trả đầy đủ tiền mua nhà theo đúng thỏa thuận tại Điều 2 của hợp đồng này;

b) Nhận bàn giao nhà ở kèm theo giấy tờ về nhà ở theo đúng thoả thuận của hợp đồng này;

c) Nộp đầy đủ các khoản thuế, phí, lệ phí liên quan đến mua bán nhà ở cho Nhà nước theo quy định của pháp luật;

d) Bảo đảm quyền, lợi ích của người thứ ba theo quy định của pháp luật (nếu nhà ở mua bán đang được cho thuê, cho mượn).`;

interface Clause3To6FormProps {
  data: Clause3To6Input;
  onNext: (data: Clause3To6Input) => void;
  onBack?: () => void;
}

const Clause3To6Form: React.FC<Clause3To6FormProps> = ({ data, onNext, onBack }) => {
  const form = useForm<Clause3To6Input>({
    resolver: zodResolver(clause3To6Schema),
    defaultValues: {
      Clause3To6: data.Clause3To6 || DEFAULT_CLAUSE_3_TO_6_CONTENT,
    },
  });

  // Đồng bộ dữ liệu form với prop data khi data thay đổi
  React.useEffect(() => {
    form.reset({
      Clause3To6: data.Clause3To6 || DEFAULT_CLAUSE_3_TO_6_CONTENT,
    });
  }, [data, form]);

  const onSubmit = (formData: Clause3To6Input) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Điều khoản 3-6</CardTitle>
        <CardDescription>
          Quyền và nghĩa vụ của bên bán, quyền và nghĩa vụ của bên mua
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="Clause3To6"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung điều khoản 3-6 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung điều khoản 3-6: Quyền và nghĩa vụ của các bên..."
                      rows={12}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-2">Gợi ý nội dung:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Điều 3: Thời hạn giao nhận nhà ở</li>
                    <li>Điều 4: Bảo hành nhà ở</li>
                    <li>Điều 5: Quyền và nghĩa vụ của bên bán</li>
                    <li>Điều 6: Quyền và nghĩa vụ của bên mua</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Quay lại
                </Button>
              )}
              <Button type="submit" className="ml-auto">
                Tiếp tục
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export { Clause3To6Form };
