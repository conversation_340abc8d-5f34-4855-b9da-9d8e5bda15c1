interface OrganizationStructuredDataProps {
  url?: string;
}

export default function OrganizationStructuredData({
  url = 'https://www.revoland.vn',
}: OrganizationStructuredDataProps) {
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Revoland',
    alternateName: 'RevoLand',
    description:
      'Nền tảng công nghệ bất động sản toàn diện, chuyên cung cấp dịch vụ mua bán, cho thuê nhà đất, biệt thự, căn hộ và đất nền với đội ngũ chuyên gia uy tín, tận tâm.',
    url: url,
    logo: {
      '@type': 'ImageObject',
      url: `${url}/LOGO_RV_red-01-01.png`,
      width: 200,
      height: 200,
    },
    image: {
      '@type': 'ImageObject',
      url: `${url}/banner_revoland.jpg`,
      width: 1200,
      height: 630,
    },
    foundingDate: '2020',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'VN',
      addressLocality: '<PERSON><PERSON>',
      addressRegion: '<PERSON><PERSON>',
    },
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['Vietnamese'],
    },
    sameAs: ['https://www.facebook.com/revoland.official', 'https://zalo.me/revoland'],
    areaServed: {
      '@type': 'Country',
      name: 'Vietnam',
    },
    serviceType: [
      'Real Estate Services',
      'Property Management',
      'Real Estate Consultation',
      'Property Investment Advisory',
    ],
    knowsAbout: [
      'Real Estate',
      'Property Management',
      'Real Estate Investment',
      'Property Sales',
      'Property Rental',
      'Real Estate Technology',
    ],
  };

  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Revoland',
    alternateName: 'RevoLand - Nền Tảng Công Nghệ Bất Động Sản',
    description:
      'Revoland - Giải pháp bất động sản toàn diện, đáng tin cậy. Chuyên cung cấp dịch vụ mua bán, cho thuê nhà đất, biệt thự, căn hộ và đất nền.',
    url: url,
    publisher: {
      '@type': 'Organization',
      name: 'Revoland',
      logo: {
        '@type': 'ImageObject',
        url: `${url}/LOGO_RV_red-01-01.png`,
      },
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${url}/properties?search={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    inLanguage: 'vi',
    copyrightYear: new Date().getFullYear(),
    copyrightHolder: {
      '@type': 'Organization',
      name: 'Revoland',
    },
  };

  const localBusinessData = {
    '@context': 'https://schema.org',
    '@type': 'RealEstateAgent',
    name: 'Revoland',
    alternateName: 'RevoLand',
    description:
      'Công ty bất động sản hàng đầu Việt Nam, chuyên cung cấp dịch vụ mua bán, cho thuê và tư vấn đầu tư bất động sản.',
    url: url,
    logo: {
      '@type': 'ImageObject',
      url: `${url}/LOGO_RV_red-01-01.png`,
    },
    image: {
      '@type': 'ImageObject',
      url: `${url}/banner_revoland.jpg`,
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'VN',
      addressLocality: 'Hồ Chí Minh',
      addressRegion: 'Hồ Chí Minh',
    },
    areaServed: [
      {
        '@type': 'Country',
        name: 'Vietnam',
      },
    ],
    serviceArea: [
      {
        '@type': 'City',
        name: 'Hồ Chí Minh',
      },
      {
        '@type': 'City',
        name: 'Hà Nội',
      },
    ],
    priceRange: '$$',
    currenciesAccepted: 'VND',
    paymentAccepted: ['Cash', 'Bank Transfer'],
    openingHours: 'Mo-Su 08:00-22:00',
    sameAs: ['https://www.facebook.com/revoland.official', 'https://zalo.me/revoland'],
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessData),
        }}
      />
    </>
  );
}
