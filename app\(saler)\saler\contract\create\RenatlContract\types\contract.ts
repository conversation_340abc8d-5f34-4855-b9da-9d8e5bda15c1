import {
  PartyAInput,
  PartyBInput,
  ContractTermsInput,
  ContractClausesArticle4to6Input,
  ContractClausesarticle7to10Input,
} from '../schemas';

export interface ContractData {
  partyA: PartyAInput;
  partyB: PartyBInput;
  terms: ContractTermsInput;
  clausesArticle4to6: ContractClausesArticle4to6Input;
  clausesarticle7to10: ContractClausesarticle7to10Input;
  contractContent?: string;
}

export type {
  PartyAInput,
  PartyBInput,
  ContractTermsInput,
  ContractClausesArticle4to6Input,
  ContractClausesarticle7to10Input,
};
