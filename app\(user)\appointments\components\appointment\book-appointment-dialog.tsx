// components/appointment/book-appointment-dialog.tsx
'use client';

import { useState } from 'react';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar, Clock, MapPin, User } from 'lucide-react';
import { useCreateAppointment } from '@/hooks/useAppointment';
import { AppointmentRequest } from '@/lib/api/services/fetchAppointment';

interface BookAppointmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDate: Date;
  propertyId?: string;
}

export function BookAppointmentDialog({
  open,
  onOpenChange,
  selectedDate,
  propertyId: initialPropertyId,
}: BookAppointmentDialogProps) {
  const [formData, setFormData] = useState({
    propertyId: initialPropertyId || '',
    time: '',
    notes: '',
  });

  const createAppointmentMutation = useCreateAppointment();

  const timeSlots = [
    '08:00',
    '08:30',
    '09:00',
    '09:30',
    '10:00',
    '10:30',
    '11:00',
    '11:30',
    '13:00',
    '13:30',
    '14:00',
    '14:30',
    '15:00',
    '15:30',
    '16:00',
    '16:30',
    '17:00',
    '17:30',
    '18:00',
    '18:30',
    '19:00',
    '19:30',
    '20:00',
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.propertyId || !formData.time) {
      return;
    }

    // Combine date and time
    const [hours, minutes] = formData.time.split(':').map(Number);
    const appointmentDateTime = new Date(selectedDate);
    appointmentDateTime.setHours(hours, minutes, 0, 0);

    const request: AppointmentRequest = {
      propertyId: formData.propertyId,
      date: appointmentDateTime.toISOString(),
    };

    try {
      await createAppointmentMutation.mutateAsync(request);
      onOpenChange(false);
      // Reset form
      setFormData({
        propertyId: initialPropertyId || '',
        time: '',
        notes: '',
      });
    } catch (error) {
      // Error is handled in the mutation
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    // Reset form when closing
    setFormData({
      propertyId: initialPropertyId || '',
      time: '',
      notes: '',
    });
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base sm:text-lg">
            <Calendar className="h-4 w-4 sm:h-5 sm:w-5" />
            Đặt lịch hẹn
          </DialogTitle>
          <DialogDescription className="text-sm">
            Tạo lịch hẹn mới cho ngày{' '}
            <span className="font-medium">
              {format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })}
            </span>
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Selected Date Display */}
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="text-sm font-medium">
              {format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })}
            </span>
          </div>

          {/* Property ID */}
          <div className="space-y-2">
            <Label htmlFor="propertyId" className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4" />
              ID Bất động sản *
            </Label>
            <Input
              id="propertyId"
              placeholder="Nhập ID bất động sản"
              value={formData.propertyId}
              onChange={e => setFormData(prev => ({ ...prev, propertyId: e.target.value }))}
              required
              className="text-sm"
            />
          </div>

          {/* Time Selection */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4" />
              Thời gian *
            </Label>
            <Select
              value={formData.time}
              onValueChange={value => setFormData(prev => ({ ...prev, time: value }))}
              required
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Chọn thời gian" />
              </SelectTrigger>
              <SelectContent>
                {timeSlots.map(time => (
                  <SelectItem key={time} value={time} className="text-sm">
                    {time}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="flex items-center gap-2 text-sm">
              <User className="h-4 w-4" />
              Ghi chú (tùy chọn)
            </Label>
            <Textarea
              id="notes"
              placeholder="Thêm ghi chú cho lịch hẹn..."
              value={formData.notes}
              onChange={e => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="text-sm resize-none"
            />
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createAppointmentMutation.isPending}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={
                createAppointmentMutation.isPending || !formData.propertyId || !formData.time
              }
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {createAppointmentMutation.isPending ? 'Đang tạo...' : 'Đặt lịch hẹn'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
