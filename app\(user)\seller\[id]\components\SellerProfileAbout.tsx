'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User, Award, Target, MapPin } from 'lucide-react';

interface SellerProfileAboutProps {
  about: string;
  specialties: string[];
  experience: string;
  joinedAt?: string;
  serviceLocations?: string | null;
}

function SellerProfileAbout({
  about,
  specialties,
  experience,
  joinedAt,
  serviceLocations,
}: SellerProfileAboutProps) {
  // Format joined date
  const formatJoinedDate = (dateString?: string) => {
    if (!dateString) return 'Chưa cập nhật';
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };
  return (
    <div className="space-y-6">
      {/* About Section */}
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <User className="size-4 md:size-5" />
            <span>Giới thiệu</span>
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Giới thiệu về chuyên viên bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="max-md:px-4 max-md:pb-3">
          <p className="text-gray-700 leading-relaxed">{about}</p>
        </CardContent>
      </Card>

      {/* Specialties Section */}
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <Target className="size-4 md:size-5" />
            <span>Chuyên môn</span>
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Danh sách các chuyên môn của chuyên viên bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="max-md:px-4 max-md:pb-3">
          <div className="flex flex-wrap gap-2">
            {specialties.map((specialty, index) => (
              <Badge key={index} variant="secondary" className="px-3 py-1">
                {specialty}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Experience Section */}
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <Award className="size-4 md:size-5" />
            <span>Kinh nghiệm</span>
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Kinh nghiệm làm việc của chuyên viên bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="max-md:px-4 max-md:pb-3">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-semibold text-gray-900">Kinh nghiệm làm việc</h4>
                <p className="text-gray-600">{experience} trong lĩnh vực bất động sản</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {experience.includes('năm') ? experience.split(' ')[0] : 'N/A'}
                </div>
                <div className="text-sm text-gray-500">Năm</div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
              <div>
                <h4 className="font-semibold text-gray-900">Ngày tham gia</h4>
                <p className="text-gray-600">Tham gia từ {formatJoinedDate(joinedAt)}</p>
              </div>
            </div>

            <div className="p-4 border rounded-lg">
              <h5 className="font-semibold text-gray-900 mb-2">Chứng chỉ</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Chứng chỉ hành nghề môi giới BDS</li>
                <li>• Chứng chỉ định giá BDS</li>
                <li>• Chứng chỉ tư vấn đầu tư</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Service Areas Section */}
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <MapPin className="size-4 md:size-5" />
            <span>Khu vực hoạt động</span>
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Các khu vực mà chuyên viên có thể cung cấp dịch vụ
          </CardDescription>
        </CardHeader>
        <CardContent className="max-md:px-4 max-md:pb-3">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {serviceLocations ? (
              serviceLocations.split(',').map((location, index) => (
                <div key={index} className="p-3 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <MapPin className="size-4 text-blue-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-800">{location.trim()}</span>
                </div>
              ))
            ) : (
              <>
                <div className="p-3 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <MapPin className="size-4 text-blue-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-800">TP. Hồ Chí Minh</span>
                </div>
                <div className="p-3 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <MapPin className="size-4 text-green-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-800">Bình Dương</span>
                </div>
                <div className="p-3 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <MapPin className="size-4 text-purple-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-800">Đồng Nai</span>
                </div>
                <div className="p-3 rounded-lg text-center">
                  <div className="flex items-center justify-center mb-2">
                    <MapPin className="size-4 text-orange-600" />
                  </div>
                  <span className="text-sm font-medium text-gray-800">Tây Ninh</span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default SellerProfileAbout;
