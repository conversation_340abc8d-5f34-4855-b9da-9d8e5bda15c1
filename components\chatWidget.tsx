'use client';

import { useState, useEffect, useRef } from 'react';
import {
  X,
  Send,
  MoreHorizontal,
  ChevronUp,
  MessageCircle,
  Smile,
  Paperclip,
  MessageCircleMore,
  Loader2,
  Play,
  FileText,
  MessageSquareShare,
  User,
  Plus,
  ChevronDown,
} from 'lucide-react';
import Image from 'next/image';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Message,
  PropertyCardMessage,
  SenderType,
  SendMessageRequest,
} from '@/lib/api/services/fetchChat';
import { signalRService } from '@/lib/realtime/signalR';
import { PropertyCard } from '@/components/PropertyCard';
import { usePathname } from 'next/navigation';
import { useChat } from '@/hooks/useChat';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import {
  useUploadAttachment,
  useUploadPropertyImage,
  useUploadPropertyVideo,
} from '@/hooks/useAttachment';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import type { Property } from '@/lib/api/services/fetchProperty';
import { useRouter } from 'next/navigation';
import { chatService } from '@/lib/api/services/fetchChat';
import { cn } from '@/lib/utils';
import { AttachmentLike } from '@/lib/api/services/fetchAttachment';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';
import { toast } from 'sonner';

// Add useIsMobile hook
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  return isMobile;
};

interface ChatWidgetProps {
  isOpen?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  property?: Property;
}

interface PropertyCardInChatProps {
  property: Property;
  direction?: string;
}
function PropertyCardInChat({ property, direction }: PropertyCardInChatProps) {
  const [, setIsHovered] = useState(false);
  const isUser = direction === 'inbound';
  return (
    <div
      className={`flex items-end pb-1 text-sm group relative ${isUser ? 'justify-end' : 'justify-start'}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Avatar for property card messages */}
      {isUser ? (
        <div style={{ width: 27, height: 27 }} />
      ) : (
        <div className="mr-2">
          <Image
            src="/logo_revoland_black.png"
            alt="Revoland Logo"
            width={27}
            height={27}
            className="rounded-full bg-white"
          />
        </div>
      )}
      {/* Property card as a message bubble */}
      <div className="flex max-w-xs lg:max-w-md  py-2">
        <div className="border border-gray-300 rounded-xl shadow-md bg-white p-1 w-full h-full overflow-hidden">
          <PropertyCard property={property} size="sm" />
        </div>
        <div className="ml-2 mt-2 h-full justify-center rounded-full bg-red-500 p-1 flex items-center justify-center">
          <User size={20} className="text-white" />
        </div>
      </div>
    </div>
  );
}

interface MessageItem extends Message {
  type: 'message';
}
type MessageItemUnion = PropertyCardMessage | MessageItem;

export const ChatWidget = ({ isOpen: isOpenProp, onOpen, onClose, property }: ChatWidgetProps) => {
  const pathname = usePathname();
  // const isPropertyDetail = pathname?.startsWith('/properties/');
  // if (pathname === '/login' || !isPropertyDetail) {
  //   return null;
  // }
  const isPropertyDetail = pathname?.startsWith('/properties/') || !(pathname === '/login');
  const [isOpenInternal, setIsOpenInternal] = useState(false);
  // const [, setCloseCount] = useState(0);
  // const [, setLastClosedTime] = useState<number | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevConversationId = useRef<string | null>(null);
  const lastAutoOpenConversationId = useRef<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { refetchConversation, sendMessage, pinMessage, unpinMessage, deleteMessage } = useChat();
  const isMobile = useIsMobile();
  const [pinnedMessages, setPinnedMessages] = useState<Message[]>([]);
  const [replyingMessage, setReplyingMessage] = useState<Message | null>(null);
  const [openMenuMessageId, setOpenMenuMessageId] = useState<string | null>(null);
  const [showPinnedList, setShowPinnedList] = useState(false);
  const messageRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const router = useRouter();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const isLoadingMoreRef = useRef(false);
  const isFirstLoad = useRef(true);
  const pageSize = 10;
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const [lastLoadMoreTime, setLastLoadMoreTime] = useState(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const uploadAttachment = useUploadAttachment();
  const uploadImage = useUploadPropertyImage();
  const uploadVideo = useUploadPropertyVideo();

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedAttachmentIds, setUploadedAttachmentIds] = useState<string[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<{ url: string; type: string } | null>(null);
  const [propertyCardMessages, setPropertyCardMessages] = useState<
    { property: Property; id: number; timestamp: Date }[]
  >([]);
  const [pendingProperty, setPendingProperty] = useState<Property | null>(null);
  const [pendingScrollMessageId, setPendingScrollMessageId] = useState<string | null>(null);

  const isControlled = typeof isOpenProp === 'boolean';
  const isOpen = isControlled ? isOpenProp : isOpenInternal;
  const setIsOpen = isControlled
    ? (open: boolean) => {
        if (open) {
          onOpen?.();
        } else {
          onClose?.();
        }
      }
    : setIsOpenInternal;

  useEffect(() => {
    const ensureConversation = async () => {
      let cid = conversationId;
      let uid = userId;

      if (property?.saler?.id && isPropertyDetail) {
        try {
          const sellerConversation = await chatService.getSellerConversation(property.saler.id);
          if (sellerConversation?.data) {
            cid = sellerConversation.data.id;
            uid =
              (typeof sellerConversation.data.platformUser?.id === 'string' &&
                sellerConversation.data.platformUser.id) ||
              (typeof (sellerConversation.data as { userId?: string })?.userId === 'string' &&
                (sellerConversation.data as { userId?: string }).userId) ||
              (typeof (sellerConversation.data as { sellerId?: string })?.sellerId === 'string' &&
                (sellerConversation.data as { sellerId?: string }).sellerId) ||
              null;
            if (Array.isArray(sellerConversation?.data?.pinMessages)) {
              setPinnedMessages(
                sellerConversation.data.pinMessages.map(msg => ({
                  ...msg,
                  messageId: msg.messageId || msg.id || '',
                  id: msg.id || '',
                  senderId: msg.senderId,
                  content: msg.content,
                  timestamp: msg.timestamp
                    ? new Date(msg.timestamp)
                    : msg.createdAt
                      ? new Date(msg.createdAt)
                      : new Date(),
                  createdAt: msg.createdAt ? new Date(msg.createdAt) : new Date(),
                  direction: msg.direction || 'inbound',
                  attachments:
                    Array.isArray(msg.attachments) &&
                    msg.attachments.length > 0 &&
                    msg.attachments.every(att => typeof att === 'object')
                      ? (msg.attachments as {
                          id: string;
                          type: string;
                          url: string;
                          fileName: string;
                        }[])
                      : [],
                }))
              );
            }
            if (typeof cid === 'string' && typeof uid === 'string') {
              setConversationId(cid);
              setUserId(uid);

              // localStorage.setItem(
              //   'chat_conversation',
              //   JSON.stringify({
              //     conversationId: cid,
              //     userId: uid,
              //     sellerId: property.saler.id,
              //   })
              // );
              await signalRService.connect();
              await signalRService.joinConversation(cid, uid);
              return { cid, uid };
            } else {
              return { cid: undefined, uid: undefined };
            }
          } else {
            console.log('No seller conversation data received');
          }
        } catch (error) {
          console.error('Failed to fetch seller conversation:', error);
        }
      } else {
        console.log('No property.saler.id or not property detail page');
      }

      if (!cid || !uid) {
        const result = await refetchConversation();
        if (result.data?.data) {
          if (Array.isArray(result.data.data)) {
            const firstConversation = result.data.data[0];
            cid = firstConversation?.id ?? null;
            let tempUid = null;
            if (typeof firstConversation?.platformUser?.id === 'string') {
              tempUid = firstConversation.platformUser.id;
            } else if (typeof (firstConversation as { userId?: string })?.userId === 'string') {
              tempUid = (firstConversation as { userId?: string }).userId ?? null;
            }
            uid = tempUid ?? null;
          } else {
            cid = result.data.data?.id ?? null;
            let tempUid = null;
            if (typeof result.data.data?.platformUser?.id === 'string') {
              tempUid = result.data.data.platformUser.id;
            } else if (typeof (result.data.data as { userId?: string })?.userId === 'string') {
              tempUid = (result.data.data as { userId?: string }).userId ?? null;
            }
            uid = tempUid ?? null;
          }

          if (cid && uid) {
            setConversationId(cid);
            setUserId(uid);
            // localStorage.setItem(
            //   'chat_conversation',
            //   JSON.stringify({ conversationId: cid, userId: uid })
            // );
          }
        }
      }

      if (!cid || !uid) {
        const stored = localStorage.getItem('chat_conversation');
        if (stored) {
          const parsed = JSON.parse(stored);
          cid = parsed.conversationId;
          uid = parsed.userId;

          setConversationId(cid);
          setUserId(uid);
        } else {
          console.log('No stored conversation in localStorage');
        }
      }
      return { cid, uid };
    };

    const connectSignalR = async () => {
      const { cid, uid } = await ensureConversation();

      if (cid && uid) {
        await signalRService.connect();
        await signalRService.joinConversation(cid, uid);
      } else {
        await signalRService.connect();
      }

      signalRService.setMessageHandler(message => {
        setMessages(prev => {
          const filtered = prev.filter(
            m =>
              !m.pending ||
              m.content !== (message.content || '[FILE]') ||
              (Array.isArray(m.attachments) && Array.isArray(message.attachments)
                ? m.attachments.length !== message.attachments.length
                : true)
          );
          const newMessage = {
            ...message,
            timestamp: new Date(message.timestamp || message.createdAt || Date.now()),
            messageId: message.messageId || message.id || '',
            id: message.id || '',
          };
          return [...filtered, newMessage] as Message[];
        });

        if (message.direction === 'outbound' && !isOpen) {
          setHasNewMessage(true);
        }

        setShouldAutoScroll(true);
      });

      signalRService.setMessageHistoryHandler(historyMessages => {
        const formattedMessages = historyMessages
          .filter(msg => msg.isDeleted === false)
          .map(msg => {
            let ts = msg.timestamp;
            if (!ts) ts = msg.createdAt;
            if (!ts) ts = new Date();
            return {
              ...msg,
              timestamp: ts instanceof Date ? ts : new Date(ts),
              messageId: msg.messageId || msg.id || '',
              id: msg.id || '',
            };
          }) as Message[];

        // Xử lý load more messages - ƯU TIÊN CAO NHẤT
        if (isLoadingMoreRef.current) {
          if (formattedMessages.length === 0) {
            setHasMoreMessages(false);
            setIsLoadingMore(false);
            return;
          }
          setMessages(prev => {
            const newMessages = formattedMessages.filter(
              newMsg =>
                !prev.some(
                  existingMsg =>
                    (existingMsg.messageId && existingMsg.messageId === newMsg.messageId) ||
                    (existingMsg.id && existingMsg.id === newMsg.id)
                )
            );
            if (formattedMessages.length === 0) {
              setHasMoreMessages(false);
            }
            const updatedMessages = [...prev, ...newMessages];
            setTimeout(() => {
              const chatEl = chatContainerRef.current;
              if (chatEl && newMessages.length > 0) {
                chatEl.scrollTop = 0;
              }
            }, 50);
            return updatedMessages;
          });

          setIsLoadingMore(false);
          setCurrentPage(prev => prev + 1);
          return;
        }

        if (isFirstLoad.current && !isLoadingMoreRef.current) {
          if (formattedMessages.length === 0) {
            setMessages(prev => {
              return prev;
            });
          } else {
            setMessages(() => {
              const sortedHistory = formattedMessages.sort(
                (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
              );
              return sortedHistory;
            });
          }
          isFirstLoad.current = false;
          setShouldAutoScroll(true);
        }
      });

      signalRService.setNewConversationHandler(data => {
        setConversationId(data.conversationId);
        setUserId(data.userId);
        // localStorage.setItem(
        //   'chat_conversation',
        //   JSON.stringify({ conversationId: data.conversationId, userId: data.userId })
        // );
        signalRService.joinConversation(data.conversationId, data.userId);
      });

      signalRService.setNotificationHandler((title, body) => {
        if (!isOpen) {
          <Alert>
            <AlertTitle>{title}</AlertTitle>
            <AlertDescription>{body}</AlertDescription>
          </Alert>;
        }
      });

      signalRService.setTypingHandler(_messageSessionId => {});

      signalRService.setReadHandler((_messageSessionId, messageIds) => {
        setMessages(prev =>
          prev.map(msg =>
            messageIds.includes(msg.messageId || '') ? { ...msg, isRead: true } : msg
          )
        );
      });

      signalRService.setOnlineStatusHandler(() => {});
    };

    connectSignalR();
    return () => {
      if (conversationId) {
        signalRService.leaveConversation(conversationId);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      signalRService.setMessageHistoryHandler(null);
    };
  }, [conversationId, userId, isOpen]);

  useEffect(() => {
    isFirstLoad.current = true;
    setCurrentPage(1);
    setHasMoreMessages(true);
    setShouldAutoScroll(true);
    setIsLoadingMore(false);
    setLastLoadMoreTime(0);
  }, [conversationId]);

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);
  };

  const handlePinMessage = async (messageId: string) => {
    if (!messageId || !conversationId) {
      return;
    }

    const msg = messages.find(m => m.messageId === messageId || m.id === messageId);
    if (!msg || pinnedMessages.some(m => m.messageId === messageId || m.id === messageId)) {
      return;
    }

    try {
      await pinMessage({ messageId, conversationId });
      setPinnedMessages(prev => [...prev, msg]);
      <Alert>
        <AlertTitle>Đã ghim tin nhắn</AlertTitle>
      </Alert>;
    } catch (err) {
      <Alert>
        <AlertTitle>Không thể ghim tin nhắn. Vui lòng thử lại.</AlertTitle>
      </Alert>;
    }
  };

  const handleUnpinMessage = async (messageId: string) => {
    if (!messageId || !conversationId) {
      return;
    }

    try {
      await unpinMessage({ messageId, conversationId }); // Gọi API unpin
      setPinnedMessages(prev => prev.filter(m => m.messageId !== messageId && m.id !== messageId));
      <Alert>
        <AlertTitle>Đã bỏ ghim tin nhắn</AlertTitle>
      </Alert>;
    } catch (err) {
      <Alert>
        <AlertTitle>Không thể bỏ ghim tin nhắn. Vui lòng thử lại.</AlertTitle>
      </Alert>;
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!messageId) {
      return;
    }

    try {
      await deleteMessage(messageId);
      setMessages(prev => prev.filter(m => m.messageId !== messageId && m.id !== messageId));
      setPinnedMessages(prev => prev.filter(m => m.messageId !== messageId && m.id !== messageId));
      <Alert>
        <AlertTitle>Xóa tin nhắn thành công</AlertTitle>
      </Alert>;
    } catch (err) {
      <Alert>
        <AlertTitle>Không thể xóa tin nhắn. Vui lòng thử lại.</AlertTitle>
      </Alert>;
    }
  };

  const handleReply = (message: Message) => {
    const messageId = message.messageId || message.id;
    if (!messageId) {
      return;
    }
    setReplyingMessage(message);
    // localStorage.setItem('chat_replying_message', JSON.stringify(message));
  };

  const handleFileSelect = async (file: File, type: 'image' | 'video' | 'file') => {
    try {
      setIsUploading(true);
      let response;

      switch (type) {
        case 'image':
          response = await uploadImage.mutateAsync({ files: [file] });
          break;
        case 'video':
          response = await uploadVideo.mutateAsync({ file });
          break;
        default:
          response = await uploadAttachment.mutateAsync({ files: [file] });
      }
      let attachment;
      if (Array.isArray(response?.data)) {
        attachment = response.data[0];
      } else if (response?.data && typeof response.data === 'object') {
        attachment = response.data;
      }
      if (attachment && attachment.id) {
        setUploadedAttachmentIds(prev => [...prev, attachment.id]);
        setSelectedFiles(prev => [...prev, file]);
      }
    } catch (error) {
      <Alert>
        <AlertTitle>Không thể tải lên file. Vui lòng thử lại.</AlertTitle>
      </Alert>;
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (file.type.startsWith('image/')) {
      handleFileSelect(file, 'image');
    } else if (file.type.startsWith('video/')) {
      handleFileSelect(file, 'video');
    } else {
      handleFileSelect(file, 'file');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    handleFileSelect(file, 'file');
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setUploadedAttachmentIds(prev => prev.filter((_, i) => i !== index));
  };

  // 3. Khi nhấn send:
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    const trimmedMessage = newMessage.trim();
    if (!trimmedMessage && !pendingProperty && uploadedAttachmentIds.length === 0) return;
    try {
      let contentToSend = trimmedMessage;
      if (pendingProperty) {
        contentToSend = JSON.stringify({ property: pendingProperty, text: trimmedMessage });
      }
      await sendMessage({
        content: contentToSend,
        conversationId,
        recipientId: property?.saler?.id,
        attachmentIds: uploadedAttachmentIds.length > 0 ? uploadedAttachmentIds : undefined,
        ...(replyingMessage && {
          replyToMessageId: replyingMessage.messageId || replyingMessage.id,
        }),
      } as SendMessageRequest);
      setNewMessage('');
      setPendingProperty(null);
      setSelectedFiles([]);
      setUploadedAttachmentIds([]);
      setReplyingMessage(null);
      localStorage.removeItem('chat_replying_message');
    } catch (error) {
      <Alert>
        <AlertTitle>Gửi tin nhắn thất bại</AlertTitle>
        <AlertDescription>
          {error instanceof Error ? error.message : 'Lỗi không xác định'}
        </AlertDescription>
      </Alert>;
    }
  };

  const handleCloseChat = () => {
    setIsOpen(false);
    // if (pathname !== '/login') {
    //   setCloseCount(prev => prev + 1);
    //   setLastClosedTime(Date.now());
    // }
  };

  const getMessageStyle = (message: Message) => {
    if (message.attachments && message.attachments.length > 0) {
      return '';
    }

    if (
      message.direction === 'inbound' ||
      message.isAnonymous ||
      message.senderId === 'user' ||
      message.senderType === SenderType.User
    ) {
      return 'bg-red-500 text-white border border-gray-290';
    }

    return 'bg-white border border-gray-150 text-gray-800';
  };

  useEffect(() => {
    if (isOpen && messages.length > 0 && shouldAutoScroll) {
      // Đợi DOM render xong
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [isOpen, messages, shouldAutoScroll]);

  useEffect(() => {
    const handleLogout = () => {
      if (isOpen) {
        handleCloseChat();
      }
    };

    window.addEventListener('logout', handleLogout);

    return () => {
      window.removeEventListener('logout', handleLogout);
    };
  }, [isOpen]);

  useEffect(() => {
    if (pathname === '/login') {
      if (conversationId) {
        signalRService.leaveConversation(conversationId);
      }
      setIsOpen(false);
      setMessages([]);
      setConversationId(null);
      setUserId(null);
    }
  }, [pathname]);

  useEffect(() => {
    if (pathname !== '/login') {
      setMessages([]);
      refetchConversation().then(result => {
        if (result.data?.data) {
          setConversationId(result.data.data.id);
          setUserId(result.data.data.platformUser?.id);
        } else {
          setConversationId(null);
          setUserId(null);
          setPinnedMessages([]);
          setShowPinnedList(false);
        }
      });
    }
  }, [pathname, refetchConversation]);

  useEffect(() => {
    if (
      conversationId &&
      userId &&
      pathname !== '/login' &&
      lastAutoOpenConversationId.current !== conversationId
    ) {
      signalRService.joinConversation(conversationId, userId);
      const timer = setTimeout(() => setIsOpen(true), 5000);
      lastAutoOpenConversationId.current = conversationId;
      return () => clearTimeout(timer);
    }
  }, [conversationId, userId, pathname]);

  useEffect(() => {
    if (conversationId && conversationId !== prevConversationId.current && pathname !== '/login') {
      setMessages([]);
      prevConversationId.current = conversationId;
    }
  }, [conversationId, pathname]);

  useEffect(() => {
    const chatEl = chatContainerRef.current;
    if (!chatEl) return;

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      e.stopPropagation();
      chatEl.scrollTop += e.deltaY;
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    chatEl.addEventListener('wheel', handleWheel, { passive: false });
    chatEl.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      chatEl.removeEventListener('wheel', handleWheel);
      chatEl.removeEventListener('touchmove', handleTouchMove);
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (replyingMessage) {
      // localStorage.setItem('chat_replying_message', JSON.stringify(replyingMessage));
    } else {
      localStorage.removeItem('chat_replying_message');
    }
  }, [replyingMessage]);

  useEffect(() => {
    const storedReplyingMessage = localStorage.getItem('chat_replying_message');
    if (storedReplyingMessage) {
      const parsedMessage = JSON.parse(storedReplyingMessage);
      parsedMessage.timestamp = new Date(parsedMessage.timestamp);
      setReplyingMessage(parsedMessage);
    }
  }, []);

  const renderReplyingMessage = () => {
    if (!replyingMessage) return null;

    return (
      <div className="flex items-start bg-gray-50 border-l-4 border-blue-500 rounded p-2 m-2 relative">
        <div className="flex-1">
          <div className="font-semibold text-xs text-gray-700 mb-0.5 flex items-center gap-1">
            <MessageCircle size={14} className="text-blue-500" />
            Trả lời{' '}
            <span className="font-bold">
              {replyingMessage.direction === 'outbound' ? 'Bạn' : 'Saller'}
            </span>
          </div>
          <div className="text-xs text-gray-600 truncate max-w-[320px]">
            {replyingMessage.content}
          </div>
        </div>
        <button
          className="ml-2 text-gray-400 hover:text-gray-600"
          onClick={() => {
            setReplyingMessage(null);
            localStorage.removeItem('chat_replying_message');
          }}
          aria-label="Hủy trả lời"
        >
          <X size={16} />
        </button>
      </div>
    );
  };

  const handleScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    if (
      scrollTop === 0 &&
      !isLoadingMoreRef.current &&
      conversationId &&
      hasMoreMessages &&
      messages.length > 0 &&
      Date.now() - lastLoadMoreTime > 1000
    ) {
      try {
        setIsLoadingMore(true);
        setShouldAutoScroll(false);
        setLastLoadMoreTime(Date.now());

        await signalRService.loadMoreMessages(conversationId, currentPage + 1, pageSize);
      } catch (error) {
        setShouldAutoScroll(true);
        setIsLoadingMore(false);
      }
    }
  };

  const renderPreviewFiles = () => {
    if (selectedFiles.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 p-2 bg-gray-50 rounded-lg">
        {selectedFiles.map((file, index) => (
          <div key={index} className="relative group">
            {file.type.startsWith('image/') ? (
              <div className="relative">
                <Image
                  src={URL.createObjectURL(file)}
                  alt="Preview"
                  width={50}
                  height={50}
                  style={{ width: 'auto' }}
                  className="rounded-lg object-cover w-[50px] h-[50px]"
                />
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : file.type.startsWith('video/') ? (
              <div className="relative">
                <video
                  src={URL.createObjectURL(file)}
                  className="w-[50px] h-[50px] rounded-lg object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                  <Play size={24} className="text-white" />
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <div className="relative bg-white p-2 rounded-lg border">
                <div className="flex items-center gap-2">
                  <FileText size={20} className="text-gray-500" />
                  <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const handleEmojiSelect = (emoji: { native: string }) => {
    setNewMessage(prev => prev + emoji.native);
    setShowEmojiPicker(false);
  };

  useEffect(() => {
    setPinnedMessages(prev =>
      prev.map(pinMsg => {
        const fullMsg = messages.find(
          m => m.messageId === pinMsg.messageId || m.id === pinMsg.messageId
        );
        if (fullMsg && fullMsg.attachments && fullMsg.attachments.length > 0) {
          return { ...pinMsg, attachments: fullMsg.attachments };
        }
        return pinMsg;
      })
    );
  }, [messages]);

  useEffect(() => {
    const handler = (e: CustomEvent<Property>) => {
      const property = e.detail;
      const newPropertyCard = { property, id: Date.now(), timestamp: new Date() };
      setPropertyCardMessages(prev => {
        const updated = [...prev, newPropertyCard];
        // localStorage.setItem('chat_property_cards', JSON.stringify(updated));
        return updated;
      });
    };
    window.addEventListener('send-property-card', handler as EventListener);
    return () => window.removeEventListener('send-property-card', handler as EventListener);
  }, []);

  useEffect(() => {
    const savedPropertyCards = localStorage.getItem('chat_property_cards');
    if (savedPropertyCards) {
      try {
        const parsed = JSON.parse(savedPropertyCards);
        const restoredCards = parsed.map(
          (card: { property: Property; id: number; timestamp: string | number | Date }) => ({
            ...card,
            timestamp: new Date(card.timestamp || card.id),
          })
        );
        setPropertyCardMessages(restoredCards);
      } catch (error) {
        toast.error('Lỗi khi phân tích tin nhắn ghim');
      }
    }
  }, []);

  useEffect(() => {
    const handler = (e: CustomEvent<string>) => {
      const propertyId = e.detail;
      setPropertyCardMessages(prev => prev.filter(card => card.property.id !== propertyId));
    };
    window.addEventListener('remove-property-card', handler as EventListener);
    return () => window.removeEventListener('remove-property-card', handler as EventListener);
  }, []);

  const handleOpenChat = async () => {
    setIsOpen(true);
    if (conversationId && userId) {
      await signalRService.joinConversation(conversationId, userId);
    }
  };

  useEffect(() => {
    if (isPropertyDetail && property?.saler?.id) {
      (async () => {
        const conversation = await chatService.getSellerConversation(property.saler.id);
        if (conversation?.data?.id) {
          setConversationId(conversation.data.id);
          setUserId(property.saler.id);
        }
      })();
    }
  }, [isPropertyDetail, property?.saler?.id]);

  useEffect(() => {
    const handler = (e: CustomEvent<string>) => {
      setNewMessage(e.detail);
    };
    window.addEventListener('set-chat-input', handler as EventListener);
    return () => window.removeEventListener('set-chat-input', handler as EventListener);
  }, []);

  useEffect(() => {
    if (isOpen && conversationId && userId) {
      signalRService.connect().then(() => {
        signalRService.joinConversation(conversationId, userId);
      });
    }
  }, [isOpen, conversationId, userId]);

  useEffect(() => {
    isLoadingMoreRef.current = isLoadingMore;
  }, [isLoadingMore]);

  const [confirmingDeleteId, setConfirmingDeleteId] = useState<string | null>(null);

  // Effect: Khi messages thay đổi, nếu có pendingScrollMessageId và ref đã xuất hiện thì scroll đến đó
  useEffect(() => {
    if (pendingScrollMessageId && messageRefs.current[pendingScrollMessageId]) {
      const el = messageRefs.current[pendingScrollMessageId];
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        el.classList.add('ring-2', 'ring-blue-400');
        setTimeout(() => {
          el.classList.remove('ring-2', 'ring-blue-400');
        }, 2000);
        setPendingScrollMessageId(null);
      }
    }
  }, [messages, pendingScrollMessageId]);

  // Khi nhấn 'Xem tin nhắn' pinned mà ref chưa có, sẽ tự động loadMore cho đến khi messageId xuất hiện hoặc hết hasMoreMessages
  useEffect(() => {
    if (
      pendingScrollMessageId &&
      !messageRefs.current[pendingScrollMessageId] &&
      hasMoreMessages &&
      !isLoadingMore &&
      conversationId
    ) {
      setIsLoadingMore(true);
      signalRService.loadMoreMessages(conversationId, currentPage + 1, pageSize);
    }
  }, [
    pendingScrollMessageId,
    hasMoreMessages,
    isLoadingMore,
    conversationId,
    currentPage,
    pageSize,
  ]);

  // Thêm component mini property card cho reply preview
  function PropertyCardMiniInReply({ property }: { property: Property }) {
    return (
      <div className="flex items-center gap-1 border border-gray-200 rounded bg-white px-1 py-0.5 max-w-[120px] overflow-hidden">
        <Image
          src={property.imageUrls?.[0] || '/logo_revoland_black.png'}
          alt={property.name || 'Property'}
          width={24}
          height={24}
          className="rounded object-cover w-6 h-6 border"
        />
        <div className="flex-1 min-w-0">
          <div className="text-xs font-semibold truncate max-w-[70px]">
            {property.name || 'BĐS'}
          </div>
          <div className="text-[10px] text-gray-500 truncate max-w-[70px]">
            {property.location?.address || ''}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: isMobile ? '20px' : '20px',
        right: isMobile ? '20px' : '20px',
        zIndex: 9999,
      }}
    >
      <div className="relative">
        {isOpen && (
          <div className="absolute bottom-[calc(100%+50px)] right-1 w-[450px] h-[613px] bg-gray-100 shadow-xl flex flex-col cursor-default rounded-lg z-[9999]">
            {/* Header khung chat */}
            <div className="bg-white text-black p-5 flex justify-between items-center border border-gray-300 ">
              {/* <div className="flex items-center gap-4"> */}
              {/* <Image
                  src="/revoland_logo.png"
                  alt="Revoland Logo"
                  width={33}
                  height={33}
                  className="rounded-full bg-white"
                /> */}
              <div className="flex items-center gap-4">
                {/* <Star className="text-white" size={20} /> */}
                <button
                  onClick={handleCloseChat}
                  className="text-black hover:text-gray-200 transition-colors "
                  aria-label="Close chat"
                >
                  <ChevronDown className="text-gray-500" size={23} />
                </button>
              </div>
              <div className="flex flex-col items-center gap-1 ">
                <h3 className="font-bold text-black">Chat với Revoland</h3>
                <span className="text-xs flex items-center gap-1">
                  Chúng tôi rất sẵn lòng giải đáp mọi thắc mắc của bạn
                </span>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <MessageSquareShare
                        aria-label="Đi tới hộp thoại"
                        className="text-gray-400 cursor-pointer"
                        onClick={() => router.push(`/myrevo/messenger`)}
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Đi tới hộp thư của bạn</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {/* </div> */}
            </div>
            {/* Khối pinned ngay dưới header */}
            {pinnedMessages.length > 0 && (
              <div className="absolute top-[88px] left-0 right-0 z-10 bg-white border-b border-x rounded-b-lg p-3 shadow-sm justify-center border-t rounded-t-lg border-gray-200 w-[420px] ml-2 mx-auto">
                <div className="flex items-center justify-between ">
                  <span className="font-semibold text-sm">
                    Danh sách ghim ({pinnedMessages.length})
                  </span>
                  <button
                    onClick={() => setShowPinnedList(!showPinnedList)}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    <ChevronUp
                      size={20}
                      className={`transform transition-transform ${showPinnedList ? 'rotate-0' : 'rotate-180'}`}
                    />
                  </button>
                </div>
                {showPinnedList && (
                  <div className="mt-2 max-h-[150px] overflow-y-auto">
                    {pinnedMessages.map(msg => {
                      const pinnedId = msg.messageId || msg.id;
                      return (
                        <div
                          key={`pinned-${pinnedId}`}
                          className="flex items-center justify-between py-1 border-b last:border-b-0 bg-gray-100 rounded-lg p-2 mb-2 group/message"
                        >
                          <div className="flex items-center gap-2 ">
                            <MessageCircleMore className="inline-block text-gray-600" size={18} />
                            <div>
                              <span className="font-medium text-xs">Tin nhắn</span>
                              <div className="text-xs text-gray-700 border-gray-200 pb-1 flex items-center gap-2 flex-wrap max-w-[200px]">
                                {/* Hiển thị text content hoặc card mini */}
                                {(() => {
                                  if (
                                    typeof msg.content === 'string' &&
                                    msg.content.trim().startsWith('{')
                                  ) {
                                    try {
                                      const parsed = JSON.parse(msg.content);
                                      if (parsed && parsed.property) {
                                        return (
                                          <PropertyCardMiniInReply property={parsed.property} />
                                        );
                                      }
                                    } catch {
                                      // fallthrough
                                    }
                                  }
                                  if (msg.content && msg.content !== '[FILE]') {
                                    return (
                                      <span className="truncate max-w-[150px]">
                                        {msg.direction === 'inbound' ? 'Bạn' : 'Saller'}:{' '}
                                        {msg.content}
                                      </span>
                                    );
                                  }
                                  // ... giữ nguyên logic preview file ...
                                  if (msg.attachments && msg.attachments.length > 0) {
                                    return (
                                      <div className="flex items-center gap-1 flex-wrap">
                                        {msg.attachments.map((attachment, idx) => {
                                          const att: AttachmentLike = attachment;
                                          const urlA = att.url;
                                          const typeA = att.type;
                                          const nameA = att.fileName;
                                          if (!urlA || !typeA) return null;
                                          const isImage = typeA.startsWith('image/');
                                          const isVideo = typeA.startsWith('video/');
                                          const isPdf = typeA === 'application/pdf';
                                          const isFile = !isImage && !isVideo && !isPdf;
                                          if (isImage) {
                                            return (
                                              <div
                                                key={idx}
                                                className="w-7 h-7 cursor-pointer"
                                                onClick={() =>
                                                  setSelectedMedia({ url: urlA, type: typeA })
                                                }
                                              >
                                                <Image
                                                  src={urlA}
                                                  alt={nameA || 'attachment'}
                                                  width={28}
                                                  height={28}
                                                  className="rounded object-cover w-7 h-7 border"
                                                />
                                              </div>
                                            );
                                          }
                                          if (isVideo) {
                                            return (
                                              <div
                                                key={idx}
                                                className="w-7 h-7 cursor-pointer flex items-center justify-center bg-gray-200 rounded"
                                                onClick={() =>
                                                  setSelectedMedia({ url: urlA, type: typeA })
                                                }
                                              >
                                                <Play size={16} className="text-blue-500" />
                                              </div>
                                            );
                                          }
                                          if (isPdf) {
                                            return (
                                              <a
                                                key={idx}
                                                href={urlA}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-7 h-7 flex items-center justify-center bg-gray-200 rounded"
                                                title={nameA}
                                              >
                                                <FileText size={16} className="text-red-500" />
                                              </a>
                                            );
                                          }
                                          if (isFile) {
                                            return (
                                              <a
                                                key={idx}
                                                href={urlA}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="w-7 h-7 flex items-center justify-center bg-gray-200 rounded"
                                                title={nameA}
                                              >
                                                <FileText size={16} className="text-gray-500" />
                                              </a>
                                            );
                                          }
                                          return null;
                                        })}
                                      </div>
                                    );
                                  }
                                  return null;
                                })()}
                              </div>
                            </div>
                          </div>
                          <Popover
                            open={openMenuMessageId === `pinned-${pinnedId}`}
                            onOpenChange={open =>
                              setOpenMenuMessageId(open ? `pinned-${pinnedId}` : null)
                            }
                          >
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white transition-opacity hover:bg-gray-100 "
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              side={msg.direction === 'inbound' ? 'left' : 'right'}
                              align="center"
                              className={cn(
                                'z-[99999] min-w-[120px] max-w-[140px] p-1 flex flex-col text-sm bg-white rounded-md shadow-md border'
                              )}
                            >
                              <div className="flex flex-col">
                                <button
                                  onClick={() => {
                                    const messageId = msg.messageId || msg.id;
                                    if (!messageId) return;
                                    const messageElement = messageRefs.current[messageId];
                                    if (messageElement) {
                                      messageElement.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center',
                                      });
                                      messageElement.classList.add('ring-2', 'ring-blue-400');
                                      setTimeout(() => {
                                        messageElement.classList.remove('ring-2', 'ring-blue-400');
                                      }, 2000);
                                      setShowPinnedList(false);
                                    } else {
                                      setPendingScrollMessageId(messageId);
                                    }
                                    setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                  }}
                                  className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors mb-1"
                                >
                                  Xem tin nhắn
                                </button>
                                <button
                                  onClick={() => {
                                    const messageId = msg.messageId || msg.id;
                                    if (messageId) {
                                      handleUnpinMessage(messageId);
                                    }
                                    setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                  }}
                                  className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors text-orange-600"
                                >
                                  Bỏ ghim
                                </button>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
            <div
              ref={chatContainerRef}
              className="flex-1 overflow-y-auto p-3 flex flex-col "
              id="chat-messages"
              style={{ transform: 'translateZ(0)', overscrollBehavior: 'contain' }}
              onScroll={handleScroll}
            >
              {/* {isLoadingMore && (
                <div className="flex justify-center items-center py-2">
                  <Loader2 className="animate-spin text-gray-400" size={24} />
                  <span className="ml-2 text-sm text-gray-500">Đang tải tin nhắn cũ...</span>
                </div>
              )} */}

              {!hasMoreMessages && messages.length > 0 && (
                <div className="flex justify-center items-center py-2">
                  <span className="text-xs text-gray-400">Đã hiển thị tất cả tin nhắn</span>
                </div>
              )}

              {(() => {
                // Combine property card messages with regular messages
                const allMessages: MessageItemUnion[] = [
                  ...propertyCardMessages.map(card => ({
                    type: 'property-card' as const,
                    id: card.id.toString(),
                    messageId: card.id.toString(),
                    timestamp: card.timestamp,
                    property: card.property,
                  })),
                  ...messages.map(msg => ({
                    type: 'message' as const,
                    ...msg,
                  })),
                ];

                // SẮP XẾP THEO THỜI GIAN TĂNG DẦN (CŨ -> MỚI)
                const sortedMessages = allMessages.sort(
                  (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
                );

                return sortedMessages.map((item, index) => {
                  // Nếu là property-card message hoặc content là JSON có trường property
                  if (item.type === 'property-card') {
                    return (
                      <PropertyCardInChat
                        property={item.property}
                        direction={'outbound'}
                        key={item.messageId}
                      />
                    );
                  }
                  if (item.type === 'message') {
                    let propertyData: Property | undefined = undefined;
                    if (typeof item.content === 'string' && item.content.trim().startsWith('{')) {
                      try {
                        const parsed = JSON.parse(item.content);
                        if (parsed && parsed.property) {
                          propertyData = parsed.property;
                        }
                      } catch {
                        propertyData = undefined;
                      }
                    }
                    if (propertyData) {
                      return (
                        <PropertyCardInChat
                          property={propertyData}
                          direction={item.direction}
                          key={item.messageId}
                        />
                      );
                    }
                    // Nếu không phải propertyCard, luôn render text message
                    const message = item as Message;
                    const hasContent = message.content && message.content.trim().length > 0;
                    const hasAttachments = message.attachments && message.attachments.length > 0;
                    const isFileMessage = message.content === '[FILE]';
                    if (!hasContent && !hasAttachments && !isFileMessage) {
                      return null;
                    }

                    const isUser = message.direction === 'inbound';
                    const nextMessage = sortedMessages
                      .slice(index + 1)
                      .find(m => m.type === 'message'); // chỉ xét tin nhắn thường
                    const isLastOfGroup =
                      !nextMessage || nextMessage.direction !== message.direction;

                    const chatId = message.messageId || message.id;

                    return (
                      <div
                        key={
                          message.messageId ||
                          message.id ||
                          `${message.content}-${message.createdAt?.toString()}`
                        }
                        ref={el => {
                          const messageId = message.messageId || message.id;
                          if (messageId) {
                            messageRefs.current[messageId] = el;
                          }
                        }}
                        className={`flex items-start pb-1 text-sm group/message ${isUser ? 'justify-end' : 'justify-start'}`}
                      >
                        {/* Tin nhắn gửi đi: dấu 3 chấm bên trái */}
                        {message.direction === 'inbound' && (
                          <Popover
                            open={openMenuMessageId === `chat-${chatId}`}
                            onOpenChange={open =>
                              setOpenMenuMessageId(open ? `chat-${chatId}` : null)
                            }
                          >
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white opacity-0 group-hover/message:opacity-100 transition-opacity hover:bg-gray-100 "
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              side="right"
                              align="center"
                              className="z-[9999] min-w-[120px] max-w-[120px] p-1 flex flex-col text-sm"
                            >
                              <button
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors mb-1"
                                onClick={() => {
                                  const messageId = message.messageId || message.id;
                                  if (messageId) {
                                    handlePinMessage(messageId);
                                  }
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                              >
                                Ghim
                              </button>
                              <button
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors mb-1"
                                onClick={() => {
                                  handleReply(message);
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                              >
                                Trả lời
                              </button>
                              <button
                                onClick={() => {
                                  setConfirmingDeleteId(message.messageId || message.id);
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors text-red-500"
                              >
                                Thu hồi
                              </button>
                            </PopoverContent>
                          </Popover>
                        )}

                        <div className="mr-2 mt-2">
                          {!isUser && isLastOfGroup ? (
                            <Image
                              src="/logo_revoland_black.png"
                              alt="Revoland Logo"
                              width={27}
                              height={27}
                              className="rounded-full bg-white p-1"
                            />
                          ) : (
                            <div style={{ width: 27, height: 27 }} />
                            // <div></div>
                          )}
                        </div>
                        {/* Bubble tin nhắn */}
                        <div
                          className={`relative max-w-[70%] rounded-lg ${isUser ? 'rounded-br-none' : 'rounded-bl-none'}  ${getMessageStyle(message)}`}
                        >
                          {message.replyToMessageId &&
                            messages.find(
                              m =>
                                m.messageId === message.replyToMessageId ||
                                m.id === message.replyToMessageId
                            ) && (
                              <div
                                className="rounded-lg bg-blue-50 border-l-4 border-blue-500 px-2 py-1 m-2 min-w-[80px] cursor-pointer"
                                onClick={() => {
                                  const el = messageRefs.current[message.replyToMessageId || ''];
                                  if (el) {
                                    el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    el.classList.add('ring-2', 'ring-blue-400');
                                    setTimeout(
                                      () => el.classList.remove('ring-2', 'ring-blue-400'),
                                      1500
                                    );
                                  }
                                  setShowPinnedList(false);
                                }}
                              >
                                <div className="font-semibold text-xs text-blue-700">
                                  {(() => {
                                    const repliedMsg = messages.find(
                                      m =>
                                        m.messageId === message.replyToMessageId ||
                                        m.id === message.replyToMessageId
                                    );
                                    if (!repliedMsg) return null;
                                    return (
                                      repliedMsg.senderName ||
                                      (repliedMsg.direction === 'outbound' ? 'Bạn' : 'Saller')
                                    );
                                  })()}
                                </div>
                                <div className="text-xs text-gray-700 truncate max-w-[180px]">
                                  {(() => {
                                    const repliedMsg = messages.find(
                                      m =>
                                        m.messageId === message.replyToMessageId ||
                                        m.id === message.replyToMessageId
                                    );
                                    if (!repliedMsg) return null;
                                    // Nếu là propertyCard (content là JSON có property)
                                    if (
                                      typeof repliedMsg.content === 'string' &&
                                      repliedMsg.content.trim().startsWith('{')
                                    ) {
                                      try {
                                        const parsed = JSON.parse(repliedMsg.content);
                                        if (parsed && parsed.property) {
                                          return (
                                            <PropertyCardMiniInReply property={parsed.property} />
                                          );
                                        }
                                      } catch {
                                        // fallthrough
                                      }
                                    }
                                    // Nếu là [FILE] và có attachments thì hiển thị preview file
                                    if (
                                      repliedMsg.content === '[FILE]' &&
                                      repliedMsg.attachments &&
                                      repliedMsg.attachments.length > 0
                                    ) {
                                      const att = repliedMsg.attachments[0];
                                      const urlA = att.url;
                                      const typeA = att.type;
                                      const nameA = att.fileName;
                                      if (!urlA || !typeA) return <span>File</span>;
                                      if (typeA.startsWith('image/')) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 rounded overflow-hidden border">
                                              <Image
                                                src={urlA}
                                                alt={nameA || 'Ảnh'}
                                                width={24}
                                                height={24}
                                                className="w-full h-full object-cover"
                                              />
                                            </div>
                                            <span>Ảnh</span>
                                          </div>
                                        );
                                      }
                                      if (typeA.startsWith('video/')) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                                              <Play size={12} className="text-gray-600" />
                                            </div>
                                            <span>Video</span>
                                          </div>
                                        );
                                      }
                                      // File khác
                                      return (
                                        <div className="flex items-center gap-2">
                                          <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                                            <FileText size={12} className="text-gray-500" />
                                          </div>
                                          <span>{nameA || 'File'}</span>
                                        </div>
                                      );
                                    }
                                    // Nếu là text bình thường hoặc [FILE] nhưng không có attachment thì hiển thị như cũ
                                    if (repliedMsg.content && repliedMsg.content !== '[FILE]')
                                      return repliedMsg.content;
                                    return null;
                                  })()}
                                </div>
                              </div>
                            )}
                          <div className="flex flex-col gap-1">
                            {message.content && message.content !== '[FILE]' && (
                              <div className="text-sm p-2">{message.content}</div>
                            )}
                            {/* Luôn render preview attachments nếu có, không phân biệt direction */}
                            {message.attachments && message.attachments.length > 0 && (
                              <>
                                <div className="flex flex-col gap-2">
                                  {message.attachments.map((attachment, index) => {
                                    const att: AttachmentLike = attachment;
                                    // Thử nhiều key khác nhau
                                    const urlA = att.url || att.fileUrl;
                                    const typeA = att.type || att.fileType;
                                    const nameA = att.fileName || att.name;
                                    if (!urlA || !typeA) return null;
                                    const isImage = typeA.startsWith('image/');
                                    const isVideo = typeA.startsWith('video/');
                                    const isPdf = typeA === 'application/pdf';
                                    const isFile = !isImage && !isVideo && !isPdf;
                                    if (isImage) {
                                      return (
                                        <img
                                          key={index}
                                          src={urlA}
                                          alt={nameA || 'attachment'}
                                          className="rounded-lg object-cover max-w-[200px] max-h-[200px] cursor-pointer hover:opacity-90 transition-opacity"
                                          onClick={() =>
                                            setSelectedMedia({ url: urlA, type: typeA })
                                          }
                                        />
                                      );
                                    }
                                    if (isVideo) {
                                      return (
                                        <video
                                          key={index}
                                          controls
                                          className="rounded-lg max-w-[200px] max-h-[200px] cursor-pointer hover:opacity-90 transition-opacity"
                                          src={urlA}
                                          onClick={() =>
                                            setSelectedMedia({ url: urlA, type: typeA })
                                          }
                                        >
                                          Your browser does not support the video tag.
                                        </video>
                                      );
                                    }
                                    if (isPdf) {
                                      return (
                                        <a
                                          key={index}
                                          href={urlA}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className={`flex items-center gap-2 text-sm px-3 py-2 transition-colors hover:text-blue-500 ${message.direction === 'outbound' ? 'text-blue-500 bg-white rounded-t-lg rounded-r-lg ' : 'text-white bg-red-500 rounded-t-lg rounded-l-lg'}`}
                                        >
                                          <span>{nameA || 'PDF file'}</span>
                                        </a>
                                      );
                                    }
                                    if (isFile) {
                                      return (
                                        <a
                                          key={index}
                                          href={urlA}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className={`flex items-center max-w-[200px] gap-2 px-3 py-2 transition-colors hover:text-blue-500 ${message.direction === 'outbound' ? 'text-blue-500 bg-white rounded-t-lg rounded-r-lg ' : 'text-white bg-red-500 rounded-t-lg rounded-l-lg'}`}
                                        >
                                          <span>{nameA || urlA}</span>
                                        </a>
                                      );
                                    }
                                    return null;
                                  })}
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        {/* Avatar user bên phải */}
                        {isUser && isLastOfGroup && (
                          <div className="ml-2 mt-2 justify-center rounded-full bg-red-500 p-1 flex items-center justify-center">
                            <User size={20} className="text-white" />
                          </div>
                        )}
                        {isUser && !isLastOfGroup && <div className="ml-2 w-[27px]"></div>}

                        {/* Tin nhắn nhận được: dấu 3 chấm bên phải */}
                        {message.direction === 'outbound' && (
                          <Popover
                            open={openMenuMessageId === `chat-${chatId}`}
                            onOpenChange={open =>
                              setOpenMenuMessageId(open ? `chat-${chatId}` : null)
                            }
                          >
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white opacity-0 group-hover/message:opacity-100 transition-opacity hover:bg-gray-100 ml-2"
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              side="left"
                              align="center"
                              className="z-[9999] min-w-[120px] max-w-[120px] p-1 flex flex-col text-sm"
                            >
                              <button
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors mb-1"
                                onClick={() => {
                                  const messageId = message.messageId || message.id;
                                  if (messageId) {
                                    handlePinMessage(messageId);
                                  }
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                              >
                                Ghim
                              </button>
                              <button
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors mb-1"
                                onClick={() => {
                                  handleReply(message);
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                              >
                                Trả lời
                              </button>
                              <button
                                onClick={() => {
                                  setConfirmingDeleteId(message.messageId || message.id);
                                  setOpenMenuMessageId(null); // Đóng menu sau khi chọn
                                }}
                                className="w-full text-center px-3 py-2 rounded-md hover:bg-gray-100 transition-colors text-red-500"
                              >
                                Thu hồi
                              </button>
                            </PopoverContent>
                          </Popover>
                        )}
                      </div>
                    );
                  }
                  return null;
                });
              })()}
              <div ref={messagesEndRef} />
            </div>

            <form onSubmit={e => handleSendMessage(e)}>
              {renderReplyingMessage()}
              <div className="flex flex-col gap-1 border m-2 bg-white rounded-lg bg-gray-100 rounded-xl px-3 py-2 relative">
                {/* Preview selected files/images */}
                {renderPreviewFiles()}
                {/* Dòng trên: input nhập tin nhắn */}
                <input
                  type="text"
                  value={newMessage}
                  onChange={handleMessageChange}
                  placeholder="Nhập tin nhắn ..."
                  className="flex-1 bg-transparent outline-none px-2 py-2"
                  onKeyDown={e => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage(e);
                    }
                  }}
                />
                {/* Dòng dưới: các icon và nút gửi */}
                <div className="flex items-center justify-between mt-1">
                  <div className="flex gap-2 items-center relative">
                    {/* Icon dấu + để upload ảnh */}
                    <button
                      type="button"
                      onClick={() => imageInputRef.current?.click()}
                      className="p-1 text-gray-500 rounded-full hover:bg-gray-200 transition"
                      title="Chọn ảnh từ thư viện"
                    >
                      <Plus size={20} />
                    </button>
                    <input
                      type="file"
                      ref={imageInputRef}
                      onChange={handleImageChange}
                      className="hidden text-gray-500"
                      accept="image/*"
                    />
                    {/* Icon emoji */}
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                        className="p-1 text-gray-400 rounded-full hover:bg-gray-200 transition"
                        title="Chèn emoji"
                      >
                        <Smile size={20} />
                      </button>
                      {showEmojiPicker && (
                        <div className="absolute z-[9999] bottom-10 left-0">
                          <Picker
                            data={data}
                            onEmojiSelect={handleEmojiSelect}
                            theme="light"
                            set="native"
                            previewPosition="none"
                            skinTonePosition="none"
                            autoFocus
                          />
                        </div>
                      )}
                    </div>
                    {/* Icon file */}
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="p-1 text-gray-400 rounded-full hover:bg-gray-200 transition"
                      title="Gửi file"
                    >
                      <Paperclip size={20} />
                    </button>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      className="hidden"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.txt,.csv,application/*"
                    />
                  </div>
                  {/* Nút gửi và icon loading */}
                  <div className="flex items-center gap-2">
                    {isUploading && <Loader2 className="animate-spin text-gray-400" size={24} />}
                    <button type="submit" className="bg-red-500 text-white rounded-full p-2 ml-2">
                      <Send size={20} />
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        )}

        {!isOpen && (
          <button
            onClick={handleOpenChat}
            className={`${isMobile ? 'w-14 h-14 rounded-full' : 'w-14 h-14 rounded-full'} flex items-center justify-center bg-red-500 shadow-xl transition-colors cursor-pointer border border-gray-200 relative`}
            aria-label="Open chat"
            style={{
              pointerEvents: 'auto',
              zIndex: 9998,
            }}
          >
            {/* {isMobile ? ( */}
            <>
              <MessageCircleMore size={28} className="text-white" />
              {hasNewMessage && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-600 rounded-full border-2 border-white animate-pulse" />
              )}
            </>
          </button>
        )}

        {/* Media Preview Modal */}
        {selectedMedia && (
          <div
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100001]"
            onClick={() => setSelectedMedia(null)}
          >
            <div className="relative max-w-[90vw] max-h-[90vh]">
              {selectedMedia.type.startsWith('image/') ? (
                <Image
                  src={selectedMedia.url}
                  alt="Full size preview"
                  width={1200}
                  height={800}
                  style={{ width: 'auto' }}
                  className="max-w-full max-h-[90vh] object-contain"
                />
              ) : selectedMedia.type.startsWith('video/') ? (
                <video
                  controls
                  className="max-w-full max-h-[90vh]"
                  src={selectedMedia.url}
                  autoPlay
                >
                  Your browser does not support the video tag.
                </video>
              ) : (
                <div className="bg-white p-4 rounded-lg max-w-md">
                  <p className="text-gray-700 mb-2">File không hỗ trợ xem trước</p>
                  <a
                    href={selectedMedia.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-700 underline"
                  >
                    Tải xuống file
                  </a>
                </div>
              )}
              <button
                className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
                onClick={() => setSelectedMedia(null)}
              >
                <X size={24} />
              </button>
            </div>
          </div>
        )}
      </div>
      {/* Confirmation dialog for message deletion */}
      {confirmingDeleteId && (
        <div className="fixed inset-0 z-[100000] flex items-center justify-center bg-black/40">
          <div className="bg-white rounded-lg shadow-lg p-6 w-[320px]">
            <div className="font-semibold mb-2">Bạn có chắc chắn muốn thu hồi tin nhắn này?</div>
            <div className="flex justify-end gap-2">
              <button
                className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300"
                onClick={() => setConfirmingDeleteId(null)}
              >
                Hủy
              </button>
              <button
                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={() => {
                  handleDeleteMessage(confirmingDeleteId);
                  setConfirmingDeleteId(null);
                }}
              >
                Xác nhận
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
