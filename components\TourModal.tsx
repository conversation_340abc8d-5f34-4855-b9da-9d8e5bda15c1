'use client';

import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { Loader2, Trash2, ArrowLeft } from 'lucide-react';
import { PropertyCard } from '@/components/PropertyCard';
import { PropertySearchAndSelect } from '@/app/(user)/properties/[id]/component/PropertySearchAndSelect';
import type { Property } from '@/lib/api/services/fetchProperty';
import { useCreateTour } from '@/hooks/useTour';

const propertySchema = z
  .object({
    propertyId: z.string().min(1, 'ID bất động sản là bắt buộc.'),
    title: z.string().optional(),
    date: z.date({ required_error: 'Ngày là bắt buộc.' }),
    startTime: z.string().regex(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Sai định dạng HH:mm'),
    endTime: z.string().regex(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Sai định dạng HH:mm'),
    assignTo: z.string().min(1, 'Agent là bắt buộc.'),
    agentFullName: z.string().optional(),
    agentEmail: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    const start = new Date(`2000-01-01T${data.startTime}:00`);
    const end = new Date(`2000-01-01T${data.endTime}:00`);
    if (start >= end) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Giờ kết thúc phải sau giờ bắt đầu.',
        path: ['endTime'],
      });
    }
  });

const formSchema = z
  .object({
    name: z.string().min(1, 'Tên lịch trình là bắt buộc.'),
    startDateTime: z.date({ required_error: 'Ngày bắt đầu là bắt buộc.' }),
    notes: z.string().optional(),
    status: z.enum(['Pending', 'Approved', 'Completed']).default('Pending'),
    properties: z.array(propertySchema).min(1, 'Phải có ít nhất một bất động sản'),
  })
  .superRefine((data, ctx) => {
    const seen = new Set();
    const startDate = new Date(data.startDateTime);
    data.properties.forEach((p, index) => {
      const key = `${p.date}_${p.startTime}_${p.endTime}`;
      if (seen.has(key)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Thời gian trùng lặp.',
          path: [`properties.${index}.startTime`],
        });
      } else {
        seen.add(key);
      }

      if (p.date < startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Ngày xem phải sau ngày bắt đầu lịch trình.',
          path: [`properties.${index}.date`],
        });
      }
    });
  });

type TourFormValues = z.infer<typeof formSchema>;

interface TourModalProps {
  selectedProperties: Property[];
  updateSelectedProperties: (properties: Property[]) => void;
  onSuccess: () => void;
  onBack: () => void;
}

export function TourModal({
  selectedProperties,
  updateSelectedProperties,
  onSuccess,
  onBack,
}: TourModalProps) {
  const { mutate: createTour, isPending: isCreatingTour } = useCreateTour();

  const form = useForm<TourFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      startDateTime: undefined,
      notes: '',
      status: 'Pending',
      properties: selectedProperties.map(prop => ({
        propertyId: prop.id,
        title: prop.title,
        date: new Date(),
        startTime: '09:00',
        endTime: '10:00',
        assignTo: prop.saler?.id || '',
        agentFullName: prop.saler?.fullName || '',
        agentEmail: prop.saler?.email || '',
      })),
    },
  });

  const { fields, append, remove } = useFieldArray({ control: form.control, name: 'properties' });

  const handleRemove = (index: number) => {
    const propertyId = fields[index].propertyId;
    remove(index);
    const updated = selectedProperties.filter(p => p.id !== propertyId);
    updateSelectedProperties(updated);
    toast.info(`Đã xoá BĐS ${fields[index].title || propertyId}`);
  };

  const handleAddProperty = (prop: Property) => {
    append({
      propertyId: prop.id,
      title: prop.title,
      date: new Date(),
      startTime: '09:00',
      endTime: '10:00',
      assignTo: prop.saler?.id || '',
      agentFullName: prop.saler?.fullName || '',
      agentEmail: prop.saler?.email || '',
    });
    updateSelectedProperties([...selectedProperties, prop]);
    toast.success(`Đã thêm ${prop.title || prop.id}`);
  };

  const onSubmit = (values: TourFormValues) => {
    const payload = values.properties.map(p => ({
      propertyId: p.propertyId,
      visitTimeStart: p.startTime,
      visitTimeEnd: p.endTime,
      assignTo: p.assignTo,
    }));

    createTour(
      {
        ...values,
        startDateTime: values.startDateTime.toISOString(),
        tourProperties: payload,
      },
      {
        onSuccess: () => {
          toast.success('Tạo lịch trình thành công');
          onSuccess();
        },
      }
    );
  };

  const isLoading = isCreatingTour;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={isLoading}
            className="flex items-center gap-2 bg-red-600 text-white hover:text-white hover:bg-red-700"
          >
            <ArrowLeft className="h-4 w-4" /> Quay lại
          </Button>
        </div>

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên lịch trình</FormLabel>
              <FormControl>
                <Input placeholder="Lịch trình xem nhà" {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="startDateTime"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ngày bắt đầu</FormLabel>
              <FormControl>
                <Input
                  type="date"
                  value={field.value ? format(field.value, 'yyyy-MM-dd') : ''}
                  onChange={e =>
                    field.onChange(e.target.value ? new Date(e.target.value) : undefined)
                  }
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {fields.map((item, index) => (
          <div key={item.id} className="flex flex-col md:flex-row gap-4 border p-4 rounded-lg">
            <div className="w-full md:w-1/3">
              <PropertyCard
                property={selectedProperties.find(p => p.id === item.propertyId)!}
                size="sm"
                isHiddenDetail
              />
            </div>
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name={`properties.${index}.date`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        value={field.value ? format(field.value, 'yyyy-MM-dd') : ''}
                        onChange={e =>
                          field.onChange(e.target.value ? new Date(e.target.value) : undefined)
                        }
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`properties.${index}.startTime`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giờ bắt đầu</FormLabel>
                    <FormControl>
                      <Input type="time" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`properties.${index}.endTime`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giờ kết thúc</FormLabel>
                    <FormControl>
                      <Input type="time" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button type="button" variant="destructive" size="icon" className="mt-2">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Xác nhận xoá</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bạn có chắc muốn xoá bất động sản này?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Huỷ</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleRemove(index)}>Xoá</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        ))}

        <div className="mt-6">
          <h3 className="mb-4 text-lg font-medium">Thêm bất động sản</h3>
          <PropertySearchAndSelect
            onAddProperty={handleAddProperty}
            existingPropertyIds={new Set(fields.map(f => f.propertyId))}
            disabled={isLoading}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ghi chú</FormLabel>
              <FormControl>
                <Textarea placeholder="Ghi chú thêm..." {...field} disabled={isLoading} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="bg-red-600 text-white hover:bg-red-700"
          disabled={isLoading}
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />} Tạo Lịch Trình
        </Button>
      </form>
    </Form>
  );
}
