import { Button } from '@/components/ui/button';

export function CallToAction() {
  return (
    <section className="w-full max-w-screen px-8 md:px-8 xl:px-32 mx-auto py-16 bg-background text-foreground ">
      <div className="max-w-screen mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 items-center gap-10">
          <div>
            <h2 className="text-3xl sm:text-4xl font-bold tracking-tight mb-4">Call to Action</h2>
            <p className="text-muted-foreground max-w-xl">Call to Action Description</p>
          </div>

          {/* Call to Action */}
          <div className="flex md:justify-end justify-center">
            <Button className="bg-primary hover:bg-primary/90 text-background px-6 h-12 rounded-md text-base">
              Call to Action Button
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
