'use client';

import { useState, useCallback, useEffect, useRef, memo, useMemo } from 'react';
import { Property, TransactionType } from '@/lib/api/services/fetchProperty';
import { useSearchParams } from 'next/navigation';

import {
  Map,
  AdvancedMarker,
  InfoWindow,
  useMap,
  useMapsLibrary,
  useAdvancedMarkerRef,
} from '@vis.gl/react-google-maps';
import { PropertyCard } from '@/components/PropertyCard';
import { Badge } from '@/components/ui/badge';

interface PropertiesMapProps {
  properties: Property[];
  center?: {
    lat: number;
    lng: number;
  };
  zoom?: number;
  onMapIdle?: (mapState: {
    bounds: google.maps.LatLngBounds;
    center: google.maps.LatLngLiteral;
    zoom: number;
  }) => void;
  hoveredPropertyId?: string | null;
  initialCenter?: { lat: number; lng: number };
  initialZoom?: number;
}

// Custom marker component that handles the price badge
const PropertyMarker = memo(function PropertyMarker({
  property,
  onClick,
  selectedProperty,
  isHovered,
  zoomLevel, // Pass zoom from parent to avoid individual listeners
}: {
  property: Property;
  onClick: () => void;
  selectedProperty: Property | null;
  isHovered: boolean;
  zoomLevel: number;
}) {
  const ZOOM_THRESHOLD = 16; // Switch to badge style at zoom level 14 and above

  function formatPriceShort(value: number): string {
    if (value >= 1_000_000_000) {
      return (value / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
    }
    if (value >= 1_000_000) {
      return (value / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (value >= 1_000) {
      return (value / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return value.toString();
  }

  const rawPrice = useMemo(
    () =>
      property.transactionType === TransactionType.FOR_SALE
        ? property.priceDetails.salePrice || 0
        : property.priceDetails.rentalPrice || 0,
    [property.transactionType, property.priceDetails.salePrice, property.priceDetails.rentalPrice]
  );

  const [markerRef] = useAdvancedMarkerRef();

  // Determine marker color based on transaction type and selection state
  const markerColor = useMemo(() => {
    if (selectedProperty?.id === property.id || isHovered) {
      return 'bg-green-600';
    }
    return property.transactionType === TransactionType.FOR_RENT ? 'bg-purple-600' : 'bg-red-600';
  }, [selectedProperty?.id, property.id, isHovered, property.transactionType]);

  const formattedPrice = useMemo(() => formatPriceShort(rawPrice), [rawPrice]);

  return (
    <AdvancedMarker
      ref={markerRef}
      position={{
        lat: property.location.latitude,
        lng: property.location.longitude,
      }}
      onClick={onClick}
    >
      {zoomLevel >= ZOOM_THRESHOLD ? (
        <Badge
          variant="default"
          className={`absolute -right-6 hover:bg-green-600 transition-colors duration-200 ${markerColor}`}
        >
          {formattedPrice}
        </Badge>
      ) : (
        <div
          className={`absolute w-3 h-3 rounded-full hover:bg-green-600 ${markerColor} transition-colors duration-200`}
          style={{
            transform: 'translate(-50%, -50%)',
            boxShadow: '0 0 0 2px white, 0 0 0 4px rgba(0,0,0,0.1)',
          }}
        />
      )}
    </AdvancedMarker>
  );
});

// Custom info window component that displays property details
function PropertyInfoWindow({ property, onClose }: { property: Property; onClose: () => void }) {
  const [isMediumScreen, setIsMediumScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMediumScreen(window.innerWidth >= 768); // 768px is the md breakpoint in Tailwind
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <InfoWindow
      shouldFocus={true}
      headerDisabled
      onCloseClick={onClose}
      position={{
        lat: property.location.latitude,
        lng: property.location.longitude,
      }}
      minWidth={200}
      maxWidth={400}
      className="font-mann"
    >
      <PropertyCard property={property} size={isMediumScreen ? 'md' : 'sm'} />
    </InfoWindow>
  );
}

// Main map component
function MapContent({
  properties,
  // center,
  // zoom,
  onMapIdle,
  hoveredPropertyId,
}: PropertiesMapProps) {
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(14);
  const map = useMap();
  const mapsLibrary = useMapsLibrary('maps');
  const hasInteracted = useRef(false);
  const isProgrammaticMove = useRef(false);
  const searchParams = useSearchParams();
  const prevSearchParams = useRef<string>('');

  // Initialize map options and listeners
  useEffect(() => {
    if (map && mapsLibrary) {
      map.setOptions({
        mapId: process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID,
      });

      // Set a flag on user interactions (not programmatic)
      const dragStartListener = map.addListener('dragstart', () => {
        if (!isProgrammaticMove.current) {
          hasInteracted.current = true;
        }
      });

      const zoomChangedListener = map.addListener('zoom_changed', () => {
        // Only count as interaction if it's not a programmatic move
        if (!isProgrammaticMove.current) {
          hasInteracted.current = true;
        }
        // Update zoom level for all markers
        const newZoom = map.getZoom() ?? 14;
        setCurrentZoom(newZoom);
      });

      // Add idle listener
      const idleListener = map.addListener('idle', () => {
        const bounds = map.getBounds();
        const center = map.getCenter();
        const zoom = map.getZoom();

        if (bounds && center && zoom && onMapIdle) {
          // Call API in these cases:
          // 1. User has interacted with the map (drag/zoom)
          // 2. Initial load when there are no properties yet
          // 3. After programmatic move from search (to get properties for new location)
          if (hasInteracted.current || properties.length === 0 || isProgrammaticMove.current) {
            onMapIdle({ bounds, center: center.toJSON(), zoom });
          }
        }

        // Reset programmatic move flag after idle
        isProgrammaticMove.current = false;
      });

      return () => {
        google.maps.event.removeListener(idleListener);

        google.maps.event.removeListener(dragStartListener);
        google.maps.event.removeListener(zoomChangedListener);
      };
    }
  }, [map, mapsLibrary, onMapIdle, properties.length]);

  // Handle map centering when search params change
  useEffect(() => {
    if (map) {
      const currentParams = searchParams.toString();
      const lat = searchParams.get('lat');
      const lng = searchParams.get('lng');
      const zoom = searchParams.get('zoom');

      // Only center if search params actually changed (to avoid infinite loops)
      if (currentParams !== prevSearchParams.current && lat && lng) {
        const centerLat = parseFloat(lat);
        const centerLng = parseFloat(lng);
        const targetZoom = zoom ? parseFloat(zoom) : 14;

        // Mark as programmatic move
        isProgrammaticMove.current = true;

        // Add a small delay to ensure map is fully ready
        setTimeout(() => {
          // Center the map on the searched location
          map.setCenter({ lat: centerLat, lng: centerLng });

          // Set zoom if provided
          if (zoom) {
            map.setZoom(targetZoom);
          }

          // This is a new search, so reset interaction flag to allow new API call
          hasInteracted.current = false;
        }, 100);

        prevSearchParams.current = currentParams;
      }
    }
  }, [map, searchParams]);

  const handleMarkerClick = useCallback((property: Property) => {
    setSelectedProperty(prevProperty => (prevProperty?.id === property.id ? null : property));
  }, []);

  // Memoize marker components to prevent unnecessary re-renders
  const memoizedMarkers = useMemo(() => {
    return properties.map(property => (
      <PropertyMarker
        key={property.id}
        property={property}
        onClick={() => handleMarkerClick(property)}
        selectedProperty={selectedProperty}
        isHovered={property.id === hoveredPropertyId}
        zoomLevel={currentZoom}
      />
    ));
  }, [properties, selectedProperty, hoveredPropertyId, currentZoom, handleMarkerClick]);

  const handleInfoWindowClose = useCallback(() => {
    setSelectedProperty(null);
  }, []);

  return (
    <>
      {memoizedMarkers}
      {selectedProperty && (
        <PropertyInfoWindow property={selectedProperty} onClose={handleInfoWindowClose} />
      )}
    </>
  );
}

// Main component
export default function PropertiesMap(props: PropertiesMapProps) {
  const defaultCenter = props.center || {
    lat: 10.842935416604869,
    lng: 106.84182012230411,
  };

  return (
    <div className="w-full h-full overflow-hidden relative">
      <Map
        defaultCenter={props.initialCenter || defaultCenter}
        defaultZoom={props.initialZoom || 14}
        mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
        mapTypeControl={true}
        fullscreenControl={true}
        streetViewControl={true}
        className="w-full h-full"
      >
        <MapContent {...props} />
      </Map>
    </div>
  );
}
