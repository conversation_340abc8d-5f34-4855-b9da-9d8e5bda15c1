'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Mail,
  Phone,
  Building2,
  MessageSquare,
  Edit,
  Trash2,
  CheckCircle,
  CalendarDays, // Thêm icon cho reschedule
} from 'lucide-react';
import { useState } from 'react'; // Thêm useState
import { CalendarEvent } from '../calendar/types';
import { RescheduleDialog } from './reschedule-dialog';
import { CancelAppointmentDialog } from './cancel-appointment-dialog';

interface EventDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event: CalendarEvent | null;
}

export function EventDetailsDialog({ open, onOpenChange, event }: EventDetailsDialogProps) {
  // Thêm state cho reschedule dialog
  const [rescheduleOpen, setRescheduleOpen] = useState(false);
  const [cancelOpen, setCancelOpen] = useState(false);

  if (!event) return null;

  const appointment = event.appointment;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-500';
      case 'closed':
        return 'bg-gray-500';
      case 'cancelled':
        return 'bg-red-500';
      case 'completed':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'Đang mở';
      case 'closed':
        return 'Đã đóng';
      case 'cancelled':
        return 'Đã hủy';
      case 'completed':
        return 'Hoàn thành';
      default:
        return status;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleComplete = () => {
    console.log('Complete appointment:', appointment.id);
  };

  const handleEdit = () => {
    console.log('Edit appointment:', appointment.id);
  };

  const handleSendMessage = () => {
    console.log('Send message for appointment:', appointment.id);
  };

  const handleCancelClick = () => {
    setCancelOpen(true);
  };
  const handleCancelSuccess = () => {
    setCancelOpen(false);
    onOpenChange(false); // Đóng dialog chính sau khi hủy thành công
  };

  // Thêm handler cho reschedule
  const handleReschedule = () => {
    setRescheduleOpen(true);
  };

  // Handler khi reschedule thành công
  const handleRescheduleSuccess = () => {
    setRescheduleOpen(false);
    // Có thể thêm logic refresh data hoặc close dialog chính
    // onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold">Chi tiết cuộc hẹn</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">#{appointment.id.slice(-8)}</p>
              </div>
              <Badge className={`${getStatusColor(appointment.status)} text-white`}>
                {getStatusText(appointment.status)}
              </Badge>
            </div>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Date & Time */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Thời gian cuộc hẹn
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Ngày hẹn</p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(appointment.date)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Giờ hẹn</p>
                        <p className="text-sm text-muted-foreground">
                          {formatTime(appointment.date)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    <span className="font-medium">Tạo lúc:</span>{' '}
                    {formatDate(appointment.createdAt)} - {formatTime(appointment.createdAt)}
                  </div>
                </CardContent>
              </Card>

              {/* Location & Property */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Địa điểm & Bất động sản
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">Địa chỉ</p>
                      <p className="text-sm text-muted-foreground mt-1">{appointment.location}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Mã bất động sản</p>
                      <p className="text-sm text-muted-foreground font-mono mt-1">
                        {appointment.propertyId}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Messages */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Tin nhắn
                    <Badge variant="secondary" className="ml-2">
                      {appointment.messages?.length || 0}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="font-medium">Chưa có tin nhắn nào</p>
                    <p className="text-sm">Tin nhắn sẽ hiển thị tại đây</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Saler Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Nhân viên bán hàng
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>{getInitials(appointment.saler.fullName)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{appointment.saler.fullName}</p>
                      <p className="text-xs text-muted-foreground font-mono">
                        ID: {appointment.saler.id.slice(-8)}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate">{appointment.saler.email}</span>
                    </div>
                    {appointment.saler.phoneNumber ? (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{appointment.saler.phoneNumber}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground italic">Chưa có số điện thoại</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Khách hàng
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>{getInitials(appointment.customer.fullName)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{appointment.customer.fullName}</p>
                      <p className="text-xs text-muted-foreground font-mono">
                        ID: {appointment.customer.id.slice(-8)}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="truncate">{appointment.customer.email}</span>
                    </div>
                    {appointment.customer.phoneNumber ? (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{appointment.customer.phoneNumber}</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground italic">Chưa có số điện thoại</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Hành động</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" onClick={handleComplete}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Hoàn thành cuộc hẹn
                  </Button>

                  <Button
                    className="w-full"
                    variant="secondary"
                    onClick={handleReschedule}
                    disabled={
                      appointment.status.toLowerCase() === 'completed' ||
                      appointment.status.toLowerCase() === 'cancelled'
                    }
                  >
                    <CalendarDays className="h-4 w-4 mr-2" />
                    Chuyển lịch hẹn
                  </Button>

                  <Button className="w-full" variant="outline" onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Chỉnh sửa thông tin
                  </Button>

                  <Button className="w-full" variant="outline" onClick={handleSendMessage}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Gửi tin nhắn
                  </Button>

                  <Separator />

                  <Button
                    className="w-full"
                    variant="destructive"
                    onClick={handleCancelClick}
                    disabled={
                      appointment.status.toLowerCase() === 'completed' ||
                      appointment.status.toLowerCase() === 'cancelled'
                    }
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Hủy cuộc hẹn
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* THÊM RESCHEDULE DIALOG */}
      <RescheduleDialog
        open={rescheduleOpen}
        onOpenChange={setRescheduleOpen}
        appointment={appointment}
        onSuccess={handleRescheduleSuccess}
      />
      <CancelAppointmentDialog
        open={cancelOpen}
        onOpenChange={setCancelOpen}
        appointment={appointment}
        onSuccess={handleCancelSuccess}
      />
    </>
  );
}
