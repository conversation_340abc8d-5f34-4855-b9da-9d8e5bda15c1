import Image from 'next/image';
import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

type ChannelChatCardProps = {
  channelName: string;
  img: string;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
};

function ChannelChatCard({ channelName, img, onClick, className }: ChannelChatCardProps) {
  return (
    <div
      className={`w-full flex justify-center p-2 rounded-[5px] cursor-pointer hover:bg-muted/90 transition-colors ${className || ''}`}
    >
      <div className="flex items-center justify-center">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Image
                src={img}
                alt="logo"
                width={35}
                height={35}
                className="rounded-[5px] cursor-pointer"
                onClick={e => {
                  onClick?.(e);
                }}
              />
            </TooltipTrigger>
            <TooltipContent side="right" className="ml-2">
              {channelName.toUpperCase()}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
export default ChannelChatCard;
