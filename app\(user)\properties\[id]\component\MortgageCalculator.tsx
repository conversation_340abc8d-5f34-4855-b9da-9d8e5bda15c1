'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Calculator,
  Building2,
  Percent,
  Calendar,
  Star,
  Download,
  CreditCard,
  TrendingUp,
  PiggyBank,
  Target,
  Clock,
  Wallet,
  BarChart3,
  PieChart,
  Info,
} from 'lucide-react';
import { usePopularBanks } from '@/hooks/useBanks';
import {
  calculateMortgage,
  formatNumber,
  formatPreciseCurrency,
  formatVietnameseCurrency,
  type MortgageCalculation,
  type MortgageInputs,
} from '@/utils/numbers/mortgageCalculations';
import { propertyService } from '@/lib/api/services/fetchProperty';
import {
  Bar,
  BarChart,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Cell,
  PieChart as RechartsPieChart,
  Pie,
  Tooltip,
  Legend,
} from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface MortgageCalculatorProps {
  initialPropertyValue?: number;
  onDialog: boolean;
}

export default function MortgageCalculator({
  initialPropertyValue,
  onDialog,
}: MortgageCalculatorProps) {
  const { popularBanks, allBanks, loading: banksLoading, error: banksError } = usePopularBanks();
  const [inputs, setInputs] = useState<MortgageInputs>({
    propertyValue: initialPropertyValue || **********,
    loanRatio: 70,
    loanAmount: (initialPropertyValue || **********) * 0.7,
    loanTermMonths: 180,
    annualInterestRate: 8.5,
    selectedBank: '',
  });
  const [calculation, setCalculation] = useState<MortgageCalculation | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [showAllBanks, setShowAllBanks] = useState(false);
  const [displayValues, setDisplayValues] = useState({
    propertyValue: formatNumber(initialPropertyValue || **********),
    loanAmount: formatNumber((initialPropertyValue || **********) * 0.7),
  });

  const handleLoanRatioChange = (ratio: number) => {
    const newLoanAmount = (inputs.propertyValue * ratio) / 100;
    setInputs(prev => ({
      ...prev,
      loanRatio: ratio,
      loanAmount: newLoanAmount,
    }));
    setDisplayValues(prev => ({ ...prev, loanAmount: formatNumber(newLoanAmount) }));
  };

  const handleLoanAmountChange = (amount: number) => {
    const newLoanRatio = inputs.propertyValue > 0 ? (amount / inputs.propertyValue) * 100 : 0;
    setInputs(prev => ({
      ...prev,
      loanAmount: amount,
      loanRatio: Math.min(newLoanRatio, 100),
    }));
  };

  const handleCalculate = () => {
    setIsCalculating(true);
    setTimeout(() => {
      const result = calculateMortgage(
        inputs.propertyValue,
        inputs.loanAmount,
        inputs.loanTermMonths,
        inputs.annualInterestRate
      );
      setCalculation(result);
      setIsCalculating(false);
    }, 500);
  };

  useEffect(() => {
    if (inputs.propertyValue > 0 && inputs.loanAmount > 0 && inputs.loanTermMonths > 0) {
      handleCalculate();
    }
  }, [inputs]);

  const banksToShow = showAllBanks ? allBanks : popularBanks;
  const selectedBankInfo = allBanks.find(bank => bank.code === inputs.selectedBank);

  const handleDownloadExcel = async () => {
    try {
      const blob = await propertyService.downloadExcel({
        LoanAmount: inputs.loanAmount,
        LoanTerm: inputs.loanTermMonths / 12,
        InterestRate: inputs.annualInterestRate,
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'BaoCao.xlsx';
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Tải file thất bại:', error);
    }
  };

  const chartData = calculation
    ? [
        {
          name: 'Tổng tiền vay',
          value: calculation.principal,
          fillId: 'gradientPrincipal',
        },
        {
          name: 'Tổng tiền lãi',
          value: calculation.totalInterest,
          fillId: 'gradientInterest',
        },
        {
          name: 'Tổng phải trả',
          value: calculation.totalPayment,
          fillId: 'gradientTotalPayment',
        },
      ]
    : [];

  // Pie chart data for principal vs interest breakdown - using red colors
  const pieChartData = calculation
    ? [
        {
          name: 'Tiền gốc',
          value: calculation.principal,
          fill: '#dc2626', // red-600
        },
        {
          name: 'Tiền lãi',
          value: calculation.totalInterest,
          fill: '#eb5e5e', // red-300 (lighter red)
        },
      ]
    : [];

  return (
    <div id="price" className="mb-4 md:mb-8">
      <div className={`grid gap-6 ${onDialog ? 'lg:grid-cols-2' : 'grid-cols-1'}`}>
        <Card>
          <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
            <CardTitle className="flex items-center gap-2 text-base md:text-xl">
              <Building2 className="size-4 md:size-5" />
              Thông Tin Khoản Vay
            </CardTitle>
            <CardDescription className="text-xs md:text-sm">
              Nhập thông tin để tính toán khoản vay thế chấp
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 max-md:px-3 max-md:pb-3 max-md:pt-2">
            {/* Giá trị bất động sản */}
            <div className="space-y-2">
              <Label
                htmlFor="propertyValue"
                className="flex items-center gap-2 text-xs md:text-base"
              >
                {/* <Building2 className="size-4 md:size-5" /> */}
                Giá trị bất động sản (VND)
              </Label>
              <Input
                disabled
                id="propertyValue"
                type="text"
                value={displayValues.propertyValue}
                onChange={e => {
                  const rawValue = e.target.value.replace(/,/g, '');
                  if (/^\d*$/.test(rawValue)) {
                    const numValue = Number(rawValue);
                    setDisplayValues(prev => ({ ...prev, propertyValue: formatNumber(numValue) }));
                    setInputs(prev => ({ ...prev, propertyValue: numValue }));
                  }
                }}
                placeholder="2,000,000,000"
              />
              <p className="text-xs md:text-sm text-muted-foreground font-medium">
                {formatVietnameseCurrency(inputs.propertyValue)} VND
              </p>
            </div>
            {/* Tỉ lệ vay và số tiền vay */}
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label
                    htmlFor="loanRatio"
                    className="flex items-center gap-2 text-xs md:text-base"
                  >
                    Tỉ lệ vay (%)
                  </Label>
                  <Input
                    id="loanRatio"
                    type="number"
                    min="0"
                    max="100"
                    value={inputs.loanRatio}
                    onChange={e => handleLoanRatioChange(Number(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="loanAmount"
                    className="flex items-center gap-2 text-xs md:text-base"
                  >
                    Số tiền vay (VND)
                  </Label>
                  <Input
                    id="loanAmount"
                    type="text"
                    value={displayValues.loanAmount}
                    onChange={e => {
                      const rawValue = e.target.value.replace(/,/g, '');
                      if (/^\d*$/.test(rawValue)) {
                        const numValue = Number(rawValue);
                        setDisplayValues(prev => ({ ...prev, loanAmount: formatNumber(numValue) }));
                        handleLoanAmountChange(numValue);
                      }
                    }}
                  />
                </div>
              </div>
              <p className="text-xs md:text-sm text-muted-foreground font-medium">
                Số tiền vay: {formatVietnameseCurrency(inputs.loanAmount)} VND
              </p>
            </div>
            {/* Thời gian vay */}
            <div className="space-y-2">
              <Label htmlFor="loanTerm" className="flex items-center gap-2 text-xs md:text-base">
                Thời gian vay (tháng)
              </Label>
              <Input
                id="loanTerm"
                type="number"
                min="1"
                max="180"
                value={inputs.loanTermMonths}
                onChange={e =>
                  setInputs(prev => ({ ...prev, loanTermMonths: Number(e.target.value) }))
                }
              />
              <p className="text-xs md:text-sm text-muted-foreground">
                {Math.round((inputs.loanTermMonths / 12) * 10) / 10} năm
              </p>
            </div>
            {/* Ngân hàng */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="bank" className="flex items-center gap-2 text-xs md:text-base">
                  Ngân hàng
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllBanks(!showAllBanks)}
                  className="text-xs"
                >
                  {showAllBanks ? 'Ẩn bớt' : 'Xem tất cả'}
                </Button>
              </div>
              <Select
                value={inputs.selectedBank}
                onValueChange={value => setInputs(prev => ({ ...prev, selectedBank: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={banksLoading ? 'Đang tải...' : 'Chọn ngân hàng'} />
                </SelectTrigger>
                <SelectContent>
                  {!showAllBanks && popularBanks.length > 0 && (
                    <>
                      <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        Ngân hàng phổ biến
                      </div>
                      <Separator className="my-1" />
                    </>
                  )}
                  {banksToShow.map(bank => (
                    <SelectItem key={bank.code} value={bank.code}>
                      <div className="flex items-center gap-2">
                        <span>{bank.shortName}</span>
                        <span className="text-muted-foreground">({bank.code})</span>
                      </div>
                    </SelectItem>
                  ))}
                  {banksError && (
                    <div className="px-2 py-1.5 text-xs text-red-500">
                      Không thể tải danh sách ngân hàng
                    </div>
                  )}
                </SelectContent>
              </Select>
              {selectedBankInfo && (
                <p className="text-xs md:text-sm text-muted-foreground">{selectedBankInfo.name}</p>
              )}
            </div>
            {/* Lãi suất */}
            <div className="space-y-2">
              <Label
                htmlFor="interestRate"
                className="flex items-center gap-2 text-xs md:text-base"
              >
                Lãi suất (%/năm)
              </Label>
              <Input
                id="interestRate"
                type="number"
                step="0.1"
                min="0"
                max="50"
                value={inputs.annualInterestRate}
                onChange={e =>
                  setInputs(prev => ({ ...prev, annualInterestRate: Number(e.target.value) }))
                }
              />
            </div>
            <Button
              onClick={handleCalculate}
              className="w-full bg-red-600 hover:bg-red-800"
              disabled={isCalculating}
            >
              {isCalculating ? 'Đang tính toán...' : 'Tính toán khoản vay'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
            <CardTitle className="flex items-center gap-2 text-base md:text-xl">
              <Calculator className="size-4 md:size-5" />
              Kết Quả Tính Toán
            </CardTitle>
            <CardDescription className="text-xs md:text-sm">
              Chi tiết khoản vay và số tiền cần trả
            </CardDescription>
          </CardHeader>
          <CardContent className="max-md:px-3 max-md:pb-3 max-md:pt-2">
            {calculation ? (
              <div className="space-y-6">
                {/* Quick Summary - Moved to Top */}
                <div className="p-5 rounded-xl border border-gray-200">
                  <div className="flex items-center gap-2 mb-3">
                    <Info className="size-4 md:size-5 text-gray-600" />
                    <h4 className="font-semibold text-gray-800">Tóm tắt nhanh</h4>
                  </div>
                  <div className="text-sm space-y-2 text-gray-700">
                    <div className="flex items-center gap-2">
                      <CreditCard className="size-4 md:size-5" />
                      <span>
                        Bạn sẽ trả{' '}
                        <strong className="text-gray-800">
                          {formatVietnameseCurrency(calculation.monthlyPayment)}
                        </strong>{' '}
                        mỗi tháng
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="size-4 md:size-5" />
                      <span>
                        Trong{' '}
                        <strong className="text-gray-800">{inputs.loanTermMonths} tháng</strong> (
                        {Math.round((inputs.loanTermMonths / 12) * 10) / 10} năm)
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      <span>
                        Tổng cộng{' '}
                        <strong className="text-gray-800">
                          {formatVietnameseCurrency(calculation.totalPayment)}
                        </strong>
                      </span>
                    </div>
                  </div>
                </div>

                {/* Main Results - No Colors */}
                <div className="space-y-4">
                  {/* Primary Result - Monthly Payment */}
                  <div className="relative overflow-hidden p-6 rounded-xl border border-gray-200">
                    <div className="absolute top-2 right-2">
                      <CreditCard className="h-6 w-6 text-gray-400 opacity-60" />
                    </div>
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <CreditCard className="h-5 w-5 text-gray-600" />
                      </div>
                      <div className="text-sm font-medium text-gray-700">
                        Số tiền trả hàng tháng
                      </div>
                    </div>
                    <div className="text-3xl font-semibold text-gray-900 mb-2">
                      {formatVietnameseCurrency(calculation.monthlyPayment)}
                    </div>
                    <div className="text-sm font-medium text-gray-600">
                      {formatPreciseCurrency(calculation.monthlyPayment)}
                    </div>
                  </div>

                  {/* Secondary Results Grid - No Colors */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-xl border border-gray-200">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="p-1.5 bg-gray-100 rounded-lg">
                          <PiggyBank className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="text-sm font-medium text-gray-700">Vốn tự có</div>
                      </div>
                      <div className="text-lg font-semibold text-gray-900 mb-1">
                        {formatVietnameseCurrency(calculation.ownCapital)}
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatPreciseCurrency(calculation.ownCapital)}
                      </div>
                    </div>

                    <div className="p-4 rounded-xl border border-gray-200">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="p-1.5 bg-gray-100 rounded-lg">
                          <TrendingUp className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="text-sm font-medium text-gray-700">Tổng tiền lãi</div>
                      </div>
                      <div className="text-lg font-semibold text-gray-900 mb-1">
                        {formatVietnameseCurrency(calculation.totalInterest)}
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatPreciseCurrency(calculation.totalInterest)}
                      </div>
                    </div>

                    <div className="p-4 rounded-xl border border-gray-200">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="p-1.5 bg-gray-100 rounded-lg">
                          <Target className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="text-sm font-medium text-gray-700">Tổng phải trả</div>
                      </div>
                      <div className="text-lg font-semibold text-gray-900 mb-1">
                        {formatVietnameseCurrency(calculation.totalPayment)}
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatPreciseCurrency(calculation.totalPayment)}
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                {/* Accordion for Detailed Information - Default Closed */}
                <div className="flex flex-wrap w-full">
                  <div className="w-full md:w-1/2 pr-2">
                    <Accordion
                      type="single"
                      collapsible
                      defaultValue="mortgage-details"
                      className="w-full"
                    >
                      <AccordionItem value="mortgage-details" className="rounded-md border">
                        <AccordionTrigger className="flex items-center gap-2 px-4">
                          <div className="flex items-center gap-2">
                            <Info className="h-5 w-5 text-gray-600" />
                            <span className="font-semibold text-gray-800">Chi tiết khoản vay</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="px-4 pb-4">
                          <div className="space-y-4">
                            <div className="flex justify-between items-center py-3 px-4 bg-gray-50 rounded-lg border border-gray-100">
                              <div className="flex items-center gap-2">
                                <Wallet className="h-4 w-4 text-gray-600" />
                                <span className="text-gray-700 font-medium">Gốc cần trả</span>
                              </div>
                              <div className="text-right">
                                <div className="font-bold text-gray-800">
                                  {formatVietnameseCurrency(calculation.principal)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {formatPreciseCurrency(calculation.principal)}
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-between items-center py-3 px-4 bg-gray-50 rounded-lg border border-gray-100">
                              <div className="flex items-center gap-2">
                                <Percent className="h-4 w-4 text-gray-600" />
                                <span className="text-gray-700 font-medium">Tỉ lệ vay</span>
                              </div>
                              <span className="font-bold text-gray-800">
                                {inputs.loanRatio.toFixed(1)}%
                              </span>
                            </div>

                            <div className="flex justify-between items-center py-3 px-4 bg-gray-50 rounded-lg border border-gray-100">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-600" />
                                <span className="text-gray-700 font-medium">Thời gian vay</span>
                              </div>
                              <span className="font-bold text-gray-800">
                                {inputs.loanTermMonths} tháng (
                                {Math.round((inputs.loanTermMonths / 12) * 10) / 10} năm)
                              </span>
                            </div>

                            <div className="flex justify-between items-center py-3 px-4 bg-gray-50 rounded-lg border border-gray-100">
                              <div className="flex items-center gap-2">
                                <TrendingUp className="h-4 w-4 text-gray-600" />
                                <span className="text-gray-700 font-medium">Lãi suất</span>
                              </div>
                              <span className="font-bold text-gray-800">
                                {inputs.annualInterestRate}%/năm
                              </span>
                            </div>
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>

                  <div className="w-full md:w-1/2 pl-2">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <BarChart3 className="h-5 w-5 text-gray-600" />
                        <h4 className="font-semibold text-gray-800">Phân tích khoản vay</h4>
                      </div>

                      <Tabs defaultValue="comparison" className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="breakdown" className="flex items-center gap-2">
                            <PieChart className="h-4 w-4" />
                            Cơ cấu khoản vay
                          </TabsTrigger>
                          <TabsTrigger value="comparison" className="flex items-center gap-2">
                            <BarChart3 className="h-4 w-4" />
                            So sánh chi phí
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="breakdown" className="space-y-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <ChartContainer
                              config={{
                                'Tiền gốc': {
                                  label: 'Tiền gốc',
                                  color: '#dc2626',
                                },
                                'Tiền lãi': {
                                  label: 'Tiền lãi',
                                  color: '#dc2626',
                                },
                              }}
                              className="h-[250px] w-full"
                            >
                              <ResponsiveContainer width="100%" height="100%">
                                <RechartsPieChart>
                                  <Pie
                                    data={pieChartData}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={60}
                                    outerRadius={100}
                                    paddingAngle={2}
                                    dataKey="value"
                                  >
                                    {pieChartData.map((entry, index) => (
                                      <Cell key={`cell-${index}`} fill={entry.fill} />
                                    ))}
                                  </Pie>
                                  <Tooltip
                                    formatter={(value: number) => formatVietnameseCurrency(value)}
                                    labelStyle={{ color: '#374151' }}
                                  />
                                  <Legend />
                                </RechartsPieChart>
                              </ResponsiveContainer>
                            </ChartContainer>
                            <div className="grid grid-cols-2 gap-4 mt-4">
                              <div className="text-center p-3 bg-gray-50 rounded-lg">
                                <div className="text-sm text-gray-600 font-medium">Tiền gốc</div>
                                <div className="text-lg font-bold text-gray-800">
                                  {(
                                    (calculation.principal / calculation.totalPayment) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </div>
                              </div>
                              <div className="text-center p-3 bg-gray-50 rounded-lg">
                                <div className="text-sm text-gray-600 font-medium">Tiền lãi</div>
                                <div className="text-lg font-bold text-gray-800">
                                  {(
                                    (calculation.totalInterest / calculation.totalPayment) *
                                    100
                                  ).toFixed(1)}
                                  %
                                </div>
                              </div>
                            </div>
                          </div>
                        </TabsContent>

                        <TabsContent value="comparison" className="space-y-4">
                          <div className="bg-white p-4 rounded-lg border">
                            <ChartContainer
                              config={{
                                'Tổng tiền vay': {
                                  label: 'Tổng tiền vay',
                                  color: '#dc2626',
                                },
                                'Tổng tiền lãi': {
                                  label: 'Tổng tiền lãi',
                                  color: '#dc2626',
                                },
                                'Tổng phải trả': {
                                  label: 'Tổng phải trả',
                                  color: '#b91c1c',
                                },
                              }}
                              className="h-[200px] w-full"
                            >
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                  data={chartData}
                                  layout="vertical"
                                  margin={{ left: 20, right: 20 }}
                                >
                                  <defs>
                                    <linearGradient
                                      id="gradientPrincipal"
                                      x1="0"
                                      y1="0"
                                      x2="1"
                                      y2="0"
                                    >
                                      <stop offset="0%" stopColor="#dc2626" />
                                      <stop offset="100%" stopColor="#ef4444" />
                                    </linearGradient>
                                    <linearGradient
                                      id="gradientInterest"
                                      x1="0"
                                      y1="0"
                                      x2="1"
                                      y2="0"
                                    >
                                      <stop offset="0%" stopColor="#fca5a5" />
                                      <stop offset="100%" stopColor="#f87171" />
                                    </linearGradient>
                                    <linearGradient
                                      id="gradientTotalPayment"
                                      x1="0"
                                      y1="0"
                                      x2="1"
                                      y2="0"
                                    >
                                      <stop offset="0%" stopColor="#b91c1c" />
                                      <stop offset="100%" stopColor="#dc2626" />
                                    </linearGradient>
                                  </defs>
                                  <XAxis
                                    type="number"
                                    dataKey="value"
                                    tickFormatter={value => formatVietnameseCurrency(value)}
                                    hide
                                  />
                                  <YAxis
                                    type="category"
                                    dataKey="name"
                                    tickLine={false}
                                    tickMargin={10}
                                    axisLine={false}
                                    width={80}
                                  />
                                  <ChartTooltip
                                    cursor={false}
                                    content={
                                      <ChartTooltipContent
                                        formatter={(
                                          value: string | number | (string | number)[]
                                        ) =>
                                          Array.isArray(value)
                                            ? value
                                                .map(v => formatVietnameseCurrency(Number(v)))
                                                .join(', ')
                                            : formatVietnameseCurrency(Number(value))
                                        }
                                      />
                                    }
                                  />
                                  <Bar dataKey="value" radius={5} barSize={20}>
                                    {chartData.map((entry, index) => (
                                      <Cell key={`cell-${index}`} fill={`url(#${entry.fillId})`} />
                                    ))}
                                  </Bar>
                                </BarChart>
                              </ResponsiveContainer>
                            </ChartContainer>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={handleDownloadExcel}
                  variant="outline"
                  size="sm"
                  className="mt-4 bg-transparent border-gray-200 text-gray-600 hover:bg-gray-50 hover:text-gray-700"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Tải về đầy đủ kết quả
                </Button>
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Calculator className="h-16 w-16 mx-auto mb-4 opacity-30" />
                <p className="text-lg font-medium">Nhập thông tin để xem kết quả tính toán</p>
                <p className="text-sm">Điền đầy đủ các trường bên trái để bắt đầu</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
