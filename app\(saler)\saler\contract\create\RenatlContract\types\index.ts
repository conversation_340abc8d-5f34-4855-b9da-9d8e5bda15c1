// Types for the rental contract form
export interface PartyA {
  name: string;
  address: string;
  phone: string;
  fax?: string;
  email: string;
  taxCode?: string;
  accountNumber?: string;
  representative: string;
  birthYear: string;
  position: string;
  idNumber: string;
  idIssuedDate: string;
  idIssuedPlace: string;
  personalName?: string;
  personalBirthYear?: string;
  personalIdNumber?: string;
  personalIdIssuedDate?: string;
  personalIdIssuedPlace?: string;
  personalAddress?: string;
  personalPhone?: string;
  ownershipType?: string;
}

export interface PartyB {
  name: string;
  idNumber: string;
  phone: string;
  birthDate: string;
  address: string;
  email: string;
  idVerification: string[];
  leaseStartDate: string;
  leaseEndDate: string;
}

export interface ContractTerms {
  monthlyRent: number;
  deposit: number;
  leaseStartDate: string;
  leaseEndDate: string;
  paymentMethod: string;
  paymentDay: number;
  propertyAddress: string;
  propertyPurpose?: string;
  propertyType?: string;
  apartmentNumber?: string;
  floor?: string;
  area?: number;
  landArea?: number;
  sharedArea?: number;
  privateArea?: number;
  ownershipOrigin?: string;
  ownershipRestrictions?: string;
  facilities?: string;
}

export interface ContractClauses {
  article4to6: string; // Điều 4-6: Quyền và nghĩa vụ của các bên, Quyền tiếp tục thuê
  article7to10: string; // Điều 7-9: Chấm dứt hợp đồng, Cam kết, Giải quyết tranh chấp
}

export interface FormData {
  partyA: PartyA;
  partyB: PartyB;
  terms: ContractTerms;
  clausesArticle4to6: { article4to6: string };
  clausesarticle7to10: { article7to10: string };
}

export interface ContractData {
  tenant: {
    name: string;
    cmnd: string;
    phone: string;
    address: string;
  };
  startDate: string;
  endDate: string;
  paymentMethod: string;
  monthlyPrice: number;
  paymentDay: number;
  deposit: number;
  contractContent: string;
}
