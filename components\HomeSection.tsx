'use client';

//import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
// import CircleButton from './CircleButton';
import HomePageSkeleton from './HomePageSkeleton';
import SearchFilter from '@/app/(user)/properties/components/SearchFilter';

export default function HeroSection() {
  return (
    <section className="w-full max-w-screen px-8 md:px-8 xl:px-32 mx-auto py-16 bg-background text-foreground ">
      <HomePageSkeleton />
      <div className="relative z-10 max-w-screen mx-auto">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-medium leading-relaxed tracking-tight ">
            Hệ thống bất động sản <br className="hidden sm:block" />
            <span className="text-red-600">RevoLand</span>
          </h1>

          {/* <p className="text-muted-foreground max-w-lg mx-auto font-normal ">
            <PERSON><PERSON> thống bất động sản RevoLand là một hệ thống bất động sản được phát triển bởi công ty RevoLand. Hệ thống bất động sản RevoLand cung cấp các dịch vụ bất động sản như cho thuê, bán, đất đai, nhà ở, và các dịch vụ liên quan khác.
          </p> */}

          <div className="max-w-7xl mx-auto">
            <SearchFilter />
          </div>

          <div className="flex flex-col sm:flex-row justify-center gap-4 ">
            <Button
              variant="outline"
              className="hover:bg-red-100 hover:text-red-600 hover:border-red-200 bg-red-50 text-red-600 border-red-200"
              asChild
            >
              <Link href="#">Learn more</Link>
            </Button>
            <Button className="bg-red-600 hover:bg-red-700" asChild>
              <Link href="/properties">Browse properties</Link>
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-10 max-w-4xl mx-auto">
            <StatBox value="200+" label="Happy customers" />
            <StatBox value="10k+" label="Properties" />
            <StatBox value="16+" label="Years experience" />
          </div>
        </div>
      </div>

      {/* <div className="hidden md:block absolute left-[52%] top-[160px] -translate-x-1/2 -translate-y-1/2 z-20">
        <CircleButton />
      </div> */}
    </section>
  );
}

function StatBox({ value, label }: { value: string; label: string }) {
  return (
    <div className="bg-muted p-4 rounded-xl text-center shadow-sm">
      <p className="text-2xl font-bold text-red-600">{value}</p>
      <p className="text-sm text-muted-foreground">{label}</p>
    </div>
  );
}
