'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  CheckCircle,
  MapPin,
  Building2,
  PlayCircle,
  Scan,
  DollarSign,
  MessageCircleMore,
  Star,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Property } from '@/lib/api/services/fetchProperty';
import { useIsMobile } from '@/hooks/useMobile';

interface StickyTabsProps {
  property: Property;
}

const tabs = [
  {
    id: 'description',
    label: 'Mô tả',
    icon: Home,
  },
  {
    id: 'amenities',
    label: 'Tiện ích',
    icon: CheckCircle,
  },

  {
    id: 'location',
    label: 'Địa chỉ',
    icon: MapPin,
  },
  {
    id: 'price',
    label: 'Chi phí',
    icon: DollarSign,
  },
  {
    id: 'floor-plans',
    label: 'Bản vẽ',
    icon: Building2,
  },

  {
    id: 'videos',
    label: 'Video',
    icon: PlayCircle,
  },
  {
    id: 'chat',
    label: 'Hỏi đáp',
    icon: MessageCircleMore,
  },
  {
    id: 'details',
    label: '<PERSON> tiết',
    icon: Scan,
  },
  {
    id: 'feedback',
    label: 'Đ<PERSON>h giá',
    icon: Star,
  },
];

export default function StickyTabs({ property }: StickyTabsProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('description');
  const isMobile = useIsMobile();

  // Filter tabs based on available content
  const availableTabs = tabs.filter(tab => {
    if (tab.id === 'floor-plans') {
      return property.floorPlanUrls && property.floorPlanUrls.length > 0;
    }
    if (tab.id === 'videos') {
      return property.video && property.video.videoUrl;
    }
    if (tab.id === 'price') {
      return (
        property.priceDetails &&
        (property.priceDetails.rentalPrice || property.priceDetails.salePrice)
      );
    }
    return true;
  });

  // Throttled scroll handler
  const handleScroll = useCallback(() => {
    const showcaseElement = document.querySelector('#property-showcase');
    if (showcaseElement) {
      const showcaseRect = showcaseElement.getBoundingClientRect();
      const shouldShow = showcaseRect.bottom < 100; // Show when showcase is mostly out of view
      setIsVisible(shouldShow);
    }
  }, []);

  // Intersection observer for active section detection
  useEffect(() => {
    const sections = availableTabs.map(tab => document.getElementById(tab.id)).filter(Boolean);

    // Responsive root margin based on device type
    const headerOffset = isMobile ? 100 : 80;
    const rootMargin = `-${headerOffset}px 0px -60% 0px`;

    const observer = new IntersectionObserver(
      entries => {
        const visibleSections = entries
          .filter(entry => entry.isIntersecting)
          .sort((a, b) => b.intersectionRatio - a.intersectionRatio);

        if (visibleSections.length > 0) {
          setActiveTab(visibleSections[0].target.id);
        }
      },
      {
        rootMargin,
        threshold: [0, 0.1, 0.5],
      }
    );

    sections.forEach(section => {
      if (section) observer.observe(section);
    });

    return () => observer.disconnect();
  }, [availableTabs, isMobile]);

  // Scroll event listener
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const throttledScrollHandler = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleScroll, 10);
    };

    window.addEventListener('scroll', throttledScrollHandler);
    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      clearTimeout(timeoutId);
    };
  }, [handleScroll]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Different offsets for mobile vs desktop
      const headerOffset = isMobile ? 100 : 80; // Smaller offset for mobile
      const offsetTop = element.offsetTop - headerOffset;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 30,
            mass: 0.8,
          }}
          className="fixed top-16 md:top-20 left-0 right-0 z-40 bg-white/70 backdrop-blur-md border-b border-gray-200 shadow-sm"
        >
          <div className="container mx-auto px-4">
            <div className="flex items-center overflow-x-auto py-2 md:py-3 scrollbar-hide">
              <div className="flex gap-1 min-w-max">
                {availableTabs.map(tab => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => scrollToSection(tab.id)}
                      className={cn(
                        'relative flex items-center gap-2 px-2 md:px-4 py-2 text-xs md:text-sm font-medium transition-all duration-200 whitespace-nowrap border-transparent hover:text-primary',
                        isActive
                          ? 'text-primary border-primary'
                          : 'text-muted-foreground hover:text-foreground'
                      )}
                      //   whileHover={{ scale: 1.02 }}
                      //   whileTap={{ scale: 0.98 }}
                    >
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <span>{tab.label}</span>

                      {/* Active indicator */}
                      {isActive && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                          initial={false}
                          transition={{
                            type: 'spring',
                            stiffness: 500,
                            damping: 30,
                          }}
                        />
                      )}
                    </motion.button>
                  );
                })}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
