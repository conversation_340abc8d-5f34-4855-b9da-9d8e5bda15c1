# Mobile Header Layout

## Overview

The mobile header layout has been redesigned to provide a better mobile app-like experience with two main sections:

1. **Top Header (Sticky)**: Contains the logo, search component, and hamburger menu
2. **Bottom Navigation (Sticky)**: Mobile app-style navigation with icons and labels

## Components

### Top Header

- **Logo**: Revoland logo on the left
- **Search**: `SearchAutocompleteLocation` component in the center (mobile only)
- **Menu**: Hamburger menu on the left for additional navigation
- **Auth**: Hidden on mobile (moved to bottom navigation)

### Bottom Navigation

- **Explore**: Main page (`/`)
- **Favorites**: Saved properties (`/myrevo?tab=saved`)
- **Tour**: Appointments (`/myrevo?tab=appointments`)
- **Messages**: Messenger (`/myrevo?tab=messenger`)
- **Account**: User avatar with dropdown menu

## Features

### Responsive Design

- Bottom navigation only shows on mobile devices (`md:hidden`)
- Top header adapts to include search component on mobile
- Desktop layout remains unchanged

### Active States

- Navigation items show active state with red color and background
- Account dropdown opens upward to avoid bottom navigation overlap

### Integration

- ContactSideBar floating bar positioned above bottom navigation
- Proper z-index layering for all components
- Bottom padding added to page content to account for navigation

## Implementation Details

### Bottom Navigation Component

```tsx
function BottomNavigation() {
  // Navigation items with icons and labels
  // Account dropdown with full user menu
  // Active state styling
}
```

### Mobile Detection

Uses the existing `useIsMobile` hook to conditionally render mobile-specific components.

### Styling

- Fixed positioning for bottom navigation
- Proper spacing and padding
- Smooth transitions and hover effects
- Red accent color for active states

## Usage

The new layout automatically activates on mobile devices (screen width < 768px) and provides:

1. **Better UX**: Mobile app-like navigation experience
2. **Easy Access**: Quick access to key features
3. **Search Integration**: Prominent search functionality
4. **Account Management**: Full account menu accessible from bottom nav

## Future Enhancements

- Dynamic active state based on current route
- Notification badges on navigation items
- Customizable navigation items
- Animation improvements
