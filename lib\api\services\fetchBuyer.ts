import apiService from '../core';

export interface Buyer {
  id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  idVerifications: string[];
  bankCode: string;
  bankName: string;
  taxCode: string;
  faxCode: string;
  createdAt: string;
  updatedAt: string;
}
export interface BuyerCreateData {
  name: string;
  phone: string;
  email: string;
  address: string;
  idVerifications?: string[] | [];
  bankCode: string;
  bankName: string;
  taxCode: string;
  faxCode: string;
}
export interface BuyerCreateResponse {
  code: string;
  status: string;
  message: string;
  data: Buyer;
}

export interface RequestGetBuyerByPhone {
  phone: string;
  email: string; // Optional, in case you want to check by both phone and email
}

export const fetchBuyer = {
  // get all buyers
  getAllBuyers: async (): Promise<BuyerCreateResponse[]> => {
    const response = await apiService.get<BuyerCreateResponse[]>('/api/buyer');
    return response.data;
  },
  // create buyer
  createBuyer: async (data: BuyerCreateData): Promise<BuyerCreateResponse> => {
    try {
      const response = await apiService.get<BuyerCreateResponse>(
        `/api/buyer/phone-or-email?phone=${data.phone}&email=${data.email}`
      );
      return response.data;
    } catch (error) {
      const response = await apiService.post<BuyerCreateResponse, BuyerCreateData>(
        '/api/buyer',
        data
      );
      return response.data;
    }
  },
  // get buyer by id
  getBuyerById: async (id: string): Promise<BuyerCreateResponse> => {
    const response = await apiService.get<BuyerCreateResponse>(`/api/buyer/${id}`);
    return response.data;
  },
  // get buyer by phone
  getBuyerByPhone: async (data: RequestGetBuyerByPhone): Promise<BuyerCreateResponse | null> => {
    const response = await apiService.get<BuyerCreateResponse>(
      `/api/buyer/phone-or-email?phone=${data.phone}&email=${data.email}`
    );
    return response.data;
  },
  // update buyer by id
  updateBuyerById: async (id: string, data: BuyerCreateData): Promise<BuyerCreateResponse> => {
    const response = await apiService.put<BuyerCreateResponse, BuyerCreateData>(
      `/api/buyer/${id}`,
      data
    );
    return response.data;
  },
};
