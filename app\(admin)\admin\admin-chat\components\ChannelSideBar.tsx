'use client';

import Image from 'next/image';
import ChannelChatCard from './ChannelChatCard';
import { ScrollArea } from '@/components/ui/scroll-area';

type ChannelSideBarProps = {
  isMobile: boolean;
  channels: [
    {
      platform: string;
    },
  ];
  setSelectedPlatform: React.Dispatch<React.SetStateAction<string>>;
  adminProfile: { name: string; avatar: string };
  platform: string;
};

const ZALO_IMG =
  'https://upload.wikimedia.org/wikipedia/commons/thumb/9/91/Icon_of_Zalo.svg/2048px-Icon_of_Zalo.svg.png';
const SYSTEM_IMG = '/quintech.webp';

export default function ChannelSideBar(props: ChannelSideBarProps) {
  const { channels, setSelectedPlatform, adminProfile, platform } = props;

  return (
    <div className="flex flex-col w-full h-full bg-background/70 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Header */}
      <div className="flex justify-center py-6 border-b-2 border-border">
        <Image src={'/quintech.webp'} alt="logo" width={40} height={40} />
      </div>

      {/* Channels List */}
      <div className="flex-1 px-0">
        <ScrollArea className="h-[calc(100vh-14rem)]">
          <div className="flex flex-col items-center gap-4 rounded-lg bg-muted/20 py-3">
            {channels.map(channel => {
              const isSelected = channel.platform === platform;
              switch (channel.platform) {
                case 'zalo':
                  return (
                    <ChannelChatCard
                      key={channel.platform}
                      onClick={() => setSelectedPlatform(channel.platform)}
                      channelName={channel.platform}
                      img={ZALO_IMG}
                      className={isSelected ? 'bg-muted/90' : ''}
                    />
                  );
                case 'system':
                  return (
                    <ChannelChatCard
                      key={channel.platform}
                      onClick={() => setSelectedPlatform(channel.platform)}
                      channelName={channel.platform}
                      img={SYSTEM_IMG}
                      className={isSelected ? 'bg-muted/90' : ''}
                    />
                  );
                default:
                  break;
              }
            })}
          </div>
        </ScrollArea>
      </div>

      <div className="flex flex-col items-center border-t-2 border-border cursor-pointer px-0">
        <div className="flex items-center w-full flex-col items-center gap-2 p-2 my-2">
          <div className="flex items-center justify-center w-10 h-10 aspect-square rounded-lg p-2 hover:bg-muted/90 transition-colors">
            <Image
              src={
                adminProfile.avatar ||
                `https://ui-avatars.com/api/?name=${encodeURIComponent(adminProfile.name)}&background=F3F4F6&color=000000`
              }
              alt="Admin avatar"
              width={40}
              height={40}
              className="rounded-full w-10 h-10 min-w-[40px] min-h-[40px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
