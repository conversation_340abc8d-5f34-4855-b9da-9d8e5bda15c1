import { useState, useEffect, useCallback, useRef } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Property } from '@/lib/api/services/fetchProperty';
import {
  recentlyViewedService,
  SyncRecentlyViewedResponse,
  FetchRecentlyViewedResponse,
  SyncRecentlyViewedRequest,
  PropertyViewHistory,
} from '@/lib/api/services/fetchRecentlyViewed';
import {
  RecentlyViewedStorage,
  RecentlyViewedStorageItem,
} from '@/lib/storage/recentlyViewedStorage';
import { RECENTLY_VIEWED_CONFIG } from '@/lib/config/recentlyViewed';
import { useAuthStore } from '@/lib/store/authStore';

export interface RecentlyViewedPropertyWithTime {
  property: Property;
  viewedAt: string;
  propertyId: string;
  id: string;
}

/**
 * Hook for syncing recently viewed property IDs to server
 */
export const useSyncRecentlyViewed = () => {
  return useMutation<SyncRecentlyViewedResponse, Error, SyncRecentlyViewedRequest[]>({
    mutationFn: (items: SyncRecentlyViewedRequest[]) =>
      recentlyViewedService.syncRecentlyViewed(items),
    retry: 2,
  });
};

/**
 * Hook for fetching recently viewed property IDs from server
 */
export const useFetchRecentlyViewed = (enabled: boolean = true) => {
  return useQuery<FetchRecentlyViewedResponse, Error>({
    queryKey: ['recently-viewed-server'],
    queryFn: () => recentlyViewedService.fetchRecentlyViewed(),
    enabled,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    select: (data: FetchRecentlyViewedResponse) => ({
      status: data.status,
      message: data.message,
      code: data.code,
      data: data.data || [],
    }),
  });
};

interface UseRecentlyViewedReturn {
  recentlyViewed: RecentlyViewedStorageItem[];
  recentlyViewedProperties: Property[];
  allRecentlyViewedProperties: Property[];
  recentlyViewedWithTime: RecentlyViewedPropertyWithTime[];
  allRecentlyViewedWithTime: RecentlyViewedPropertyWithTime[];
  isLoading: boolean;
  error: string | null;
  addToRecentlyViewed: (property: Property) => void;
  removeFromRecentlyViewed: (propertyId: string) => void;
  clearRecentlyViewed: () => void;
  refreshRecentlyViewed: () => RecentlyViewedStorageItem[];
  hasRecentlyViewed: boolean;
  isProcessing: boolean;
  hasSynced: boolean;
}

interface UseRecentlyViewedOptions {
  enableSync?: boolean;
  serverOnly?: boolean;
}

/**
 * Combined hook for recently viewed properties
 * Handles localStorage operations and server sync
 */
export function useRecentlyViewed(options: UseRecentlyViewedOptions = {}): UseRecentlyViewedReturn {
  const { enableSync = true } = options;

  const [recentlyViewed, setRecentlyViewed] = useState<RecentlyViewedStorageItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const storageRef = useRef<RecentlyViewedStorage | null>(null);

  // Sync state
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const hasSyncedRef = useRef(false);
  const isProcessingRef = useRef(false);

  // Initialize storage once
  if (!storageRef.current) {
    storageRef.current = new RecentlyViewedStorage();
  }

  // API hooks
  const syncMutation = useSyncRecentlyViewed();
  const shouldFetchFromServer =
    enableSync && isAuthenticated && !hasSyncedRef.current && !isProcessingRef.current;
  const { data: serverResponse, isLoading: isLoadingServer } =
    useFetchRecentlyViewed(shouldFetchFromServer);

  // Reload from localStorage
  const reloadFromStorage = useCallback((): RecentlyViewedStorageItem[] => {
    try {
      const items = storageRef.current!.getItems();
      setRecentlyViewed(items);
      setError(null);
      return items;
    } catch (err) {
      console.error('Failed to load recently viewed properties:', err);
      setError('Unable to load recently viewed properties');
      return [];
    }
  }, []);

  // Initial load on mount
  useEffect(() => {
    reloadFromStorage();
    setIsLoading(false);
  }, [reloadFromStorage]);

  // Merge localStorage + server data when user logs in
  useEffect(() => {
    if (
      !enableSync ||
      !isAuthenticated ||
      hasSyncedRef.current ||
      isProcessingRef.current ||
      !serverResponse?.data
    ) {
      return;
    }

    const serverViewHistory = serverResponse.data;
    const serverPropertyIds = serverViewHistory.map(item => item.propertyId);
    isProcessingRef.current = true;

    const mergeAndSync = async (): Promise<void> => {
      try {
        const localItems = storageRef.current!.getItems();
        const localPropertyIds = localItems.map(item => item.propertyId);

        // Create merged unique IDs
        const uniqueIds = new Set([...localPropertyIds, ...serverPropertyIds]);
        const allPropertyIds = Array.from(uniqueIds);

        if (allPropertyIds.length === 0) {
          hasSyncedRef.current = true;
          return;
        }

        // Fetch missing properties from server
        const missingFromLocal = serverPropertyIds.filter(id => !localPropertyIds.includes(id));

        if (missingFromLocal.length > 0) {
          await fetchAndAddMissingProperties(missingFromLocal);
        }

        // Sync merged data to server
        if (allPropertyIds.length > 0) {
          const syncItems = allPropertyIds.map(id => ({
            id,
            viewAt: new Date().toISOString(),
          }));
          await syncMutation.mutateAsync(syncItems);
        }

        hasSyncedRef.current = true;
      } catch (error) {
        console.error('Error during merge and sync process:', error);
        hasSyncedRef.current = true;
      } finally {
        isProcessingRef.current = false;
      }
    };

    const fetchAndAddMissingProperties = async (missingPropertyIds: string[]): Promise<void> => {
      try {
        const { default: propertyService } = await import('@/lib/api/services/fetchProperty');

        const propertyPromises = missingPropertyIds.map(
          async (propertyId): Promise<Property | null> => {
            try {
              const response = await propertyService.getProperty(propertyId);
              return response.status && response.data ? response.data : null;
            } catch (error) {
              console.error(`Error fetching property ${propertyId}:`, error);
              return null;
            }
          }
        );

        const fetchedProperties = await Promise.all(propertyPromises);
        const validProperties = fetchedProperties.filter(
          (property): property is Property => property !== null
        );

        // Add to localStorage
        for (const property of validProperties) {
          try {
            storageRef.current!.addItem(property);
          } catch (error) {
            console.error(`Error adding property ${property.id} to localStorage:`, error);
          }
        }

        reloadFromStorage();
      } catch (error) {
        console.error('Error fetching missing properties:', error);
      }
    };

    mergeAndSync();
  }, [enableSync, isAuthenticated, serverResponse, syncMutation, reloadFromStorage]);

  // Reset sync flags on logout
  useEffect(() => {
    if (!isAuthenticated) {
      hasSyncedRef.current = false;
      isProcessingRef.current = false;
    }
  }, [isAuthenticated]);

  // Add property to recently viewed
  const addToRecentlyViewed = useCallback(
    (property: Property): void => {
      if (!property?.id) {
        console.warn('Invalid property provided to addToRecentlyViewed');
        return;
      }

      try {
        storageRef.current!.addItem(property);
        reloadFromStorage();
      } catch (err) {
        console.error('Failed to add property to recently viewed:', err);
        setError('Unable to save property to recently viewed');
      }
    },
    [reloadFromStorage]
  );

  // Remove property from recently viewed
  const removeFromRecentlyViewed = useCallback(
    (propertyId: string): void => {
      if (!propertyId) {
        console.warn('Invalid property ID provided to removeFromRecentlyViewed');
        return;
      }

      try {
        storageRef.current!.removeItem(propertyId);
        reloadFromStorage();
      } catch (err) {
        console.error('Failed to remove property from recently viewed:', err);
        setError('Unable to remove property from recently viewed');
      }
    },
    [reloadFromStorage]
  );

  // Clear all recently viewed
  const clearRecentlyViewed = useCallback((): void => {
    try {
      storageRef.current!.clearAll();
      reloadFromStorage();
    } catch (err) {
      console.error('Failed to clear recently viewed properties:', err);
      setError('Unable to clear recently viewed properties');
    }
  }, [reloadFromStorage]);

  // Manual refresh
  const refreshRecentlyViewed = useCallback((): RecentlyViewedStorageItem[] => {
    return reloadFromStorage();
  }, [reloadFromStorage]);

  // Extract properties with time for display
  const allRecentlyViewedWithTime: RecentlyViewedPropertyWithTime[] = recentlyViewed
    .filter((item): item is RecentlyViewedStorageItem & { property: Property } =>
      Boolean(item.property?.id)
    )
    .map(item => ({
      property: item.property,
      viewedAt: item.viewedAt,
      propertyId: item.propertyId,
      id: item.id,
    }));

  // Extract properties for display (backward compatibility)
  const allRecentlyViewedProperties = allRecentlyViewedWithTime.map(item => item.property);

  // Limited for homepage
  const recentlyViewedProperties = allRecentlyViewedProperties.slice(
    0,
    RECENTLY_VIEWED_CONFIG.displayItems
  );

  // Limited with time for homepage
  const recentlyViewedWithTime = allRecentlyViewedWithTime.slice(
    0,
    RECENTLY_VIEWED_CONFIG.displayItems
  );

  return {
    recentlyViewed,
    recentlyViewedProperties,
    allRecentlyViewedProperties,
    recentlyViewedWithTime,
    allRecentlyViewedWithTime,
    isLoading: isLoading || (enableSync && isLoadingServer),
    error,
    addToRecentlyViewed,
    removeFromRecentlyViewed,
    clearRecentlyViewed,
    refreshRecentlyViewed,
    hasRecentlyViewed: recentlyViewed.length > 0,
    isProcessing: isProcessingRef.current,
    hasSynced: hasSyncedRef.current,
  };
}

// Re-export types for convenience
export type { SyncRecentlyViewedResponse, FetchRecentlyViewedResponse, PropertyViewHistory };
