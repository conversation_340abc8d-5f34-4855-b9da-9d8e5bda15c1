'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Loader2, Eye, Shield, Users } from 'lucide-react';
import { toast } from 'sonner';

interface PrivacySettings {
  profileVisible: boolean;
  showEmail: boolean;
  showPhone: boolean;
  showBirthdate: boolean;
  allowSearch: boolean;
  allowContact: boolean;
  showOnlineStatus: boolean;
  allowAnalytics: boolean;
}

interface PrivacySettingsProps {
  onSuccess?: () => void;
}

export function PrivacySettings({ onSuccess }: PrivacySettingsProps) {
  const [settings, setSettings] = useState<PrivacySettings>({
    profileVisible: true,
    showEmail: false,
    showPhone: false,
    showBirthdate: false,
    allowSearch: true,
    allowContact: true,
    showOnlineStatus: true,
    allowAnalytics: true,
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load settings from localStorage or API
    const savedSettings = localStorage.getItem('userPrivacySettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }
  }, []);

  const handleSettingChange = (key: keyof PrivacySettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    localStorage.setItem('userPrivacySettings', JSON.stringify(newSettings));

    // Simulate API call
    setIsSaving(true);
    setTimeout(() => {
      toast.success('Cài đặt quyền riêng tư đã được cập nhật');
      setIsSaving(false);
      onSuccess?.();
    }, 1000);
  };

  const saveAllSettings = async () => {
    setIsSaving(true);

    try {
      // Simulate API call to save all settings
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Tất cả cài đặt quyền riêng tư đã được lưu');
      onSuccess?.();
    } catch (error) {
      toast.error('Có lỗi xảy ra khi lưu cài đặt');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Cài đặt quyền riêng tư</h3>
          <p className="text-sm text-muted-foreground">
            Kiểm soát thông tin cá nhân và quyền riêng tư của bạn
          </p>
        </div>
        <Button onClick={saveAllSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Đang lưu
            </>
          ) : (
            'Lưu tất cả'
          )}
        </Button>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Hiển thị thông tin
            </CardTitle>
            <CardDescription>Kiểm soát thông tin hiển thị trên hồ sơ công khai</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hồ sơ công khai</Label>
                <p className="text-sm text-muted-foreground">
                  Cho phép người khác xem hồ sơ của bạn
                </p>
              </div>
              <Switch
                checked={settings.profileVisible}
                onCheckedChange={checked => handleSettingChange('profileVisible', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hiển thị email</Label>
                <p className="text-sm text-muted-foreground">Cho phép hiển thị địa chỉ email</p>
              </div>
              <Switch
                checked={settings.showEmail}
                onCheckedChange={checked => handleSettingChange('showEmail', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hiển thị số điện thoại</Label>
                <p className="text-sm text-muted-foreground">Cho phép hiển thị số điện thoại</p>
              </div>
              <Switch
                checked={settings.showPhone}
                onCheckedChange={checked => handleSettingChange('showPhone', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hiển thị ngày sinh</Label>
                <p className="text-sm text-muted-foreground">Cho phép hiển thị ngày sinh</p>
              </div>
              <Switch
                checked={settings.showBirthdate}
                onCheckedChange={checked => handleSettingChange('showBirthdate', checked)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Tương tác
            </CardTitle>
            <CardDescription>Kiểm soát cách người khác tương tác với bạn</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cho phép tìm kiếm</Label>
                <p className="text-sm text-muted-foreground">Cho phép người khác tìm thấy bạn</p>
              </div>
              <Switch
                checked={settings.allowSearch}
                onCheckedChange={checked => handleSettingChange('allowSearch', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cho phép liên hệ</Label>
                <p className="text-sm text-muted-foreground">Cho phép người khác liên hệ với bạn</p>
              </div>
              <Switch
                checked={settings.allowContact}
                onCheckedChange={checked => handleSettingChange('allowContact', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Hiển thị trạng thái online</Label>
                <p className="text-sm text-muted-foreground">
                  Cho phép hiển thị trạng thái hoạt động
                </p>
              </div>
              <Switch
                checked={settings.showOnlineStatus}
                onCheckedChange={checked => handleSettingChange('showOnlineStatus', checked)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Bảo mật dữ liệu
            </CardTitle>
            <CardDescription>Kiểm soát việc thu thập và sử dụng dữ liệu</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cho phép phân tích</Label>
                <p className="text-sm text-muted-foreground">
                  Cho phép thu thập dữ liệu để cải thiện dịch vụ
                </p>
              </div>
              <Switch
                checked={settings.allowAnalytics}
                onCheckedChange={checked => handleSettingChange('allowAnalytics', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
