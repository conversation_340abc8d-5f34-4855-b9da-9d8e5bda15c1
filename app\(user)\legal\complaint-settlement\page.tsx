'use client';

import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function ComplaintSettlementPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="flex items-center p-6">
        <Button variant="ghost" className="mr-4" onClick={() => router.back()}>
          <ArrowLeft className="w-6 h-6" />
        </Button>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 pb-12">
        <h1 className="text-4xl font-normal mb-4">Ch<PERSON>h sách giải quyết khiếu nại</h1>
        <p className="mb-12 italic">Hiệu lực: 25 tháng 7, 2025</p>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-2xl font-semibold mt-12 mb-4">
            1. <PERSON><PERSON><PERSON><PERSON> tắc giải quyết tranh chấp, khi<PERSON><PERSON> nại
          </h2>

          <p className=" leading-relaxed mb-6">
            Revoland là nền tảng trung gian cung cấp giải pháp công nghệ kết nối giữa bên bán và bên
            mua, bên cho thuê và bên thuê bất động sản. Revoland không tham gia vào quá trình đàm
            phán hoặc nội dung của các giao dịch giữa các bên.
          </p>

          <p className=" leading-relaxed mb-6">
            Người sử dụng nền tảng Revoland (bao gồm cả người đăng tin và người tham gia giao dịch)
            có trách nhiệm hoàn toàn trong việc xác minh, kiểm tra và đảm bảo tính chính xác, minh
            bạch của thông tin về bất động sản, dịch vụ cũng như thông tin cá nhân do chính mình
            cung cấp.
          </p>

          <p className=" leading-relaxed mb-6">
            Revoland miễn trách nhiệm đối với các thông tin không chính xác hoặc gây hiểu lầm do
            người dùng cung cấp, theo quy định tại Điều 13 Luật Bảo vệ quyền lợi người tiêu dùng năm
            2010.
          </p>

          <p className=" leading-relaxed mb-6">
            Revoland cam kết bảo vệ quyền lợi người tiêu dùng và tuân thủ nghiêm ngặt các quy định
            pháp luật hiện hành.
          </p>

          <p className=" leading-relaxed mb-6">
            Người đăng tin cần cung cấp thông tin đầy đủ, rõ ràng, trung thực về bất động sản và
            dịch vụ liên quan (nếu có). Mọi hành vi gian lận, lừa đảo hoặc đăng tin sai sự thật sẽ
            bị xử lý nghiêm và người đăng tin phải chịu hoàn toàn trách nhiệm trước pháp luật.
          </p>

          <p className=" leading-relaxed mb-6">
            Trong mọi giao dịch, các bên liên quan (người đăng tin, người mua/bán/thuê/cho thuê) cần
            chủ động phối hợp để xử lý các phát sinh nếu có. Khi xảy ra khiếu nại, người dùng có
            thể:
          </p>

          <ul className=" leading-relaxed mb-6 list-disc ml-6 space-y-2">
            <li>Gửi trực tiếp đến người đăng tin</li>
            <li>Phản ánh đến Ban quản trị Revoland để được hỗ trợ xử lý.</li>
          </ul>

          <p className=" leading-relaxed mb-6">
            Khi tiếp nhận phản ánh, Revoland sẽ chuyển tiếp nội dung khiếu nại đến người đăng tin
            bằng các phương thức nhanh chóng. Người đăng tin có trách nhiệm cung cấp tài liệu xác
            thực liên quan và phối hợp giải quyết.
          </p>

          <p className=" leading-relaxed mb-6">
            Trong mọi trường hợp, người đăng tin phải có trách nhiệm xử lý đầy đủ và nhanh chóng các
            vấn đề liên quan đến nội dung đã đăng và các giao dịch có liên quan (nếu có).
          </p>

          <p className=" leading-relaxed mb-6">
            Nếu phát sinh tranh chấp, các bên nên ưu tiên giải quyết bằng thương lượng, hòa giải.
          </p>

          <p className=" leading-relaxed mb-8">
            Trong trường hợp không đạt được thỏa thuận, Revoland khuyến nghị các bên gửi đơn đến cơ
            quan nhà nước có thẩm quyền để được xử lý theo quy định pháp luật.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            2. Quy trình tiếp nhận và giải quyết khiếu nại
          </h2>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 1:</h3>
          <p className=" leading-relaxed mb-4">
            Người sử dụng gửi yêu cầu khiếu nại đến Bộ phận Chăm sóc Khách hàng của Revoland qua các
            kênh liên hệ sau:
          </p>
          <div className="bg-gray-100 p-6 rounded-lg mb-6">
            <ul className=" space-y-2">
              <li>
                <strong>Hotline:</strong> 0968070478
              </li>
              <li>
                <strong>Email:</strong> <EMAIL> / <EMAIL>
              </li>
            </ul>
          </div>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 2:</h3>
          <p className=" leading-relaxed mb-6">
            Bộ phận Chăm sóc Khách hàng tiếp nhận thông tin, xác minh nội dung khiếu nại, đối chiếu
            dữ liệu hệ thống và chuyển tiếp đến bộ phận chuyên trách.
          </p>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 3:</h3>
          <p className=" leading-relaxed mb-6">
            Bộ phận liên quan đề xuất phương án giải quyết và phản hồi lại cho Bộ phận Chăm sóc
            Khách hàng.
          </p>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 4:</h3>
          <p className=" leading-relaxed mb-6">
            Bộ phận Chăm sóc Khách hàng trình phương án xử lý lên Ban Giám đốc để phê duyệt.
          </p>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 5:</h3>
          <p className=" leading-relaxed mb-6">
            Ban Giám đốc xem xét và phê duyệt phương án giải quyết khiếu nại.
          </p>

          <h3 className="text-xl font-medium mt-8 mb-4">Bước 6:</h3>
          <p className=" leading-relaxed mb-4">
            Bộ phận Chăm sóc Khách hàng thông báo kết quả xử lý cho người gửi khiếu nại:
          </p>
          <ul className=" leading-relaxed mb-6 list-disc ml-6 space-y-2">
            <li>Trường hợp đồng ý với kết quả, quy trình kết thúc.</li>
            <li>
              Trường hợp không đồng ý, người khiếu nại có thể yêu cầu xử lý lại. Khi đó, quy trình
              sẽ lặp lại từ Bước 2 đến Bước 6.
            </li>
            <li>
              Nếu sau quá trình xử lý lại, vẫn không đạt được thỏa thuận, người khiếu nại có quyền
              khởi kiện ra Tòa án hoặc Trọng tài theo quy định pháp luật.
            </li>
          </ul>

          <h2 className="text-2xl font-semibold mt-12 mb-4">3. Thời điểm áp dụng</h2>

          <p className=" leading-relaxed mb-6">
            Chính sách giải quyết khiếu nại này có hiệu lực kể từ ngày: <strong>25/7/2025</strong>
          </p>
        </div>
      </div>
    </div>
  );
}
