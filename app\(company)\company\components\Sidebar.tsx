import React from 'react';
import Link from 'next/link';
import {
  Home,
  BarChart3,
  Users,
  Settings,
  Plus,
  Search,
  Bell,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  UserPlus,
  Inbox,
  Activity,
} from 'lucide-react';

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  currentPage: string;
  onNavigateToTeamManagement: () => void;
  onNavigateToAgentInvitation: () => void;
  onNavigateToDashboard: () => void;
  onNavigateToSettings: () => void;
  onNavigateToLeadInbox: () => void;
  onNavigateToPipelineKanban: () => void;
}

interface NavigationItem {
  icon: React.ElementType;
  label: string;
  count?: number;
  href?: string;
  active?: boolean;
  onClick?: () => void;
  special?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  onToggle,
  currentPage,
  onNavigateToTeamManagement,
  onNavigateToAgentInvitation,
  onNavigateToDashboard,
  onNavigateToSettings,
  onNavigateToLeadInbox,
  onNavigateToPipelineKanban,
}) => {
  const navigationItems: NavigationItem[] = [
    { icon: Home, label: 'Trang chủ', active: currentPage === 'home', href: '/' },
    {
      icon: BarChart3,
      label: 'Báo cáo',
      active: currentPage === 'reports',
      onClick: onNavigateToDashboard,
    },
    {
      icon: Inbox,
      label: 'Lead Inbox',
      active: currentPage === 'lead-inbox',
      onClick: onNavigateToLeadInbox,
    },
    {
      icon: Activity,
      label: 'Pipeline Kanban',
      active: currentPage === 'pipeline-kanban',
      onClick: onNavigateToPipelineKanban,
    },
    // Special items below
    {
      icon: Users,
      label: 'Quản lý Nhóm',
      active: currentPage === 'team-management',
      onClick: onNavigateToTeamManagement,
      special: true,
    },
    {
      icon: UserPlus,
      label: 'Mời Thành viên',
      active: currentPage === 'agent-invitation',
      onClick: onNavigateToAgentInvitation,
      special: true,
    },
    {
      icon: Settings,
      label: 'Cài đặt',
      active: currentPage === 'settings',
      onClick: onNavigateToSettings,
    },
  ];

  return (
    <div
      className={`${collapsed ? 'w-20' : 'w-72'} bg-white border-r border-gray-100 flex flex-col min-h-screen shadow-sm transition-all duration-300 ease-in-out relative`}
    >
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="absolute -right-3 top-8 w-6 h-6 bg-white border border-gray-200 rounded-full flex items-center justify-center text-gray-400 hover:text-gray-600 hover:border-red-300 transition-all duration-200 shadow-sm z-10"
      >
        {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
      </button>

      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center flex-shrink-0">
              <span className="text-white font-bold text-lg">R</span>
            </div>
            {!collapsed && (
              <>
                <span className="text-gray-900 font-bold text-xl">Revoland</span>
                <button className="text-gray-400 hover:text-gray-600 transition-colors ml-auto">
                  <ChevronDown size={18} />
                </button>
              </>
            )}
          </div>
        </div>

        {!collapsed && (
          <div className="relative">
            <Search
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={18}
            />
            <input
              type="text"
              placeholder="Tìm kiếm..."
              className="w-full bg-gray-50 text-gray-900 placeholder-gray-400 pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
            />
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-6">
        {!collapsed && (
          <div className="mb-8">
            <button className="flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all font-medium">
              <Plus size={18} />
              <span>Tạo mới</span>
            </button>
          </div>
        )}

        {collapsed && (
          <div className="mb-8">
            <button className="flex items-center justify-center w-full p-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all">
              <Plus size={18} />
            </button>
          </div>
        )}

        <div className="space-y-2">
          {navigationItems.map((item, index) => {
            if (item.special || item.onClick) {
              return (
                <button
                  key={index}
                  onClick={item.onClick}
                  className={`flex items-center ${collapsed ? 'justify-center' : 'justify-between'} w-full px-4 py-3 rounded-xl transition-all font-medium group relative ${
                    item.active
                      ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                  title={collapsed ? item.label : undefined}
                >
                  <div className={`flex items-center ${collapsed ? 'justify-center' : 'gap-3'}`}>
                    <item.icon size={20} />
                    {!collapsed && <span>{item.label}</span>}
                  </div>
                </button>
              );
            }
            if (item.href) {
              return (
                <Link
                  key={index}
                  href={item.href}
                  className={`flex items-center ${collapsed ? 'justify-center' : 'justify-between'} w-full px-4 py-3 rounded-xl transition-all font-medium group relative ${
                    item.active
                      ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                  title={collapsed ? item.label : undefined}
                >
                  <div className={`flex items-center ${collapsed ? 'justify-center' : 'gap-3'}`}>
                    <item.icon size={20} />
                    {!collapsed && <span>{item.label}</span>}
                  </div>
                  {!collapsed && item.count && (
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${
                        item.active ? 'bg-white/20 text-white' : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {item.count}
                    </span>
                  )}
                  {collapsed && item.count && (
                    <span
                      className={`absolute -top-1 -right-1 text-xs px-1.5 py-0.5 rounded-full font-medium ${
                        item.active ? 'bg-white text-red-500' : 'bg-red-500 text-white'
                      }`}
                    >
                      {item.count}
                    </span>
                  )}
                </Link>
              );
            }
            return null;
          })}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-6 border-t border-gray-100">
        <div className={`flex items-center ${collapsed ? 'justify-center' : 'justify-between'}`}>
          {!collapsed && (
            <div className="flex items-center gap-3">
              <button className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-50 rounded-lg">
                <Bell size={20} />
              </button>
              <button className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-50 rounded-lg">
                <Settings size={20} />
              </button>
            </div>
          )}

          {collapsed && (
            <div className="flex flex-col gap-2">
              <button className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-50 rounded-lg">
                <Bell size={20} />
              </button>
              <button className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-50 rounded-lg">
                <Settings size={20} />
              </button>
            </div>
          )}

          <div className={`flex items-center ${collapsed ? 'justify-center mt-4' : 'gap-3'}`}>
            {!collapsed && (
              <div>
                <p className="text-sm font-medium text-gray-900">An San</p>
                <p className="text-xs text-gray-500">Quản trị viên</p>
              </div>
            )}
            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
              AS
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
