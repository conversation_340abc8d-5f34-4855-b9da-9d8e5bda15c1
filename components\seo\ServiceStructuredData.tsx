interface ServiceArea {
  name: string;
  type?: 'City' | 'State' | 'Country';
}

interface ServiceStructuredDataProps {
  serviceName: string;
  description: string;
  serviceType: string;
  provider: string;
  areaServed: ServiceArea[];
  url?: string;
  offers?: {
    name: string;
    description: string;
    priceRange?: string;
  }[];
}

export default function ServiceStructuredData({
  serviceName,
  description,
  serviceType,
  provider,
  areaServed,
  url = 'https://www.revoland.vn',
  offers = [],
}: ServiceStructuredDataProps) {
  const serviceData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: serviceName,
    description: description,
    serviceType: serviceType,
    provider: {
      '@type': 'Organization',
      name: provider,
      url: url,
      logo: {
        '@type': 'ImageObject',
        url: `${url}/LOGO_RV_red-01-01.png`,
      },
    },
    areaServed: areaServed.map(area => ({
      '@type': area.type || 'City',
      name: area.name,
    })),
    offers: offers.map(offer => ({
      '@type': 'Offer',
      name: offer.name,
      description: offer.description,
      ...(offer.priceRange && { priceRange: offer.priceRange }),
      priceCurrency: 'VND',
      seller: {
        '@type': 'Organization',
        name: provider,
      },
    })),
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: `${serviceName} Services`,
      itemListElement: offers.map((offer, index) => ({
        '@type': 'OfferCatalog',
        position: index + 1,
        itemOffered: {
          '@type': 'Service',
          name: offer.name,
          description: offer.description,
        },
      })),
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(serviceData),
      }}
    />
  );
}
