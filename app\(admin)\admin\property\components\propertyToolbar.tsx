import React, { FormEvent, useState } from 'react';
import { Search, Grid, List, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { PropertySearchParams, PropertyStatus } from '@/lib/api/services/fetchProperty';

interface PropertyToolbarProps {
  searchParams: Partial<PropertySearchParams>;
  onFilterChange: (params: PropertySearchParams) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  totalItems: number;
  isLoading?: boolean;
  hasFilters?: boolean;
  onToggleFilters?: () => void;
}

export function PropertyToolbar({
  searchParams,
  onFilterChange,
  viewMode,
  onViewModeChange,
  totalItems,
  isLoading = false,
  hasFilters = false,
  onToggleFilters,
}: PropertyToolbarProps) {
  const [searchQuery, setSearchQuery] = useState(searchParams.searchTerm || '');

  // const sortOptions: SortOption[] = [
  // {
  //   value: 'default',
  //   label: t('properties.sort.default'),
  //   field: 'createdAt',
  //   direction: 'desc',
  // },
  // {
  //   value: 'price_asc',
  //   label: t('properties.sort.price_low_high'),
  //   field: 'salePrice',
  //   direction: 'asc',
  // },
  // {
  //   value: 'price_desc',
  //   label: t('properties.sort.price_high_low'),
  //   field: 'salePrice',
  //   direction: 'desc',
  // },
  // {
  //   value: 'area_asc',
  //   label: t('properties.sort.area_small_large'),
  //   field: 'buildingArea',
  //   direction: 'asc',
  // },
  // {
  //   value: 'area_desc',
  //   label: t('properties.sort.area_large_small'),
  //   field: 'buildingArea',
  //   direction: 'desc',
  // },
  // {
  //   value: 'date_newest',
  //   label: t('properties.sort.newest_first'),
  //   field: 'createdAt',
  //   direction: 'desc',
  // },
  // {
  //   value: 'date_oldest',
  //   label: t('properties.sort.oldest_first'),
  //   field: 'createdAt',
  //   direction: 'asc',
  // },
  // ];

  //   const getCurrentSortValue = () => {
  //     const sortBy = searchParams.sortBy;
  //     const sortOrder = searchParams.sortOrder || 'desc';

  //     if (!sortBy) return 'default';

  //     const option = sortOptions.find(
  //       opt => opt.field === sortBy && opt.direction === sortOrder
  //     );
  //     return option?.value || 'default';
  //   };

  const handleSearchSubmit = (e: FormEvent) => {
    e.preventDefault();
    onFilterChange({
      ...searchParams,
      searchTerm: searchQuery.trim() || undefined,
      pageNumber: 1,
    });
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    // Debounced search could be implemented here
  };

  //   const handleSortChange = (value: string) => {
  //     const option = sortOptions.find(opt => opt.value === value);
  //     if (!option) return;

  //     onFilterChange({
  //       ...searchParams,
  //       sortBy: option.field,
  //       sortOrder: option.direction,
  //       pageNumber: 1,
  //     });
  //   };

  const clearSearch = () => {
    setSearchQuery('');
    onFilterChange({
      ...searchParams,
      searchTerm: undefined,
      pageNumber: 1,
    });
  };

  return (
    <div className="flex flex-col gap-3 lg:gap-4">
      {/* Main toolbar */}
      <div className="flex items-center justify-between gap-4">
        {/* Left side - Search */}
        <div className="flex-1 max-w-md">
          <form onSubmit={handleSearchSubmit} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Tìm kiếm"
              value={searchQuery}
              onChange={e => handleSearchChange(e.target.value)}
              className="pl-10 pr-10 h-9"
              disabled={isLoading}
            />
            {searchQuery && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted"
              >
                ×
              </Button>
            )}
          </form>
        </div>

        {/* <div className="flex items-center gap-2">
          <Button variant="outline" className="hidden lg:flex h-9 text-sm">
            <Upload className="h-4 w-4 mr-2" />
            Nhập bất động sản
          </Button>

          <Link href="/admin/property/action">
            <Button className="h-9 text-sm">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Thêm mới</span>
              <span className="sm:hidden">Thêm</span>
            </Button>
          </Link>
        </div> */}
      </div>

      {/* Secondary toolbar */}
      <div className="flex items-center justify-between gap-4">
        {/* Left side - View controls and filters */}
        <div className="flex items-center gap-2">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-md p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="h-7 px-2 text-xs"
            >
              <Grid className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="h-7 px-2 text-xs"
            >
              <List className="h-3 w-3" />
            </Button>
          </div>

          {/* Filter Toggle Button */}
          <Button
            variant={hasFilters ? 'default' : 'outline'}
            size="sm"
            onClick={onToggleFilters}
            className="flex items-center gap-2"
          >
            <Filter className="h-3 w-3" />
            <span className="hidden sm:inline">Bộ lọc</span>
            {hasFilters && (
              <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                {
                  Object.keys(searchParams).filter(
                    key =>
                      key !== 'pageNumber' &&
                      key !== 'pageSize' &&
                      searchParams[key as keyof PropertySearchParams] !== undefined
                  ).length
                }
              </Badge>
            )}
          </Button>

          {/* Results count */}
          <div className="text-sm text-muted-foreground hidden md:block">
            {isLoading ? (
              <span>Đang tải...</span>
            ) : (
              <span>
                {totalItems} {totalItems === 1 ? 'bất động sản' : 'bất động sản'}
              </span>
            )}
          </div>
        </div>

        {/* Right side - Sort */}
        {/* <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground hidden sm:block">
            {t('properties.sort_by')}:
          </span>
          <Select
            value={getCurrentSortValue()}
            onValueChange={handleSortChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    {option.direction === 'asc' ? (
                      <SortAsc className="h-3 w-3" />
                    ) : (
                      <SortDesc className="h-3 w-3" />
                    )}
                    {option.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div> */}
      </div>

      {/* Active filters display */}
      {Object.keys(searchParams).filter(
        key =>
          key !== 'pageNumber' &&
          key !== 'pageSize' &&
          searchParams[key as keyof PropertySearchParams] !== undefined
      ).length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Bộ lọc đang áp dụng:</span>
          {searchParams.searchTerm && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Tìm kiếm: {searchParams.searchTerm}
              <Button
                onClick={() =>
                  onFilterChange({ ...searchParams, searchTerm: undefined, pageNumber: 1 })
                }
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          {searchParams.type && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Danh mục: {searchParams.type}
              <Button
                onClick={() => onFilterChange({ ...searchParams, type: undefined, pageNumber: 1 })}
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          {searchParams.status && searchParams.status !== PropertyStatus.AVAILABLE && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Trạng thái: {searchParams.status}
              <Button
                onClick={() =>
                  onFilterChange({ ...searchParams, status: undefined, pageNumber: 1 })
                }
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          {/* Add more filter badges as needed */}
        </div>
      )}
    </div>
  );
}
