import React, { useState } from 'react';
import {
  ShoppingCart,
  Home,
  Building,
  FileText,
  Upload,
  Check,
  ArrowLeft,
  ArrowRight,
  UserPlus,
  Mail,
  Phone,
  Trash2,
} from 'lucide-react';

/**
 * Props for WelcomePopup
 */
interface WelcomePopupProps {
  onSelect: (data: unknown) => void;
}

interface Member {
  id: string;
  contact: string;
  type: 'email' | 'phone';
  role: string;
}

interface FormData {
  businessType: string;
  companyName: string;
  email: string;
  address: string;
  website: string;
  businessLicense: string;
  taxCode: string;
  logo: File | null;
  members: Member[];
}

const WelcomePopup: React.FC<WelcomePopupProps> = ({ onSelect }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    businessType: '',
    companyName: '',
    email: '',
    address: '',
    website: '',
    businessLicense: '',
    taxCode: '',
    logo: null,
    members: [],
  });

  const [newMemberContact, setNewMemberContact] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('member');

  const businessTypes = [
    {
      id: 'buy-sell',
      title: 'Mua Bán',
      subtitle: 'Kinh doanh mua bán bất động sản',
      icon: ShoppingCart,
      color: 'from-red-500 to-orange-500',
    },
    {
      id: 'rental',
      title: 'Cho Thuê',
      subtitle: 'Kinh doanh cho thuê bất động sản',
      icon: Home,
      color: 'from-blue-500 to-cyan-500',
    },
  ];

  const memberRoles = [
    { id: 'admin', label: 'Quản trị viên', description: 'Toàn quyền quản lý' },
    { id: 'manager', label: 'Quản lý', description: 'Quản lý nhóm và dự án' },
    { id: 'member', label: 'Thành viên', description: 'Tham gia và thực hiện công việc' },
    { id: 'viewer', label: 'Người xem', description: 'Chỉ xem, không chỉnh sửa' },
  ];

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, logo: file }));
  };

  const detectContactType = (contact: string): 'email' | 'phone' => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // const phoneRegex = /^[\+]?\d[\d\s\-()]{9,}$/;
    const phoneRegex = /^[+]?\d[\d\s\-()]{9,}$/;
    if (emailRegex.test(contact)) return 'email';
    if (phoneRegex.test(contact)) return 'phone';
    return 'email';
  };

  const addMember = () => {
    if (!newMemberContact.trim()) return;
    const contactType = detectContactType(newMemberContact);
    const newMember: Member = {
      id: Date.now().toString(),
      contact: newMemberContact.trim(),
      type: contactType,
      role: newMemberRole,
    };
    setFormData(prev => ({
      ...prev,
      members: [...prev.members, newMember],
    }));
    setNewMemberContact('');
    setNewMemberRole('member');
  };

  const removeMember = (memberId: string) => {
    setFormData(prev => ({
      ...prev,
      members: prev.members.filter(member => member.id !== memberId),
    }));
  };

  const canContinue = () => {
    switch (currentStep) {
      case 1:
        return formData.businessType !== '';
      case 2:
        return formData.companyName && formData.email && formData.address;
      case 3:
        return formData.businessLicense && formData.taxCode;
      case 4:
        return formData.logo !== null;
      case 5:
        return true;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    } else {
      onSelect(formData);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Chọn Loại Hình Kinh Doanh</h3>
              <p className="text-gray-600">Vui lòng chọn loại hình kinh doanh của bạn</p>
            </div>

            <div className="space-y-4">
              {businessTypes.map(type => (
                <button
                  key={type.id}
                  onClick={() => handleInputChange('businessType', type.id)}
                  className={`w-full p-6 rounded-2xl border-2 transition-all duration-200 hover:scale-[1.02] ${
                    formData.businessType === type.id
                      ? 'border-red-500 bg-red-50 shadow-lg shadow-red-500/10'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                  }`}
                >
                  <div className="flex items-center gap-5">
                    <div className={`p-4 rounded-xl bg-gradient-to-br ${type.color} shadow-lg`}>
                      <type.icon size={28} className="text-white" />
                    </div>
                    <div className="flex-1 text-left">
                      <h4 className="font-bold text-gray-900 text-lg">{type.title}</h4>
                      <p className="text-gray-600">{type.subtitle}</p>
                    </div>
                    <div
                      className={`w-6 h-6 rounded-full border-2 transition-all ${
                        formData.businessType === type.id
                          ? 'border-red-500 bg-red-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {formData.businessType === type.id && (
                        <Check size={14} className="text-white m-0.5" />
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Thông Tin Công Ty</h3>
              <p className="text-gray-600">Vui lòng nhập thông tin chi tiết về công ty</p>
            </div>

            <div className="space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Tên Công Ty <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={e => handleInputChange('companyName', e.target.value)}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  placeholder="Nhập tên công ty"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Địa Chỉ <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={formData.address}
                  onChange={e => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all resize-none"
                  placeholder="Nhập địa chỉ công ty"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">Website</label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={e => handleInputChange('website', e.target.value)}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  placeholder="https://www.company.com"
                />
              </div>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Giấy Tờ Pháp Lý</h3>
              <p className="text-gray-600">Nhập thông tin giấy phép và mã số thuế</p>
            </div>

            <div className="space-y-5">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Giấy Phép Đăng Ký <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.businessLicense}
                  onChange={e => handleInputChange('businessLicense', e.target.value)}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  placeholder="Nhập số giấy phép đăng ký kinh doanh"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Mã Số Thuế <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.taxCode}
                  onChange={e => handleInputChange('taxCode', e.target.value)}
                  className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  placeholder="Nhập mã số thuế"
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-xl p-5">
                <div className="flex items-start gap-4">
                  <FileText className="text-blue-500 mt-1" size={24} />
                  <div>
                    <h4 className="text-gray-900 font-semibold mb-2">Lưu ý quan trọng</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Vui lòng đảm bảo thông tin giấy phép và mã số thuế chính xác. Thông tin này sẽ
                      được sử dụng cho các giao dịch và báo cáo thuế.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Chọn Logo Công Ty</h3>
              <p className="text-gray-600">Tải lên logo để hoàn tất thiết lập</p>
            </div>

            <div className="space-y-5">
              <div className="border-2 border-dashed border-gray-300 rounded-2xl p-10 text-center hover:border-red-500 transition-colors bg-gray-50 hover:bg-red-50">
                <input
                  type="file"
                  id="logo-upload"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <label htmlFor="logo-upload" className="cursor-pointer">
                  {formData.logo ? (
                    <div className="space-y-4">
                      <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
                        <Check size={40} className="text-white" />
                      </div>
                      <div>
                        <p className="text-green-600 font-semibold text-lg">{formData.logo.name}</p>
                        <p className="text-gray-600">Logo đã được tải lên thành công</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto">
                        <Upload size={40} className="text-gray-500" />
                      </div>
                      <div>
                        <p className="text-gray-900 font-semibold text-lg">Tải lên logo công ty</p>
                        <p className="text-gray-600">PNG, JPG, SVG tối đa 5MB</p>
                      </div>
                    </div>
                  )}
                </label>
              </div>

              {formData.logo && (
                <div className="bg-green-50 border border-green-200 rounded-xl p-5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                        <Building size={24} className="text-white" />
                      </div>
                      <div>
                        <p className="text-gray-900 font-semibold">Logo đã sẵn sàng</p>
                        <p className="text-gray-600 text-sm">Sẽ được sử dụng trong hệ thống</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setFormData(prev => ({ ...prev, logo: null }))}
                      className="text-gray-400 hover:text-red-500 transition-colors p-2 hover:bg-red-50 rounded-lg"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Mời Thành Viên</h3>
              <p className="text-gray-600">Thêm thành viên vào workspace của bạn</p>
            </div>

            <div className="space-y-5">
              {/* Add Member Form */}
              <div className="bg-gray-50 border border-gray-200 rounded-xl p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Email hoặc Số điện thoại
                    </label>
                    <input
                      type="text"
                      value={newMemberContact}
                      onChange={e => setNewMemberContact(e.target.value)}
                      onKeyPress={e => e.key === 'Enter' && addMember()}
                      className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                      placeholder="<EMAIL> hoặc 0123456789"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Vai trò
                    </label>
                    <select
                      value={newMemberRole}
                      onChange={e => setNewMemberRole(e.target.value)}
                      className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                    >
                      {memberRoles.map(role => (
                        <option key={role.id} value={role.id}>
                          {role.label} - {role.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    onClick={addMember}
                    disabled={!newMemberContact.trim()}
                    className={`w-full flex items-center justify-center gap-3 px-6 py-4 rounded-xl font-semibold transition-all ${
                      newMemberContact.trim()
                        ? 'bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-lg shadow-red-500/25'
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <UserPlus size={20} />
                    Thêm thành viên
                  </button>
                </div>
              </div>

              {/* Members List */}
              {formData.members.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-700">
                    Thành viên đã mời ({formData.members.length})
                  </h4>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {formData.members.map(member => (
                      <div
                        key={member.id}
                        className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                            {member.type === 'email' ? (
                              <Mail size={16} className="text-white" />
                            ) : (
                              <Phone size={16} className="text-white" />
                            )}
                          </div>
                          <div>
                            <p className="text-gray-900 font-medium">{member.contact}</p>
                            <p className="text-gray-600 text-sm">
                              {memberRoles.find(r => r.id === member.role)?.label}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => removeMember(member.id)}
                          className="text-gray-400 hover:text-red-500 transition-colors p-2 hover:bg-red-50 rounded-lg"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Skip Option */}
              <div className="text-center pt-6 border-t border-gray-200">
                <p className="text-gray-600">Bạn có thể bỏ qua bước này và mời thành viên sau</p>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl border border-gray-200 shadow-2xl max-w-2xl w-full mx-auto transform animate-in fade-in-0 zoom-in-95 duration-300">
        {/* Header */}
        <div className="relative p-8 border-b border-gray-100">
          <div className="text-center">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-14 h-14 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25">
                <span className="text-white font-bold text-xl">R</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900">Revoland</h1>
            </div>

            {/* Progress Steps */}
            <div className="flex justify-center gap-3 mb-6">
              {[1, 2, 3, 4, 5].map(step => (
                <div
                  key={step}
                  className={`w-8 h-2 rounded-full transition-all duration-300 ${
                    step <= currentStep
                      ? 'bg-gradient-to-r from-red-500 to-orange-500'
                      : 'bg-gray-200'
                  }`}
                />
              ))}
            </div>
            <p className="text-gray-600">Bước {currentStep} / 5</p>

            {/* Mandatory Notice */}
            <div className="mt-4 bg-amber-50 border border-amber-200 rounded-xl p-4">
              <p className="text-amber-800 text-sm font-medium">
                ⚠️ Vui lòng hoàn thành tất cả các bước để tiếp tục sử dụng hệ thống
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {renderStep()}

          {/* Actions */}
          <div className="flex gap-4 mt-10">
            {currentStep > 1 && (
              <button
                onClick={handleBack}
                className="flex items-center gap-2 px-6 py-3 text-gray-600 hover:text-gray-900 transition-colors font-semibold"
              >
                <ArrowLeft size={18} />
                Quay lại
              </button>
            )}

            <div className="flex-1"></div>

            <button
              onClick={handleNext}
              disabled={!canContinue()}
              className={`flex items-center gap-3 px-8 py-3 rounded-xl font-semibold transition-all ${
                canContinue()
                  ? 'bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-lg shadow-red-500/25 hover:shadow-red-500/40'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {currentStep === 5 ? 'Hoàn tất thiết lập' : 'Tiếp tục'}
              {currentStep < 5 && <ArrowRight size={18} />}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomePopup;
