/**
 * Utility functions for MyRevo page navigation
 */

export type MyRevoTab =
  | 'saved-homes'
  | 'collections'
  | 'saved-searches'
  | 'inbox'
  | 'your-homes'
  | 'account-settings'
  | 'recently-viewed'
  | 'manage-tours'
  | 'appointments'
  | 'tour-planner';

/**
 * Generate a URL to navigate to a specific MyRevo tab
 * @param tab - The tab to navigate to
 * @param baseUrl - Optional base URL, defaults to '/myrevo'
 * @returns The complete URL with tab parameter
 */
export function getMyRevoTabUrl(tab: MyRevoTab, baseUrl: string = '/myrevo'): string {
  const url = new URL(baseUrl, window.location.origin);
  url.searchParams.set('tab', tab);
  return url.pathname + url.search;
}

/**
 * Navigate to a specific MyRevo tab using Next.js router
 * @param tab - The tab to navigate to
 * @param router - Next.js router instance
 * @param baseUrl - Optional base URL, defaults to '/myrevo'
 */
export function navigateToMyRevoTab(
  tab: MyRevoTab,
  router: { push: (url: string) => void },
  baseUrl: string = '/myrevo'
): void {
  const url = getMyRevoTabUrl(tab, baseUrl);
  router.push(url);
}

/**
 * Check if a tab value is valid
 * @param tab - The tab value to check
 * @returns True if the tab is valid
 */
export function isValidMyRevoTab(tab: string): tab is MyRevoTab {
  const validTabs: MyRevoTab[] = [
    'saved-homes',
    'collections',
    'saved-searches',
    'inbox',
    'your-homes',
    'account-settings',
    'recently-viewed',
    'manage-tours',
    'appointments',
    'tour-planner',
  ];
  return validTabs.includes(tab as MyRevoTab);
}
