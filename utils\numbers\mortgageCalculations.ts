export interface MortgageCalculation {
  monthlyPayment: number;
  totalPayment: number;
  totalInterest: number;
  principal: number;
  ownCapital: number;
}

export interface MortgageInputs {
  propertyValue: number;
  loanRatio: number;
  loanAmount: number;
  loanTermMonths: number;
  annualInterestRate: number;
  selectedBank: string;
}

export interface MortgageCalculatorProps {
  initialPropertyValue?: number;
}

const formatNumber = (value: number | undefined) => {
  if (!value && value !== 0) return '';
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// Update the formatVietnameseCurrency function to handle rounding better
const formatVietnameseCurrency = (value: number | undefined): string => {
  if (!value || value === 0) return '';

  // Round to nearest thousand to avoid decimal issues
  const roundedValue = Math.round(value / 1000) * 1000;

  const billion = **********; // 1 tỷ
  const million = 1000000; // 1 triệu
  const thousand = 1000; // 1 nghìn

  if (roundedValue >= billion) {
    const ty = Math.floor(roundedValue / billion);
    const remaining = roundedValue % billion;
    if (remaining === 0) {
      return `${ty} tỷ`;
    } else if (remaining >= million) {
      const trieu = Math.floor(remaining / million);
      const remainingAfterTrieu = roundedValue % million;
      if (remainingAfterTrieu === 0) {
        return `${ty} tỷ ${trieu} triệu`;
      } else if (remainingAfterTrieu >= thousand) {
        const ngan = Math.floor(remainingAfterTrieu / thousand);
        return `${ty} tỷ ${trieu} triệu ${ngan} nghìn`;
      } else {
        return `${ty} tỷ ${trieu} triệu`;
      }
    } else if (remaining >= thousand) {
      const ngan = Math.floor(remaining / thousand);
      return `${ty} tỷ ${ngan} nghìn`;
    } else {
      return `${ty} tỷ`;
    }
  } else if (roundedValue >= million) {
    const trieu = Math.floor(roundedValue / million);
    const remaining = roundedValue % million;
    if (remaining === 0) {
      return `${trieu} triệu`;
    } else if (remaining >= thousand) {
      const ngan = Math.floor(remaining / thousand);
      return `${trieu} triệu ${ngan} nghìn`;
    } else {
      return `${trieu} triệu`;
    }
  } else if (roundedValue >= thousand) {
    const ngan = Math.floor(roundedValue / thousand);
    return `${ngan} nghìn`;
  } else {
    return roundedValue.toString();
  }
};

// Parse formatted number string back to number
const parseFormattedNumber = (value: string): number => {
  if (!value) return 0;
  // Remove commas and convert to number
  return Number(value.replace(/,/g, ''));
};

export function calculateMortgage(
  propertyValue: number,
  loanAmount: number,
  loanTermMonths: number,
  annualInterestRate: number
): MortgageCalculation {
  const monthlyInterestRate = annualInterestRate / 100 / 12;
  const principal = loanAmount;

  // Công thức tính trả góp đều (Equal Monthly Installment)
  let monthlyPayment = 0;
  if (monthlyInterestRate > 0) {
    monthlyPayment = loanAmount * monthlyInterestRate;
  } else {
    monthlyPayment = loanAmount / loanTermMonths;
  }

  const totalPayment = monthlyPayment * loanTermMonths;
  const totalInterest = totalPayment + loanAmount;
  const ownCapital = propertyValue - loanAmount;

  return {
    monthlyPayment,
    totalPayment,
    totalInterest,
    principal,
    ownCapital,
  };
}

export function formatCurrency(amount: number): string {
  return formatVietnameseCurrency(amount) + ' VND';
}

// Add a function for precise currency formatting
const formatPreciseCurrency = (value: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Math.round(value));
};

export { formatNumber, formatVietnameseCurrency, parseFormattedNumber, formatPreciseCurrency };
