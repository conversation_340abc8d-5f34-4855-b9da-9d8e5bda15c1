import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { clause11To14Schema, Clause11To14Input } from '../schemas';

const DEFAULT_CLAUSE_11_TO_14_CONTENT = `Điều 11. Chấm dứt hợp đồng

Hợp đồng này sẽ chấm dứt trong các trường hợp sau:

1. <PERSON> bên đồng ý chấm dứt hợp đồng bằng văn bản. Trong trường hợp này, hai bên sẽ thoả thuận các điều kiện và thời hạn chấm dứt.

2. <PERSON><PERSON><PERSON> mua chậm trễ thanh toán tiền mua nhà quá .......ngày (hoặc tháng) theo thoả thuận tại Điều 2 của hợp đồng này.

3. Bên bán chậm trễ bàn giao nhà ở quá.......ngày (hoặc tháng) theo thời hạn đã thỏa thuận tại Điều 3 của hợp đồng này.

4. Các thoả thuận khác....................................................

Điều 12. Các thỏa thuận khác

(Các thỏa thuận trong hợp đồng này phải phù hợp với quy định của pháp luật và không trái đạo đức xã hội).

1.........................................................

2.........................................................

Điều 13. Giải quyết tranh chấp

Trường hợp các bên có tranh chấp về nội dung của hợp đồng này thì hai bên cùng bàn bạc giải quyết thông qua thương lượng. Trong trường hợp các bên không thương lượng được thì một trong hai bên có quyền yêu cầu Tòa án nhân dân giải quyết theo quy định của pháp luật.

Điều 14. Hiệu lực của hợp đồng

1. Hợp đồng này có hiệu lực kể từ ngày.............(hoặc có hiệu lực kể từ ngày được công chứng chứng nhận hoặc được UBND chứng thực đối với trường hợp pháp luật quy định phải công chứng hoặc chứng thực).

2. Hợp đồng này được lập thành ..... bản và có giá trị pháp lý như nhau, mỗi bên giữ .....bản, .... bản lưu tại cơ quan thuế, .... bản lưu tại cơ quan công chứng hoặc chứng thực (nếu có) và .....bản lưu tại cơ quan có thẩm quyền cấp giấy chứng nhận quyền sở hữu đối với nhà ở.

(Trong trường hợp Bên bán, Bên mua là người nước ngoài hoặc người Việt Nam định cư ở nước ngoài thì có thể thoả thuận lập thêm hợp đồng bằng Tiếng Anh, các bản Hợp đồng bằng tiếng Anh và tiếng Việt có giá trị pháp lý như nhau).`;

interface Clause11To14FormProps {
  data: Clause11To14Input;
  onNext: (data: Clause11To14Input) => void;
  onBack?: () => void;
}

const Clause11To14Form: React.FC<Clause11To14FormProps> = ({ data, onNext, onBack }) => {
  const form = useForm<Clause11To14Input>({
    resolver: zodResolver(clause11To14Schema),
    defaultValues: {
      Clause11To14: data.Clause11To14 || DEFAULT_CLAUSE_11_TO_14_CONTENT,
    },
  });

  // Đồng bộ dữ liệu form với prop data khi data thay đổi
  React.useEffect(() => {
    form.reset({
      Clause11To14: data.Clause11To14 || DEFAULT_CLAUSE_11_TO_14_CONTENT,
    });
  }, [data, form]);

  const onSubmit = (formData: Clause11To14Input) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Điều khoản 11-14</CardTitle>
        <CardDescription>
          Hiệu lực hợp đồng, điều khoản cuối cùng và thông tin bổ sung
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="Clause11To14"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung điều khoản 11-14 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung điều khoản 11-14: Hiệu lực hợp đồng, điều khoản cuối cùng..."
                      rows={12}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-2">Gợi ý nội dung:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Điều 11: Chấm dứt hợp đồng</li>
                    <li>Điều 12: Các thỏa thuận khác</li>
                    <li>Điều 13: Giải quyết tranh chấp</li>
                    <li>Điều 14: Hiệu lực của hợp đồng</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Quay lại
                </Button>
              )}
              <Button type="submit" className="ml-auto">
                Tiếp tục
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
export { Clause11To14Form };
