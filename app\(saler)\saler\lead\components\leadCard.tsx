'use client';

import { useState } from 'react';
import { MapPin, Phone, Mail, User, Calendar, Eye } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Lead, LeadScore } from '@/lib/api/services/fetchLead';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';

interface LeadCardProps {
  lead: Lead;
  status?: LeadScore;
  isDragging?: boolean;
  isLoading?: boolean;
}

export function LeadCard({ lead, isDragging, isLoading }: LeadCardProps) {
  const [open, setOpen] = useState(false);

  const initials = lead.name
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  const getPriorityConfig = (score: LeadScore) => {
    switch (score) {
      case LeadScore.Hot:
        return { label: 'Ưu tiên cao', color: 'bg-red-100 text-red-700' };
      case LeadScore.Warm:
        return { label: 'Ưu tiên trung bình', color: 'bg-amber-100 text-amber-700' };
      case LeadScore.Cold:
        return { label: 'Ưu tiên thấp', color: 'bg-blue-100 text-blue-700' };
      default:
        return { label: 'Không xác định', color: 'bg-gray-100 text-gray-700' };
    }
  };

  const getSourceConfig = (source: string) => {
    switch (source) {
      case 'Web':
        return { label: 'Revoland', color: 'bg-pink-50 text-pink-700 border-pink-200' };
      case 'Admin':
        return { label: 'Admin', color: 'bg-purple-50 text-purple-700 border-purple-200' };
      default:
        return { label: 'Đặt lịch', color: 'bg-green-50 text-green-700 border-green-200' };
    }
  };

  const priorityConfig = getPriorityConfig(lead.score);
  const sourceConfig = getSourceConfig(lead.source || '');

  return (
    <div
      className={cn(
        'group relative bg-white rounded-lg overflow-hidden',
        'border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200',
        isDragging && 'rotate-2 scale-105 shadow-lg',
        isLoading && 'opacity-50 pointer-events-none'
      )}
    >
      <div className="p-4">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 border-2 border-gray-50">
              <AvatarFallback className="bg-gray-100 text-gray-700 font-medium">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{lead.name}</h3>
            </div>
          </div>

          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-50">
                <Eye className="h-4 w-4 text-gray-500" />
              </Button>
            </SheetTrigger>
            <SheetContent className="w-full sm:max-w-xl">
              <SheetHeader>
                <SheetTitle className="text-xl font-bold flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Chi tiết khách hàng
                </SheetTitle>
                <SheetDescription>Thông tin chi tiết về khách hàng</SheetDescription>
              </SheetHeader>

              <div className="space-y-6 mx-4">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Thông tin cơ bản</h3>
                  <div className="grid gap-4 text-sm">
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Họ và tên</p>
                        <p className="font-medium">{lead.name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Email</p>
                        <p className="font-medium">{lead.email || '--'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Số điện thoại</p>
                        <p className="font-medium">{lead.phone || '--'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Additional Information */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Thông tin bổ sung</h3>
                  <div className="grid gap-4 text-sm">
                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Địa chỉ</p>
                        <p className="font-medium">{lead.address || '--'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Nguồn</p>
                        <p className="font-medium">{lead.source || '--'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Assign/Unassign Section */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Phân công sale</h3>

                  {lead.assignedTo?.length ? (
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">Đang phụ trách</p>
                      <div className="grid gap-2">
                        {lead.assignedTo.map(seller => (
                          <div key={seller.id} className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                <AvatarFallback>
                                  {seller.name.slice(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <div className="text-sm">
                                <div className="font-medium leading-none">{seller.name}</div>
                                <div className="text-muted-foreground text-xs">{seller.email}</div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-xs text-muted-foreground">Chưa có người phụ trách</p>
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Badges Section */}
        <div className="flex items-center gap-2 mb-3">
          <Badge variant="outline" className={cn('font-normal', priorityConfig.color)}>
            {priorityConfig.label}
          </Badge>
          <Badge variant="outline" className={cn('font-normal', sourceConfig.color)}>
            {sourceConfig.label}
          </Badge>
        </div>

        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600 truncate">{lead.email || '--'}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600 truncate">{lead.phone || '--'}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
