'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { contractTermsSchema, ContractTermsInput } from '../schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Step3Props {
  data: ContractTermsInput;
  onNext: (data: ContractTermsInput) => void;
  onBack: () => void;
}

const Step3: React.FC<Step3Props> = ({ data, onNext, onBack }) => {
  // Helper to format number as currency (1.000.000)
  const formatCurrency = (value: number | string) => {
    if (value === null || value === undefined || value === '') return '';
    const num = typeof value === 'number' ? value : Number((value + '').replace(/\D/g, ''));
    if (isNaN(num)) return '';
    return num.toLocaleString('vi-VN');
  };

  // List of number fields to format
  const numberFields = [
    'area',
    'landArea',
    'sharedArea',
    'privateArea',
    'monthlyRent',
    'deposit',
    'paymentDay',
  ];

  // State for formatted values
  const [formatted, setFormatted] = useState<{ [key: string]: string }>({});

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isValid },
  } = useForm<ContractTermsInput>({
    resolver: zodResolver(contractTermsSchema),
    defaultValues: data,
    mode: 'onChange',
  });

  // Handler for number input formatting
  const handleNumberChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value.replace(/\D/g, '');
    const num = raw ? parseInt(raw, 10) : '';
    setValue(field as keyof ContractTermsInput, num === '' ? undefined : Number(num), {
      shouldValidate: true,
    });
    setFormatted(prev => ({ ...prev, [field]: formatCurrency(raw) }));
  };

  // Initialize formatted values from default data
  React.useEffect(() => {
    const initial: { [key: string]: string } = {};
    numberFields.forEach(field => {
      const val = (data as Record<string, string | number | undefined>)[field];
      if (val !== undefined && val !== null && val !== '') {
        initial[field] = formatCurrency(val as string | number);
      }
    });
    setFormatted(initial);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const onSubmit = (formData: ContractTermsInput) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Bước 3: Điều khoản hợp đồng
        </CardTitle>
        <CardDescription className="text-center">
          Vui lòng nhập thông tin về điều khoản thuê
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="propertyAddress" className="text-sm font-medium">
              Địa chỉ căn hộ cho thuê *
            </Label>
            <Input
              id="propertyAddress"
              {...register('propertyAddress')}
              placeholder="Nhập địa chỉ căn hộ đầy đủ"
              className={errors.propertyAddress ? 'border-red-500' : ''}
            />
            {errors.propertyAddress && (
              <p className="text-sm text-red-500">{errors.propertyAddress.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="apartmentNumber" className="text-sm font-medium">
                Số căn hộ
              </Label>
              <Input
                id="apartmentNumber"
                {...register('apartmentNumber')}
                placeholder="Ví dụ: A1203"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="floor" className="text-sm font-medium">
                Tầng
              </Label>
              <Input id="floor" {...register('floor')} placeholder="Ví dụ: 12" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="area" className="text-sm font-medium">
                Diện tích căn hộ (m²)
              </Label>
              <Input
                id="area"
                inputMode="numeric"
                value={formatted.area ?? ''}
                onChange={handleNumberChange('area')}
                placeholder="Ví dụ: 80"
                className={errors.area ? 'border-red-500' : ''}
              />
              {errors.area && <p className="text-sm text-red-500">{errors.area.message}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="landArea" className="text-sm font-medium">
                Diện tích đất gắn liền (m²)
              </Label>
              <Input
                id="landArea"
                inputMode="numeric"
                value={formatted.landArea ?? ''}
                onChange={handleNumberChange('landArea')}
                placeholder="Ví dụ: 100"
                className={errors.landArea ? 'border-red-500' : ''}
              />
              {errors.landArea && <p className="text-sm text-red-500">{errors.landArea.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="sharedArea" className="text-sm font-medium">
                Sử dụng chung (m²)
              </Label>
              <Input
                id="sharedArea"
                inputMode="numeric"
                value={formatted.sharedArea ?? ''}
                onChange={handleNumberChange('sharedArea')}
                placeholder="Ví dụ: 60"
                className={errors.sharedArea ? 'border-red-500' : ''}
              />
              {errors.sharedArea && (
                <p className="text-sm text-red-500">{errors.sharedArea.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="privateArea" className="text-sm font-medium">
                Sử dụng riêng (m²)
              </Label>
              <Input
                id="privateArea"
                inputMode="numeric"
                value={formatted.privateArea ?? ''}
                onChange={handleNumberChange('privateArea')}
                placeholder="Ví dụ: 40"
                className={errors.privateArea ? 'border-red-500' : ''}
              />
              {errors.privateArea && (
                <p className="text-sm text-red-500">{errors.privateArea.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="propertyType" className="text-sm font-medium">
                Loại căn hộ
              </Label>
              <Input
                id="propertyType"
                {...register('propertyType')}
                placeholder="Ví dụ: Căn hộ chung cư"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="propertyPurpose" className="text-sm font-medium">
                Mục đích sử dụng
              </Label>
              <Input
                id="propertyPurpose"
                {...register('propertyPurpose')}
                placeholder="Ví dụ: Để ở"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="facilities" className="text-sm font-medium">
              Trang thiết bị kèm theo
            </Label>
            <Input
              id="facilities"
              {...register('facilities')}
              placeholder="Mô tả trang thiết bị gắn liền với căn hộ (không bắt buộc)"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ownershipOrigin" className="text-sm font-medium">
                Nguồn gốc sở hữu
              </Label>
              <Input
                id="ownershipOrigin"
                {...register('ownershipOrigin')}
                placeholder="Ví dụ: Mua bán, thừa kế, tặng cho..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ownershipRestrictions" className="text-sm font-medium">
                Hạn chế về quyền sở hữu căn hộ (nếu có)
              </Label>
              <Input
                id="ownershipRestrictions"
                {...register('ownershipRestrictions')}
                placeholder="Mô tả các hạn chế (nếu có)"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="monthlyRent" className="text-sm font-medium">
                Tiền thuê hàng tháng (VNĐ) *
              </Label>
              <Input
                id="monthlyRent"
                inputMode="numeric"
                value={formatted.monthlyRent ?? ''}
                onChange={handleNumberChange('monthlyRent')}
                placeholder="Ví dụ: 10.000.000"
                className={errors.monthlyRent ? 'border-red-500' : ''}
              />
              {errors.monthlyRent && (
                <p className="text-sm text-red-500">{errors.monthlyRent.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="deposit" className="text-sm font-medium">
                Tiền đặt cọc (VNĐ) *
              </Label>
              <Input
                id="deposit"
                inputMode="numeric"
                value={formatted.deposit ?? ''}
                onChange={handleNumberChange('deposit')}
                placeholder="Ví dụ: 20.000.000"
                className={errors.deposit ? 'border-red-500' : ''}
              />
              {errors.deposit && <p className="text-sm text-red-500">{errors.deposit.message}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="paymentMethod" className="text-sm font-medium">
                Điều khoảng thanh toán *
              </Label>
              <select
                id="paymentMethod"
                {...register('paymentMethod')}
                className="flex h-9 w-full rounded-md border border-input  px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Chọn điều khoảng thanh toán</option>
                <option value="Monthly">Tháng</option>
                <option value="Quarterly">Quý</option>
                <option value="Annually">Hằng năm</option>
                <option value="Biannually">Hai năm một lần</option>
              </select>
              {errors.paymentMethod && (
                <p className="text-sm text-red-500">{errors.paymentMethod.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentDay" className="text-sm font-medium">
                Ngày thanh toán hàng tháng *
              </Label>
              <Input
                id="paymentDay"
                inputMode="numeric"
                min="1"
                max="31"
                value={formatted.paymentDay ?? ''}
                onChange={handleNumberChange('paymentDay')}
                placeholder="Ví dụ: 5"
                className={errors.paymentDay ? 'border-red-500' : ''}
              />
              {errors.paymentDay && (
                <p className="text-sm text-red-500">{errors.paymentDay.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-between">
            <Button type="button" onClick={onBack} variant="outline" className="px-8 py-2">
              Quay lại
            </Button>
            <Button
              type="submit"
              disabled={!isValid}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              Tiếp theo
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default Step3;
