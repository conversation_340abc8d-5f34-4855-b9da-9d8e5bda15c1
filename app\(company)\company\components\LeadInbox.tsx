import React, { useState } from 'react';
import {
  ArrowLeft,
  Inbox,
  Search,
  MoreVertical,
  Phone,
  Mail,
  Calendar,
  User,
  MapPin,
  Building2,
  Clock,
  Edit,
  UserPlus,
  Send,
  Archive,
  Flag,
  XCircle,
  DollarSign,
  Users,
  RefreshCw,
  Download,
  Plus,
  Zap,
  Globe,
  Facebook,
  MessageCircle,
} from 'lucide-react';

interface LeadInboxProps {
  onBack: () => void;
}

interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  property: {
    name: string;
    type: string;
    location: string;
    price: number;
  };
  status: 'new' | 'contacted' | 'qualified' | 'proposal' | 'negotiation' | 'closed' | 'lost';
  priority: 'high' | 'medium' | 'low';
  source: 'website' | 'facebook' | 'zalo' | 'referral' | 'phone' | 'email' | 'walk-in';
  assignedAgent: string;
  lastContact: string;
  nextFollowUp: string;
  notes: string;
  tags: string[];
  score: number;
  createdAt: string;
  updatedAt: string;
  interactions: number;
  value: number;
}

const LeadInbox: React.FC<LeadInboxProps> = ({ onBack }) => {
  const [leads] = useState<Lead[]>([
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '0123456789',
      property: {
        name: 'Căn hộ Vinhomes Central Park',
        type: 'Căn hộ',
        location: 'Quận 1, TP.HCM',
        price: 5200000000,
      },
      status: 'new',
      priority: 'high',
      source: 'website',
      assignedAgent: 'Nguyễn Văn A',
      lastContact: '2 giờ trước',
      nextFollowUp: 'Hôm nay, 15:00',
      notes: 'Khách hàng quan tâm căn 2PN, tầng cao, view sông',
      tags: ['VIP', 'Hot Lead', 'Cash Buyer'],
      score: 95,
      createdAt: '2024-01-20',
      updatedAt: '2024-01-20',
      interactions: 3,
      value: 5200000000,
    },
    {
      id: '2',
      name: 'Lê Thị Mai',
      email: '<EMAIL>',
      phone: '0987654321',
      property: {
        name: 'Biệt thự Phú Mỹ Hưng',
        type: 'Biệt thự',
        location: 'Quận 7, TP.HCM',
        price: 12000000000,
      },
      status: 'contacted',
      priority: 'high',
      source: 'facebook',
      assignedAgent: 'Trần Thị B',
      lastContact: '1 ngày trước',
      nextFollowUp: 'Ngày mai, 10:00',
      notes: 'Đã gọi điện, khách quan tâm xem nhà cuối tuần',
      tags: ['Luxury', 'Family'],
      score: 88,
      createdAt: '2024-01-19',
      updatedAt: '2024-01-20',
      interactions: 5,
      value: 12000000000,
    },
    {
      id: '3',
      name: 'Trần Văn Nam',
      email: '<EMAIL>',
      phone: '0369852147',
      property: {
        name: 'Chung cư The Manor',
        type: 'Chung cư',
        location: 'Quận 5, TP.HCM',
        price: 3800000000,
      },
      status: 'qualified',
      priority: 'medium',
      source: 'referral',
      assignedAgent: 'Lê Văn C',
      lastContact: '3 ngày trước',
      nextFollowUp: 'Thứ 2, 14:00',
      notes: 'Khách được giới thiệu từ anh Minh, đã xác nhận nhu cầu',
      tags: ['Referral', 'First Time Buyer'],
      score: 75,
      createdAt: '2024-01-17',
      updatedAt: '2024-01-18',
      interactions: 8,
      value: 3800000000,
    },
    {
      id: '4',
      name: 'Phạm Thị Lan',
      email: '<EMAIL>',
      phone: '0258147963',
      property: {
        name: 'Nhà phố Thảo Điền',
        type: 'Nhà phố',
        location: 'Quận 2, TP.HCM',
        price: 8500000000,
      },
      status: 'proposal',
      priority: 'high',
      source: 'zalo',
      assignedAgent: 'Phạm Thị D',
      lastContact: '30 phút trước',
      nextFollowUp: 'Hôm nay, 16:30',
      notes: 'Đã gửi proposal, chờ phản hồi từ khách',
      tags: ['Hot Lead', 'Investment'],
      score: 92,
      createdAt: '2024-01-15',
      updatedAt: '2024-01-20',
      interactions: 12,
      value: 8500000000,
    },
    {
      id: '5',
      name: 'Hoàng Văn Đức',
      email: '<EMAIL>',
      phone: '0147258369',
      property: {
        name: 'Căn hộ Landmark 81',
        type: 'Căn hộ',
        location: 'Quận 1, TP.HCM',
        price: 15000000000,
      },
      status: 'negotiation',
      priority: 'high',
      source: 'phone',
      assignedAgent: 'Nguyễn Văn A',
      lastContact: '1 giờ trước',
      nextFollowUp: 'Hôm nay, 17:00',
      notes: 'Đang thương lượng giá, khách muốn giảm 5%',
      tags: ['VIP', 'Luxury', 'Negotiating'],
      score: 98,
      createdAt: '2024-01-10',
      updatedAt: '2024-01-20',
      interactions: 25,
      value: 15000000000,
    },
    {
      id: '6',
      name: 'Vũ Thị Hương',
      email: '<EMAIL>',
      phone: '0321654987',
      property: {
        name: 'Shophouse Saigon South',
        type: 'Shophouse',
        location: 'Quận 7, TP.HCM',
        price: 18000000000,
      },
      status: 'lost',
      priority: 'low',
      source: 'email',
      assignedAgent: 'Lê Văn C',
      lastContact: '1 tuần trước',
      nextFollowUp: '',
      notes: 'Khách đã mua từ đối thủ cạnh tranh',
      tags: ['Lost', 'Competitor'],
      score: 45,
      createdAt: '2024-01-05',
      updatedAt: '2024-01-13',
      interactions: 15,
      value: 18000000000,
    },
  ]);

  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSource, setFilterSource] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);

  const statusConfig = {
    new: { label: 'Mới', color: 'bg-blue-100 text-blue-800', dot: 'bg-blue-500' },
    contacted: {
      label: 'Đã liên hệ',
      color: 'bg-yellow-100 text-yellow-800',
      dot: 'bg-yellow-500',
    },
    qualified: { label: 'Đủ điều kiện', color: 'bg-green-100 text-green-800', dot: 'bg-green-500' },
    proposal: { label: 'Đề xuất', color: 'bg-purple-100 text-purple-800', dot: 'bg-purple-500' },
    negotiation: {
      label: 'Thương lượng',
      color: 'bg-orange-100 text-orange-800',
      dot: 'bg-orange-500',
    },
    closed: {
      label: 'Thành công',
      color: 'bg-emerald-100 text-emerald-800',
      dot: 'bg-emerald-500',
    },
    lost: { label: 'Thất bại', color: 'bg-red-100 text-red-800', dot: 'bg-red-500' },
  };

  const priorityConfig = {
    high: { label: 'Cao', color: 'bg-red-100 text-red-800', icon: '🔥' },
    medium: { label: 'Trung bình', color: 'bg-yellow-100 text-yellow-800', icon: '⚡' },
    low: { label: 'Thấp', color: 'bg-gray-100 text-gray-800', icon: '📋' },
  };

  const sourceConfig = {
    website: { label: 'Website', icon: Globe, color: 'text-blue-600 bg-blue-100' },
    facebook: { label: 'Facebook', icon: Facebook, color: 'text-blue-600 bg-blue-100' },
    zalo: { label: 'Zalo', icon: MessageCircle, color: 'text-blue-600 bg-blue-100' },
    referral: { label: 'Giới thiệu', icon: Users, color: 'text-green-600 bg-green-100' },
    phone: { label: 'Điện thoại', icon: Phone, color: 'text-purple-600 bg-purple-100' },
    email: { label: 'Email', icon: Mail, color: 'text-orange-600 bg-orange-100' },
    'walk-in': { label: 'Khách vãng lai', icon: User, color: 'text-gray-600 bg-gray-100' },
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(amount);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch =
      lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.property.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || lead.status === filterStatus;
    const matchesSource = filterSource === 'all' || lead.source === filterSource;
    const matchesPriority = filterPriority === 'all' || lead.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesSource && matchesPriority;
  });

  const sortedLeads = [...filteredLeads].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'score':
        return b.score - a.score;
      case 'value':
        return b.value - a.value;
      case 'name':
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  const handleSelectLead = (leadId: string) => {
    setSelectedLeads(prev =>
      prev.includes(leadId) ? prev.filter(id => id !== leadId) : [...prev, leadId]
    );
  };

  const handleSelectAll = () => {
    setSelectedLeads(
      selectedLeads.length === sortedLeads.length ? [] : sortedLeads.map(lead => lead.id)
    );
  };

  const getLeadStats = () => {
    const total = leads.length;
    const newLeads = leads.filter(l => l.status === 'new').length;
    const hotLeads = leads.filter(l => l.priority === 'high').length;
    const totalValue = leads.reduce((sum, lead) => sum + lead.value, 0);

    return { total, newLeads, hotLeads, totalValue };
  };

  const stats = getLeadStats();

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Lead Inbox</h1>
              <p className="text-gray-600">Quản lý tất cả leads từ mọi nguồn</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-medium shadow-lg shadow-red-500/25 transition-all">
              <Plus size={16} />
              <span>Thêm Lead</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <Download size={16} />
              <span className="text-sm font-medium">Xuất Excel</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <RefreshCw size={16} />
              <span className="text-sm font-medium">Làm mới</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Inbox size={24} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                  <p className="text-gray-600 text-sm">Tổng Leads</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <Zap size={24} className="text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.newLeads}</p>
                  <p className="text-gray-600 text-sm">Leads Mới</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                  <Flag size={24} className="text-red-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{stats.hotLeads}</p>
                  <p className="text-gray-600 text-sm">Leads Nóng</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <DollarSign size={24} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(stats.totalValue)}
                  </p>
                  <p className="text-gray-600 text-sm">Tổng Giá trị</p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                      placeholder="Tìm kiếm theo tên, email, BDS..."
                    />
                  </div>
                </div>

                {/* Filters */}
                <div className="flex gap-3">
                  <select
                    value={filterStatus}
                    onChange={e => setFilterStatus(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="all">Tất cả trạng thái</option>
                    <option value="new">Mới</option>
                    <option value="contacted">Đã liên hệ</option>
                    <option value="qualified">Đủ điều kiện</option>
                    <option value="proposal">Đề xuất</option>
                    <option value="negotiation">Thương lượng</option>
                    <option value="closed">Thành công</option>
                    <option value="lost">Thất bại</option>
                  </select>

                  <select
                    value={filterSource}
                    onChange={e => setFilterSource(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="all">Tất cả nguồn</option>
                    <option value="website">Website</option>
                    <option value="facebook">Facebook</option>
                    <option value="zalo">Zalo</option>
                    <option value="referral">Giới thiệu</option>
                    <option value="phone">Điện thoại</option>
                    <option value="email">Email</option>
                    <option value="walk-in">Khách vãng lai</option>
                  </select>

                  <select
                    value={filterPriority}
                    onChange={e => setFilterPriority(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="all">Tất cả độ ưu tiên</option>
                    <option value="high">Cao</option>
                    <option value="medium">Trung bình</option>
                    <option value="low">Thấp</option>
                  </select>

                  <select
                    value={sortBy}
                    onChange={e => setSortBy(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="newest">Mới nhất</option>
                    <option value="oldest">Cũ nhất</option>
                    <option value="score">Điểm cao nhất</option>
                    <option value="value">Giá trị cao nhất</option>
                    <option value="name">Tên A-Z</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedLeads.length > 0 && (
              <div className="p-4 bg-blue-50 border-b border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="text-blue-800 font-medium">
                      Đã chọn {selectedLeads.length} leads
                    </span>
                    <div className="flex items-center gap-2">
                      <button className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-all">
                        <UserPlus size={14} />
                        Phân công
                      </button>
                      <button className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-all">
                        <Send size={14} />
                        Gửi email
                      </button>
                      <button className="flex items-center gap-2 px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-all">
                        <Archive size={14} />
                        Lưu trữ
                      </button>
                    </div>
                  </div>
                  <button
                    onClick={() => setSelectedLeads([])}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Bỏ chọn
                  </button>
                </div>
              </div>
            )}

            {/* Table Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedLeads.length === sortedLeads.length && sortedLeads.length > 0}
                    onChange={handleSelectAll}
                    className="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                  />
                </label>
                <div className="flex-1 grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                  <div className="col-span-3">Khách hàng</div>
                  <div className="col-span-3">Bất động sản</div>
                  <div className="col-span-2">Trạng thái</div>
                  <div className="col-span-2">Agent</div>
                  <div className="col-span-1">Điểm</div>
                  <div className="col-span-1">Thao tác</div>
                </div>
              </div>
            </div>

            {/* Leads List */}
            <div className="divide-y divide-gray-100">
              {sortedLeads.length === 0 ? (
                <div className="p-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Inbox size={32} className="text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Không tìm thấy leads</h3>
                  <p className="text-gray-600">Thử thay đổi bộ lọc hoặc thêm leads mới</p>
                </div>
              ) : (
                sortedLeads.map(lead => {
                  const statusInfo = statusConfig[lead.status];
                  const priorityInfo = priorityConfig[lead.priority];
                  const sourceInfo = sourceConfig[lead.source];
                  const SourceIcon = sourceInfo.icon;

                  return (
                    <div
                      key={lead.id}
                      className={`p-6 hover:bg-gray-50 transition-colors cursor-pointer ${
                        selectedLeads.includes(lead.id)
                          ? 'bg-blue-50 border-l-4 border-blue-500'
                          : ''
                      }`}
                      onClick={() => setSelectedLead(lead)}
                    >
                      <div className="flex items-center gap-4">
                        <label className="flex items-center" onClick={e => e.stopPropagation()}>
                          <input
                            type="checkbox"
                            checked={selectedLeads.includes(lead.id)}
                            onChange={() => handleSelectLead(lead.id)}
                            className="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
                          />
                        </label>

                        <div className="flex-1 grid grid-cols-12 gap-4">
                          {/* Customer Info */}
                          <div className="col-span-3">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold text-sm flex-shrink-0">
                                {lead.name.charAt(0)}
                              </div>
                              <div className="min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-semibold text-gray-900 truncate">
                                    {lead.name}
                                  </h4>
                                  <span
                                    className={`px-2 py-0.5 rounded-full text-xs font-medium ${priorityInfo.color}`}
                                  >
                                    {priorityInfo.icon}
                                  </span>
                                </div>
                                <div className="flex items-center gap-3 text-sm text-gray-600">
                                  <div className="flex items-center gap-1">
                                    <Mail size={12} />
                                    <span className="truncate">{lead.email}</span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1 text-sm text-gray-600 mt-1">
                                  <Phone size={12} />
                                  <span>{lead.phone}</span>
                                </div>
                                <div className="flex items-center gap-2 mt-1">
                                  <div
                                    className={`flex items-center gap-1 px-2 py-0.5 rounded-lg ${sourceInfo.color}`}
                                  >
                                    <SourceIcon size={12} />
                                    <span className="text-xs font-medium">{sourceInfo.label}</span>
                                  </div>
                                  {lead.tags.slice(0, 2).map((tag, index) => (
                                    <span
                                      key={index}
                                      className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Property Info */}
                          <div className="col-span-3">
                            <div className="space-y-1">
                              <h4 className="font-semibold text-gray-900 truncate">
                                {lead.property.name}
                              </h4>
                              <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Building2 size={12} />
                                <span>{lead.property.type}</span>
                              </div>
                              <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin size={12} />
                                <span className="truncate">{lead.property.location}</span>
                              </div>
                              <p className="text-sm font-semibold text-gray-900">
                                {formatCurrency(lead.property.price)}
                              </p>
                            </div>
                          </div>

                          {/* Status */}
                          <div className="col-span-2">
                            <div className="space-y-2">
                              <span
                                className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${statusInfo.color}`}
                              >
                                <div className={`w-2 h-2 ${statusInfo.dot} rounded-full`}></div>
                                {statusInfo.label}
                              </span>
                              <div className="text-xs text-gray-500">
                                <div className="flex items-center gap-1">
                                  <Clock size={10} />
                                  <span>Liên hệ: {lead.lastContact}</span>
                                </div>
                                {lead.nextFollowUp && (
                                  <div className="flex items-center gap-1 mt-1">
                                    <Calendar size={10} />
                                    <span>Tiếp theo: {lead.nextFollowUp}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Agent */}
                          <div className="col-span-2">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                                {lead.assignedAgent
                                  .split(' ')
                                  .map(n => n.charAt(0))
                                  .join('')}
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {lead.assignedAgent}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {lead.interactions} tương tác
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Score */}
                          <div className="col-span-1">
                            <div className="text-center">
                              <div
                                className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${getScoreColor(lead.score)}`}
                              >
                                <span className="text-sm font-bold">{lead.score}</span>
                              </div>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="col-span-1">
                            <div
                              className="flex items-center gap-1"
                              onClick={e => e.stopPropagation()}
                            >
                              <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all">
                                <Phone size={14} />
                              </button>
                              <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all">
                                <Mail size={14} />
                              </button>
                              <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all">
                                <MoreVertical size={14} />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* Lead Detail Modal */}
          {selectedLead && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl border border-gray-200 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {selectedLead.name.charAt(0)}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{selectedLead.name}</h3>
                        <p className="text-gray-600">{selectedLead.property.name}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setSelectedLead(null)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all"
                    >
                      <XCircle size={24} />
                    </button>
                  </div>
                </div>

                <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Contact Info */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          Thông tin liên hệ
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center gap-3">
                            <Mail size={16} className="text-gray-400" />
                            <span className="text-gray-900">{selectedLead.email}</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <Phone size={16} className="text-gray-400" />
                            <span className="text-gray-900">{selectedLead.phone}</span>
                          </div>
                          <div className="flex items-center gap-3">
                            <Building2 size={16} className="text-gray-400" />
                            <span className="text-gray-900">{selectedLead.property.location}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Ghi chú</h4>
                        <p className="text-gray-700 bg-gray-50 p-4 rounded-xl">
                          {selectedLead.notes}
                        </p>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Tags</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedLead.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Property & Status */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">Thông tin BDS</h4>
                        <div className="bg-gray-50 p-4 rounded-xl space-y-3">
                          <h5 className="font-semibold text-gray-900">
                            {selectedLead.property.name}
                          </h5>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Building2 size={14} />
                            <span>{selectedLead.property.type}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <MapPin size={14} />
                            <span>{selectedLead.property.location}</span>
                          </div>
                          <p className="text-lg font-bold text-gray-900">
                            {formatCurrency(selectedLead.property.price)}
                          </p>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          Trạng thái & Điểm
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Trạng thái:</span>
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-medium ${statusConfig[selectedLead.status].color}`}
                            >
                              {statusConfig[selectedLead.status].label}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Độ ưu tiên:</span>
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-medium ${priorityConfig[selectedLead.priority].color}`}
                            >
                              {priorityConfig[selectedLead.priority].label}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Điểm lead:</span>
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-bold ${getScoreColor(selectedLead.score)}`}
                            >
                              {selectedLead.score}/100
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Nguồn:</span>
                            <div
                              className={`flex items-center gap-1 px-2 py-1 rounded-lg ${sourceConfig[selectedLead.source].color}`}
                            >
                              {(() => {
                                const SourceIcon = sourceConfig[selectedLead.source].icon;
                                return <SourceIcon size={14} />;
                              })()}
                              <span className="text-sm font-medium">
                                {sourceConfig[selectedLead.source].label}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex gap-4">
                      <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all">
                        <Phone size={16} />
                        Gọi điện
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all">
                        <Mail size={16} />
                        Gửi email
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all">
                        <Calendar size={16} />
                        Đặt lịch hẹn
                      </button>
                      <button className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-all">
                        <Edit size={16} />
                        Chỉnh sửa
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LeadInbox;
