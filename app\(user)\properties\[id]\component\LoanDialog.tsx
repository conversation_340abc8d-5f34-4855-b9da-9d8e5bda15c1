import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Property } from '@/lib/api/services/fetchProperty';
import { HandCoins } from 'lucide-react';
import { formatPrice } from '@/utils/numbers/formatCurrency';
import MortgageCalculator from './MortgageCalculator';

interface LoanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  property: Property;
}

export default function LoanDialog({ isOpen, onClose, property }: LoanDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-scroll scrollbar-hide  ">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HandCoins className="h-5 w-5" />
            T<PERSON>h Toán <PERSON> Vay
          </DialogTitle>
          <DialogDescription>
            T<PERSON>h toán khoản vay thế chấp cho bất động sản này với giá{' '}
            {formatPrice(property.priceDetails.salePrice || 0, property.transactionType)} VND
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          <MortgageCalculator
            initialPropertyValue={property.priceDetails.salePrice || 0}
            onDialog={false}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
