// lib/utils/tour-utils.ts

/**
 * Returns display text and a Tailwind CSS color class for a given tour status.
 * @param status - The tour status string (e.g., 'Pending', 'Approved', 'Completed').
 * @returns An object containing `text` (display string) and `colorClass` (Tailwind CSS class).
 */
// utils/tourStatus.ts
export function getTourStatusDisplay(status: string) {
  switch (status) {
    case 'Pending':
      return { text: 'Đang chờ xử lý', colorClass: 'text-yellow-600 bg-yellow-100' };
    case 'Approved':
      return { text: 'Đã phê duyệt', colorClass: 'text-green-600 bg-green-100' };
    case 'Completed':
      return { text: 'Đã hoàn thành', colorClass: 'text-blue-600 bg-blue-100' };
    case 'Cancelled':
      return { text: 'Đã hủy', colorClass: 'text-gray-600 bg-gray-100' };
    case 'Rejected':
      return { text: 'Bị từ chối', colorClass: 'text-red-600 bg-red-100' };
    default:
      return { text: status, colorClass: 'text-gray-600 bg-gray-100' };
  }
}

export function getButtonStatusColors(status: string) {
  switch (status) {
    case 'Pending':
      return { bgColorClass: 'bg-yellow-500 hover:bg-yellow-600', textColorClass: 'text-white' };
    case 'Approved':
      return { bgColorClass: 'bg-green-200', textColorClass: 'text-green-700' }; // Example: light green for approved
    case 'Completed':
      return { bgColorClass: 'bg-blue-200', textColorClass: 'text-blue-700' }; // Example: light blue for completed
    case 'Cancelled':
      return { bgColorClass: 'bg-gray-300', textColorClass: 'text-gray-700' }; // Example: muted gray for cancelled
    case 'Rejected':
      return { bgColorClass: 'bg-red-200', textColorClass: 'text-red-700' }; // Example: light red for rejected
    default:
      return { bgColorClass: 'bg-gray-500 hover:bg-gray-600', textColorClass: 'text-white' };
  }
}

export function formatTourTime(dateString: string | number | Date) {
  const date = new Date(dateString);
  return date.toISOString().substring(11, 16); // Extract HH:mm
}
