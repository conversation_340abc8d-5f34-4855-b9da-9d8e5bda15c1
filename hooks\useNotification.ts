'use client';

import { useNotificationContext } from '@/components/providers/notificationProvider';
import { useEffect } from 'react';

export function useNotifications(token?: string) {
  const context = useNotificationContext();

  // Auto-connect when token is available
  useEffect(() => {
    if (token && !context.isConnected) {
      context.connect(token).catch(console.error);
    }
  }, [token, context.isConnected, context.connect]);

  // Auto-disconnect when token is removed
  useEffect(() => {
    if (!token && context.isConnected) {
      context.disconnect().catch(console.error);
    }
  }, [token, context.isConnected, context.disconnect]);

  return context;
}

// Hook for manual connection management
export function useNotificationConnection() {
  return useNotificationContext();
}
