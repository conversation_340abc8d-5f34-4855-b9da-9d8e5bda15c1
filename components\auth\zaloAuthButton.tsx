import { FC, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ZaloAuthButtonProps {
  mode: 'login' | 'register';
  onError: (error: string | null) => void;
  className?: string;
}

export const ZaloAuthButton: FC<ZaloAuthButtonProps> = ({ mode, onError }) => {
  const { zaloLogin, zaloLoginLoading, zaloLoginError } = useAuth();

  useEffect(() => {
    if (zaloLoginError) {
      onError(zaloLoginError);

      setTimeout(() => onError(null), 3000);
    }
  }, [zaloLoginError, onError]);

  return (
    <Button
      type="button"
      variant="outline"
      className={`w-full`}
      onClick={zaloLogin}
      disabled={zaloLoginLoading}
    >
      {zaloLoginLoading ? (
        <Loader2 className="size-5 animate-spin" />
      ) : (
        <img
          src="https://upload.wikimedia.org/wikipedia/commons/9/91/Icon_of_Zalo.svg"
          alt="Zalo Icon"
          className="size-5"
        />
      )}
      <span className="flex flex-1 justify-center">
        {zaloLoginLoading
          ? 'Đang xử lý...'
          : mode === 'login'
            ? 'Đăng nhập với Zalo'
            : 'Đăng ký với Zalo'}
      </span>
    </Button>
  );
};
