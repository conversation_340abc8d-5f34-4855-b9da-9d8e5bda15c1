'use client';

import React, { createContext, useState, useCallback, useEffect } from 'react';
import { ChatWidget } from '@/components/chatWidget';
import { usePathname } from 'next/navigation';
import type { Property } from '@/lib/api/services/fetchProperty';
import { chatService } from '@/lib/api/services/fetchChat';

export const ChatWidgetVisibilityContext = createContext<{
  showWidget: boolean;
  openChat: () => void;
  closeChat: () => void;
}>({
  showWidget: false,
  openChat: () => {},
  closeChat: () => {},
});

interface ChatWidgetProviderProps {
  children: React.ReactNode;
  property?: Property;
}

export default function ChatProvider({ children, property }: ChatWidgetProviderProps) {
  const [showWidget, setShowWidget] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);

  const pathname = usePathname();

  // <PERSON>hi vào trang, tự động lấy conversation
  useEffect(() => {
    const ensureConversation = async () => {
      if (property?.saler?.id && pathname?.startsWith('/properties/')) {
        try {
          const sellerConversation = await chatService.getSellerConversation(property.saler.id);
          if (sellerConversation?.data?.id) {
            setConversationId(sellerConversation.data.id);
            setShowWidget(true);
          } else {
            setConversationId(null);
            setShowWidget(false);
            setIsOpen(false);
          }
        } catch (error) {
          setConversationId(null);
          setShowWidget(false);
          setIsOpen(false);
        }
      }
    };
    ensureConversation();
  }, [property?.saler?.id, pathname]);

  // Khi nhấn Ask, nếu chưa có conversation thì tạo và show widget
  const openChat = useCallback(async () => {
    if (!conversationId && property?.saler?.id) {
      const sellerConversation = await chatService.getSellerConversation(property.saler.id);
      if (sellerConversation?.data?.id) {
        setConversationId(sellerConversation.data.id);
        setShowWidget(true);
        setIsOpen(true);
      } else {
        // Nếu cần, có thể gọi API tạo conversation ở đây
        setShowWidget(true);
        setIsOpen(true);
      }
    } else {
      setShowWidget(true);
      setIsOpen(true);
    }
  }, [conversationId, property?.saler?.id]);

  const closeChat = useCallback(() => setIsOpen(false), []);

  return (
    <ChatWidgetVisibilityContext.Provider value={{ showWidget, openChat, closeChat }}>
      {children}
      {showWidget && (
        <ChatWidget
          isOpen={isOpen}
          onOpen={() => setIsOpen(true)}
          onClose={closeChat}
          property={property}
        />
      )}
    </ChatWidgetVisibilityContext.Provider>
  );
}
