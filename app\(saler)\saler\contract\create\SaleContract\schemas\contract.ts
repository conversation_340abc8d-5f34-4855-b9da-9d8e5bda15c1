import { z } from 'zod';

// Schema cho Bên A (<PERSON><PERSON><PERSON> bán)
export const partyASchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  idNumber: z.string().regex(/^\d{9}$|^\d{12}$/, 'Số CMND/CCCD không hợp lệ'),
  idIssuedDate: z.string().min(1, '<PERSON><PERSON>y cấp không được để trống'),
  idIssuedPlace: z.string().min(1, 'Nơi cấp không được để trống'),
  permanentAddress: z.string().min(1, 'Địa chỉ thường trú không được để trống'),
  contactAddress: z.string().min(1, 'Địa chỉ liên hệ không được để trống'),
  phone: z.string().regex(/^((\+84|84|0)[3|5|7|8|9])+([0-9]{8})$/, '<PERSON><PERSON> điện thoại không hợp lệ'),
  fax: z.string().optional(),
  accountNumber: z.string().optional(),
  bankName: z.string().optional(),
  taxCode: z.string().optional(),
  bankCode: z.string().optional(),
  idVerifications: z.array(z.string()).min(1, 'Phải có ít nhất một giấy tờ tùy thân'),
});

// Schema cho Bên B (Bên mua)
export const partyBSchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  idNumber: z.string().regex(/^\d{9}$|^\d{12}$/, 'Số CMND/CCCD không hợp lệ'),
  idIssuedDate: z.string().min(1, 'Ngày cấp không được để trống'),
  idIssuedPlace: z.string().min(1, 'Nơi cấp không được để trống'),
  permanentAddress: z.string().min(1, 'Địa chỉ thường trú không được để trống'),
  contactAddress: z.string().min(1, 'Địa chỉ liên hệ không được để trống'),
  phone: z.string().regex(/^((\+84|84|0)[3|5|7|8|9])+([0-9]{8})$/, 'Số điện thoại không hợp lệ'),
  fax: z.string().min(1, 'Số fax không được để trống'),
  accountNumber: z.string().min(1, 'Số thuế không được để trống'),
  bankName: z.string().min(1, 'Tên ngân hàng không được để trống'),
  taxCode: z.string().min(1, 'Mã số thuế không được để trống'),
  bankCode: z.string().min(1, 'Mã ngân hàng không được để trống'),
  idVerifications: z.array(z.string()).min(1, 'Phải có ít nhất một giấy tờ tùy thân'),
});

// Schema cho điều khoản hợp đồng mua bán
export const contractTermsSchema = z.object({
  propertyAddress: z.string().min(1, 'Địa chỉ tài sản không được để trống'),
  propertyType: z.string().min(1, 'Loại tài sản không được để trống'),
  totalFloorArea: z.number().positive('Diện tích sàn phải lớn hơn 0'),
  totalLandArea: z.number().positive('Diện tích đất phải lớn hơn 0'),
  privateArea: z.number().positive('Diện tích sử dụng riêng phải lớn hơn 0'),
  sharedArea: z.number().positive('Diện tích sử dụng chung phải lớn hơn 0'),
  landOrigin: z.string().min(1, 'Nguồn gốc đất không được để trống'),
  equipmentDetails: z.string().min(1, 'Chi tiết thiết bị không được để trống'),
  legalDocuments: z.array(z.string()).min(1, 'Phải có ít nhất một tài liệu pháp lý'),
  salePrice: z.number().positive('Giá bán phải lớn hơn 0'),
  paymentMethod: z.string().min(1, 'Phương thức thanh toán không được để trống'),
  paymentSchedule: z
    .array(
      z.object({
        amount: z.number().positive('Số tiền phải lớn hơn 0'),
        paymentDate: z.string().min(1, 'Ngày thanh toán không được để trống'),
        note: z.string().optional(),
      })
    )
    .min(1, 'Phải có ít nhất một lịch thanh toán'),
});

// Types
export type PartyAFormData = z.infer<typeof partyASchema>;
export type PartyBFormData = z.infer<typeof partyBSchema>;
export type ContractTermsFormData = z.infer<typeof contractTermsSchema>;
