import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Trash2 } from 'lucide-react';
import { clause1To2InputSchema, Clause1To2Input } from '../schemas';

interface PropertyDetailsFormProps {
  data: Clause1To2Input;
  onNext: (data: Clause1To2Input) => void;
  onBack?: () => void;
}

const PropertyDetailsForm: React.FC<PropertyDetailsFormProps> = ({ data, onNext, onBack }) => {
  const form = useForm<Clause1To2Input>({
    resolver: zodResolver(clause1To2InputSchema),
    defaultValues: data,
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'paymentSchedule',
  });

  const onSubmit = (formData: Clause1To2Input) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Tài sản & Thanh toán</CardTitle>
        <CardDescription>Thông tin chi tiết về tài sản và phương thức thanh toán</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Property Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin tài sản</h3>

              <FormField
                control={form.control}
                name="propertyType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loại tài sản *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn loại tài sản" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="apartment">Căn hộ chung cư</SelectItem>
                        <SelectItem value="house">Nhà riêng</SelectItem>
                        <SelectItem value="villa">Biệt thự</SelectItem>
                        <SelectItem value="townhouse">Nhà phố</SelectItem>
                        <SelectItem value="land">Đất nền</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ tài sản *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Nhập địa chỉ đầy đủ của tài sản"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="totalFloorArea"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diện tích sàn (m²) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="totalLandArea"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diện tích đất (m²) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="privateArea"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diện tích sử dụng riêng (m²) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sharedArea"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diện tích sử dụng chung (m²) *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="landOrigin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nguồn gốc đất *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Mô tả nguồn gốc và lịch sử của đất"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="equipmentDetails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Chi tiết thiết bị *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Mô tả các thiết bị, đồ đạc kèm theo tài sản"
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="legalDocuments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tài liệu pháp lý *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Danh sách các giấy tờ pháp lý (sổ đỏ, giấy chứng nhận, v.v.)"
                        className="resize-none"
                        rows={2}
                        {...field}
                        onChange={e => {
                          const value = e.target.value;
                          const documents = value.split('\n').filter(doc => doc.trim() !== '');
                          field.onChange(documents);
                        }}
                        value={Array.isArray(field.value) ? field.value.join('\n') : ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Payment Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Thông tin thanh toán</h3>

              <FormField
                control={form.control}
                name="salePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giá bán (VNĐ) *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="0"
                        {...field}
                        onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phương thức thanh toán *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn phương thức thanh toán" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cash">Tiền mặt</SelectItem>
                        <SelectItem value="bank_transfer">Chuyển khoản ngân hàng</SelectItem>
                        <SelectItem value="loan">Vay ngân hàng</SelectItem>
                        <SelectItem value="mixed">Hỗn hợp</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Payment Schedule */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-md font-medium">Lịch thanh toán *</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => append({ amount: 0, paymentDate: '', note: '' })}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Thêm đợt thanh toán
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-4 gap-2 items-end">
                    <FormField
                      control={form.control}
                      name={`paymentSchedule.${index}.amount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Số tiền (VNĐ)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              placeholder="0"
                              {...field}
                              onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`paymentSchedule.${index}.paymentDate`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ngày thanh toán</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`paymentSchedule.${index}.note`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ghi chú</FormLabel>
                          <FormControl>
                            <Input placeholder="Ghi chú (tùy chọn)" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => remove(index)}
                      disabled={fields.length === 1}
                      className="flex items-center gap-1"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-4">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Quay lại
                </Button>
              )}
              <div className="ml-auto">
                <Button type="submit">Tiếp tục</Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default PropertyDetailsForm;
