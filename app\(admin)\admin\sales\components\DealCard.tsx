'use client';

import { useState } from 'react';
import { MapPin, Phone, Mail, User, Calendar, Eye } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Deal, DealStatus, DealPriority } from '@/lib/api/services/fetchDeal';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';

interface DealCardProps {
  deal: Deal;
  status?: DealStatus;
  isDragging?: boolean;
  isLoading?: boolean;
}

export function DealCard({ deal, isDragging, isLoading }: DealCardProps) {
  const [open, setOpen] = useState(false);

  const customerName = deal.customer?.name || deal.title || 'Không xác định';
  const initials = customerName
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();

  const getPriorityConfig = (priority?: DealPriority) => {
    switch (priority) {
      case DealPriority.High:
        return { label: 'Ưu tiên cao', color: 'bg-red-100 text-red-700' };
      case DealPriority.Medium:
        return { label: 'Ưu tiên trung bình', color: 'bg-amber-100 text-amber-700' };
      case DealPriority.Low:
        return { label: 'Ưu tiên thấp', color: 'bg-blue-100 text-blue-700' };
      default:
        return { label: 'Không xác định', color: 'bg-gray-100 text-gray-700' };
    }
  };

  const getStatusConfig = (status: DealStatus) => {
    switch (status) {
      case DealStatus.New:
        return { label: 'Mới', color: 'bg-blue-50 text-blue-700 border-blue-200' };
      case DealStatus.Contacted:
        return { label: 'Đã liên hệ', color: 'bg-yellow-50 text-yellow-700 border-yellow-200' };
      case DealStatus.Negotiation:
        return { label: 'Đàm phán', color: 'bg-orange-50 text-orange-700 border-orange-200' };
      case DealStatus.Closing:
        return { label: 'Sắp chốt', color: 'bg-purple-50 text-purple-700 border-purple-200' };
      case DealStatus.Won:
        return { label: 'Thành công', color: 'bg-green-50 text-green-700 border-green-200' };
      case DealStatus.Lost:
        return { label: 'Thất bại', color: 'bg-red-50 text-red-700 border-red-200' };
      default:
        return { label: 'Không xác định', color: 'bg-gray-50 text-gray-700 border-gray-200' };
    }
  };

  const priorityConfig = getPriorityConfig(deal.priority);
  const statusConfig = getStatusConfig(deal.status);

  return (
    <div
      className={cn(
        'group relative bg-white rounded-lg overflow-hidden',
        'border border-gray-100 shadow-sm hover:shadow-md transition-all duration-200',
        isDragging && 'rotate-2 scale-105 shadow-lg',
        isLoading && 'opacity-50 pointer-events-none'
      )}
    >
      <div className="p-4">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 border-2 border-gray-50">
              <AvatarFallback className="bg-gray-100 text-gray-700 font-medium">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{customerName}</h3>
            </div>
          </div>

          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-50">
                <Eye className="h-4 w-4 text-gray-500" />
              </Button>
            </SheetTrigger>
            <SheetContent className="w-full sm:max-w-xl">
              <SheetHeader>
                <SheetTitle className="text-xl font-bold flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Chi tiết deal
                </SheetTitle>
                <SheetDescription>Thông tin chi tiết về deal và khách hàng</SheetDescription>
              </SheetHeader>

              <div className="space-y-6 px-4">
                {/* Deal Information */}
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Thông tin deal</h3>
                  <div className="grid gap-4 text-sm">
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Tiêu đề</p>
                        <p className="font-medium">{deal.title || '--'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-muted-foreground">Mô tả</p>
                        <p className="font-medium">{deal.description || '--'}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Customer Information */}
                {deal.customer && (
                  <>
                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">Thông tin khách hàng</h3>
                      <div className="grid gap-4 text-sm">
                        <div className="flex items-center gap-3">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-muted-foreground">Họ và tên</p>
                            <p className="font-medium">{deal.customer.name}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-muted-foreground">Email</p>
                            <p className="font-medium">{deal.customer.email || '--'}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-muted-foreground">Số điện thoại</p>
                            <p className="font-medium">{deal.customer.phone || '--'}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-muted-foreground">Địa chỉ</p>
                            <p className="font-medium">{deal.customer.address || '--'}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Separator />
                  </>
                )}

                {/* Sales Rep Information */}
                {deal.saler && (
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">Nhân viên phụ trách</h3>
                    <div className="grid gap-4 text-sm">
                      <div className="flex items-center gap-3">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-muted-foreground">Họ và tên</p>
                          <p className="font-medium">{deal.saler.fullName}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-muted-foreground">Email</p>
                          <p className="font-medium">{deal.saler.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-muted-foreground">Số điện thoại</p>
                          <p className="font-medium">{deal.saler.phoneNumber || '--'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Badges Section */}
        <div className="flex items-center gap-2 mb-3">
          <Badge variant="outline" className={cn('font-normal', priorityConfig.color)}>
            {priorityConfig.label}
          </Badge>
          <Badge variant="outline" className={cn('font-normal', statusConfig.color)}>
            {statusConfig.label}
          </Badge>
        </div>

        {/* Contact Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600 truncate">{deal.customer?.email || '--'}</span>
          </div>
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600 truncate">{deal.customer?.phone || '--'}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
