import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import favoritePropertyService, {
  FavoritePropertyRequest,
} from '@/lib/api/services/fetchFavoriteProperty';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/store/authStore';

export function useFavoriteProperty() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  // Query for getting favorite properties
  const favoritePropertiesQuery = useQuery({
    queryKey: ['favorite-properties'],
    queryFn: favoritePropertyService.getFavoriteProperties,
    enabled: isAuthenticated,
  });

  const addFavoriteMutation = useMutation({
    mutationFn: (request: FavoritePropertyRequest) =>
      favoritePropertyService.addFavoriteProperty(request),
    onSuccess: data => {
      if (data.status) {
        toast.success('Đã lưu nhà vào danh sách yêu thích', {
          // icon: React.createElement(Check, {
          //   className: "size-4 p-0.5 bg-green-600 text-white rounded-full"
          // }),
          style: {
            backgroundColor: 'white',
            color: 'black',
          },
          action: {
            label: 'Xem',
            actionButtonStyle: {
              backgroundColor: 'green',
              color: 'white',
            },
            onClick: () => router.push(`/myrevo`),
          },
        });
        // Invalidate favorite properties list and specific property query
        queryClient.invalidateQueries({
          queryKey: ['favorite-properties'],
        });
        queryClient.invalidateQueries({
          queryKey: ['properties', 'detail', data.data?.propertyId],
        });
      } else {
        toast.error(data.message || 'Thêm nhà vào danh sách yêu thích thất bại');
      }
    },
    onError: error => {
      toast.error('Thêm nhà vào danh sách yêu thích thất bại');
      console.error('Lỗi khi thêm nhà vào danh sách yêu thích:', error);
    },
  });

  const removeFavoriteMutation = useMutation({
    mutationFn: (propertyId: string) => favoritePropertyService.removeFavoriteProperty(propertyId),
    onSuccess: data => {
      if (data.status) {
        toast.success('Đã xóa nhà khỏi danh sách yêu thích', {
          // icon: React.createElement(Check, {
          //   className: "size-4 p-0.5 bg-green-600 text-white rounded-full"
          // }),
          style: {
            backgroundColor: 'white',
            color: 'black',
          },
          action: {
            label: 'Xem',
            actionButtonStyle: {
              backgroundColor: 'green',
              color: 'white',
            },
            onClick: () => router.push(`/myrevo`),
          },
        });
        // Invalidate favorite properties list and specific property query
        queryClient.invalidateQueries({
          queryKey: ['favorite-properties'],
        });
        queryClient.invalidateQueries({
          queryKey: ['properties', 'detail', data.data?.propertyId],
        });
      } else {
        toast.error(data.message || 'Xóa nhà khỏi danh sách yêu thích thất bại');
      }
    },
    onError: error => {
      toast.error('Xóa nhà khỏi danh sách yêu thích thất bại');
      console.error('Lỗi khi xóa nhà khỏi danh sách yêu thích:', error);
    },
  });

  return {
    // Query data
    favoriteProperties: favoritePropertiesQuery.data,
    isLoading: favoritePropertiesQuery.isLoading,
    isError: favoritePropertiesQuery.isError,
    error: favoritePropertiesQuery.error,

    // Mutations
    addFavorite: addFavoriteMutation.mutate,
    removeFavorite: removeFavoriteMutation.mutate,
    isAddingFavorite: addFavoriteMutation.isPending,
    isRemovingFavorite: removeFavoriteMutation.isPending,
  };
}
