'use client';

import { useQuery } from '@tanstack/react-query';
import banksService from '@/lib/api/services/fetchBanks';
import { toast } from 'sonner';

export function useBanks() {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['banks'],
    queryFn: banksService.getBanks,
    staleTime: 1000 * 60 * 60, // 1 hour - banks data doesn't change often
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    select: data => ({
      banks: data?.data || [],
      status: data?.status || false,
      message: data?.message || '',
    }),
    meta: {
      errorMessage: 'Không thể tải danh sách ngân hàng',
    },
  });

  // Handle error with toast
  if (error) {
    toast.error('Không thể tải danh sách ngân hàng', {
      style: {
        backgroundColor: 'white',
        color: 'black',
      },
      action: {
        label: 'Thử lại',
        actionButtonStyle: {
          backgroundColor: 'red',
          color: 'white',
        },
        onClick: () => refetch(),
      },
    });
  }

  return {
    banks: data?.banks || [],
    loading: isLoading,
    error: error?.message || null,
    status: data?.status || false,
    message: data?.message || '',
    refetch,
  };
}

// Hook for getting specific bank by code
export function useBank(bankCode: string) {
  const { banks, loading, error } = useBanks();

  const bank = banks.find(bank => bank.code === bankCode);

  return {
    bank,
    loading,
    error,
    found: !!bank,
  };
}

// Hook for getting popular banks (top banks in Vietnam)
export function usePopularBanks() {
  const { banks, loading, error } = useBanks();

  // List of popular bank codes in Vietnam
  const popularBankCodes = [
    'VCB', // Vietcombank
    'TCB', // Techcombank
    'BIDV', // BIDV
    'VTB', // Vietinbank
    'CTG', // VietinBank
    'MBB', // MB Bank
    'ACB', // ACB
    'SHB', // SHB
    'VPB', // VPBank
    'TPB', // TPBank
    'STB', // Sacombank
    'EIB', // Eximbank
  ];

  const popularBanks = banks.filter(bank => popularBankCodes.includes(bank.code));

  return {
    popularBanks,
    allBanks: banks,
    loading,
    error,
  };
}
