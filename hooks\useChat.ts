import { chatService, Conversation, SendMessageRequest } from '@/lib/api/services/fetchChat';
import { signalRService } from '@/lib/realtime/signalR';
import { useQuery, useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Property } from '@/lib/api/services/fetchProperty';

/**
 * React Query hook to handle all chat-related operations
 * @returns {Object} Chat operations and states
 */
export function useChat() {
  // Query for current conversation (currently not implemented, always returns null)
  const conversationQuery = useQuery<Conversation | null, Error>({
    queryKey: ['chat', 'currentUserConversation'],
    queryFn: () => chatService.getUserConversation(),
  });

  // Mutation for sending messages
  const sendMessageMutation = useMutation({
    mutationFn: async (request: SendMessageRequest) => {
      return await chatService.sendMessage(request);
    },
    onError: () => {
      toast.error('Không thể gửi tin nhắn. Vui lòng thử lại.');
    },
  });

  // Mutation for pinning messages
  const pinMessageMutation = useMutation({
    mutationFn: async ({
      messageId,
      conversationId,
    }: {
      messageId: string;
      conversationId: string;
    }) => {
      await chatService.pinMessage(messageId, conversationId);
    },
    onError: () => {
      toast.error('Không thể ghim tin nhắn. Vui lòng thử lại.');
    },
  });

  // Mutation for unpinning messages
  const unpinMessageMutation = useMutation({
    mutationFn: async ({
      messageId,
      conversationId,
    }: {
      messageId: string;
      conversationId: string;
    }) => {
      await chatService.unpinMessage(messageId, conversationId);
    },
    onError: () => {
      toast.error('Không thể bỏ ghim tin nhắn. Vui lòng thử lại.');
    },
  });

  // Mutation for deleting messages
  const deleteMessageMutation = useMutation({
    mutationFn: async (messageId: string) => {
      await chatService.deleteMessage(messageId);
    },
    onError: () => {
      toast.error('Không thể xóa tin nhắn. Vui lòng thử lại.');
    },
  });

  // Hàm gửi propertyCard vào conversation với saler, join vào SignalR
  const askRevolandWithPropertyCard = async (salerId: string, property: Property) => {
    // 1. Lấy hoặc tạo conversation với saler
    let conversation = await chatService.getSellerConversation(salerId);
    let conversationId = conversation?.data?.id;

    // 2. Nếu có conversationId, join vào SignalR để load tin nhắn cũ
    if (conversationId) {
      await signalRService.joinConversation(conversationId, salerId);
    }

    // 3. Gửi propertyCard message
    await chatService.sendMessage({
      conversationId,
      recipientId: salerId,
      content: JSON.stringify({ property }),
      messageType: 'property-card',
      direction: 'inbound',
    });

    // 4. Sau khi gửi, gọi lại getSellerConversation để lấy conversationId mới nhất
    conversation = await chatService.getSellerConversation(salerId);
    console.log('conversation', conversation);
    conversationId = conversation?.data?.id;

    // 5. Join lại nếu conversationId mới được tạo
    if (conversationId) {
      await signalRService.joinConversation(conversationId, salerId);
      console.log('joined conversation', conversationId);
    }

    // 6. Return conversationId for follow-up message
    return conversationId;
  };

  return {
    // Query states
    conversation: conversationQuery.data,
    isLoading: conversationQuery.isLoading,
    isError: conversationQuery.isError,
    refetchConversation: conversationQuery.refetch,
    conversationQuery: conversationQuery.data,

    // Mutations
    sendMessage: sendMessageMutation.mutateAsync,
    isSending: sendMessageMutation.isPending,
    pinMessage: pinMessageMutation.mutateAsync,
    unpinMessage: unpinMessageMutation.mutateAsync,
    deleteMessage: deleteMessageMutation.mutateAsync,

    // Helper
    askRevolandWithPropertyCard,

    // SignalR handlers
    setMessageHandler: signalRService.setMessageHandler,
    setMessageHistoryHandler: signalRService.setMessageHistoryHandler,
    setNewConversationHandler: signalRService.setNewConversationHandler,
    setNotificationHandler: signalRService.setNotificationHandler,
    setTypingHandler: signalRService.setTypingHandler,
    setReadHandler: signalRService.setReadHandler,
    setOnlineStatusHandler: signalRService.setOnlineStatusHandler,
  };
}
