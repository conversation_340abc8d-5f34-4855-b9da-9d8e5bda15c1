import { numberToVietnameseMoney } from '@/utils/numbers/numberToVietnameseMoney';

import { translatePaymentTerm } from '../../../utils/formatPayTems';
import { SaleContractInput } from '../schemas';

export const generateSaleContractHtml = (formData: SaleContractInput): string => {
  const { partyA, partyB, clause1To2Input, clause3To6, clause7To10, clause11To14 } = formData;

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  // Format dates with error handling
  const formatDate = (dateString: string): { day: string; month: string; year: string } => {
    if (!dateString) {
      return { day: '….', month: '…', year: '….' };
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return { day: '….', month: '…', year: '….' };
      }

      return {
        day: date.getDate().toString().padStart(2, '0'),
        month: (date.getMonth() + 1).toString().padStart(2, '0'),
        year: date.getFullYear().toString(),
      };
    } catch {
      return { day: '….', month: '…', year: '….' };
    }
  };

  // Generate payment schedule HTML
  const generatePaymentScheduleHtml = () => {
    if (!clause1To2Input.paymentSchedule || clause1To2Input.paymentSchedule.length === 0) {
      return '<p style="font-family: \'Times New Roman\', serif;">- Đợt 1 là ..................đồng vào ngày.......tháng.......năm.......</p>';
    }

    return clause1To2Input.paymentSchedule
      .map((payment, index) => {
        const paymentDateFormatted = formatDate(payment.paymentDate);
        return `<p style="font-family: 'Times New Roman', serif;">- Đợt ${index + 1} là <strong>${formatCurrency(payment.amount)}</strong> đồng vào ngày <strong>${paymentDateFormatted.day}</strong> tháng <strong>${paymentDateFormatted.month}</strong> năm <strong>${paymentDateFormatted.year}</strong>${payment.note ? ` (${payment.note})` : ''}.</p>`;
      })
      .join('');
  };

  return `
    <style>
      .contract-container, .contract-container * {
        font-family: 'Times New Roman', serif !important;
      }
      h1, h2, h3, h4, h5, h6 {
        font-family: 'Times New Roman', serif !important;
      }
      p, div, span, strong, em {
        font-family: 'Times New Roman', serif !important;
      }
      .text-center {
        text-align: center !important;
      }
      .text-right {
        text-align: right !important;
      }
      .text-left {
        text-align: left !important;
      }
      .article-header {
        text-align: left !important;
        font-weight: bold !important;
        margin: 15px 0 8px 0 !important;
      }
      .section-header {
        text-align: left !important;
        font-weight: bold !important;
        margin: 15px 0 8px 0 !important;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
      }
      td {
        padding: 10px;
        text-align: center;
        vertical-align: top;
        border: none;
      }
      .signature-section table {
        margin-top: 50px;
      }
      .signature-section td {
        width: 50%;
        padding: 0 20px;
      }
      .signature-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 30px;
      }
      .signature-cell {
        width: 50%;
        text-align: center;
        vertical-align: top;
        padding: 20px;
        border: none;
      }
    </style>
    <div class="contract-container" style="font-family: 'Times New Roman', serif;">
      <h2 class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><strong>CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</strong></h2>
      <p class="text-center" style="text-align: center; font-weight: bold; font-family: 'Times New Roman', serif;">Độc lập - Tự do - Hạnh phúc</p>
      <p class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;">---------------o0o---------------</p>
      <h3 class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><strong>HỢP ĐỒNG MUA BÁN NHÀ Ở</strong></h3>
      <p class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><em>(Số......../HĐ)</em></p>
      <br/>

      <div class="text-right" style="text-align: right; font-family: 'Times New Roman', serif;">
        <p>............., ngày..........tháng...........năm..........</p>
      </div>

      <br/>
      <p style="font-family: 'Times New Roman', serif;">Hai bên chúng tôi gồm:</p>
      
      <h3 class="section-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>BÊN BÁN NHÀ Ở (sau đây gọi tắt là Bên bán):</strong></h3>
      <p style="font-family: 'Times New Roman', serif;">- Ông (bà): <strong>${partyA.name || '.....................................................................................................................................'}</strong></p>
      <p style="font-family: 'Times New Roman', serif;">- Số CCCD (hộ chiếu): ${partyA.idNumber || '.........................'} cấp ngày ${partyA.idIssuedDate || '......../......./......'}, tại ${partyA.idIssuedPlace || '...............................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Hộ khẩu thường trú: ${partyA.permanentAddress || '.....................................................................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Địa chỉ liên hệ: ${partyA.contactAddress || '..............................................................................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Điện thoại: ${partyA.phone || '...........................................'}  Fax (nếu có): ${partyA.fax || '...................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Số tài khoản: ${partyA.accountNumber || '.........................................'}tại Ngân hàng: ${partyA.bankName || '................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Mã số thuế: ${partyA.taxCode || '..................................................................................................................................'}</p>

      <h3 class="section-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>BÊN MUA NHÀ Ở (sau đây gọi tắt là Bên mua):</strong></h3>
      <p style="font-family: 'Times New Roman', serif;">- Ông (bà): <strong>${partyB.name || '....................................................................................................................................'}</strong></p>
      <p style="font-family: 'Times New Roman', serif;">- Số CCCD (hộ chiếu): ${partyB.idNumber || '............................'} cấp ngày ${partyB.idIssuedDate || '....../......./.....'}, tại ${partyB.idIssuedPlace || '..............................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Hộ khẩu thường trú: ${partyB.permanentAddress || '....................................................................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Địa chỉ liên hệ: ${partyB.contactAddress || '.............................................................................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Điện thoại: ${partyB.phone || '............................................'} Fax (nếu có): ${partyB.fax || '..................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Số tài khoản: ${partyB.accountNumber || '..........................................'} tại Ngân hàng: ${partyB.bankName || '..............................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">- Mã số thuế: ${partyB.taxCode || '..................................................................................................................................'}</p>

      <p style="font-family: 'Times New Roman', serif;">Hai bên chúng tôi thống nhất ký kết hợp đồng mua bán nhà ở với các nội dung sau đây:</p>

      <h3 class="article-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>Điều 1. Đặc điểm chính của nhà ở mua bán</strong></h3>
      <p style="font-family: 'Times New Roman', serif;"><strong>1.</strong> Loại nhà ở (biệt thự, căn hộ chung cư hoặc nhà ở riêng lẻ): ${clause1To2Input.propertyType || '...................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>2.</strong> Địa chỉ nhà ở: ${clause1To2Input.address || '...........................................................................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;">(Đối với hợp đồng mua bán nhà ở hình thành trong tương lai thì ghi rõ địa điểm nơi nhà ở được xây dựng theo quy hoạch đã được duyệt).</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>3.</strong> Tổng diện tích sàn xây dựng: <strong>${clause1To2Input.totalFloorArea || '.................'}</strong>m2</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>4.</strong> Tổng diện tích sử dụng đất ở: <strong>${clause1To2Input.totalLandArea || '.................'}</strong>m2, trong đó:</p>
      <p style="font-family: 'Times New Roman', serif;">Sử dụng riêng: <strong>${clause1To2Input.privateArea || '...........'}</strong>m2; Sử dụng chung (nếu có): <strong>${clause1To2Input.sharedArea || '............'}</strong>m2</p>
      <p style="font-family: 'Times New Roman', serif;">Nguồn gốc sử dụng đất (được giao, được công nhận hoặc thuê...): ${clause1To2Input.landOrigin || '………………………………..'}
      <br/>…………………………………………………………………………………………………………………</p>
      <p style="font-family: 'Times New Roman', serif;">(Nếu là thuê đất thì phải ghi thêm thông tin về số hợp đồng, ngày ký hợp đồng thuê đất, thời gian thuê từ ngày....đến ngày....).</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>5.</strong> Các trang thiết bị chủ yếu gắn liền với nhà ở: ${clause1To2Input.equipmentDetails || '...........................................................................'}</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>6.</strong> Giấy tờ pháp lý về nhà ở, đất ở kèm theo: ${clause1To2Input.legalDocuments?.join(', ') || '................................................................................'}</p>

      <h3 class="article-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>Điều 2. Giá bán và phương thức thanh toán</strong></h3>
      <p style="font-family: 'Times New Roman', serif;"><strong>1.</strong> Giá bán nhà ở là <strong>${formatCurrency(clause1To2Input.salePrice)}</strong> đồng</p>
      <p style="font-family: 'Times New Roman', serif;">(Bằng chữ: <em>${numberToVietnameseMoney(clause1To2Input.salePrice)} đồng</em>).</p>
      <p style="font-family: 'Times New Roman', serif;">Giá bán này đã bao gồm giá trị quyền sử dụng đất và thuế giá trị gia tăng VAT (nếu bên bán thuộc diện phải nộp thuế VAT).</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>2.</strong> Phương thức thanh toán: thanh toán bằng tiền Việt Nam thông qua hình thức ${translatePaymentTerm(clause1To2Input.paymentMethod)}</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>3.</strong> Thời hạn thực hiện thanh toán</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>a)</strong> Thanh toán một lần vào ngày..........tháng........năm..........(hoặc trong thời hạn ......ngày, kể từ sau ngày kí kết hợp đồng này).</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>b)</strong> Trường hợp mua nhà ở theo phương thức trả chậm, trả dần thì thực hiện thanh toán vào các đợt như sau:</p>
      ${generatePaymentScheduleHtml()}
      <p style="font-family: 'Times New Roman', serif;">- Đợt tiếp theo..................................................</p>
      <p style="font-family: 'Times New Roman', serif;">Trước mỗi đợt thanh toán theo thỏa thuận tại khoản này, Bên bán có trách nhiệm thông báo bằng văn bản (thông qua hình thức như fax, chuyển bưu điện....) cho Bên mua biết rõ số tiền phải thanh toán và thời hạn phải thanh toán kể từ ngày nhận được thông báo này.</p>

      <h3 class="article-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>Điều 3. Thời hạn giao nhận nhà ở</strong></h3>
      <p style="font-family: 'Times New Roman', serif;"><strong>1.</strong> Bên bán có trách nhiệm bàn giao nhà ở kèm theo các trang thiết bị gắn với nhà ở đó và giấy tờ pháp lý về nhà ở nêu tại Điều 1 của hợp đồng này cho Bên mua trong thời hạn là...............ngày, kể từ ngày Bên mua thanh toán đủ số tiền mua nhà ở (hoặc kể từ ngày hợp đồng này được ký kết). Việc bàn giao nhà ở phải lập thành biên bản có chữ ký xác nhận của hai bên.</p>
      <p style="font-family: 'Times New Roman', serif;"><strong>2.</strong> Các trường hợp thỏa thuận khác...............................</p>

      <div style="white-space: pre-wrap;">${clause3To6.Clause3To6 || 'Nội dung điều khoản 3-6 chưa được cập nhật.'}</div>

      <div style="white-space: pre-wrap;">${clause7To10.Clause7To10 || 'Nội dung điều khoản 7-10 chưa được cập nhật.'}</div>

      <div style="white-space: pre-wrap;">${clause11To14.Clause11To14 || 'Nội dung điều khoản 11-14 chưa được cập nhật.'}</div>

    </div>
  `;
};
