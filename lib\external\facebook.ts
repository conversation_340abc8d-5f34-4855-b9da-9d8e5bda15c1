// lib/facebook.ts

export const initFacebookSDK = () => {
  return new Promise<void>(resolve => {
    // Nếu SDK đã được load
    if ((window as any).FB) {
      resolve();
      return;
    }

    // Load Facebook SDK
    window.fbAsyncInit = function () {
      window.FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v18.0',
      });
      resolve();
    };

    // Load SDK script
    const script = document.createElement('script');
    script.id = 'facebook-jssdk';
    script.src = 'https://connect.facebook.net/vi_VN/sdk.js';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
  });
};
