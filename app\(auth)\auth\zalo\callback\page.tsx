'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, XCircle, CheckCircle } from 'lucide-react';

export default function ZaloCallbackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { zaloLoginError, zaloLoginSuccess, isAuthenticated } = useAuth();
  const [status, setStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('processing');
  const [error, setError] = useState<string | null>(null);
  const hasInitialized = useRef(false);

  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;

    if (isAuthenticated) {
      setStatus('success');
      setTimeout(() => router.push('/'), 1000);
      return;
    }

    const code = searchParams?.get('code');
    const state = searchParams?.get('state');
    const errorParam = searchParams?.get('error');

    if (errorParam) {
      setError('Zalo authentication failed: ' + errorParam);
      setStatus('error');
      return;
    }

    if (!code || !state) {
      setError('Missing required parameters for authentication');
      setStatus('error');
      return;
    }

    setStatus('processing');
  }, [searchParams, isAuthenticated, router]);

  // Chỉ giữ lại useEffect này để handle kết quả
  useEffect(() => {
    if (zaloLoginError) {
      setError(zaloLoginError);
      setStatus('error');
    }
    if (zaloLoginSuccess) {
      setStatus('success');
      setTimeout(() => router.push('/'), 1500);
    }
  }, [zaloLoginError, zaloLoginSuccess, router]);

  return (
    <div className="flex justify-start">
      <div className="w-full max-w-md mx-auto rounded-xl shadow-xl bg-white p-8 flex flex-col items-center gap-6  ">
        <div className="flex flex-col items-center gap-2 text-center w-full">
          {status === 'processing' && (
            <Alert className="w-full flex flex-col items-center">
              <Loader2 className="h-10 w-10 text-[#008FF7] animate-spin mb-2" />
              <AlertTitle className="text-xl md:text-2xl font-bold tracking-tight text-[#008FF7] mb-1">
                Đang xác thực Zalo...
              </AlertTitle>
              <AlertDescription className="text-xs md:text-sm text-muted-foreground mb-2">
                Vui lòng chờ trong giây lát, hệ thống đang xác thực tài khoản Zalo của bạn.
              </AlertDescription>
              <Button disabled className="w-full mt-2">
                Đang xử lý...
              </Button>
            </Alert>
          )}
          {status === 'error' && (
            <Alert variant="destructive" className="w-full flex flex-col items-center">
              <XCircle className="h-12 w-12 text-red-500 mb-2" />
              <AlertTitle className="text-xl md:text-2xl font-bold tracking-tight text-red-500 mb-1">
                Đăng nhập thất bại
              </AlertTitle>
              <AlertDescription className="text-xs md:text-sm text-muted-foreground mb-3">
                {error}
              </AlertDescription>
              <Button
                onClick={() => router.push('/login')}
                className="w-full mt-2"
                variant="default"
              >
                Quay lại đăng nhập
              </Button>
            </Alert>
          )}
          {status === 'success' && (
            <Alert className="w-full flex flex-col items-center">
              <CheckCircle className="h-12 w-12 text-green-500 mb-2" />
              <AlertTitle className="text-xl md:text-2xl font-bold tracking-tight text-green-600 mb-1">
                Đăng nhập thành công!
              </AlertTitle>
              <AlertDescription className="text-xs md:text-sm text-muted-foreground mb-2">
                Đang chuyển hướng về trang chủ...
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </div>
  );
}
