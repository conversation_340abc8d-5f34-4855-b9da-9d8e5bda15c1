'use client';

import { useState } from 'react';
import { Plus, User, Mail, Phone, MapPin, Target, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useCreateLead } from '@/hooks/useLead';
import { LeadScore } from '@/lib/api/services/fetchLead';
import { cn } from '@/lib/utils';

// Start of Selection
const formSchema = z.object({
  name: z
    .string()
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(50, 'Họ và tên không được vượt quá 50 ký tự')
    .regex(/^[a-zA-ZÀ-ỹ\s]+$/, 'Họ và tên chỉ được chứa chữ cái và khoảng trắng'),
  email: z.string().email('Vui lòng nhập địa chỉ email hợp lệ').min(1, 'Email là bắt buộc'),
  phone: z
    .string()
    .min(10, 'Số điện thoại phải có ít nhất 10 số')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ'),
  address: z
    .string()
    .min(10, 'Địa chỉ phải có ít nhất 10 ký tự')
    .max(200, 'Địa chỉ không được vượt quá 200 ký tự'),
  score: z.nativeEnum(LeadScore, {
    required_error: 'Vui lòng chọn mức độ ưu tiên',
  }),
});

type FormData = z.infer<typeof formSchema>;

export function AddLeadButton() {
  const [open, setOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const createLead = useCreateLead();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      score: LeadScore.Cold,
    },
    mode: 'onChange',
  });

  const watchedFields = form.watch();
  const isStepValid = (step: number) => {
    switch (step) {
      case 1:
        return !!watchedFields.name && !form.formState.errors.name;
      case 2:
        return (
          !!watchedFields.email &&
          !!watchedFields.phone &&
          !form.formState.errors.email &&
          !form.formState.errors.phone
        );
      case 3:
        return !!watchedFields.address && !form.formState.errors.address;
      default:
        return true;
    }
  };

  const onSubmit = (values: FormData) => {
    createLead.mutate(values, {
      onSuccess: data => {
        if (data.status) {
          form.reset();
          setCurrentStep(1);
          setOpen(false);
        }
      },
    });
  };

  const handleNext = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1);
  };

  const handlePrev = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const resetForm = () => {
    form.reset();
    setCurrentStep(1);
    setOpen(false);
  };

  const getScoreConfig = (score: LeadScore) => {
    switch (score) {
      case LeadScore.Hot:
        return {
          label: 'Ưu tiên cao',
          description: 'Khách hàng sẵn sàng mua, cần liên hệ trong 24h',
          priority: 'Khẩn cấp',
        };
      case LeadScore.Warm:
        return {
          label: 'Ưu tiên trung bình',
          description: 'Khách hàng quan tâm, cần theo dõi và nuôi dưỡng',
          priority: 'Bình thường',
        };
      case LeadScore.Cold:
        return {
          label: 'Ưu tiên thấp',
          description: 'Khách hàng tiềm năng, cần chiến lược dài hạn',
          priority: 'Thấp',
        };
    }
  };

  const getBadgeColor = (score: LeadScore) => {
    switch (score) {
      case LeadScore.Hot:
        return 'bg-red-500/10 text-red-500';
      case LeadScore.Warm:
        return 'bg-yellow-500/10 text-yellow-500';
      case LeadScore.Cold:
        return 'bg-blue-500/10 text-blue-500';
      default:
        return 'bg-gray-500/10 text-gray-500';
    }
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="default" size="default" className="gap-2">
          <Plus className="h-4 w-4" />
          Thêm khách hàng tiềm năng
        </Button>
      </SheetTrigger>

      <SheetContent side="right" className="w-full max-w-2xl p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          <SheetHeader className="px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div>
                <SheetTitle className="text-xl font-semibold">Thêm khách hàng tiềm năng</SheetTitle>
                <SheetDescription className="mt-1">
                  Vui lòng điền đầy đủ thông tin để tạo hồ sơ khách hàng mới
                </SheetDescription>
              </div>
            </div>

            {/* Progress indicator */}
            <div className="flex items-center gap-2 mt-4">
              {[1, 2, 3, 4].map(step => (
                <div key={step} className="flex items-center">
                  <div
                    className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-medium transition-colors ${
                      step === currentStep
                        ? 'border-primary bg-primary text-primary-foreground'
                        : step < currentStep
                          ? 'border-primary bg-primary text-primary-foreground'
                          : 'border-muted-foreground/30 text-muted-foreground'
                    }`}
                  >
                    {step < currentStep ? <CheckCircle2 className="h-4 w-4" /> : step}
                  </div>
                  {step < 4 && (
                    <div
                      className={`w-8 h-px mx-1 ${
                        step < currentStep ? 'bg-primary' : 'bg-muted-foreground/30'
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 space-y-6">
                {/* Step 1: Personal Information */}
                {currentStep === 1 && (
                  <Card>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <CardTitle className="text-lg">Thông tin cá nhân</CardTitle>
                      </div>
                      <CardDescription>Nhập họ tên đầy đủ của khách hàng tiềm năng</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">
                              Họ và tên <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="Họ và tên" className="h-10" {...field} />
                            </FormControl>
                            <FormDescription className="text-xs text-muted-foreground">
                              Nhập họ tên đầy đủ, chính xác của khách hàng
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                )}

                {/* Step 2: Contact Information */}
                {currentStep === 2 && (
                  <Card>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <Phone className="h-5 w-5 text-muted-foreground" />
                        <CardTitle className="text-lg">Thông tin liên hệ</CardTitle>
                      </div>
                      <CardDescription>
                        Cung cấp thông tin liên hệ để có thể tương tác với khách hàng
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              Địa chỉ email <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                className="h-10"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-xs text-muted-foreground">
                              Email chính để liên hệ và gửi thông tin
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              Số điện thoại <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="0901 234 567" className="h-10" {...field} />
                            </FormControl>
                            <FormDescription className="text-xs text-muted-foreground">
                              Số điện thoại di động để liên hệ với khách hàng
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                )}

                {/* Step 3: Address */}
                {currentStep === 3 && (
                  <Card>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-muted-foreground" />
                        <CardTitle className="text-lg">Địa chỉ</CardTitle>
                      </div>
                      <CardDescription>
                        Địa chỉ liên hệ hoặc địa chỉ làm việc của khách hàng
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">
                              Địa chỉ đầy đủ <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Số nhà, tên đường, phường/xã, quận/huyện, tỉnh/thành phố"
                                className="h-10"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-xs text-muted-foreground">
                              Nhập địa chỉ chi tiết của khách hàng
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                )}

                {/* Step 4: Lead Assessment */}
                {currentStep === 4 && (
                  <Card>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-2">
                        <Target className="h-5 w-5 text-muted-foreground" />
                        <CardTitle className="text-lg">Đánh giá mức độ ưu tiên</CardTitle>
                      </div>
                      <CardDescription>
                        Xác định mức độ ưu tiên để có chiến lược tiếp cận phù hợp
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <FormField
                        control={form.control}
                        name="score"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">
                              Mức độ ưu tiên <span className="text-destructive">*</span>
                            </FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="h-10">
                                  <SelectValue placeholder="Chọn mức độ ưu tiên" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value={LeadScore.Hot}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>Ưu tiên cao</span>
                                    <Badge
                                      variant="destructive"
                                      className={cn('ml-2 text-xs', getBadgeColor(LeadScore.Hot))}
                                    >
                                      Khẩn cấp
                                    </Badge>
                                  </div>
                                </SelectItem>
                                <SelectItem value={LeadScore.Warm}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>Ưu tiên trung bình</span>
                                    <Badge
                                      variant="secondary"
                                      className={cn('ml-2 text-xs', getBadgeColor(LeadScore.Warm))}
                                    >
                                      Bình thường
                                    </Badge>
                                  </div>
                                </SelectItem>
                                <SelectItem value={LeadScore.Cold}>
                                  <div className="flex items-center justify-between w-full">
                                    <span>Ưu tiên thấp</span>
                                    <Badge
                                      variant="outline"
                                      className={cn('ml-2 text-xs', getBadgeColor(LeadScore.Cold))}
                                    >
                                      Thấp
                                    </Badge>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Priority explanation */}
                      {watchedFields.score && (
                        <div className="mt-4 p-4 bg-muted/50 rounded-lg border">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">
                                {getScoreConfig(watchedFields.score).label}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {getScoreConfig(watchedFields.score).description}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Summary */}
                      <div className="mt-6 p-4 bg-accent/50 rounded-lg">
                        <h4 className="text-sm font-medium mb-2">Chi tiết khách hàng tiềm năng</h4>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <p>
                            <strong>Tên:</strong> {watchedFields.name || 'Chưa nhập'}
                          </p>
                          <p>
                            <strong>Email:</strong> {watchedFields.email || 'Chưa nhập'}
                          </p>
                          <p>
                            <strong>Điện thoại:</strong> {watchedFields.phone || 'Chưa nhập'}
                          </p>
                          <p>
                            <strong>Địa chỉ:</strong> {watchedFields.address || 'Chưa nhập'}
                          </p>
                          <p>
                            <strong>Ưu tiên:</strong>{' '}
                            {watchedFields.score
                              ? getScoreConfig(watchedFields.score).label
                              : 'Chưa chọn'}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </form>
            </Form>
          </div>

          <Separator />

          <SheetFooter className="p-6">
            <div className="flex justify-between w-full">
              <div className="flex gap-2">
                {currentStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrev}
                    disabled={createLead.isPending}
                  >
                    Quay lại
                  </Button>
                )}
                <SheetClose asChild>
                  <Button variant="ghost" onClick={resetForm} disabled={createLead.isPending}>
                    Hủy bỏ
                  </Button>
                </SheetClose>
              </div>

              <div className="flex gap-2">
                {currentStep < 4 ? (
                  <Button type="button" onClick={handleNext} disabled={!isStepValid(currentStep)}>
                    Tiếp tục
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    onClick={form.handleSubmit(onSubmit)}
                    disabled={createLead.isPending || !form.formState.isValid}
                    className="min-w-[120px]"
                  >
                    {createLead.isPending ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white animate-spin rounded-full mr-2" />
                        Đang tạo...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 className="mr-2 h-4 w-4" />
                        Tạo khách hàng
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}
