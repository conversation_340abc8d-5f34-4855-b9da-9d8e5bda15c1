'use client';
import React, { useState } from 'react';
import Sidebar from './Sidebar';
import MainContent from './MainContent';
import WelcomePopup from './WelcomePopup';
import TeamManagementPage from './TeamManagementPage';
import AgentInvitationPage from './AgentInvitationPage';
import CompanySettingsPage from './CompanySettingsPage';
import CompanyDashboard from './CompanyDashboard';
import LeadInbox from './LeadInbox';
import PipelineKanban from './PipelineKanban';

function CompanyWorkspace() {
  const [showWelcomePopup, setShowWelcomePopup] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState<
    | 'main'
    | 'team-management'
    | 'agent-invitation'
    | 'settings'
    | 'reports'
    | 'lead-inbox'
    | 'pipeline-kanban'
  >('main');

  const handleBusinessDataSubmit = (data: unknown) => {
    setShowWelcomePopup(false);
    console.log('Business registration data:', data);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Navigation handlers
  const onNavigateToTeamManagement = () => setCurrentPage('team-management');
  const onNavigateToAgentInvitation = () => setCurrentPage('agent-invitation');
  const onNavigateToSettings = () => setCurrentPage('settings');
  const onNavigateToDashboard = () => setCurrentPage('reports');
  const onNavigateToLeadInbox = () => setCurrentPage('lead-inbox');
  const onNavigateToPipelineKanban = () => setCurrentPage('pipeline-kanban');
  const onBack = () => setCurrentPage('main');

  // Main content switch
  let content: React.ReactNode;
  switch (currentPage) {
    case 'team-management':
      content = <TeamManagementPage onBack={onBack} />;
      break;
    case 'agent-invitation':
      content = <AgentInvitationPage onBack={onBack} />;
      break;
    case 'settings':
      content = <CompanySettingsPage onBack={onBack} businessData={{}} />;
      break;
    case 'reports':
      content = <CompanyDashboard onBack={onBack} />;
      break;
    case 'lead-inbox':
      content = <LeadInbox onBack={onBack} />;
      break;
    case 'pipeline-kanban':
      content = <PipelineKanban onBack={onBack} />;
      break;
    default:
      content = <MainContent sidebarCollapsed={sidebarCollapsed} onToggleSidebar={toggleSidebar} />;
  }

  return (
    <div className="min-h-screen bg-white flex">
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        currentPage={currentPage}
        onNavigateToTeamManagement={onNavigateToTeamManagement}
        onNavigateToAgentInvitation={onNavigateToAgentInvitation}
        onNavigateToDashboard={onNavigateToDashboard}
        onNavigateToSettings={onNavigateToSettings}
        onNavigateToLeadInbox={onNavigateToLeadInbox}
        onNavigateToPipelineKanban={onNavigateToPipelineKanban}
      />
      {content}
      {showWelcomePopup && <WelcomePopup onSelect={handleBusinessDataSubmit} />}
    </div>
  );
}

export default CompanyWorkspace;
