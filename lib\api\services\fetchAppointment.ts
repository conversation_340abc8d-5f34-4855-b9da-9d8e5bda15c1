import apiService from '../core';

export interface AppointmentRequest {
  propertyId: string;
  date: string;
}

export interface RescheduleAppointmentRequest {
  date: string;
}

export interface CancelAppointmentRequest {
  cancellationReason: string;
  notes?: string;
}

export interface Saler {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface Customer {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface Appointment {
  id: string;
  leadId: string;
  saler: Saler;
  customer: Customer;
  messages?: string[];
  propertyId: string;
  status: string;
  date: string;
  createdAt: string;
  updatedAt: string;
  location: string;
}

export interface AppointmentResponse {
  code: number;
  status: boolean;
  message: string;
  data?: Appointment;
}

export interface ActionAppointmentResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface UserAppointmentsResponse {
  code: number;
  status: boolean;
  message: string;
  data: Appointment[];
}

export const appointmentService = {
  // Create a new appointment
  createAppointment: async (request: AppointmentRequest): Promise<AppointmentResponse> => {
    const response = await apiService.post<AppointmentResponse, AppointmentRequest>(
      '/api/appointments',
      request
    );
    return response.data;
  },
  rescheduleAppointment: async (
    id: string,
    request: RescheduleAppointmentRequest
  ): Promise<ActionAppointmentResponse> => {
    // Validate input
    if (!id || !request.date) {
      throw new Error('ID cuộc hẹn và ngày mới là bắt buộc');
    }

    if (!validateRescheduleDate(request.date)) {
      throw new Error('Ngày hẹn phải là thời gian trong tương lai');
    }

    try {
      const response = await apiService.patch<
        ActionAppointmentResponse,
        RescheduleAppointmentRequest
      >(`/api/appointments/${id}/reschedule`, request);
      return response.data;
    } catch (error) {
      // Enhanced error handling
      if (error instanceof Error && 'response' in error) {
        const httpError = error as { response: { status: number } };
        if (httpError.response?.status === 404) {
          throw new Error('Không tìm thấy cuộc hẹn');
        } else if (httpError.response?.status === 400) {
          throw new Error('Thông tin chuyển lịch không hợp lệ');
        } else if (httpError.response?.status === 409) {
          throw new Error('Thời gian này đã có lịch hẹn khác');
        }
      }
      throw error;
    }
  },
  cancelAppointment: async (
    id: string,
    request: CancelAppointmentRequest
  ): Promise<ActionAppointmentResponse> => {
    // Validate input
    if (!id) {
      throw new Error('ID cuộc hẹn là bắt buộc');
    }

    if (!request.cancellationReason || request.cancellationReason.trim().length === 0) {
      throw new Error('Lý do hủy cuộc hẹn là bắt buộc');
    }

    try {
      const response = await apiService.patch<ActionAppointmentResponse, CancelAppointmentRequest>(
        `/api/appointments/${id}/cancel`,
        {
          cancellationReason: request.cancellationReason.trim(),
          notes: request.notes?.trim() || '',
        }
      );
      return response.data;
    } catch (error) {
      // Enhanced error handling
      if (error instanceof Error && 'response' in error) {
        const httpError = error as { response: { status: number } };
        if (httpError.response?.status === 404) {
          throw new Error('Không tìm thấy cuộc hẹn');
        } else if (httpError.response?.status === 400) {
          throw new Error('Thông tin hủy cuộc hẹn không hợp lệ');
        } else if (httpError.response?.status === 409) {
          throw new Error('Cuộc hẹn này không thể hủy (có thể đã hoàn thành hoặc đã hủy)');
        } else if (httpError.response?.status === 403) {
          throw new Error('Bạn không có quyền hủy cuộc hẹn này');
        }
      }
      throw error;
    }
  },
  getAppointmentById: async (id: string): Promise<AppointmentResponse> => {
    if (!id) {
      throw new Error('ID cuộc hẹn là bắt buộc');
    }

    try {
      const response = await apiService.get<AppointmentResponse>(`/api/appointments/${id}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        throw new Error('Không tìm thấy cuộc hẹn');
      } else if (error.response?.status === 403) {
        throw new Error('Bạn không có quyền xem cuộc hẹn này');
      }
      throw error;
    }
  },

  // Get all appointments for the current user
  getUserAppointments: async (): Promise<UserAppointmentsResponse> => {
    const response = await apiService.get<UserAppointmentsResponse>('/api/appointments/user');
    return response.data;
  },
};

export default appointmentService;

export const validateRescheduleDate = (date: string): boolean => {
  const appointmentDate = new Date(date);
  const now = new Date();

  // Check if date is valid
  if (isNaN(appointmentDate.getTime())) {
    return false;
  }

  // Check if date is in the future
  if (appointmentDate <= now) {
    return false;
  }

  return true;
};
