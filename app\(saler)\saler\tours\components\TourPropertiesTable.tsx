'use client';

import type React from 'react';
import { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, ChevronLeft, ChevronRight } from 'lucide-react';
import { debounce } from 'lodash';
import { useSalerTours, useUpdateTourStatus } from '@/hooks/useTour';
import { getTourStatusDisplay } from '@/utils/tour';
import { formatDateWithLocale, formatTimeFromISOString } from '@/utils/dates/formatDate';

interface TourPropertiesTableProps {
  disabled?: boolean;
}

export function TourPropertiesTable({ disabled }: TourPropertiesTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 5; // Number of tours per page
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce function using lodash.debounce
  const debouncedUpdate = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedSearchTerm(value);
        setPageNumber(1); // Reset page on search change
      }, 500), // 500ms debounce delay
    []
  );

  // Update searchTerm immediately, but debounce API call
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedUpdate(value);
  };

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedUpdate.cancel();
    };
  }, [debouncedUpdate]);

  // Use the useSalerTours hook with the debounced search term
  const { tourProperties, isLoading, isError, error, totalPages } = useSalerTours({
    searchTerm: debouncedSearchTerm,
    pageNumber,
    pageSize,
    sortBy: 'VisitTimeStart',
  });

  // Use the update tour status hook
  const updateTourStatus = useUpdateTourStatus();

  useEffect(() => {
    if (pageNumber > totalPages && totalPages > 0) {
      setPageNumber(totalPages);
    }
  }, [totalPages, pageNumber]);

  const handleApproveTour = (tourId: string) => {
    updateTourStatus.mutate({ id: tourId, status: 'Approved' });
  };

  const handleRejectTour = (tourId: string) => {
    updateTourStatus.mutate({ id: tourId, status: 'Rejected' });
  };

  const isEmpty = !isLoading && tourProperties.length === 0;
  const isTableDisabled = disabled || updateTourStatus.isPending;

  return (
    <div className="space-y-4">
      <Input
        placeholder="Tìm kiếm lịch trình theo tên..."
        value={searchTerm}
        onChange={handleSearchChange}
        disabled={isTableDisabled}
      />
      {isLoading ? (
        // Skeleton loading state for the table
        <div className="py-8">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Tên Lịch Trình</TableHead>
                <TableHead>Thời gian bắt đầu</TableHead>
                <TableHead>Thời gian kết thúc</TableHead>
                <TableHead>Ngày Bắt Đầu</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead className="text-right">Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: pageSize }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse" />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="h-8 w-32 bg-gray-200 rounded animate-pulse ml-auto" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Đang tìm kiếm lịch trình...</span>
          </div>
        </div>
      ) : isError ? (
        <div className="text-center text-red-500 py-8">
          <p>{error?.message || 'Không thể tải danh sách lịch trình.'}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Thử lại
          </Button>
        </div>
      ) : isEmpty ? (
        <div className="text-center py-8 text-muted-foreground">
          {searchTerm ? 'Không tìm thấy lịch trình phù hợp.' : 'Bắt đầu tìm kiếm lịch trình.'}
        </div>
      ) : (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Tên Lịch Trình</TableHead>
                <TableHead>Thời gian bắt đầu</TableHead>
                <TableHead>Thời gian kết thúc</TableHead>
                <TableHead>Ngày Bắt Đầu</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tourProperties.map(tour => {
                const currentStatus = tour.status || 'Pending';
                const statusDisplay = getTourStatusDisplay(currentStatus);
                // const buttonColors = getButtonStatusColors(currentStatus); // Not directly used for button classes anymore
                return (
                  <TableRow key={tour.id}>
                    <TableCell className="w-[200px] max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap font-medium">
                      {tour.response.title}
                    </TableCell>
                    <TableCell>
                      {formatTimeFromISOString(tour.visitTimeStart, { useUTC: true })}
                    </TableCell>
                    <TableCell>
                      {formatTimeFromISOString(tour.visitTimeEnd, { useUTC: true })}
                    </TableCell>
                    <TableCell>{formatDateWithLocale(tour.visitTimeStart, 'vi')}</TableCell>
                    <TableCell>
                      <span className={statusDisplay.colorClass}>{statusDisplay.text}</span>
                    </TableCell>
                    {(currentStatus === 'Pending' || currentStatus === 'Approved') && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            variant="default"
                            size="sm"
                            onClick={() => handleApproveTour(tour.id)}
                            disabled={currentStatus !== 'Pending' || isTableDisabled}
                            className={
                              currentStatus === 'Pending'
                                ? `bg-amber-400 text-amber-900 hover:bg-amber-400/90 disabled:opacity-50`
                                : currentStatus === 'Approved'
                                  ? `bg-emerald-100 text-emerald-800 hover:bg-emerald-100/90 disabled:opacity-50`
                                  : `disabled:opacity-50` // Fallback for other statuses if needed
                            }
                          >
                            {currentStatus === 'Pending'
                              ? 'Phê duyệt'
                              : currentStatus === 'Approved'
                                ? 'Đã Phê Duyệt'
                                : 'Đã hoàn thành'}
                          </Button>
                          {currentStatus === 'Pending' && (
                            <Button
                              type="button"
                              variant="default"
                              size="sm"
                              onClick={() => handleRejectTour(tour.id)}
                              disabled={isTableDisabled}
                              className={`bg-red-500 text-white hover:bg-red-600 disabled:opacity-50`}
                            >
                              Từ chối
                            </Button>
                          )}
                          {updateTourStatus.isPending && (
                            <Loader2 className="h-4 w-4 animate-spin text-primary" />
                          )}
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          <div className="flex justify-around items-center !mt-10 gap-4">
            <Button
              variant="outline"
              onClick={() => setPageNumber(prev => Math.max(1, prev - 1))}
              disabled={pageNumber <= 1 || isTableDisabled}
              type="button"
            >
              <ChevronLeft className="h-4 w-4 mr-2" /> Trước
            </Button>
            <span className="text-sm text-muted-foreground">
              Trang {pageNumber} / {totalPages}
            </span>
            <Button
              type="button"
              variant="outline"
              onClick={() => setPageNumber(prev => prev + 1)}
              disabled={pageNumber >= totalPages || isTableDisabled}
            >
              Tiếp <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
