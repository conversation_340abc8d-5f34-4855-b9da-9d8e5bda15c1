// 'use client';

// import React, { useState, useEffect, useMemo } from 'react';
// import dynamic from 'next/dynamic';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import {
//   type PartyAInput,
//   type PartyBInput,
//   type ContractTermsInput,
//   type ContractClausesArticle4to6Input,
//   type ContractClausesarticle7to10Input,
// } from '../schemas';
// import { ContractData } from '../types/contract';
// import Step1 from './Step1';
// import Step2 from './Step2';
// import Step3 from './Step3';
// import Step4 from './Step4';
// import Step5 from './Step5';

// // Dynamic import for ReactQuill to avoid SSR issues
// const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
// import 'react-quill/dist/quill.snow.css';

// interface StepFormProps {
//   step: number;
//   onNext: () => void;
//   onPrev: () => void;
//   onSubmit: (data: ContractData) => void;
//   data: Partial<ContractData>;
//   onDataChange: (data: Partial<ContractData>) => void;
// }

// const StepForm: React.FC<StepFormProps> = ({
//   step,
//   onNext,
//   onPrev,
//   onSubmit,
//   data,
//   onDataChange,
// }) => {
//   const [contractContent, setContractContent] = useState('');
//   const [isContentEdited, setIsContentEdited] = useState(false);

//   // Combine current form values with existing data for preview
//   const previewData = useMemo(() => data, [data]);

//   // Helper to generate contract content from data
//   const generateContractContent = (data: Partial<ContractData>): string => {
//     // You can customize this template as needed
//     return `
//       <h2>Hợp đồng thuê nhà</h2>
//       <h3>Bên A</h3>
//       <p>${data.partyA ? JSON.stringify(data.partyA) : ''}</p>
//       <h3>Bên B</h3>
//       <p>${data.partyB ? JSON.stringify(data.partyB) : ''}</p>
//       <h3>Điều khoản</h3>
//       <p>${data.terms ? JSON.stringify(data.terms) : ''}</p>
//       <h3>Điều khoản bổ sung 4-6</h3>
//       <p>${data.clausesArticle4to6 ? JSON.stringify(data.clausesArticle4to6) : ''}</p>
//       <h3>Điều khoản bổ sung 7-9</h3>
//       <p>${data.clausesarticle7to10 ? JSON.stringify(data.clausesarticle7to10) : ''}</p>
//     `;
//   };

//   // Update contract content when data changes
//   useEffect(() => {
//     if (!isContentEdited && step === 5) {
//       const newContent = generateContractContent(previewData);
//       setContractContent(newContent);
//     }
//   }, [previewData, isContentEdited, step]);

//   // Handle step data changes
//   const handlePartyANext = (partyAData: PartyAInput) => {
//     const updatedData = { ...data, partyA: partyAData };
//     onDataChange(updatedData);
//     onNext();
//   };

//   const handlePartyBNext = (partyBData: PartyBInput) => {
//     const updatedData = { ...data, partyB: partyBData };
//     onDataChange(updatedData);
//     onNext();
//   };

//   const handleTermsNext = (termsData: ContractTermsInput) => {
//     const updatedData = { ...data, terms: termsData };
//     onDataChange(updatedData);
//     onNext();
//   };

//   const handleStep4Next = (clausesData: ContractClausesArticle4to6Input) => {
//     const updatedData = { ...data, clausesArticle4to6: clausesData };
//     onDataChange(updatedData);
//     onNext();
//   };

//   const handleStep5Next = (clausesData: ContractClausesarticle7to10Input) => {
//     const updatedData = { ...data, clausesarticle7to10: clausesData };
//     onDataChange(updatedData);
//     onNext();
//   };

//   // Handle contract content change
//   const handleContentChange = (content: string) => {
//     setContractContent(content);
//     setIsContentEdited(true);
//   };

//   // Export functions
//   const handleExportPDF = async () => {
//     try {
//       // Use exportPDFWithPdfMakeAdvanced since we have the HTML content
//       const { exportPDFWithPdfMakeAdvanced } = await import('../utils/pdfMakeExporter');
//       // Ensure data is fully populated before exporting
//       if (
//         data.partyA &&
//         data.partyB &&
//         data.terms &&
//         data.clausesArticle4to6 &&
//         data.clausesarticle7to10
//       ) {
//         await exportPDFWithPdfMakeAdvanced(data as ContractData);
//       } else {
//         alert('Vui lòng điền đầy đủ thông tin hợp đồng trước khi xuất PDF');
//       }
//     } catch {
//       alert('Có lỗi xảy ra khi xuất PDF');
//     }
//   };

//   const handleSaveContract = () => {
//     // Implement save contract logic
//     alert('Lưu hợp đồng thành công');
//   };

//   const handleFinalSubmit = () => {
//     const finalData = { ...data, contractContent } as ContractData;
//     onSubmit(finalData);
//   };

//   const renderStep = () => {
//     switch (step) {
//       case 1:
//         return <Step1 data={data.partyA || ({} as PartyAInput)} onNext={handlePartyANext} />;
//       case 2:
//         return (
//           <Step2
//             data={data.partyB || ({} as PartyBInput)}
//             onNext={handlePartyBNext}
//             onBack={onPrev}
//           />
//         );
//       case 3:
//         return (
//           <Step3
//             data={data.terms || ({} as ContractTermsInput)}
//             onNext={handleTermsNext}
//             onBack={onPrev}
//           />
//         );
//       case 4:
//         return (
//           <Step4
//             data={
//               data.clausesArticle4to6 || ({ article4to6: '' } as ContractClausesArticle4to6Input)
//             }
//             onNext={handleStep4Next}
//             onBack={onPrev}
//           />
//         );
//       case 5:
//         return (
//           <Step5
//             data={
//               data.clausesarticle7to10 || ({ article7to10: '' } as ContractClausesarticle7to10Input)
//             }
//             onNext={handleStep5Next}
//             onBack={onPrev}
//           />
//         );
//       case 6:
//         return (
//           <div className="space-y-6">
//             <div className="text-center">
//               <h2 className="text-2xl font-bold text-gray-900 mb-2">
//                 Xem trước và hoàn tất hợp đồng
//               </h2>
//               <p className="text-gray-600">
//                 Kiểm tra lại nội dung hợp đồng và thực hiện các thao tác cuối cùng
//               </p>
//             </div>

//             <div className="flex justify-between space-x-4">
//               <Button type="button" variant="outline" onClick={onPrev} className="px-6 py-2">
//                 ← Quay lại
//               </Button>
//               <div className="flex space-x-2">
//                 <Button
//                   type="button"
//                   onClick={handleExportPDF}
//                   className="px-4 py-2 bg-green-600 hover:bg-green-700"
//                 >
//                   📄 Xuất PDF
//                 </Button>
//                 <Button
//                   type="button"
//                   onClick={handleSaveContract}
//                   className="px-4 py-2 bg-blue-600 hover:bg-blue-700"
//                 >
//                   💾 Lưu hợp đồng
//                 </Button>
//                 <Button
//                   type="button"
//                   onClick={handleFinalSubmit}
//                   className="px-4 py-2 bg-purple-600 hover:bg-purple-700"
//                 >
//                   ✅ Hoàn tất
//                 </Button>
//               </div>
//             </div>
//           </div>
//         );
//       default:
//         return null;
//     }
//   };
//   return (
//     <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-screen">
//       {/* Left side - Form */}
//       <div className="p-6 overflow-y-auto">{renderStep()}</div>

//       {/* Right side - Contract Preview */}
//       {step === 6 && (
//         <div className="p-6 bg-gray-50 overflow-y-auto">
//           <Card className="h-full">
//             <CardHeader>
//               <CardTitle>Xem trước hợp đồng</CardTitle>
//             </CardHeader>
//             <CardContent className="h-full">
//               <div style={{ height: 'calc(100vh - 200px)' }}>
//                 <ReactQuill
//                   value={contractContent}
//                   onChange={handleContentChange}
//                   theme="snow"
//                   style={{ height: '100%' }}
//                   modules={{
//                     toolbar: [
//                       [{ header: [1, 2, 3, false] }],
//                       ['bold', 'italic', 'underline'],
//                       [{ list: 'ordered' }, { list: 'bullet' }],
//                       [{ align: [] }],
//                       ['clean'],
//                     ],
//                   }}
//                 />
//               </div>
//             </CardContent>
//           </Card>
//         </div>
//       )}
//     </div>
//   );
// };

// export default StepForm;
