import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Home, Bookmark } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-6 p-8">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-muted-foreground">404</h1>
          <h2 className="text-2xl font-bold text-foreground">Không tìm thấy bộ sưu tập</h2>
        </div>
        <p className="text-muted-foreground max-w-md">
          Bộ sưu tập bạn đang tìm kiếm không tồn tại hoặc đã được gỡ bỏ. Vui lòng kiểm tra lại đường
          dẫn hoặc xem các bộ sưu tập khác.
        </p>
        <div className="flex gap-4 justify-center">
          <Button asChild>
            <Link href="/myrevo">
              <Bookmark className="mr-2 h-4 w-4" />
              Xem bộ sưu tập
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Về trang chủ
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
