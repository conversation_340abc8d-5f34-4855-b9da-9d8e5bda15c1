import { ReactNode, Suspense } from 'react';
import { ThemeProvider } from '@/lib/providers/themeProvider';
import { QueryProvider } from '@/lib/providers/queryProvider';
import { AuthProvider } from '@/lib/providers/authProvider';
import { Property } from '../api/services/fetchProperty';
import ChatProvider from './chatProvider';

interface ProvidersProps {
  children: ReactNode;
  property?: Property;
}

export function Providers({ children, property }: ProvidersProps) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      <QueryProvider>
        <Suspense>
          <AuthProvider>
            <ChatProvider property={property}>{children}</ChatProvider>
          </AuthProvider>
        </Suspense>
      </QueryProvider>
    </ThemeProvider>
  );
}
