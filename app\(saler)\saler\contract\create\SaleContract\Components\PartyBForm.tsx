import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { partyBSchema, PartyBInput } from '../schemas';
import { useUploadPropertyImage } from '@/hooks/useAttachment';

interface PartyBFormProps {
  data: PartyBInput;
  onNext: (data: PartyBInput) => void;
  onBack?: () => void;
}

const PartyBForm: React.FC<PartyBFormProps> = ({ data, onNext, onBack }) => {
  const form = useForm<PartyBInput>({
    resolver: zodResolver(partyBSchema),
    defaultValues: data,
  });

  const {
    setValue,
    trigger,
    formState: { errors },
  } = form;

  const [uploadedFiles, setUploadedFiles] = useState<string[]>(data.idVerifications || []);
  const updateApi = useUploadPropertyImage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Sync uploadedFiles with React Hook Form
  useEffect(() => {
    setValue('idVerifications', uploadedFiles);
    trigger('idVerifications');
  }, [uploadedFiles, setValue, trigger]);

  const handleFileUpload = async (files: FileList | null) => {
    if (files && files.length > 0) {
      setIsUploading(true);
      try {
        const uploadResponse = await updateApi.mutateAsync({
          files: Array.from(files),
        });
        const newFileUrls = uploadResponse.data.map(file => file.fileUrl);
        const allFiles = [...uploadedFiles, ...newFileUrls];
        setUploadedFiles(allFiles);
        // Update React Hook Form value and trigger validation
        setValue('idVerifications', allFiles);
        await trigger('idVerifications');
      } catch (error) {
        toast.error('Lỗi khi tải lên tài liệu');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const onSubmit = (formData: PartyBInput) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Thông tin Bên Mua</CardTitle>
        <CardDescription>Vui lòng điền đầy đủ thông tin của bên mua nhà ở</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Họ và tên *</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập họ và tên đầy đủ" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="idNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số CMND/CCCD *</FormLabel>
                    <FormControl>
                      <Input placeholder="Số CMND/CCCD" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="idIssuedDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày cấp *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="idIssuedPlace"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nơi cấp *</FormLabel>
                  <FormControl>
                    <Input placeholder="Nơi cấp CMND/CCCD" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="permanentAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ thường trú *</FormLabel>
                  <FormControl>
                    <Input placeholder="Địa chỉ thường trú" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contactAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Địa chỉ liên hệ *</FormLabel>
                  <FormControl>
                    <Input placeholder="Địa chỉ liên hệ" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Điện thoại *</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="Số điện thoại" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="fax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fax</FormLabel>
                    <FormControl>
                      <Input placeholder="Số fax" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="accountNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số tài khoản *</FormLabel>
                    <FormControl>
                      <Input placeholder="Số tài khoản ngân hàng" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bankName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngân hàng *</FormLabel>
                    <FormControl>
                      <Input placeholder="Tên ngân hàng" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="bankCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã ngân hàng *</FormLabel>
                    <FormControl>
                      <Input placeholder="Mã ngân hàng" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="Địa chỉ email *" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="taxCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mã số thuế *</FormLabel>
                    <FormControl>
                      <Input placeholder="Mã số thuế" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* ID Verification Upload */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Ảnh CCCD/Hộ chiếu *</Label>

                <div className="space-y-2">
                  <Input
                    type="file"
                    accept="image/*,.pdf"
                    multiple
                    ref={fileInputRef}
                    onChange={e => handleFileUpload(e.target.files)}
                    disabled={isUploading}
                  />
                  {isUploading && (
                    <p className="text-sm text-muted-foreground">Đang tải lên tài liệu...</p>
                  )}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-muted-foreground">
                        Đã tải lên {uploadedFiles.length} tài liệu
                      </p>
                    </div>
                  )}
                </div>
                {errors.idVerifications && (
                  <p className="text-sm text-red-500">{errors.idVerifications.message}</p>
                )}
              </div>
            </div>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Quay lại
                </Button>
              )}
              <Button type="submit" className="ml-auto">
                Tiếp tục
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default PartyBForm;
