'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  UserPlus,
  Mail,
  Phone,
  Trash2,
  Send,
  Copy,
  Check,
  Users,
  Crown,
  Shield,
  Eye,
  Edit,
  Ban,
  Search,
} from 'lucide-react';

interface Member {
  id: string;
  contact: string;
  type: 'email' | 'phone';
  role: string;
  status: 'pending' | 'active' | 'inactive';
  joinedDate?: string;
  lastActive?: string;
}

interface AgentInvitationPageProps {
  onBack?: () => void;
}

const AgentInvitationPage: React.FC<AgentInvitationPageProps> = ({ onBack }) => {
  const router = useRouter();
  const [newMemberContact, setNewMemberContact] = useState('');
  const [newMemberRole, setNewMemberRole] = useState('member');
  const [members, setMembers] = useState<Member[]>([
    {
      id: '1',
      contact: '<EMAIL>',
      type: 'email',
      role: 'manager',
      status: 'active',
      joinedDate: '2024-01-15',
      lastActive: '2 giờ trước',
    },
    {
      id: '2',
      contact: '0123456789',
      type: 'phone',
      role: 'member',
      status: 'pending',
      joinedDate: '2024-01-20',
    },
    {
      id: '3',
      contact: '<EMAIL>',
      type: 'email',
      role: 'viewer',
      status: 'active',
      joinedDate: '2024-01-10',
      lastActive: '1 ngày trước',
    },
  ]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [copiedInviteLink, setCopiedInviteLink] = useState(false);

  const memberRoles = [
    {
      id: 'admin',
      label: 'Quản trị viên',
      description: 'Toàn quyền quản lý',
      icon: Crown,
      color: 'text-purple-600 bg-purple-100',
    },
    {
      id: 'manager',
      label: 'Quản lý',
      description: 'Quản lý nhóm và dự án',
      icon: Shield,
      color: 'text-blue-600 bg-blue-100',
    },
    {
      id: 'member',
      label: 'Thành viên',
      description: 'Tham gia và thực hiện công việc',
      icon: Users,
      color: 'text-green-600 bg-green-100',
    },
    {
      id: 'viewer',
      label: 'Người xem',
      description: 'Chỉ xem, không chỉnh sửa',
      icon: Eye,
      color: 'text-gray-600 bg-gray-100',
    },
  ];

  const statusConfig = {
    active: { label: 'Hoạt động', color: 'bg-green-100 text-green-800' },
    pending: { label: 'Chờ xác nhận', color: 'bg-yellow-100 text-yellow-800' },
    inactive: { label: 'Không hoạt động', color: 'bg-gray-100 text-gray-800' },
  };

  const detectContactType = (contact: string): 'email' | 'phone' => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[+]?\d[\d\s\-()]{9,}$/;
    if (emailRegex.test(contact)) return 'email';
    if (phoneRegex.test(contact)) return 'phone';
    return 'email';
  };

  const addMember = () => {
    if (!newMemberContact.trim()) return;
    const contactType = detectContactType(newMemberContact);
    const newMember: Member = {
      id: Date.now().toString(),
      contact: newMemberContact.trim(),
      type: contactType,
      role: newMemberRole,
      status: 'pending',
      joinedDate: new Date().toISOString().split('T')[0],
    };
    setMembers(prev => [newMember, ...prev]);
    setNewMemberContact('');
    setNewMemberRole('member');
  };

  const removeMember = (memberId: string) => {
    setMembers(prev => prev.filter(member => member.id !== memberId));
  };

  const updateMemberRole = (memberId: string, newRole: string) => {
    setMembers(prev =>
      prev.map(member => (member.id === memberId ? { ...member, role: newRole } : member))
    );
  };

  const copyInviteLink = () => {
    const inviteLink = 'https://revoland.vn/invite/abc123';
    navigator.clipboard.writeText(inviteLink);
    setCopiedInviteLink(true);
    setTimeout(() => setCopiedInviteLink(false), 2000);
  };

  const filteredMembers = members.filter(member => {
    const matchesSearch = member.contact.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || member.role === filterRole;
    const matchesStatus = filterStatus === 'all' || member.status === filterStatus;
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleConfig = (roleId: string) => {
    return memberRoles.find(role => role.id === roleId) || memberRoles[2];
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack ? onBack : () => router.back()}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý Thành viên</h1>
              <p className="text-gray-600">Mời và quản lý thành viên trong workspace</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={copyInviteLink}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl border transition-all ${
                copiedInviteLink
                  ? 'border-green-500 bg-green-50 text-green-600'
                  : 'border-gray-200 bg-white text-gray-600 hover:border-gray-300'
              }`}
            >
              {copiedInviteLink ? <Check size={16} /> : <Copy size={16} />}
              <span className="text-sm font-medium">
                {copiedInviteLink ? 'Đã sao chép!' : 'Sao chép link mời'}
              </span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Invite New Member */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center">
                  <UserPlus size={20} className="text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Mời Thành viên Mới</h2>
              </div>
              <p className="text-gray-600">Thêm thành viên mới vào workspace của bạn</p>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    Email hoặc Số điện thoại
                  </label>
                  <input
                    type="text"
                    value={newMemberContact}
                    onChange={e => setNewMemberContact(e.target.value)}
                    onKeyPress={e => e.key === 'Enter' && addMember()}
                    className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                    placeholder="<EMAIL> hoặc 0123456789"
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">Vai trò</label>
                  <select
                    value={newMemberRole}
                    onChange={e => setNewMemberRole(e.target.value)}
                    className="w-full px-4 py-4 bg-white border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    {memberRoles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <button
                  onClick={addMember}
                  disabled={!newMemberContact.trim()}
                  className={`flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all ${
                    newMemberContact.trim()
                      ? 'bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-lg shadow-red-500/25 hover:shadow-red-500/40'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <Send size={18} />
                  Gửi lời mời
                </button>
              </div>
            </div>
          </div>

          {/* Members List */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Danh sách Thành viên ({filteredMembers.length})
                  </h2>
                  <p className="text-gray-600">Quản lý và theo dõi hoạt động của thành viên</p>
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                      placeholder="Tìm kiếm thành viên..."
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <select
                    value={filterRole}
                    onChange={e => setFilterRole(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="all">Tất cả vai trò</option>
                    {memberRoles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.label}
                      </option>
                    ))}
                  </select>

                  <select
                    value={filterStatus}
                    onChange={e => setFilterStatus(e.target.value)}
                    className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                  >
                    <option value="all">Tất cả trạng thái</option>
                    <option value="active">Hoạt động</option>
                    <option value="pending">Chờ xác nhận</option>
                    <option value="inactive">Không hoạt động</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-100">
              {filteredMembers.length === 0 ? (
                <div className="p-12 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users size={32} className="text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Không tìm thấy thành viên
                  </h3>
                  <p className="text-gray-600">Thử thay đổi bộ lọc hoặc mời thành viên mới</p>
                </div>
              ) : (
                filteredMembers.map(member => {
                  const roleConfig = getRoleConfig(member.role);
                  const statusConfig_ = statusConfig[member.status];

                  return (
                    <div key={member.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                            {member.type === 'email' ? (
                              <Mail size={20} className="text-white" />
                            ) : (
                              <Phone size={20} className="text-white" />
                            )}
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                              <p className="text-gray-900 font-semibold">{member.contact}</p>
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig_.color}`}
                              >
                                {statusConfig_.label}
                              </span>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-gray-600">
                              <div
                                className={`flex items-center gap-2 px-2 py-1 rounded-lg ${roleConfig.color}`}
                              >
                                <roleConfig.icon size={14} />
                                <span className="font-medium">{roleConfig.label}</span>
                              </div>

                              <span>
                                Tham gia: {new Date(member.joinedDate!).toLocaleDateString('vi-VN')}
                              </span>

                              {member.lastActive && <span>Hoạt động: {member.lastActive}</span>}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <select
                            value={member.role}
                            onChange={e => updateMemberRole(member.id, e.target.value)}
                            className="px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:border-red-500 focus:outline-none focus:ring-1 focus:ring-red-100 transition-all"
                          >
                            {memberRoles.map(role => (
                              <option key={role.id} value={role.id}>
                                {role.label}
                              </option>
                            ))}
                          </select>

                          <div className="flex items-center gap-1">
                            <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all">
                              <Edit size={16} />
                            </button>
                            <button className="p-2 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-all">
                              <Ban size={16} />
                            </button>
                            <button
                              onClick={() => removeMember(member.id)}
                              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users size={24} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{members.length}</p>
                  <p className="text-gray-600 text-sm">Tổng thành viên</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <Check size={24} className="text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {members.filter(m => m.status === 'active').length}
                  </p>
                  <p className="text-gray-600 text-sm">Đang hoạt động</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Send size={24} className="text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {members.filter(m => m.status === 'pending').length}
                  </p>
                  <p className="text-gray-600 text-sm">Chờ xác nhận</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Crown size={24} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {members.filter(m => m.role === 'admin' || m.role === 'manager').length}
                  </p>
                  <p className="text-gray-600 text-sm">Quản lý</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentInvitationPage;
