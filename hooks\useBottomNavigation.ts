import { useState, useEffect } from 'react';

let isVisible = true;
let listeners: ((visible: boolean) => void)[] = [];

export function useBottomNavigation() {
  const [visible, setVisible] = useState(isVisible);

  useEffect(() => {
    const listener = (newVisible: boolean) => {
      setVisible(newVisible);
    };

    listeners.push(listener);
    setVisible(isVisible);

    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);

  const setBottomNavigationVisible = (newVisible: boolean) => {
    console.log('Setting bottom navigation visible:', newVisible);
    isVisible = newVisible;
    listeners.forEach(listener => listener(newVisible));
  };

  return { isVisible: visible, setBottomNavigationVisible };
}
