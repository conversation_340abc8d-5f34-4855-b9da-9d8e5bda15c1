import React, { useEffect } from 'react';
import { DollarSign, Info, CreditCard, PiggyBank, Calculator } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { PriceDetail, TransactionType } from '@/lib/api/services/fetchProperty';

interface FormData {
  priceDetails: PriceDetail;
  transactionType: TransactionType;
  propertyDetails: {
    landArea?: number;
    buildingArea?: number;
  };
}

interface PricingDetailsFormProps {
  formData: FormData;
  handleSelectChange: (id: string, value: string) => void;
  handleNext: () => void;
}

const paymentMethodOptions = [
  { value: 'Cash', label: 'Tiền mặt', icon: DollarSign },
  { value: 'BankTransfer', label: '<PERSON>y<PERSON>n khoản ngân hàng', icon: CreditCard },
  { value: 'CreditCard', label: 'Thẻ tín dụng', icon: CreditCard },
  { value: 'Other', label: 'Khác', icon: PiggyBank },
];

const PricingDetailsForm: React.FC<PricingDetailsFormProps> = ({
  formData,
  handleSelectChange,
  handleNext,
}) => {
  // Default active tab based on transaction type
  const defaultTab = formData.transactionType === TransactionType.FOR_RENT ? 'rental' : 'sale';
  const [activeTab, setActiveTab] = React.useState(defaultTab);

  // When transaction type changes, update the active tab
  useEffect(() => {
    setActiveTab(formData.transactionType === TransactionType.FOR_RENT ? 'rental' : 'sale');
  }, [formData.transactionType]);

  // Format number with thousand separators
  const formatNumber = (value: number | undefined) => {
    if (!value && value !== 0) return '';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  // Format large numbers to Vietnamese currency notation
  const formatVietnameseCurrency = (value: number | undefined): string => {
    if (!value || value === 0) return '';

    const billion = **********; // 1 tỷ
    const million = 1000000; // 1 triệu
    const thousand = 1000; // 1 nghìn

    if (value >= billion) {
      const ty = Math.floor(value / billion);
      const remaining = value % billion;

      if (remaining === 0) {
        return `${ty} tỷ`;
      } else if (remaining >= million) {
        const trieu = Math.floor(remaining / million);
        const remainingAfterTrieu = remaining % million;

        if (remainingAfterTrieu === 0) {
          return `${ty} tỷ ${trieu} triệu`;
        } else if (remainingAfterTrieu >= thousand) {
          const ngan = Math.floor(remainingAfterTrieu / thousand);
          return `${ty} tỷ ${trieu} triệu ${ngan} nghìn`;
        } else {
          return `${ty} tỷ ${trieu} triệu ${remainingAfterTrieu}`;
        }
      } else if (remaining >= thousand) {
        const ngan = Math.floor(remaining / thousand);
        return `${ty} tỷ ${ngan} nghìn`;
      } else {
        return `${ty} tỷ ${remaining}`;
      }
    } else if (value >= million) {
      const trieu = Math.floor(value / million);
      const remaining = value % million;

      if (remaining === 0) {
        return `${trieu} triệu`;
      } else if (remaining >= thousand) {
        const ngan = Math.floor(remaining / thousand);
        const remainingAfterNgan = remaining % thousand;

        if (remainingAfterNgan === 0) {
          return `${trieu} triệu ${ngan} nghìn`;
        } else {
          return `${trieu} triệu ${ngan} nghìn ${remainingAfterNgan}`;
        }
      } else {
        return `${trieu} triệu ${remaining}`;
      }
    } else if (value >= thousand) {
      const ngan = Math.floor(value / thousand);
      const remaining = value % thousand;

      if (remaining === 0) {
        return `${ngan} nghìn`;
      } else {
        return `${ngan} nghìn ${remaining}`;
      }
    } else {
      return value.toString();
    }
  };

  // Handle manual input with formatting
  const handlePriceInput = (field: string, value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    handleSelectChange(`priceDetails.${field}`, numericValue);
  };

  // Calculate price per square meter
  const calculatePricePerSquareMeter = () => {
    const price =
      formData.transactionType === TransactionType.FOR_RENT
        ? formData.priceDetails.rentalPrice
        : formData.priceDetails.salePrice;

    const area = formData.propertyDetails.buildingArea || formData.propertyDetails.landArea;

    if (price && area && area > 0) {
      const pricePerSqm = Math.round(price / area);
      handleSelectChange('priceDetails.pricePerSquareMeter', pricePerSqm.toString());
    }
  };

  // Auto-calculate price per square meter when price or area changes
  useEffect(() => {
    const price =
      formData.transactionType === TransactionType.FOR_RENT
        ? formData.priceDetails.rentalPrice
        : formData.priceDetails.salePrice;

    const area = formData.propertyDetails.buildingArea || formData.propertyDetails.landArea;

    if (price && area && area > 0) {
      const pricePerSqm = Math.round(price / area);
      // Only update if the calculated value is different from current value
      if (pricePerSqm !== formData.priceDetails.pricePerSquareMeter) {
        handleSelectChange('priceDetails.pricePerSquareMeter', pricePerSqm.toString());
      }
    }
  }, [
    formData.priceDetails.salePrice,
    formData.priceDetails.rentalPrice,
    formData.propertyDetails.buildingArea,
    formData.propertyDetails.landArea,
    formData.transactionType,
    formData.priceDetails.pricePerSquareMeter,
    handleSelectChange,
  ]);

  const handlePaymentMethodChange = (value: string) => {
    const currentMethods = Array.isArray(formData.priceDetails.paymentMethods)
      ? formData.priceDetails.paymentMethods
      : [];

    const newMethods = currentMethods.includes(value)
      ? currentMethods.filter(method => method !== value)
      : [...currentMethods, value];

    // Convert array to string for form data update
    handleSelectChange('priceDetails.paymentMethods', JSON.stringify(newMethods));
  };

  // Helper function to check if a payment method is selected
  const isPaymentMethodSelected = (method: string): boolean => {
    const methods = formData.priceDetails.paymentMethods;
    if (!methods) return false;

    // Handle if it's an array
    if (Array.isArray(methods)) {
      // Direct match in array
      if (methods.includes(method)) {
        return true;
      }

      // Special case: Array with a single string element that contains JSON
      if (methods.length === 1 && typeof methods[0] === 'string') {
        try {
          const parsedArray = JSON.parse(methods[0]);
          if (Array.isArray(parsedArray) && parsedArray.includes(method)) {
            return true;
          }
        } catch (e) {
          // Not a JSON string
        }
      }

      return false;
    }

    // Handle string representation
    if (typeof methods === 'string') {
      try {
        const parsedMethods = JSON.parse(methods);
        return Array.isArray(parsedMethods) && parsedMethods.includes(method);
      } catch (e) {
        // Not a JSON string
      }
    }

    return false;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Header Section */}
      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <DollarSign className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">Thông tin giá</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Nhập thông tin giá cho bất động sản
            </p>
          </div>
        </div>
      </div>

      {/* Pricing Information Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4 md:pb-6">
          <CardTitle className="text-base md:text-lg font-medium">Thông tin giá</CardTitle>
          <CardDescription className="text-sm">Nhập thông tin giá cho bất động sản</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 md:space-y-6 lg:space-y-8">
          {/* Tabs for Sale vs Rental */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger
                value="sale"
                disabled={formData.transactionType === TransactionType.FOR_RENT}
                className={
                  formData.transactionType === TransactionType.FOR_RENT ? 'opacity-50' : ''
                }
              >
                Bán
              </TabsTrigger>
              <TabsTrigger
                value="rental"
                disabled={formData.transactionType === TransactionType.FOR_SALE}
                className={
                  formData.transactionType === TransactionType.FOR_SALE ? 'opacity-50' : ''
                }
              >
                Cho thuê
              </TabsTrigger>
            </TabsList> */}

            {/* For Sale Content */}
            <TabsContent value="sale" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="priceDetails.salePrice" className="text-sm font-medium">
                      Giá bán
                    </Label>
                    <span className="text-destructive text-sm">*</span>
                  </div>
                  <div className="relative">
                    <Input
                      id="priceDetails.salePrice"
                      value={formatNumber(formData.priceDetails.salePrice || 0)}
                      onChange={e => handlePriceInput('salePrice', e.target.value)}
                      className="h-11 pl-12"
                      placeholder="Nhập giá bán"
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                      ₫
                    </span>
                  </div>
                  {/* {formData.priceDetails.salePrice && formData.priceDetails.salePrice > 0 && (
                    <p className="text-xs text-blue-600 font-medium">
                      {formatVietnameseCurrency(formData.priceDetails.salePrice)}
                    </p>
                  )} */}
                  <p className="text-xs text-muted-foreground">
                    Giá bán của bất động sản:{' '}
                    {formatVietnameseCurrency(formData.priceDetails.salePrice || 0)}
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor="priceDetails.pricePerSquareMeter"
                      className="text-sm font-medium"
                    >
                      Giá mỗi mét vuông
                    </Label>
                    <span className="text-xs text-muted-foreground">
                      Diện tích đã nhập:{' '}
                      {formData.propertyDetails.buildingArea ||
                        formData.propertyDetails.landArea ||
                        0}{' '}
                      m²
                    </span>
                  </div>
                  <div className="relative">
                    <Input
                      id="priceDetails.pricePerSquareMeter"
                      value={formatNumber(formData.priceDetails.pricePerSquareMeter || 0)}
                      onChange={e => handlePriceInput('pricePerSquareMeter', e.target.value)}
                      className="h-11 pl-12 pr-20"
                      placeholder="Nhập giá mỗi mét vuông"
                      disabled={
                        !formData.priceDetails.salePrice && !formData.priceDetails.rentalPrice
                      }
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                      ₫
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={calculatePricePerSquareMeter}
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-9 px-2"
                      disabled={
                        !formData.priceDetails.salePrice && !formData.priceDetails.rentalPrice
                      }
                    >
                      <Calculator className="h-4 w-4" />
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    Giá mỗi mét vuông của bất động sản:{' '}
                    {formatVietnameseCurrency(formData.priceDetails.pricePerSquareMeter || 0)}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <Label htmlFor="priceDetails.maintenanceFee" className="text-sm font-medium">
                  Phí bảo trì
                </Label>
                <div className="relative">
                  <Input
                    id="priceDetails.maintenanceFee"
                    value={formatNumber(formData.priceDetails.maintenanceFee)}
                    onChange={e => handlePriceInput('maintenanceFee', e.target.value)}
                    className="h-11 pl-12"
                    placeholder="Nhập phí bảo trì"
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                    ₫
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Phí bảo trì của bất động sản:{' '}
                  {formatVietnameseCurrency(formData.priceDetails.maintenanceFee || 0)}
                </p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="priceDetails.depositAmount" className="text-sm font-medium">
                  Tiền đặt cọc
                </Label>
                <div className="relative">
                  <Input
                    id="priceDetails.depositAmount"
                    value={formatNumber(formData.priceDetails.depositAmount)}
                    onChange={e => handlePriceInput('depositAmount', e.target.value)}
                    className="h-11 pl-12"
                    placeholder="Nhập tiền đặt cọc"
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                    ₫
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Tiền đặt cọc của bất động sản:{' '}
                  {formatVietnameseCurrency(formData.priceDetails.depositAmount || 0)}
                </p>
              </div>
            </TabsContent>

            {/*    Rental Content */}
            <TabsContent value="rental" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="priceDetails.rentalPrice" className="text-sm font-medium">
                      Giá cho thuê
                    </Label>
                    <span className="text-destructive text-sm">*</span>
                  </div>
                  <div className="relative">
                    <Input
                      id="priceDetails.rentalPrice"
                      value={formatNumber(formData.priceDetails.rentalPrice || 0)}
                      onChange={e => handlePriceInput('rentalPrice', e.target.value)}
                      className="h-11 pl-12"
                      placeholder="Nhập giá cho thuê"
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                      ₫
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Giá cho thuê của bất động sản:{' '}
                    {formatVietnameseCurrency(formData.priceDetails.rentalPrice || 0)}
                  </p>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="priceDetails.maintenanceFee" className="text-sm font-medium">
                    Phí bảo trì
                  </Label>
                  <div className="relative">
                    <Input
                      id="priceDetails.maintenanceFee"
                      value={formatNumber(formData.priceDetails.maintenanceFee || 0)}
                      onChange={e => handlePriceInput('maintenanceFee', e.target.value)}
                      className="h-11 pl-12"
                      placeholder="Nhập phí bảo trì"
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                      ₫
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Phí bảo trì của bất động sản:{' '}
                    {formatVietnameseCurrency(formData.priceDetails.maintenanceFee || 0)}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <Label htmlFor="priceDetails.depositAmount" className="text-sm font-medium">
                  Tiền đặt cọc
                </Label>
                <div className="relative">
                  <Input
                    id="priceDetails.depositAmount"
                    value={formatNumber(formData.priceDetails.depositAmount)}
                    onChange={e => handlePriceInput('depositAmount', e.target.value)}
                    className="h-11 pl-12"
                    placeholder="Nhập tiền đặt cọc"
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground font-medium">
                    ₫
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Tiền đặt cọc của bất động sản:{' '}
                  {formatVietnameseCurrency(formData.priceDetails.depositAmount || 0)}
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Payment Methods Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Phương thức thanh toán
          </CardTitle>
          <CardDescription>
            Chọn phương thức thanh toán bạn chấp nhận cho bất động sản này
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-3">
            {paymentMethodOptions.map(option => {
              const Icon = option.icon;
              return (
                <div
                  key={option.value}
                  className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                >
                  <Checkbox
                    id={`payment-${option.value}`}
                    checked={isPaymentMethodSelected(option.value)}
                    onCheckedChange={() => handlePaymentMethodChange(option.value)}
                  />
                  <div className="flex items-center gap-3 flex-1">
                    <div className="flex h-8 w-8 items-center justify-center rounded-md">
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="space-y-1">
                      <Label
                        htmlFor={`payment-${option.value}`}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {option.label}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        Chấp nhận thanh toán qua {option.label.toLowerCase()}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Help Card */}
      <Card className="border-dashed bg-muted/30">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Gợi ý về giá</p>
              <p className="text-xs text-muted-foreground">
                Nghiên cứu các bất động sản tương tự trong khu vực để đặt giá cạnh tranh. Xem xét
                các yếu tố như vị trí, điều kiện, và nhu cầu thị trường. Bạn có thể điều chỉnh giá
                sau này dựa trên phản hồi thị trường.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-6 border-t">
        <Button onClick={handleNext} size="lg" className="px-8">
          Tiếp theo
        </Button>
      </div>
    </div>
  );
};

export default PricingDetailsForm;
