import { z } from 'zod';

export const partyASchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  idNumber: z.string().regex(/^\d{9}$|^\d{12}$/, 'Số CMND/CCCD không hợp lệ'),
  idIssuedDate: z.string().min(1, '<PERSON><PERSON><PERSON> cấp không được để trống'),
  idIssuedPlace: z.string().min(1, 'N<PERSON>i cấp không được để trống'),
  permanentAddress: z.string().min(1, 'Địa chỉ thường trú không được để trống'),
  contactAddress: z.string().min(1, 'Địa chỉ liên hệ không được để trống'),
  email: z.string().email('<PERSON>ail không hợp lệ'),
  phone: z.string().regex(/^((\+84|84|0)[3|5|7|8|9])+([0-9]{8})$/, '<PERSON><PERSON> điện thoại không hợp lệ'),
  fax: z.string().optional(),
  accountNumber: z.string().optional(),
  bankName: z.string().optional(),
  taxCode: z.string().optional(),
  bankCode: z.string().optional(),
});

export const partyBSchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  idNumber: z.string().regex(/^\d{9}$|^\d{12}$/, 'Số CMND/CCCD không hợp lệ'),
  idIssuedDate: z.string().min(1, 'Ngày cấp không được để trống'),
  idIssuedPlace: z.string().min(1, 'Nơi cấp không được để trống'),
  permanentAddress: z.string().min(1, 'Địa chỉ thường trú không được để trống'),
  contactAddress: z.string().min(1, 'Địa chỉ liên hệ không được để trống'),
  phone: z.string().regex(/^((\+84|84|0)[3|5|7|8|9])+([0-9]{8})$/, 'Số điện thoại không hợp lệ'),
  email: z.string().email('Email không hợp lệ'),
  fax: z.string().optional(),
  accountNumber: z.string(),
  bankName: z.string(),
  bankCode: z.string(),
  idVerifications: z.array(z.string()).min(1, 'Phải có ít nhất một giấy tờ tùy thân'),
  taxCode: z.string(),
});

export const clause1To2InputSchema = z.object({
  propertyType: z.string().min(1, 'Loại tài sản không được để trống'),
  address: z.string().min(1, 'Địa chỉ không được để trống'),
  totalFloorArea: z.number().positive('Diện tích sàn phải lớn hơn 0'),
  totalLandArea: z.number().positive('Diện tích đất phải lớn hơn 0'),
  privateArea: z.number().positive('Diện tích sử dụng riêng phải lớn hơn 0'),
  sharedArea: z.number().positive('Diện tích sử dụng chung phải lớn hơn 0'),
  landOrigin: z.string().min(1, 'Nguồn gốc đất không được để trống'),
  equipmentDetails: z.string().min(1, 'Chi tiết thiết bị không được để trống'),
  legalDocuments: z.array(z.string()).min(1, 'Phải có ít nhất một tài liệu pháp lý'),
  salePrice: z.number().positive('Giá bán phải lớn hơn 0'),
  paymentMethod: z.string().min(1, 'Phương thức thanh toán không được để trống'),
  paymentSchedule: z
    .array(
      z.object({
        amount: z.number().positive('Số tiền phải lớn hơn 0'),
        paymentDate: z.string().min(1, 'Ngày thanh toán không được để trống'),
        note: z.string().optional(),
      })
    )
    .min(1, 'Phải có ít nhất một lịch thanh toán'),
});

export const clause3To6Schema = z.object({
  Clause3To6: z.string().min(1, 'Nội dung điều khoản 3-6 không được để trống'),
});

export const clause7To10Schema = z.object({
  Clause7To10: z.string().min(1, 'Nội dung điều khoản 7-10 không được để trống'),
});

export const clause11To14Schema = z.object({
  Clause11To14: z.string().min(1, 'Nội dung điều khoản 11-14 không được để trống'),
});

export const saleContractDataSchema = z.object({
  partyA: partyASchema,
  partyB: partyBSchema,
  clause1To2Input: clause1To2InputSchema,
  clause3To6: clause3To6Schema,
  clause7To10: clause7To10Schema,
  clause11To14: clause11To14Schema,
});

export type PartyAInput = z.infer<typeof partyASchema>;
export type PartyBInput = z.infer<typeof partyBSchema>;
export type Clause1To2Input = z.infer<typeof clause1To2InputSchema>;
export type Clause3To6Input = z.infer<typeof clause3To6Schema>;
export type Clause7To10Input = z.infer<typeof clause7To10Schema>;
export type Clause11To14Input = z.infer<typeof clause11To14Schema>;
export type SaleContractInput = z.infer<typeof saleContractDataSchema>;
