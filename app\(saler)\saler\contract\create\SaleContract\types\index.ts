// Types for the sale contract form
export interface PartyA {
  name: string;
  idNumber: string;
  idIssuedDate: string;
  idIssuedPlace: string;
  permanentAddress: string;
  contactAddress: string;
  phone: string;
  fax?: string;
  accountNumber: string;
  bankName: string;
  taxCode: string;
  bankCode: string; // Optional field for bank code
  email: string; // Optional field for email
}

export interface PartyB {
  name: string;
  idNumber: string;
  idIssuedDate: string;
  idIssuedPlace: string;
  permanentAddress: string;
  contactAddress: string;
  phone: string;
  fax?: string;
  accountNumber: string;
  bankName: string;
  taxCode: string;
  bankCode: string; // Optional field for bank code
  idVerifications: string[]; // Array of ID verification documents
  email: string; // Optional field for email
}

export interface InputPaymentSchedule {
  amount: number;
  paymentDate: string;
  note?: string; // Optional note for the payment
}

export interface Clause1To2Input {
  propertyType: string;
  address: string;
  totalFloorArea: number;
  totalLandArea: number;
  privateArea: number;
  sharedArea: number;
  landOrigin: string;
  equipmentDetails: string;
  legalDocuments: string[];
  salePrice: number;
  paymentMethod: string;
  paymentSchedule: InputPaymentSchedule[]; // Changed to array of InputPaymentSchedule
}

export interface Clause3To6 {
  Clause3To6: string;
}

export interface Clause7To10 {
  Clause7To10: string;
}

export interface Clause11To14 {
  Clause11To14: string;
}

export interface SaleContractData {
  partyA: PartyA;
  partyB: PartyB;
  clause1To2Input: Clause1To2Input;
  clause3To6: Clause3To6;
  clause7To10: Clause7To10;
  clause11To14: Clause11To14;
}
