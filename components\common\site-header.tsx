'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  CalendarIcon,
  Users,
  Settings,
  NotebookPen,
  Presentation,
  CreditCard,
  ChevronsUpDown,
  Sparkles,
  Bell,
  Sun,
  LogOut,
  Hash,
  Atom,
} from 'lucide-react';

import React from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';

export function SiteHeader() {
  return (
    <header className="sticky top-0 z-50 w-full bg-sidebar">
      <div className="flex h-[--header-height] w-full items-center gap-4 px-4">
        {/* Logo Section - Left */}
        <div className="relative flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium text-sm text-white">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-white text-black">
              <Atom className="size-5" />
            </div>
            OCEA Inc.
          </a>
        </div>
        {/* Search Bar and Calendar - Middle */}
        <div className="flex-1 flex items-center justify-center space-x-2">
          <div className="relative w-full max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-zinc-300" size={16} />
            <Input
              type="search"
              placeholder="Search..."
              className="pl-9 w-full h-8 bg-[#424242]  text-white border-zinc-700 focus:outline-none focus:ring-0 focus:border-transparent"
            />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-zinc-300 h-8 w-8 hover:bg-[#595959] bg-[#424242] hover:text-zinc-400"
          >
            <CalendarIcon size={16} />
          </Button>
        </div>

        {/* Right Section - Friends, Server, Avatar */}
        <div className="flex items-center space-x-2">
          {/* Friends Link */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={'/friends'}
                className="text-white rounded-lg hover:bg-zinc-700 hover:text-white p-2"
              >
                <Users className="h-[0.9rem] w-[0.9rem]" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>Friends</TooltipContent>
          </Tooltip>
          {/* Server Link */}

          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={'/spaces'}
                className="text-white rounded-lg hover:bg-zinc-700 hover:text-white p-2"
              >
                <Hash className="h-[0.9rem] w-[0.9rem]" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>Spaces</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={'/docs'}
                className="text-white rounded-lg hover:bg-zinc-700 hover:text-white p-2"
              >
                <NotebookPen className="h-[0.9rem] w-[0.9rem]" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>Doc</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={'/whiteboard'}
                className="text-white rounded-lg hover:bg-zinc-700 hover:text-white p-2"
              >
                <Presentation className="h-[0.9rem] w-[0.9rem]" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>Whiteboard</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={'/ai'}
                className="text-white rounded-lg hover:bg-zinc-700 hover:text-white p-2"
              >
                <Sparkles className="h-[0.9rem] w-[0.9rem]" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>OCEA AI</TooltipContent>
          </Tooltip>
          {/* Avatar Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage className="object-cover" src="" alt={''} />
                  <AvatarFallback className="rounded-lg bg-zinc-800 text-white">BT</AvatarFallback>
                </Avatar>
                <ChevronsUpDown className="ml-auto size-4 text-white" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              side="bottom"
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage className="object-cover" src="" alt={''} />
                    <AvatarFallback className="rounded-lg">BT</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">Bảo Toàn</span>
                    <span className="truncate text-xs dark:text-zinc-400 text-zinc-600">
                      <EMAIL>
                    </span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                  <Sparkles className="size-4" />
                  Upgrade to Pro
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  {/* <BadgeCheck /> */}
                  Account
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <CreditCard />
                  Billing
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell />
                  Notifications
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  <Settings />
                  Settings
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Sun className="size-4" />
                Light Mode
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <LogOut />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
