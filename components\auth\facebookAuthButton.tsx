import { FC, useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface FacebookAuthButtonProps {
  mode: 'login' | 'register';
  onError: (error: string) => void;
  className?: string;
}

declare global {
  interface Window {
    FB: {
      init: (options: Record<string, unknown>) => void;
      getLoginStatus: (callback: (response: unknown) => void) => void;
      login: (callback: (response: unknown) => void, options?: Record<string, unknown>) => void;
      api: (
        path: string,
        params: Record<string, unknown>,
        callback: (response: unknown) => void
      ) => void;
    };
    fbAsyncInit: () => void;
  }
}

export const FacebookAuthButton: FC<FacebookAuthButtonProps> = ({ mode, onError, className }) => {
  const { facebookLogin } = useAuth();
  const [sdkReady, setSdkReady] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isHttpsRequired, setIsHttpsRequired] = useState(false);

  // Helper function to check if we're on a secure environment
  const isSecureEnvironment = useCallback(() => {
    if (typeof window === 'undefined') return false;

    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      window.location.hostname.includes('localhost');
    const isHttps = window.location.protocol === 'https:';

    return isLocalhost || isHttps;
  }, []);

  // Check HTTPS requirement on mount
  useEffect(() => {
    if (!isSecureEnvironment()) {
      setIsHttpsRequired(true);
      onError('Facebook login requires HTTPS. Please use HTTPS or localhost.');
    }
  }, [isSecureEnvironment, onError]);

  // Initialize Facebook SDK
  useEffect(() => {
    if (!isSecureEnvironment()) {
      return;
    }

    if (window.FB) {
      setSdkReady(true);
      return;
    }

    // Suppress Facebook SDK console errors
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    window.fbAsyncInit = function () {
      // Temporarily suppress console errors during FB init
      console.error = (...args) => {
        const message = args.join(' ');
        if (
          !message.includes('Failed to fetch') &&
          !message.includes('facebook.com') &&
          !message.includes('connect.facebook.net')
        ) {
          originalConsoleError(...args);
        }
      };

      console.warn = (...args) => {
        const message = args.join(' ');
        if (
          !message.includes('Failed to fetch') &&
          !message.includes('facebook.com') &&
          !message.includes('connect.facebook.net')
        ) {
          originalConsoleWarn(...args);
        }
      };

      try {
        window.FB.init({
          appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_CONSUMER_ID || '',
          cookie: true,
          xfbml: false,
          version: 'v18.0',
        });

        setSdkReady(true);
      } catch (error) {
        console.error('Facebook SDK initialization error:', error);
        setSdkReady(true); // Still set ready to allow attempts
      } finally {
        // Restore original console methods after a delay
        setTimeout(() => {
          console.error = originalConsoleError;
          console.warn = originalConsoleWarn;
        }, 2000);
      }
    };

    const script = document.createElement('script');
    script.src = 'https://connect.facebook.net/en_US/sdk.js';
    script.async = true;
    script.defer = true;
    script.crossOrigin = 'anonymous';

    script.onerror = () => {
      onError('Failed to load Facebook SDK');
    };

    document.body.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        'script[src="https://connect.facebook.net/en_US/sdk.js"]'
      );
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
      // Restore console methods on cleanup
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
    };
  }, [isSecureEnvironment, onError]);

  const handleFacebookLogin = useCallback(() => {
    if (!isSecureEnvironment()) {
      onError('Facebook login requires HTTPS. Please use HTTPS or localhost.');
      return;
    }

    if (!sdkReady || !window.FB) {
      onError('Facebook SDK chưa sẵn sàng');
      return;
    }

    if (isLoading) {
      return;
    }

    setIsLoading(true);

    // Wrap in try-catch and handle Facebook SDK errors gracefully
    try {
      window.FB.getLoginStatus(function (response: unknown) {
        try {
          if ((response as any).status === 'connected') {
            // User is already logged in, proceed with authentication
            handleSuccessfulLogin((response as any).authResponse.accessToken);
          } else {
            // Perform login
            performFacebookLogin();
          }
        } catch (error) {
          console.warn('Facebook getLoginStatus error (non-critical):', error);
          // Still attempt login even if status check fails
          performFacebookLogin();
        }
      });
    } catch (error) {
      console.warn('Facebook API error (attempting login anyway):', error);
      // Try direct login as fallback
      performFacebookLogin();
    }
  }, [sdkReady, isLoading, facebookLogin, onError, isSecureEnvironment]);

  const performFacebookLogin = useCallback(() => {
    // Suppress fetch errors during login process
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      return originalFetch(...args).catch(error => {
        // Suppress Facebook analytics fetch errors
        if (
          args[0]?.toString().includes('facebook.com') ||
          args[0]?.toString().includes('connect.facebook.net')
        ) {
          return Promise.resolve(new Response('{}', { status: 200 }));
        }
        throw error;
      });
    };

    try {
      window.FB.login(
        function (loginResponse: unknown) {
          // Restore original fetch
          window.fetch = originalFetch;

          setIsLoading(false);

          if ((loginResponse as any).authResponse) {
            const accessToken = (loginResponse as any).authResponse.accessToken;
            handleSuccessfulLogin(accessToken);
          } else {
            handleLoginError(loginResponse);
          }
        },
        {
          scope: 'public_profile,email',
          display: 'popup',
          return_scopes: true,
          auth_type: 'rerequest',
        }
      );
    } catch (error) {
      // Restore original fetch on error
      window.fetch = originalFetch;
      setIsLoading(false);
      console.error('Facebook login error:', error);
      onError('Lỗi khi kết nối Facebook. Vui lòng thử lại.');
    }
  }, [facebookLogin, onError]);

  const handleSuccessfulLogin = useCallback(
    (accessToken: string) => {
      try {
        // Verify user data before proceeding
        window.FB.api('/me', { fields: 'id,name,email,picture' }, function (userResponse: unknown) {
          if ((userResponse as any) && (userResponse as any).email) {
            facebookLogin(accessToken);
          } else if ((userResponse as any) && !(userResponse as any).email) {
            onError('Không thể lấy thông tin email. Vui lòng kiểm tra quyền truy cập.');
          } else {
            onError('Không thể lấy thông tin người dùng. Vui lòng thử lại.');
          }
        });
      } catch (error) {
        console.error('Error getting user info:', error);
        // Still proceed with login if user info fetch fails
        facebookLogin(accessToken);
      }
    },
    [facebookLogin, onError]
  );

  const handleLoginError = useCallback(
    (loginResponse: unknown) => {
      if ((loginResponse as any).status === 'not_authorized') {
        onError('Bạn đã từ chối quyền truy cập Facebook');
      } else if ((loginResponse as any).status === 'unknown') {
        onError('Đăng nhập Facebook thất bại. Vui lòng thử lại.');
      } else {
        onError('Đăng nhập Facebook thất bại hoặc bị huỷ');
      }
    },
    [onError]
  );

  if (isHttpsRequired) {
    return (
      <Button
        type="button"
        variant="outline"
        className={`w-full !border-[#0866fe] !text-[#0866fe] h-[42px] opacity-50 cursor-not-allowed ${className || ''}`}
        disabled
      >
        <img
          src="https://cdn.shadcnstudio.com/ss-assets/brand-logo/facebook-icon.png?width=20&height=20&format=auto"
          alt="Facebook Icon"
          className="size-5"
        />
        <span className="flex flex-1 justify-center">Requires HTTPS</span>
      </Button>
    );
  }

  return (
    <Button
      type="button"
      variant="outline"
      className={`w-full`}
      onClick={handleFacebookLogin}
      disabled={!sdkReady || isLoading}
    >
      {isLoading ? (
        <Loader2 className="size-5 animate-spin" />
      ) : (
        <img
          src="https://upload.wikimedia.org/wikipedia/commons/b/b9/2023_Facebook_icon.svg"
          alt="Facebook Icon"
          className="size-5"
        />
      )}
      <span className="flex flex-1 justify-center">
        {isLoading
          ? 'Đang xử lý...'
          : mode === 'login'
            ? 'Đăng nhập với Facebook'
            : 'Đăng ký với Facebook'}
      </span>
    </Button>
  );
};
