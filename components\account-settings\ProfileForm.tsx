'use client';

import { useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Loader2 } from 'lucide-react';
import { useUpdateProfile } from '@/hooks/useUsers';
import { toast } from 'sonner';
import { User } from '@/lib/api/services/fetchUser';

interface ProfileFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  about?: string;
  userName: string;
  birthdate?: string;
  status?: string;
  yearsOfExperience?: number;
  companyName?: string;
  fieldOfWorks?: string[];
  serviceLocations?: {
    city: string;
    ward: string;
  }[];
}

interface ProfileFormProps {
  profile: ProfileFormData;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const profileFormSchema = z.object({
  fullName: z
    .string()
    .min(2, { message: 'Tên phải có ít nhất 2 ký tự.' })
    .max(50, { message: 'Tên không được dài quá 50 ký tự.' }),
  userName: z
    .string()
    .min(3, { message: 'Tên người dùng phải có ít nhất 3 ký tự.' })
    .max(30, { message: 'Tên người dùng không được dài quá 30 ký tự.' }),
  email: z.string().email({ message: 'Vui lòng nhập email hợp lệ.' }),
  phoneNumber: z
    .string()
    .min(5, { message: 'Số điện thoại phải có ít nhất 5 ký tự.' })
    .max(15, { message: 'Số điện thoại không được dài quá 15 ký tự.' })
    .optional()
    .or(z.literal('')),
  about: z
    .string()
    .max(1000, { message: 'Giới thiệu không được dài quá 1000 ký tự.' })
    .optional()
    .or(z.literal('')),
  birthDay: z.string().optional(),
  birthMonth: z.string().optional(),
  birthYear: z.string().optional(),
  status: z.enum(['Online', 'Idle', 'DoNotDisturb', 'Invisible']).optional(),
  yearsOfExperience: z.number().min(0).max(50).optional(),
  companyName: z.string().max(100).optional(),
  fieldOfWorks: z.array(z.string()).optional(),
  serviceLocations: z
    .array(
      z.object({
        city: z.string(),
        ward: z.string(),
      })
    )
    .optional(),
});

// Utility function to parse birthdate
const parseBirthdate = (birthdate?: string) => {
  if (!birthdate) return { day: '', month: '', year: '' };

  try {
    const date = new Date(birthdate);
    if (isNaN(date.getTime())) return { day: '', month: '', year: '' };

    return {
      day: date.getDate().toString(),
      month: (date.getMonth() + 1).toString(),
      year: date.getFullYear().toString(),
    };
  } catch {
    return { day: '', month: '', year: '' };
  }
};

// Utility function to create birthdate string
const createBirthdateString = (day?: string, month?: string, year?: string) => {
  if (!day || !month || !year) return '';

  try {
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    if (isNaN(date.getTime())) return '';
    return date.toISOString();
  } catch {
    return '';
  }
};

export function ProfileForm({ profile, onSuccess, onCancel }: ProfileFormProps) {
  const { mutate: updateProfile, isPending } = useUpdateProfile();

  // Parse birthdate using utility function
  const { day: birthDay, month: birthMonth, year: birthYear } = parseBirthdate(profile.birthdate);

  // Memoize select options for better performance
  const dayOptions = useMemo(() => Array.from({ length: 31 }, (_, i) => i + 1), []);

  const monthOptions = useMemo(
    () => [
      'Tháng 1',
      'Tháng 2',
      'Tháng 3',
      'Tháng 4',
      'Tháng 5',
      'Tháng 6',
      'Tháng 7',
      'Tháng 8',
      'Tháng 9',
      'Tháng 10',
      'Tháng 11',
      'Tháng 12',
    ],
    []
  );

  const yearOptions = useMemo(
    () => Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i),
    []
  );

  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: profile.fullName || '',
      userName: profile.userName || '',
      email: profile.email || '',
      phoneNumber: profile.phoneNumber || '',
      about: profile.about || '',
      birthDay,
      birthMonth,
      birthYear,
      status: (profile.status as 'Online' | 'Idle' | 'DoNotDisturb' | 'Invisible') || 'Online',
      yearsOfExperience: profile.yearsOfExperience || undefined,
      companyName: profile.companyName || '',
      fieldOfWorks: profile.fieldOfWorks || [],
      serviceLocations: profile.serviceLocations || [],
    },
  });

  const onSubmit = async (values: z.infer<typeof profileFormSchema>) => {
    try {
      const profileData: Partial<User> = {
        userName: values.userName,
        fullName: values.fullName,
        email: values.email,
        phoneNumber: values.phoneNumber || '',
        about: values.about || '',
        status: values.status || 'Online',
        yearsOfExperience: values.yearsOfExperience,
        companyName: values.companyName || '',
        fieldOfWorks: values.fieldOfWorks || [],
        serviceLocations: values.serviceLocations || [],
      };

      // Create birthDate using utility function
      const birthDate = createBirthdateString(values.birthDay, values.birthMonth, values.birthYear);
      if (birthDate) {
        profileData.birthdate = birthDate;
      }

      updateProfile(profileData, {
        onSuccess: data => {
          if (data.status) {
            toast.success('Thông tin đã được cập nhật thành công');
            onSuccess?.();
          } else {
            toast.error(data.message || 'Có lỗi xảy ra khi cập nhật thông tin');
          }
        },
        onError: (error: any) => {
          console.error('Update profile error:', error);
          toast.error(error.message || 'Có lỗi xảy ra khi cập nhật thông tin');
        },
      });
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Đã xảy ra lỗi khi gửi form.');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Họ và tên</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập họ và tên" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="userName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên người dùng</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập tên người dùng" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" type="email" {...field} disabled />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Số điện thoại</FormLabel>
                <FormControl>
                  <Input placeholder="+84 123 456 789" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <FormLabel>Ngày sinh</FormLabel>
            <div className="grid grid-cols-3 gap-3">
              <FormField
                control={form.control}
                name="birthDay"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Ngày" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {dayOptions.map(day => (
                          <SelectItem key={day} value={day.toString()}>
                            {day}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="birthMonth"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Tháng" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {monthOptions.map((month, index) => (
                          <SelectItem key={index + 1} value={(index + 1).toString()}>
                            {month}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="birthYear"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Năm" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {yearOptions.map(year => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Trạng thái</FormLabel>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Online">Trực tuyến</SelectItem>
                    <SelectItem value="Idle">Không hoạt động</SelectItem>
                    <SelectItem value="DoNotDisturb">Không làm phiền</SelectItem>
                    <SelectItem value="Invisible">Ẩn</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="about"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Giới thiệu</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Giới thiệu về bản thân..."
                  className="min-h-32 resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="yearsOfExperience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Số năm kinh nghiệm</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="0"
                    min="0"
                    max="50"
                    {...field}
                    onChange={e =>
                      field.onChange(e.target.value ? parseInt(e.target.value) : undefined)
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="companyName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tên công ty</FormLabel>
                <FormControl>
                  <Input placeholder="Tên công ty..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="serviceLocations"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Địa điểm phục vụ</FormLabel>
              <FormControl>
                <div className="space-y-2">
                  {field.value?.map((location, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        placeholder="Thành phố"
                        value={location.city}
                        onChange={e => {
                          const newLocations = [...(field.value || [])];
                          newLocations[index] = { ...location, city: e.target.value };
                          field.onChange(newLocations);
                        }}
                      />
                      <Input
                        placeholder="Quận/Huyện"
                        value={location.ward}
                        onChange={e => {
                          const newLocations = [...(field.value || [])];
                          newLocations[index] = { ...location, ward: e.target.value };
                          field.onChange(newLocations);
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newLocations = field.value?.filter((_, i) => i !== index) || [];
                          field.onChange(newLocations);
                        }}
                      >
                        Xóa
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const newLocations = [...(field.value || []), { city: '', ward: '' }];
                      field.onChange(newLocations);
                    }}
                  >
                    Thêm địa điểm
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-3 justify-end pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel} disabled={isPending}>
              Hủy
            </Button>
          )}
          <Button type="submit" disabled={isPending}>
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang lưu
              </>
            ) : (
              'Lưu thay đổi'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
