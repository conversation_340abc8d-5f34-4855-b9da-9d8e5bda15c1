'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { useUserProfile } from '@/hooks/useUsers';
import { useCreateFeedback } from '@/hooks/useFeedback';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Star, Loader2 } from 'lucide-react';

interface AgentFeedbackFormProps {
  agentId: string;
  salerName: string;
  onSubmitted?: () => void;
}

export default function AgentFeedbackForm({ agentId, salerName }: AgentFeedbackFormProps) {
  const [rating, setRating] = useState(0);
  const [content, setContent] = useState('');
  const [open, setOpen] = useState(false);
  const { data: profile } = useUserProfile();
  const userId = profile?.profile?.userName;
  const { mutate: createFeedback, isPending } = useCreateFeedback();
  const [isSending, setIsSending] = useState(false);

  const handleSubmit = () => {
    if (!userId) return toast.error('Bạn cần đăng nhập để đánh giá');
    if (rating < 1 || content.trim() === '')
      return toast.error('Vui lòng nhập nội dung và chọn số sao');

    setIsSending(true);
    const startTime = Date.now();

    createFeedback(
      { agentId, userId, rating, content },
      {
        onSuccess: async () => {
          const elapsed = Date.now() - startTime;
          const minDuration = 1000;
          const remaining = minDuration - elapsed;

          if (remaining > 0) await new Promise(res => setTimeout(res, remaining));
          setIsSending(false);
          setOpen(false);
          toast.success('Gửi đánh giá thành công');
        },
        onError: err => {
          setIsSending(false);
          toast.error(err.message || 'Gửi đánh giá thất bại');
        },
      }
    );
  };

  return (
    <Dialog
      open={open}
      onOpenChange={value => {
        setOpen(value);
        if (!value && !isSending && rating > 0 && content !== '') {
          setRating(0);
          setContent('');
        }
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full text-sm">
          <Star className="w-4 h-4 mr-2" />
          Đánh giá môi giới
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Đánh giá môi giới - {salerName}</DialogTitle>
          <p className="text-sm text-gray-500 mt-1">Hãy chia sẻ cảm nhận của bạn</p>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <div className="flex items-center justify-center gap-1">
            {[1, 2, 3, 4, 5].map(i => (
              <Star
                key={i}
                className={`w-6 h-6 cursor-pointer ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
                onClick={() => setRating(i)}
                fill={i <= rating ? 'currentColor' : 'none'}
              />
            ))}
          </div>
          <Textarea
            placeholder="Ví dụ: Môi giới rất chuyên nghiệp, hỗ trợ tận tình..."
            rows={4}
            value={content}
            onChange={e => setContent(e.target.value)}
          />
        </div>

        <DialogFooter>
          <Button
            onClick={handleSubmit}
            disabled={isSending || isPending}
            className="w-full flex items-center justify-center gap-2"
          >
            {isSending || isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin text-white" />
                Đang gửi...
              </>
            ) : (
              'Gửi đánh giá'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
