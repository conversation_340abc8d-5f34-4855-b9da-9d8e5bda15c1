'use client';

import Link from 'next/link';
import { Send, Phone, Mail, MapPin, Facebook, Instagram } from 'lucide-react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface FooterProps {
  containerWidth?: number;
}

// Helper function to determine layout based on container width
function getFooterLayout(width: number) {
  return {
    // Main grid layout
    mainGridCols: width >= 1024 ? 'grid-cols-5' : width >= 768 ? 'grid-cols-2' : 'grid-cols-1',
    companyColSpan: width >= 1024 ? 'lg:col-span-2' : '',
    rightSideColSpan: width >= 1024 ? 'lg:col-span-3' : width >= 768 ? 'col-span-2' : '',

    // Right side grid layout
    rightSideGrid:
      width >= 1024
        ? 'lg:grid-cols-3 lg:grid-rows-2'
        : width >= 768
          ? 'md:grid-cols-2'
          : 'grid-cols-1',

    // Section layouts
    servicesRowStart: width >= 1024 ? 'lg:row-start-1' : '',
    propertyRowStart: width >= 1024 ? 'lg:row-start-1' : '',
    paymentRowStart: width >= 1024 ? 'lg:row-start-1' : '',
    newsletterColSpan: width >= 1024 ? 'lg:col-span-2 lg:row-start-2' : '',
    platformRowStart: width >= 1024 ? 'lg:row-start-2' : '',

    // Text sizes
    headingSize: width >= 768 ? 'md:text-base text-sm' : 'text-sm',
    companyNameSize: width >= 768 ? 'md:text-sm text-xs' : 'text-xs',
    textSize: width >= 768 ? 'md:text-sm text-xs' : 'text-xs',

    // Gaps and spacing
    mainGap: width >= 768 ? 'gap-12' : 'gap-8',
    sectionGap: width >= 768 ? 'gap-12' : 'gap-8',
  };
}

export default function Footer({ containerWidth = 0 }: FooterProps) {
  // Use viewport width if no container width provided (for backward compatibility)
  const effectiveWidth =
    containerWidth > 0 ? containerWidth : typeof window !== 'undefined' ? window.innerWidth : 1200;
  const layout = getFooterLayout(effectiveWidth);

  return (
    <footer className="w-full bg-white text-zinc-900">
      <div className="container mx-auto px-4 pt-16 mb-4">
        <div className={`grid ${layout.mainGap} ${layout.mainGridCols}`}>
          {/* Company Info */}
          <div className={`space-y-6 ${layout.companyColSpan}`}>
            <Link href="/" className="flex items-center">
              <Image
                src="/logo_revoland_red.png"
                alt="Revoland icon"
                width={40}
                height={40}
                priority
                className="rounded-md"
              />
              <span className="text-2xl font-medium text-red-500 ml-2">Revoland</span>
            </Link>

            {/* Company Details */}
            <div className="space-y-4">
              <div>
                <h3 className={`${layout.companyNameSize} font-semibold text-zinc-900 mb-2`}>
                  CÔNG TY TNHH CÔNG NGHỆ BẤT ĐỘNG SẢN REVOLAND
                </h3>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <div className={`${layout.textSize} text-zinc-600`}>
                  <p>Số 40 Đường D2A, The Manhattan Vinhomes Grand Park</p>
                  <p>Phường Long Bình, Thành phố Thủ Đức</p>
                  <p>Thành phố Hồ Chí Minh, Việt Nam</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-red-500 flex-shrink-0" />
                <Link
                  href="tel:+84968070478"
                  className={`${layout.textSize} text-zinc-600 hover:text-red-500`}
                >
                  (*************
                </Link>
              </div>

              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-red-500 flex-shrink-0" />
                <Link
                  href="mailto:<EMAIL>"
                  className={`${layout.textSize} text-zinc-600 hover:text-red-500`}
                >
                  <EMAIL>
                </Link>
              </div>
            </div>

            {/* Social Media */}
            <div className="space-y-3">
              <h4 className={`${layout.textSize} font-semibold text-zinc-900`}>
                Theo dõi chúng tôi
              </h4>
              <div className="flex gap-3 flex-wrap">
                <Link
                  href="https://www.facebook.com/profile.php?id=61566950557141"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="outline"
                    size="icon"
                    className="hover:bg-blue-50 hover:border-blue-300"
                  >
                    <Facebook className="w-4 h-4 text-blue-600" />
                  </Button>
                </Link>
                <Link
                  href="https://www.instagram.com/revoland_vn/"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="outline"
                    size="icon"
                    className="hover:bg-pink-50 hover:border-pink-300"
                  >
                    <Instagram className="w-4 h-4 text-pink-600" />
                  </Button>
                </Link>
                <Link
                  href="https://www.tiktok.com/@revolandvn?lang=en"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button
                    variant="outline"
                    size="icon"
                    className="hover:bg-black/5 hover:border-black/20"
                  >
                    <svg className="w-4 h-4 text-black" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
                    </svg>
                  </Button>
                </Link>
                <Link href="https://x.com/" target="_blank" rel="noopener noreferrer">
                  <Button
                    variant="outline"
                    size="icon"
                    className="hover:bg-gray-50 hover:border-gray-400"
                  >
                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                    </svg>
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Right Side Container */}
          <div
            className={`${layout.rightSideColSpan} grid ${layout.sectionGap} ${layout.rightSideGrid}`}
          >
            {/* Services & Legal */}
            <div className={`space-y-6 ${layout.servicesRowStart}`}>
              <h3 className={`${layout.headingSize} font-semibold text-zinc-900`}>
                Dịch vụ & Pháp lý
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    href="/legal/pricing"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Bảng giá dịch vụ
                  </Link>
                </li>
                <li>
                  <Link
                    href="/legal/regulations"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Quy định đăng tin
                  </Link>
                </li>
                <li>
                  <Link
                    href="/legal/faq"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Câu hỏi thường gặp
                  </Link>
                </li>
                <li>
                  <Link
                    href="/legal/complaint-settlement"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Giải quyết khiếu nại
                  </Link>
                </li>
              </ul>
            </div>

            {/* Property Links */}
            <div className={`space-y-6 ${layout.propertyRowStart}`}>
              <h3 className={`${layout.headingSize} font-semibold text-zinc-900`}>Bất động sản</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    href="/properties?type=mua"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Mua bất động sản
                  </Link>
                </li>
                <li>
                  <Link
                    href="/properties?type=ban"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Bán bất động sản
                  </Link>
                </li>
                <li>
                  <Link
                    href="/properties?type=thue"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Cho thuê
                  </Link>
                </li>
                <li>
                  <Link
                    href="/login"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Đăng nhập
                  </Link>
                </li>
                <li>
                  <Link
                    href="/register"
                    className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    Đăng ký
                  </Link>
                </li>
              </ul>
            </div>

            {/* Payment Methods */}
            <div className={`space-y-6 ${layout.paymentRowStart}`}>
              <h3 className={`${layout.headingSize} font-semibold text-zinc-900`}>Thanh toán</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div className="flex items-center justify-center p-3 border border-zinc-200 rounded-lg bg-zinc-50 hover:bg-zinc-100 transition-colors">
                    <span className="text-sm font-medium text-red-600">VNPay</span>
                  </div>
                  <div className="flex items-center justify-center p-3 border border-zinc-200 rounded-lg bg-zinc-50 hover:bg-zinc-100 transition-colors">
                    <span className="text-sm font-medium text-pink-600">MoMo</span>
                  </div>
                  <div className="flex items-center justify-center p-3 border border-zinc-200 rounded-lg bg-zinc-50 hover:bg-zinc-100 transition-colors">
                    <span className="text-sm font-medium text-blue-600">VISA</span>
                  </div>
                  <div className="flex items-center justify-center p-3 border border-zinc-200 rounded-lg bg-zinc-50 hover:bg-zinc-100 transition-colors">
                    <span className="text-sm font-medium text-orange-600">Mastercard</span>
                  </div>
                </div>
                <p className="text-xs text-zinc-500 text-center">
                  Hỗ trợ thanh toán an toàn và bảo mật
                </p>
              </div>
            </div>

            {/* Newsletter Section */}
            <div className={`${layout.newsletterColSpan}`}>
              <div className="space-y-4">
                <h3 className={`${layout.headingSize} font-semibold text-zinc-900`}>
                  Đăng ký nhận tin
                </h3>
                <p className="text-sm text-zinc-600">
                  Nhận thông tin mới nhất về bất động sản và ưu đãi đặc biệt từ Revoland
                </p>
                <div className="flex max-w-md">
                  <Input
                    type="email"
                    placeholder="Nhập email của bạn"
                    className="rounded-r-none bg-zinc-50 border-zinc-200 text-zinc-900 placeholder:text-zinc-400 focus:ring-1 focus:ring-red-500"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="rounded-l-none border border-l-0 border-zinc-200 bg-red-500 hover:bg-red-600 text-white hover:text-white"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Platform Availability */}
            <div className={layout.platformRowStart}>
              <div className="space-y-4">
                <h3 className={`${layout.headingSize} font-semibold text-zinc-900`}>Có sẵn trên</h3>
                <div className="space-y-3">
                  <Link
                    href="/"
                    className="flex items-center gap-2 text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Website
                  </Link>
                  <Link
                    href="https://play.google.com/store/apps/details?id=com.revoland"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                    </svg>
                    CH Play
                  </Link>
                  <Link
                    href="https://apps.apple.com/app/revoland/id123456789"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.19 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z" />
                    </svg>
                    App Store
                  </Link>
                  <Link
                    href="/download/desktop"
                    className="flex items-center gap-2 text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Desktop App
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator className="bg-zinc-200" />

      {/* Bottom Footer */}
      <div className="bg-zinc-50">
        <div className="container mx-auto px-4 py-8">
          {/* Legal Links */}
          <div className="flex flex-wrap items-center justify-center gap-6 mb-6">
            <Link
              href="/legal/terms-of-agreement"
              className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
            >
              Điều khoản thỏa thuận
            </Link>
            <Link
              href="/legal/privacy-policy"
              className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
            >
              Chính sách bảo mật
            </Link>
            <Link
              href="/legal"
              className="text-sm text-zinc-600 hover:text-red-500 transition-colors duration-200"
            >
              Chính sách pháp lý
            </Link>
          </div>

          <Separator className="bg-zinc-200 mb-6" />

          {/* Copyright and Legal Information */}
          <div className="text-center space-y-4">
            <div className="text-sm text-zinc-600 space-y-2">
              <p className="font-semibold">
                ©{new Date().getFullYear()} Revoland.vn - Bản quyền thuộc về Công ty TNHH Công Nghệ
                Bất Động Sản Revoland
              </p>
              <p>
                Giấy ĐKKD số 0319016269 do Sở Tài Chính TP Hồ Chí Minh cấp lần đầu ngày 20/06/2025
              </p>
              <p>Chịu trách nhiệm nội dung: Đặng Tiến | Chịu trách nhiệm sàn GDTMĐT: Đặng Tiến</p>
              <p>Quy chế, quy định giao dịch có hiệu lực từ 20/06/2025</p>
              <p className="italic">
                Ghi rõ nguồn &quot;Revoland.vn&quot; khi phát hành lại thông tin từ website này.
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
