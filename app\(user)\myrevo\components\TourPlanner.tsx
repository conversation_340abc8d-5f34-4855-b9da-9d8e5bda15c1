'use client';

import { useUserTours } from '@/hooks/useTour';
import { useEffect, useState } from 'react';
import { CreateTourForm } from '../../properties/[id]/component/CreateTourForm';
import { CalendarCheck, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TourList } from '@/components/TourList';
import { Tour } from '@/lib/api/services/fetchTour';

export default function TourPlanner() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTour, setSelectedTour] = useState<Tour | undefined>(undefined);
  const { tours, isLoading, isError, error, refetch } = useUserTours(); // Use the hook
  useEffect(() => {
    setShowCreateForm(false); // Always show tour list first when dialog opens
    refetch(); // Manually refetch when dialog opens
  }, [refetch]);

  const handleFormSuccess = () => {
    setShowCreateForm(false); // Switch back to tour list view
    refetch(); // Re-fetch tours to show the newly created one
  };

  const handleEditTour = (tour: Tour) => {
    setSelectedTour(tour); // Set the tour to be edited
    setShowCreateForm(true); // Show the create form
  };

  const handleBackToList = () => {
    setShowCreateForm(false);
    setSelectedTour(undefined); // Reset selected tour when going back
  };

  return (
    <div className="mt-4">
      {selectedTour ? (
        <CreateTourForm
          onSuccess={handleFormSuccess}
          onBack={handleBackToList}
          tour={selectedTour}
          isDialog={false}
        />
      ) : showCreateForm ? (
        <CreateTourForm
          onSuccess={handleFormSuccess}
          onBack={() => setShowCreateForm(false)}
          isDialog={false}
        />
      ) : (
        <div className="grid gap-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Đang tải tour của bạn...</span>
            </div>
          ) : isError ? (
            <div className="text-center text-red-500 py-8">
              <p>{error?.message || 'Không thể tải danh sách tour.'}</p>
              <Button onClick={() => refetch()} className="mt-4">
                Thử lại
              </Button>
            </div>
          ) : (
            <>
              {tours && tours.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-lg text-muted-foreground mb-4">
                    Bạn chưa có lịch trình tour nào.
                  </p>
                  <Button
                    onClick={() => setShowCreateForm(true)}
                    className="bg-red-600 text-white hover:bg-red-700"
                  >
                    Tạo Lịch Trình Mới Ngay
                  </Button>
                </div>
              ) : (
                <div className="">
                  <div className=" flex justify-between mb-6">
                    <div className="">
                      <div className="flex items-center">
                        <CalendarCheck className="h-5 w-5" />
                        <h2 className="text-lg font-semibold ml-2">Lịch Trình Xem Nhà Của Bạn</h2>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Xem và quản lý các lịch trình xem nhà đã tạo.
                      </p>
                    </div>
                    <Button
                      onClick={() => setShowCreateForm(true)}
                      className="mb-4 self-end bg-red-600 text-white hover:bg-red-700"
                    >
                      Tạo Lịch Trình Mới
                    </Button>
                  </div>
                  <TourList disabled={isLoading} onEditTour={handleEditTour} isDialog={false} />
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}
