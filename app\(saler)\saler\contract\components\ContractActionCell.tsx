import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { MoreVertical } from 'lucide-react';
import ContractDetailModal from './ContractDetail';
import { ContractItem } from '@/lib/api/services/fetchContract';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { useContract } from '@/hooks/useContract';
import DeleteConfirmModal from './DeleteConfirmModal';

interface ContractActionCellProps {
  contract: ContractItem; // Assuming ContractItem is the type for contract details
}

const ContractActionCell = ({ contract }: ContractActionCellProps) => {
  const [isOpenDetail, setIsOpenDetail] = useState(false);
  const [isModalDelete, setIsModalDelete] = useState(false);
  const [isLoadingDelete, setIsLoadingDelete] = useState(false);
  const { deleteMutation } = useContract();

  const handleDelete = () => {
    setIsLoadingDelete(true);
    deleteMutation.mutate(contract.id, {
      onSuccess: () => {
        setIsModalDelete(false);
      },
    });
  };

  return (
    <>
      <DropdownMenu key={isOpenDetail ? 'modal-open' : 'modal-closed'}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreVertical className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setIsOpenDetail(true)}>Xem chi tiết</DropdownMenuItem>
          <DropdownMenuItem onClick={() => console.log('Update', contract.id)}>
            Cập nhật
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setIsModalDelete(true)} className="text-red-500">
            Xóa
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {isModalDelete && (
        <DeleteConfirmModal
          open={isModalDelete}
          onOpenChange={setIsModalDelete}
          onConfirm={handleDelete}
          isLoading={isLoadingDelete}
          title="Xác nhận xóa hợp đồng"
          description={`Bạn có chắc chắn muốn xóa hợp đồng của bất động sản ${contract.property.name} không? Hành động này không thể hoàn tác.`}
        />
      )}
      {isOpenDetail && (
        <Dialog open={isOpenDetail} onOpenChange={open => setIsOpenDetail(open)}>
          <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto scrollbar-hide">
            <DialogHeader>
              <DialogTitle>Chi tiết hợp đồng</DialogTitle>
              <DialogDescription>Thông tin chi tiết về hợp đồng đã chọn.</DialogDescription>
            </DialogHeader>
            <ContractDetailModal contract={contract} />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default ContractActionCell;
