'use client';

import SellerProfileHeader from './SellerProfileHeader';
import SellerProfileStats from './SellerProfileStats';
import SellerProfileAbout from './SellerProfileAbout';
import SellerProfileListings from './SellerProfileListings';
import Footer from '@/components/Footer';
import { useSellerProfile } from '@/hooks/useUsers';
import { useFeedbackStats } from '@/hooks/useFeedback';
import { Loader2 } from 'lucide-react';
import { notFound } from 'next/navigation';
import AgentFeedbackList from '@/app/(user)/properties/[id]/component/AgentReviewList';

interface SellerProfileClientProps {
  sellerId: string;
}

// Mock data removed - now using real API data

export default function SellerProfileClient({ sellerId }: SellerProfileClientProps) {
  const { data: sellerData, isLoading, error } = useSellerProfile(sellerId);
  const { data: feedbackStats } = useFeedbackStats(sellerId);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Đang tải thông tin môi giới...</span>
        </div>
      </div>
    );
  }

  if (error || !sellerData?.profile) {
    notFound();
  }

  const profile = sellerData.profile;

  // Transform API data to match component props
  const transformedSellerData = {
    id: profile.sellerId,
    fullName: profile.name || 'Chưa cập nhật',
    avatar: profile.avatar || '/images/default-avatar.jpg',
    coverImage: profile.coverPhoto || '/images/default-cover.jpg',
    title: 'Chuyên viên bất động sản',
    company: profile.companyName || 'Chưa cập nhật',
    rating: feedbackStats?.averageRating || 0,
    reviewCount: feedbackStats?.totalFeedback || profile.agentFeedbacks?.length || 0,
    phoneNumber: profile.phone || 'Chưa cập nhật',
    email: 'Chưa cập nhật', // Not provided by API
    experience: profile.yearsOfExperience ? `${profile.yearsOfExperience} năm` : 'Chưa cập nhật',
    specialties: profile.fieldOfWorks ? [profile.fieldOfWorks] : ['Bất động sản'],
    about: profile.introduction || 'Chưa có thông tin giới thiệu.',
    joinedAt: profile.joinedAt,
    serviceLocations: profile.serviceLocations,
    stats: {
      totalSales: 0, // Not provided by API
      totalListings: profile.properties?.length || 0,
      averageDaysOnMarket: 0, // Not provided by API
      clientSatisfaction: 0, // Not provided by API
      joinedAt: profile.joinedAt,
      yearsOfExperience: profile.yearsOfExperience,
    },
    socialMedia: {
      facebook: '',
      linkedin: '',
      twitter: '',
    },
  };

  return (
    <div className="min-h-screen bg-background mt-4 md:mt-8">
      {/* Header with cover image and basic info */}
      <SellerProfileHeader seller={transformedSellerData} />

      {/* Stats section */}
      <SellerProfileStats stats={transformedSellerData.stats} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
        <div className="space-y-8">
          {/* About section */}
          <SellerProfileAbout
            about={transformedSellerData.about}
            specialties={transformedSellerData.specialties}
            experience={transformedSellerData.experience}
            joinedAt={transformedSellerData.joinedAt}
            serviceLocations={transformedSellerData.serviceLocations}
          />
          {/* Reviews section */}
          <AgentFeedbackList agentId={sellerId} />
          {/* Contact section - moved to main content */}
          {/* <SellerProfileContact /> */}
          {/* Listings section */}
          <SellerProfileListings sellerId={sellerId} />
        </div>
      </div>

      <Footer />
    </div>
  );
}
