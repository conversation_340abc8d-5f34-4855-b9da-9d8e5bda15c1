import { RequestParams } from '../core';
export interface Ward {
  name: string;
  code: number;
  division_type: string;
  codename: string;
  province_code: number;
}

export interface Province {
  name: string;
  code: number;
  division_type: string;
  codename: string;
  phone_code: number;
  wards: Ward[];
}

export interface WardSearchParams {
  depth: 1 | 2 | 3;
}

export const convertPropertyFilters = (filter: WardSearchParams): RequestParams => {
  const params: RequestParams = {};
  if (filter.depth) params.depth = filter.depth;
  return params;
};

export const provinceService = {
  getProvinces: async (): Promise<Province[]> => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_VIETNAM_PROVINCE_API}/p/`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching provinces:', error);
      throw error;
    }
  },

  getWardsByProvince: async (provinceCode: string): Promise<Ward[]> => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_VIETNAM_PROVINCE_API}/p/${provinceCode}?depth=2`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.wards || [];
    } catch (error) {
      console.error('Error fetching wards:', error);
      throw error;
    }
  },
};

export default provinceService;
