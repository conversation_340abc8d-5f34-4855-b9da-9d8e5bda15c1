import { ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Footer from '@/components/Footer';

interface PolicyItem {
  title: string;
  href: string;
}

interface PolicySection {
  category: string;
  items: PolicyItem[];
}

const policyData: PolicySection[] = [
  {
    category: 'CHÍNH SÁCH PHÁP LÝ',
    items: [
      {
        title: 'Điều khoản thỏa thuận',
        href: '/legal/terms-of-agreement',
      },
      {
        title: '<PERSON><PERSON>h sách bảo mật thông tin',
        href: '/legal/privacy-policy',
      },
      {
        title: '<PERSON><PERSON>h sách giải quyết khiếu nại',
        href: '/legal/complaint-settlement',
      },
    ],
  },
  {
    category: 'QUY ĐỊNH VÀ HƯỚNG DẪN',
    items: [
      {
        title: 'Quy định đăng tin',
        href: '/legal/regulations',
      },
      {
        title: '<PERSON>âu hỏi thường gặp',
        href: '/legal/faq',
      },
    ],
  },
  {
    category: 'DỊCH VỤ VÀ ĐỊNH GIÁ',
    items: [
      {
        title: 'Bảng giá dịch vụ',
        href: '/legal/pricing',
      },
    ],
  },
];

export default function LegalPage() {
  return (
    <>
      <div className="min-h-screen">
        <div className="container mx-auto px-6 py-16 max-w-4xl">
          <h1 className="md:text-6xl text-4xl font-light mb-16 tracking-tight">
            Chính sách pháp lý
          </h1>

          <div className="space-y-16">
            {policyData.map(section => (
              <div key={section.category} className="space-y-6">
                <h2 className="text-sm font-medium tracking-wider uppercase">{section.category}</h2>

                <div className="space-y-4">
                  {section.items.map(item => (
                    <Card key={item.title} className="bg-transparent">
                      <Button
                        variant="ghost"
                        className="w-full h-auto p-6 justify-between text-left"
                        asChild
                      >
                        <a href={item.href} className="flex items-center justify-between group">
                          <span className="text-base">{item.title}</span>
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}
