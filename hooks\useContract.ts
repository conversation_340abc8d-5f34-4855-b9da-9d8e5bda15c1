import {
  ContractDetailResponse,
  ContractFilterParams,
  ContractListResponse,
  HouseSaleContractRequest,
  RequestSendContract,
  contractService,
  createRentalContractState,
} from '@/lib/api/services/fetchContract';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';

export function useContract() {
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Get all house sale contracts
  function useGetAllContractsBySaler(filters?: ContractFilterParams) {
    return useQuery<ContractListResponse, Error>({
      queryKey: ['contracts', filters],
      queryFn: () => contractService.getAllContractsBySaler(filters),
    });
  }

  // Get contract by id
  function useGetContractById(id: string) {
    return useQuery<ContractDetailResponse, Error>({
      queryKey: ['contract', id],
      queryFn: () => contractService.getContractById(id),
      enabled: !!id,
    });
  }

  // Create house sale contract
  const createMutationSale = useMutation({
    mutationFn: (data: HouseSaleContractRequest) => contractService.createHouseSaleContract(data),
    onError: (e: Error) => setError(e.message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['houseSaleContracts'] });
    },
  });

  // Create rental contract
  const createMutationRental = useMutation({
    mutationFn: (data: createRentalContractState) => contractService.createRentalContract(data),
    onError: (e: Error) => setError(e.message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rentalContracts'] });
    },
  });

  //update rental contract
  const updateMutationRental = useMutation({
    mutationFn: ({ data, id }: { data: createRentalContractState; id: string }) =>
      contractService.updateRentalContractById(id, data),
    onError: (e: Error) => setError(e.message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rentalContracts'] });
    },
  });

  //update house sale contract
  const updateMutationSale = useMutation({
    mutationFn: ({ data, id }: { data: HouseSaleContractRequest; id: string }) =>
      contractService.updateSaleContractById(id, data),
    onError: (e: Error) => setError(e.message),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['houseSaleContracts'] });
    },
  });
  //request to send contract to related parties
  const sendContractMutation = useMutation({
    mutationFn: (data: RequestSendContract) => contractService.sendContractToRelatedParties(data),
    onError: (e: Error) => {
      setError(e.message);
      toast.error(e.message || 'Gửi hợp đồng thất bại');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Gửi hợp đồng thành công');
    },
  });

  // Update house sale contract
  //   const updateMutation = useMutation({
  //     mutationFn: (data: HouseSaleContractResponse) =>
  //       contractService.updateHouseSaleContract(data),
  //     onError: (e: Error) => setError(e.message),
  //     onSuccess: () => {
  //       queryClient.invalidateQueries({ queryKey: ['houseSaleContracts'] });
  //     },
  //   });

  // Delete contract by id
  const deleteMutation = useMutation({
    mutationFn: (id: string) => contractService.deleteContractById(id),
    onError: (e: Error) => {
      setError(e.message);
      toast.error(e.message || 'Xóa hợp đồng thất bại');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      toast.success('Xóa hợp đồng thành công');
    },
  });

  return {
    error,
    setError,
    useGetAllContractsBySaler,
    useGetContractById,
    createMutationSale,
    createMutationRental,
    sendContractMutation,
    updateMutationRental,
    updateMutationSale,
    deleteMutation,
  };
}
