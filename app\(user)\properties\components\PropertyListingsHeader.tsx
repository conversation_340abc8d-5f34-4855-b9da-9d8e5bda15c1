'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  ChevronDown,
  Calendar,
  DollarSign,
  Type,
  ArrowUpDown,
  LayoutGrid,
  Map,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Add type definitions for sort options
type SortOption = {
  key: string;
  label: string;
  sortBy?: 'price' | 'createdAt' | 'name';
  sortOrder?: 'asc' | 'desc';
  icon: React.ComponentType<{ className?: string }>;
};

const sortOptions: SortOption[] = [
  {
    key: 'createdAt-desc',
    label: 'Mới nhất',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    icon: Calendar,
  },
  {
    key: 'price-asc',
    label: '<PERSON><PERSON><PERSON> thấp nhất',
    sortBy: 'price',
    sortOrder: 'asc',
    icon: DollarSign,
  },
  {
    key: 'price-desc',
    label: 'Gi<PERSON> cao nhất',
    sortBy: 'price',
    sortOrder: 'desc',
    icon: DollarSign,
  },
  {
    key: 'createdAt-asc',
    label: 'Cũ nhất',
    sortBy: 'createdAt',
    sortOrder: 'asc',
    icon: Calendar,
  },
  {
    key: 'name-asc',
    label: 'Tên A-Z',
    sortBy: 'name',
    sortOrder: 'asc',
    icon: Type,
  },
  {
    key: 'name-desc',
    label: 'Tên Z-A',
    sortBy: 'name',
    sortOrder: 'desc',
    icon: Type,
  },
  {
    key: 'default',
    label: 'Mặc định',
    icon: ArrowUpDown,
  },
];

interface PropertyListingsHeaderProps {
  title: string;
  count: number;
  isMapView: boolean;
  onViewToggle: (isMapView: boolean) => void;
  currentSortOption: SortOption;
  onSortChange: (option: SortOption) => void;
}

export default function PropertyListingsHeader({
  title,
  count,
  isMapView,
  onViewToggle,
  currentSortOption,
  onSortChange,
}: PropertyListingsHeaderProps) {
  return (
    <div className="flex-shrink-0 p-4 border-b">
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-semibold">
          {title} <span className="text-sm text-muted-foreground">- {count} bất động sản</span>
        </h1>
        <div className="flex items-center gap-3">
          {/* View Toggle Switch */}
          <div className="flex items-center gap-2">
            <LayoutGrid
              className={`h-4 w-4 ${!isMapView ? 'text-primary' : 'text-muted-foreground'}`}
            />
            <Switch
              checked={isMapView}
              onCheckedChange={onViewToggle}
              aria-label="Toggle between grid and map view"
            />
            <Map className={`h-4 w-4 ${isMapView ? 'text-primary' : 'text-muted-foreground'}`} />
          </div>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowUpDown className="h-4 w-4" />
                {currentSortOption.label}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48 shadow-none">
              <DropdownMenuGroup>
                {sortOptions.map(option => (
                  <DropdownMenuItem
                    key={option.key}
                    onClick={() => onSortChange(option)}
                    className={option.key === currentSortOption.key ? 'bg-accent' : ''}
                  >
                    <option.icon className="mr-2 h-4 w-4" />
                    {option.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}

export { sortOptions };
export type { SortOption };
