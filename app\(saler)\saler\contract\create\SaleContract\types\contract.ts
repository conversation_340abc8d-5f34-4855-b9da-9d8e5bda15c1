import {
  Clause11To14Input,
  Clause3To6Input,
  Clause7To10Input,
  PartyAInput,
  PartyBInput,
} from '../schemas';
import { ContractTermsFormData } from '../schemas/contract';

export interface SaleContractData {
  partyA: PartyAInput;
  partyB: PartyBInput;
  terms: ContractTermsFormData;
  clausesArticle3to6: Clause3To6Input;
  clausesArticle7to10: Clause7To10Input;
  clause11To14Schema: Clause11To14Input;
  contractContent?: string;
}

export type { PartyAInput, PartyBInput, Clause11To14Input, Clause3To6Input, Clause7To10Input };
