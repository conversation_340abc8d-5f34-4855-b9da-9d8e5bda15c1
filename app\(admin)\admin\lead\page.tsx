import { SiteHeader } from '@/components/common/siteHeader';
import { SidebarInset } from '@/components/ui/sidebar';
import { AddLeadButton } from './components/addLead';
import { LeadGrid } from './components/leadGrid';

export default function LeadPage() {
  return (
    <SidebarInset>
      <SiteHeader title="Quản lý khách hàng tiềm năng" />
      <div className="flex min-h-screen flex-col bg-background">
        <main className="flex-1 w-full px-6 py-6 mx-auto">
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl lg:text-2xl font-bold tracking-tight">
                  Quản lý khách hàng tiềm năng
                </h1>
                <p className="text-sm text-muted-foreground">Quản lý khách hàng tiềm năng</p>
              </div>
              <AddLeadButton />
            </div>
            <LeadGrid />
          </div>
        </main>
      </div>
    </SidebarInset>
  );
}
