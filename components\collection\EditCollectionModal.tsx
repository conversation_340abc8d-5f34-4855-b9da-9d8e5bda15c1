import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useCollections } from '@/hooks/useCollections';
import { Collection } from '@/lib/api/services/fetchCollection';

interface EditCollectionModalProps {
  collection: Collection | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function EditCollectionModal({
  collection,
  isOpen,
  onClose,
}: EditCollectionModalProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const { updateCollection, isUpdatingCollection } = useCollections();

  useEffect(() => {
    if (collection) {
      setName(collection.name);
      setDescription(collection.description || '');
    }
  }, [collection]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!collection || !name.trim()) return;

    updateCollection(
      { collectionId: collection.id, data: { name: name.trim(), description: description.trim() } },
      {
        onSuccess: () => {
          onClose();
          setName('');
          setDescription('');
        },
      }
    );
  };

  const handleClose = () => {
    onClose();
    setName('');
    setDescription('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[425px]" onCloseAutoFocus={e => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Chỉnh sửa bộ sưu tập</DialogTitle>
          <DialogDescription>Cập nhật thông tin cho bộ sưu tập của bạn.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              Tên bộ sưu tập <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Nhập tên bộ sưu tập"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Nhập mô tả cho bộ sưu tập"
              rows={3}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button type="submit" disabled={!name.trim() || isUpdatingCollection}>
              {isUpdatingCollection ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
