'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Collection detail page error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-4 p-8">
        <div className="flex justify-center">
          <AlertTriangle className="h-16 w-16 text-destructive" />
        </div>
        <h2 className="text-2xl font-bold text-foreground">
          Có lỗi xảy ra khi tải thông tin bộ sưu tập
        </h2>
        <p className="text-muted-foreground max-w-md">
          Không thể tải thông tin bộ sưu tập. Vui lòng thử lại hoặc liên hệ hỗ trợ nếu vấn đề vẫn
          tiếp tục.
        </p>
        <div className="flex gap-4 justify-center">
          <Button onClick={reset} variant="default">
            Thử lại
          </Button>
          <Button onClick={() => window.history.back()} variant="outline">
            Quay lại
          </Button>
        </div>
      </div>
    </div>
  );
}
