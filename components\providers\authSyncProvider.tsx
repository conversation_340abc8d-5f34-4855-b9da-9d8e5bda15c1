'use client';

import { useEffect } from 'react';
import { getCookie } from 'cookies-next';
import { useAuthStore } from '@/lib/store/authStore';
import apiService from '@/lib/api/core';

export function AuthSyncProvider({ children }: { children: React.ReactNode }) {
  const { setToken, token, isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    const cookieToken = getCookie('auth-token');

    console.log('[AuthSyncProvider] Checking auth state:', {
      cookieToken: cookieToken ? 'present' : 'missing',
      storeToken: token ? 'present' : 'missing',
      isAuthenticated,
    });

    // If cookie exists but store doesn't have token, sync them
    if (cookieToken && (!token || token !== cookieToken)) {
      console.log('[AuthSyncProvider] Syncing token from cookie to store');
      apiService.setAuthToken(cookieToken as string);
      setToken(cookieToken as string);
    }
    // If store has token but no cookie, clear store
    else if (token && !cookieToken) {
      console.log(
        '[AuthSyncProvider] No cookie found, clearing store - this indicates logout completed successfully'
      );
      setToken(null);
    }
    // If both exist and match, ensure API service has token
    else if (cookieToken && token && cookieToken === token) {
      console.log('[AuthSyncProvider] Token sync verified, setting API token');
      apiService.setAuthToken(cookieToken as string);
    }
  }, []); // Run only once on mount

  return <>{children}</>;
}
