/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Don't run ESLint during builds
    // ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        hostname: 'storage.googleapis.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'www.atlys.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'mvcdn1-dulich.vnecdn.net',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'imagedelivery.net',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'lh3.googleusercontent.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'images.unsplash.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'www.atlys.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'media.vneconomy.vn',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'vcdn1-dulich.vnecdn.net',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'ik.imagekit.io',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'heza.gov.vn',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'www.youtube.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'artlist.io',
        protocol: 'https',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'encrypted-tbn0.gstatic.com',
        pathname: '/**',
      },

      {
        protocol: 'https',
        hostname: 'upload.wikimedia.org',
        pathname: '/**',
      },

      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
        pathname: '/api/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn-media.sforum.vn',
        pathname: '/**',
      },
    ],
    // Optimize image loading and caching
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp'],
    minimumCacheTTL: 60,
    // Increase the timeout for image optimization
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
};

export default nextConfig;
