'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, Trash2 } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  itemName?: string;
  type?: 'collection' | 'property';
  isLoading?: boolean;
}

export default function DeleteConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  itemName,
  type = 'collection',
  isLoading = false,
}: DeleteConfirmationDialogProps) {
  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent
        className="sm:max-w-[500px] max-w-[90vw] w-full mx-auto p-4 sm:p-6"
        onCloseAutoFocus={e => e.preventDefault()}
      >
        <AlertDialogHeader className="space-y-3 sm:space-y-4">
          <div className="flex items-start gap-3 sm:gap-4">
            <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="min-w-0 flex-1">
              <AlertDialogTitle className="text-left text-lg sm:text-xl font-semibold leading-tight">
                {title}
              </AlertDialogTitle>
            </div>
          </div>

          <AlertDialogDescription className="text-left break-words text-sm sm:text-base text-gray-600 dark:text-gray-300 leading-relaxed">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-3 sm:space-y-4">
          {itemName && (
            <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border overflow-hidden">
              <div className="flex items-start gap-3 min-w-0">
                <Trash2 className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500 flex-shrink-0 mt-1" />
                <span className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100 break-words min-w-0 flex-1 leading-relaxed">
                  {itemName}
                </span>
              </div>
            </div>
          )}

          <div className="p-3 sm:p-4 bg-amber-50 dark:bg-amber-950/20 rounded-lg border border-amber-200 dark:border-amber-800 overflow-hidden">
            <p className="text-xs sm:text-sm text-amber-800 dark:text-amber-200 flex items-start gap-2 sm:gap-3 min-w-0">
              <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span className="break-words min-w-0 flex-1 leading-relaxed">
                {type === 'collection'
                  ? 'Hành động này không thể hoàn tác. Tất cả bất động sản trong bộ sưu tập sẽ bị xóa khỏi danh sách yêu thích của bạn.'
                  : 'Bất động sản sẽ bị xóa khỏi bộ sưu tập này nhưng vẫn có thể được thêm lại sau.'}
              </span>
            </p>
          </div>
        </div>

        <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-3 pt-4 sm:pt-6">
          <AlertDialogCancel
            disabled={isLoading}
            className="w-full sm:w-auto order-2 sm:order-1 min-h-[44px] sm:min-h-[40px] text-sm sm:text-base"
          >
            Hủy bỏ
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto order-1 sm:order-2 min-h-[44px] sm:min-h-[40px] text-sm sm:text-base bg-red-600 hover:bg-red-700 focus:ring-red-600 font-medium"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Đang xóa...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <Trash2 className="h-4 w-4" />
                <span>Xác nhận xóa</span>
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
