'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/store/authStore';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { LoginForm } from '@/app/(auth)/components/login-form';

interface AuthSheetProps {
  children: React.ReactNode;
}

export function AuthSheet({ children }: AuthSheetProps) {
  const { isAuthenticated, syncAuthState } = useAuthStore();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    syncAuthState();
  }, [syncAuthState]);

  if (!isMounted) {
    return null;
  }

  if (isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <Sheet open={!isAuthenticated} onOpenChange={() => {}}>
      <SheetContent
        className="w-full max-w-md mx-auto sm:max-w-lg"
        onPointerDownOutside={e => e.preventDefault()}
        onEscapeKeyDown={e => e.preventDefault()}
      >
        <SheetHeader className="text-center space-y-4">
          <SheetTitle className="text-xl font-semibold">
            Đăng nhập để tiếp tục với Revoland
          </SheetTitle>
          <SheetDescription className="text-sm text-muted-foreground">
            <LoginForm />
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
