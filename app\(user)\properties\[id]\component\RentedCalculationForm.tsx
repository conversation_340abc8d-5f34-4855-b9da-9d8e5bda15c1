import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import PropertyCostCalculator from './PropertyCostCalculator';
import { PriceDetail } from '@/lib/api/services/fetchProperty';

interface RentedCalculationFormProps {
  isOpen: boolean;
  onClose: () => void;
  propertyData: PriceDetail;
}

export default function RentedCalculationForm({
  isOpen,
  onClose,
  propertyData,
}: RentedCalculationFormProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-6 scrollbar-hide">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-lg md:text-xl font-semibold text-neutral-900">
            Công cụ tính chi phí thuê nhà
          </DialogTitle>
        </DialogHeader>
        <PropertyCostCalculator propertyData={propertyData} />
      </DialogContent>
    </Dialog>
  );
}
