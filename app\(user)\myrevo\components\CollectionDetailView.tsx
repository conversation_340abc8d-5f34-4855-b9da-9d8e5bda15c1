import { useState } from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCollections } from '@/hooks/useCollections';
import { CollectionWithItems } from '@/lib/api/services/fetchCollection';
import { Property } from '@/lib/api/services/fetchProperty';
import { Image as ImageIcon, ArrowLeft, Folder, Trash, Share2 } from 'lucide-react';
import DeleteConfirmationDialog from '@/components/collection/DeleteConfirmationDialog';
import ShareDialog from '@/app/(user)/properties/[id]/component/ShareDialog';
import { TooltipProvider } from '@/components/ui/tooltip';
import Image from 'next/image';

interface CollectionThumbnailProps {
  images: string[];
  collectionName: string;
  className?: string;
}

function CollectionThumbnail({ images, collectionName, className = '' }: CollectionThumbnailProps) {
  const placeholderImages = Array(4).fill(null);

  return (
    <div className={`w-12 h-12 rounded-lg overflow-hidden bg-gray-100 ${className}`}>
      {images.length === 0 ? (
        // No images - show folder icon
        <div className="w-full h-full flex items-center justify-center">
          <Folder className="w-6 h-6 text-gray-400" />
        </div>
      ) : images.length === 1 ? (
        // 1 image - show single image
        <div className="w-full h-full relative">
          <Image src={images[0]} alt={collectionName} fill className="object-cover" sizes="48px" />
        </div>
      ) : (
        // Multiple images - show 4-image grid layout
        <div className="w-full h-full grid grid-cols-2 gap-0.5">
          {placeholderImages.map((_, index) => (
            <div key={index} className="relative bg-gray-200">
              {images[index] ? (
                <Image
                  src={images[index]}
                  alt={`${collectionName} - ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="24px"
                />
              ) : (
                <div className="w-full h-full bg-gray-100" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface CollectionDetailViewProps {
  collection: CollectionWithItems;
  onBack: () => void;
}

export default function CollectionDetailView({ collection, onBack }: CollectionDetailViewProps) {
  // const [properties, setProperties] = useState<Property[]>([]);
  const [removingProperty, setRemovingProperty] = useState<{ id: string; name: string } | null>(
    null
  );
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const { removeFromCollection, isRemovingFromCollection } = useCollections();

  // // Get favorite properties to check favorite status
  // const { favoriteProperties } = useFavoriteProperty();

  // // Get collection with properties
  // const {
  //   data: collectionWithProperties,
  //   isLoading: isLoadingProperties,
  //   error: propertiesError,
  // } = useCollectionWithProperties(collection.id);

  // useEffect(() => {
  //   if (collectionWithProperties) {
  //     // Get list of favorite property IDs
  //     const favoritePropertyIds = favoriteProperties?.data?.[0]?.properties?.map(p => p.id) || [];

  //     // Update properties with correct favorite status
  //     const updatedProperties = (collectionWithProperties.properties || []).map(property => ({
  //       ...property,
  //       isFavorite: favoritePropertyIds.includes(property.id),
  //     }));

  //     setProperties(updatedProperties);
  //   }
  // }, [collectionWithProperties, favoriteProperties]);

  const handleRemoveProperty = (propertyId: string, propertyName: string) => {
    setRemovingProperty({ id: propertyId, name: propertyName });
  };

  const handleConfirmRemove = () => {
    if (removingProperty) {
      removeFromCollection({ collectionId: collection.id, propertyId: removingProperty.id });
      setRemovingProperty(null);
    }
  };

  const handleCancelRemove = () => {
    setRemovingProperty(null);
  };

  return (
    <TooltipProvider>
      <div className="space-y-4 sm:space-y-6">
        {/* Mobile-optimized Header */}
        <div className="space-y-4">
          {/* Back button - Mobile optimized */}
          <Button
            variant="ghost"
            onClick={onBack}
            className="flex items-center gap-2 -ml-2 sm:-ml-4 px-2 sm:px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm sm:text-base">Quay lại bộ sưu tập</span>
          </Button>

          {/* Title and description with thumbnail */}
          <div className="space-y-3">
            <div className="flex items-center gap-4">
              <CollectionThumbnail
                images={collection.collectionImage || []}
                collectionName={collection.name}
                className="w-16 h-16 sm:w-20 sm:h-20"
              />
              <div className="flex-1">
                <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold leading-tight">
                  {collection.name}
                </h1>
                <div className="flex flex-col xs:flex-row xs:items-center gap-2 xs:gap-4 mt-2">
                  <Badge variant="secondary" className="w-fit px-3 py-1">
                    {collection.itemCount} bất động sản
                  </Badge>
                  <span className="text-xs sm:text-sm text-gray-500">
                    Cập nhật {new Date(collection.updatedAt).toLocaleDateString('vi-VN')}
                  </span>
                </div>
              </div>
              {/* Share button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsShareDialogOpen(true)}
                className="flex items-center gap-2 shrink-0"
              >
                <Share2 className="h-4 w-4" />
                <span className="hidden sm:inline">Chia sẻ</span>
              </Button>
            </div>
            {collection.description && (
              <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                {collection.description}
              </p>
            )}
          </div>
        </div>

        {/* Properties Grid - Mobile-first responsive */}
        {collection.properties?.length === 0 ? (
          <div className="text-center py-16 sm:py-20">
            <div className="mx-auto max-w-md px-4">
              <ImageIcon className="h-16 w-16 sm:h-20 sm:w-20 text-gray-300 mx-auto mb-6" />
              <h3 className="text-lg sm:text-xl font-medium text-gray-900 mb-2">
                Chưa có bất động sản nào
              </h3>
              <p className="text-sm sm:text-base text-gray-500 leading-relaxed">
                Bộ sưu tập này chưa có bất động sản nào. Hãy thêm một số bất động sản yêu thích của
                bạn!
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
            {collection.properties?.map((property: Property) => (
              <div key={property.id} className="relative group">
                <PropertyCard property={property} size="md" />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  onClick={() => handleRemoveProperty(property.id, property.title)}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        <DeleteConfirmationDialog
          isOpen={!!removingProperty}
          onClose={handleCancelRemove}
          onConfirm={handleConfirmRemove}
          title="Xóa bất động sản"
          description="Bạn có chắc chắn muốn xóa bất động sản này khỏi bộ sưu tập không?"
          itemName={removingProperty?.name}
          type="property"
          isLoading={isRemovingFromCollection}
        />

        <ShareDialog
          isOpen={isShareDialogOpen}
          onClose={() => setIsShareDialogOpen(false)}
          collection={collection}
        />
      </div>
    </TooltipProvider>
  );
}
