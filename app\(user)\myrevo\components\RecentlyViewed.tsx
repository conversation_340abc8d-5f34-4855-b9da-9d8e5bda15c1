'use client';

import { useState, useMemo } from 'react';
import { useRecentlyViewed } from '@/hooks/useRecentlyViewed';
import { PropertyCard } from '@/components/PropertyCard';
import { ViewedTimeFilter } from './ViewedTimeFilter';
import { PropertyGroupSection } from './PropertyGroupSection';
import {
  ViewedTimeFilter as ViewedTimeFilterType,
  filterPropertiesByViewedTime,
  groupPropertiesByViewedTime,
  getGroupLabel,
} from '@/utils/dates/filterViewedTime';

export default function RecentlyViewed() {
  const { allRecentlyViewedWithTime, isLoading, error } = useRecentlyViewed({ serverOnly: true });
  const [selectedFilter, setSelectedFilter] = useState<ViewedTimeFilterType>('all');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  // Filter properties based on selected filter and date
  const filteredProperties = useMemo(() => {
    return filterPropertiesByViewedTime(allRecentlyViewedWithTime, selectedFilter, selectedDate);
  }, [allRecentlyViewedWithTime, selectedFilter, selectedDate]);

  // Group properties by viewed time when showing all
  const groupedProperties = useMemo(() => {
    if (selectedFilter === 'all') {
      return groupPropertiesByViewedTime(allRecentlyViewedWithTime);
    }
    return null;
  }, [allRecentlyViewedWithTime, selectedFilter]);

  const handleFilterChange = (filter: ViewedTimeFilterType, date?: Date) => {
    setSelectedFilter(filter);
    setSelectedDate(date);
  };

  console.log('🔍 MyRevo RecentlyViewed State:', {
    propertiesCount: allRecentlyViewedWithTime.length,
    filteredCount: filteredProperties.length,
    selectedFilter,
    selectedDate,
    isLoading,
    error,
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900">
            Đã xem gần đây
          </h2>
          <div className="flex-shrink-0">
            <div className="animate-pulse bg-gray-200 h-10 w-48 rounded-md"></div>
          </div>
        </div>
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-200 aspect-square rounded-2xl mb-3"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Đã xem gần đây</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (allRecentlyViewedWithTime.length === 0) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Đã xem gần đây</h2>
        <div className="text-center py-8">
          <p className="text-gray-500">Chưa có bất động sản nào được xem</p>
        </div>
      </div>
    );
  }

  // Show filter and filtered results
  if (filteredProperties.length === 0 && selectedFilter !== 'all') {
    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900">
            Đã xem gần đây ({allRecentlyViewedWithTime.length})
          </h2>
          <div className="flex-shrink-0">
            <ViewedTimeFilter
              properties={allRecentlyViewedWithTime}
              selectedFilter={selectedFilter}
              selectedDate={selectedDate}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">
            Không có bất động sản nào được xem{' '}
            {selectedFilter === 'today'
              ? 'hôm nay'
              : selectedFilter === 'yesterday'
                ? 'hôm qua'
                : selectedFilter === 'custom' && selectedDate
                  ? `ngày ${selectedDate.getDate()}/${selectedDate.getMonth() + 1}/${selectedDate.getFullYear()}`
                  : 'trong thời gian này'}
          </p>
        </div>
      </div>
    );
  }

  // Show empty state when all groups are empty (for 'all' filter)
  if (
    selectedFilter === 'all' &&
    groupedProperties &&
    groupedProperties.today.length === 0 &&
    groupedProperties.yesterday.length === 0 &&
    groupedProperties.older.length === 0
  ) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900">
            Đã xem gần đây ({allRecentlyViewedWithTime.length})
          </h2>
          <div className="flex-shrink-0">
            <ViewedTimeFilter
              properties={allRecentlyViewedWithTime}
              selectedFilter={selectedFilter}
              selectedDate={selectedDate}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500">Chưa có bất động sản nào được xem</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header section with responsive layout */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-900">
          Đã xem gần đây ({allRecentlyViewedWithTime.length})
        </h2>
        <div className="flex-shrink-0">
          <ViewedTimeFilter
            properties={allRecentlyViewedWithTime}
            selectedFilter={selectedFilter}
            selectedDate={selectedDate}
            onFilterChange={handleFilterChange}
          />
        </div>
      </div>

      {/* Render grouped properties when filter is 'all' */}
      {selectedFilter === 'all' && groupedProperties ? (
        <div className="space-y-8">
          <PropertyGroupSection
            title={getGroupLabel('today')}
            properties={groupedProperties.today}
          />
          <PropertyGroupSection
            title={getGroupLabel('yesterday')}
            properties={groupedProperties.yesterday}
          />
          <PropertyGroupSection
            title={getGroupLabel('older')}
            properties={groupedProperties.older}
          />
        </div>
      ) : /* Render filtered properties for other filters */
      filteredProperties.length > 0 ? (
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {filteredProperties.map(({ property, viewedAt, id }) => (
            <PropertyCard
              key={id}
              property={property}
              priority={false}
              size="md"
              viewedTime={viewedAt}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">Không có bất động sản nào phù hợp với bộ lọc</p>
        </div>
      )}
    </div>
  );
}
