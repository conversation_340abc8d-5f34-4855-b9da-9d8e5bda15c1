import { numberToVietnameseMoney } from '@/utils/numbers/numberToVietnameseMoney';
import { FormData } from '../types';
import { translatePaymentTerm } from '../../../utils/formatPayTems';

export const generateRentalContractHtml = (formData: FormData): string => {
  const { partyA, partyB, terms, clausesArticle4to6, clausesarticle7to10 } = formData;

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  // Format dates with error handling
  const formatDate = (dateString: string): { day: string; month: string; year: string } => {
    if (!dateString) {
      return { day: '….', month: '…', year: '….' };
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return { day: '….', month: '…', year: '….' };
      }

      return {
        day: date.getDate().toString().padStart(2, '0'),
        month: (date.getMonth() + 1).toString().padStart(2, '0'),
        year: date.getFullYear().toString(),
      };
    } catch {
      return { day: '….', month: '…', year: '….' };
    }
  };

  const startDateFormatted = formatDate(terms.leaseStartDate);
  const endDateFormatted = formatDate(terms.leaseEndDate);

  return `
    <style>
      .contract-container, .contract-container * {
        font-family: 'Times New Roman', serif !important;
      }
      h1, h2, h3, h4, h5, h6 {
        font-family: 'Times New Roman', serif !important;
      }
      p, div, span, strong, em {
        font-family: 'Times New Roman', serif !important;
      }
      .text-center {
        text-align: center !important;
      }
      .text-right {
        text-align: right !important;
      }
      .text-left {
        text-align: left !important;
      }
      .article-header {
        text-align: left !important;
        font-weight: bold !important;
        margin: 15px 0 8px 0 !important;
      }
      .section-header {
        text-align: left !important;
        font-weight: bold !important;
        margin: 15px 0 8px 0 !important;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
      }
      td {
        padding: 10px;
        text-align: center;
        vertical-align: top;
        border: none;
      }
      .signature-section table {
        margin-top: 50px;
      }
      .signature-section td {
        width: 50%;
        padding: 0 20px;
      }
      .signature-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 30px;
      }
      .signature-cell {
        width: 50%;
        text-align: center;
        vertical-align: top;
        padding: 20px;
        border: none;
      }
    </style>
    <div class="contract-container" style="font-family: 'Times New Roman', serif;">
      <h2 class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><strong>CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</strong></h2>
      <p class="text-center" style="text-align: center; font-weight: bold; font-family: 'Times New Roman', serif;">Độc lập - Tự do - Hạnh phúc</p>
      <p class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;">---------------o0o---------------</p>
      <h3 class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><strong>HỢP ĐỒNG THUÊ CĂN HỘ NHÀ CHUNG CƯ</strong></h3>
      <p class="text-center" style="text-align: center; font-family: 'Times New Roman', serif;"><em>(Số: ……………./HĐTCHNCC)</em></p>
      <br/>

      <div class="text-right" style="text-align: right; font-family: 'Times New Roman', serif;">
        <p>……………, ngày …… tháng …… năm ……</p>
      </div>

      <br/>
    <p style="font-family: 'Times New Roman', serif;">Chúng tôi gồm có:</p>
    <h3 class="section-header ql-align-left" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>BÊN CHO THUÊ (BÊN A):</strong></h3>
    <p style="font-family: 'Times New Roman', serif;">Tên: <strong>${partyA.name || '………………………………………………………'}</strong></p>
    <p style="font-family: 'Times New Roman', serif;">Địa chỉ: ${partyA.address || '……………………………………………'}</p>
    <p style="font-family: 'Times New Roman', serif;">Điện thoại: ${partyA.phone || '…………………………………………'} Fax: ${partyA.fax || '…………………'}</p>
    <p style="font-family: 'Times New Roman', serif;">Mã số thuế: ${partyA.taxCode || '……………………………………………………………'}</p>
    <p style="font-family: 'Times New Roman', serif;">Tài khoản số: ${partyA.accountNumber || '……………………………………………….'}</p>
    <p style="font-family: 'Times New Roman', serif;">Do ông (bà): ${
      partyA.representative || '……………………………………………'
    } Năm sinh: ${partyA.birthYear || '…………'}</p>
    <p style="font-family: 'Times New Roman', serif;">Chức vụ: ${partyA.position || '……………………………'} làm đại diện.</p>
    <p style="font-family: 'Times New Roman', serif;">Số CCCD (hộ chiếu): ${
      partyA.idNumber || '……………'
    } cấp ngày ${partyA.idIssuedDate || '....../...../...'}, tại ${
      partyA.idIssuedPlace || '…………….'
    }</p>


    <h3 class="section-header ql-align-left" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>BÊN THUÊ (BÊN B):</strong></h3>
    <p style="font-family: 'Times New Roman', serif;">Tên: <strong>${partyB.name || '…………………………………………………………………………'}</strong></p>
    <p style="font-family: 'Times New Roman', serif;">Địa chỉ: ${partyB.address || '.…………………………………………………………………………………………………………'}</p>
    <p style="font-family: 'Times New Roman', serif;">Điện thoại: ${partyB.phone || '………………………………………………'} Email: ${
      partyB.email || '……………………………………….………'
    }</p>
    <p style="font-family: 'Times New Roman', serif;">Số CMND/CCCD: ${partyB.idNumber || '.......................'} cấp ngày ${
      partyB.birthDate || '....../...../.....'
    }</p>

    <p style="font-family: 'Times New Roman', serif;">Hai bên cùng thỏa thuận ký hợp đồng với những nội dung sau:</p>

    <h3 class="article-header ql-align-left" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>ĐIỀU 1: ĐỐI TƯỢNG VÀ NỘI DUNG CỦA HỢP ĐỒNG</strong></h3>
    <p style="font-family: 'Times New Roman', serif;"><strong>1.1.</strong> Bên A cho bên B thuê: ${terms.propertyType || 'căn hộ'}</p>
    <p style="font-family: 'Times New Roman', serif;">Tại: ${
      terms.propertyAddress ||
      '.................................................................................................................................'
    }</p>
    <p style="font-family: 'Times New Roman', serif;">Để sử dụng vào mục đích: ${terms.propertyPurpose || 'ở'}</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>1.2.</strong> Quyền sở hữu của bên A đối với căn hộ theo giấy chứng nhận quyền sử dụng đất và quyền sở hữu nhà ở, cụ thể như sau:</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>a)</strong> Địa chỉ căn hộ: ${
      terms.propertyAddress ||
      '............................................................................................'
    }</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>b)</strong> Căn hộ số: ${
      terms.apartmentNumber ||
      '.................................................................................................'
    }</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>c)</strong> Số tầng nhà chung cư: ${
      terms.floor ||
      '..................................................................................'
    }</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>d)</strong> Tổng diện tích sàn căn hộ là: ${terms.area || '...............'} m2</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>e)</strong> Trang thiết bị gắn liền với căn hộ: ${
      terms.facilities || '..................................................................'
    }</p>

    <h3 class="article-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>ĐIỀU 2: GIÁ THUÊ, PHƯƠNG THỨC VÀ THỜI HẠN THANH TOÁN</strong></h3>
    <p style="font-family: 'Times New Roman', serif;"><strong>2.1.</strong> Giá cho thuê nhà ở là <strong>${formatCurrency(
      terms.monthlyRent
    )}</strong> đồng Việt Nam/01 tháng.</p>
    <p style="font-family: 'Times New Roman', serif;">(Bằng chữ: <em>${numberToVietnameseMoney(terms.monthlyRent)} đồng</em>).</p>
    <p style="font-family: 'Times New Roman', serif;">Giá cho thuê này đã bao gồm chi phí bảo trì, quản lý vận hành nhà ở và các khoản thuế mà Bên A phải nộp cho Nhà nước theo quy định.</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>2.2.</strong> Các chi phí sử dụng điện, nước, điện thoại và các dịch vụ khác do Bên B thanh toán cho bên cung cấp điện, nước, điện thoại và các cơ quan cung cấp dịch vụ khác.</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>2.3.</strong> Phương thức thanh toán: thanh toán bằng tiền Việt Nam thông qua hình thức: <strong>${translatePaymentTerm(
      terms.paymentMethod
    )}</strong></p>
    <p style="font-family: 'Times New Roman', serif;"><strong>2.4.</strong> Thời hạn thanh toán: Bên B trả tiền thuê nhà vào ngày <strong>${
      terms.paymentDay
    }</strong> hàng tháng.</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>2.5.</strong> Tiền đặt cọc: <strong>${formatCurrency(
      terms.deposit
    )}</strong> đồng (Bằng chữ: <em>${numberToVietnameseMoney(terms.deposit)} đồng</em>).</p>

    <h3 class="article-header" style="text-align: left; font-weight: bold; margin: 15px 0 8px 0; font-family: 'Times New Roman', serif;"><strong>ĐIỀU 3: THỜI ĐIỂM GIAO NHẬN VÀ THỜI HẠN THUÊ NHÀ Ở</strong></h3>
    <p style="font-family: 'Times New Roman', serif;"><strong>3.1.</strong> Thời điểm giao nhận nhà ở là ngày <strong>${
      startDateFormatted.day
    }</strong> tháng <strong>${startDateFormatted.month}</strong> năm <strong>${
      startDateFormatted.year
    }</strong>.</p>
    <p style="font-family: 'Times New Roman', serif;"><strong>3.2.</strong> Thời hạn cho thuê nhà ở kể từ ngày <strong>${
      startDateFormatted.day
    }</strong> tháng <strong>${startDateFormatted.month}</strong> năm <strong>${
      startDateFormatted.year
    }</strong> đến ngày <strong>${endDateFormatted.day}</strong> tháng <strong>${
      endDateFormatted.month
    }</strong> năm <strong>${endDateFormatted.year}</strong>.</p>

    <div style="white-space: pre-wrap;">${
      clausesArticle4to6.article4to6 || 'Nội dung điều khoản 4-6 chưa được cập nhật.'
    }</div>

    <div style="white-space: pre-wrap;">${
      clausesarticle7to10.article7to10 || 'Nội dung điều khoản 7-10 chưa được cập nhật.'
    }</div>

  </div>
  `;
};
