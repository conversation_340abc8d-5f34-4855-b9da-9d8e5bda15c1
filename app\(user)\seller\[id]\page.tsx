import { Metadata } from 'next';
import SellerProfileClient from './components/SellerProfileClient';
import { userService } from '@/lib/api/services/fetchUser';
import { feedbackService } from '@/lib/api/services/fetchFeedback';

export interface SellerProfilePageProps {
  params: { id: string };
}

export async function generateMetadata({ params }: SellerProfilePageProps): Promise<Metadata> {
  try {
    const response = await userService.getSellerProfileById(params.id);
    const profile = response.data;
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://revoland.vn';

    if (!profile) {
      return {
        title: '<PERSON>ôi giới không tồn tại - RevoLand',
        description: 'Không tìm thấy thông tin môi giới này',
        robots: {
          index: false,
          follow: false,
        },
      };
    }

    // Fetch feedback stats for additional metadata
    let feedbackStats = null;
    try {
      const statsResponse = await feedbackService.getFeedbackStats(params.id);
      feedbackStats = statsResponse.data;
    } catch (error) {
      console.log('Could not fetch feedback stats:', error);
    }

    // Construct the agent profile URL
    const profileUrl = `${baseUrl}/seller/${profile.sellerId}`;

    // Create SEO-friendly title and description
    const seoTitle = generateSEOTitle(profile, feedbackStats);
    const seoDescription = generateSEODescription(profile, feedbackStats);

    // Get the main image for Open Graph
    const ogImageUrl = profile.avatar ? profile.avatar : `${baseUrl}/images/default-avatar.jpg`;

    return {
      title: seoTitle,
      description: seoDescription,
      openGraph: {
        type: 'profile',
        title: seoTitle,
        description: seoDescription,
        images: [
          {
            url: ogImageUrl,
            width: 1200,
            height: 630,
            alt: `${profile.name} - Chuyên viên bất động sản`,
          },
        ],
        siteName: 'RevoLand',
        locale: 'vi_VN',
        url: profileUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title: seoTitle,
        description: seoDescription,
        images: [ogImageUrl],
        site: '@RevoLand',
      },
      alternates: {
        canonical: profileUrl,
      },
      keywords: [
        profile.name,
        'môi giới bất động sản',
        'chuyên viên bất động sản',
        'agent bất động sản',
        profile.companyName,
        profile.fieldOfWorks,
        profile.serviceLocations,
        'RevoLand',
        'mua bán nhà đất',
        'cho thuê bất động sản',
        'tư vấn bất động sản',
        ...generateAgentKeywords(profile),
      ].filter((keyword): keyword is string => Boolean(keyword)),
      creator: 'RevoLand',
      publisher: 'RevoLand',
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'profile:first_name': profile.name?.split(' ')[0] || '',
        'profile:last_name': profile.name?.split(' ').slice(1).join(' ') || '',
        'profile:username': profile.sellerId,
        'agent:company': profile.companyName || '',
        'agent:experience_years': profile.yearsOfExperience?.toString() || '',
        'agent:service_locations': profile.serviceLocations || '',
        'agent:field_of_work': profile.fieldOfWorks || '',
        'agent:total_properties': profile.properties?.length.toString() || '0',
        'agent:rating': feedbackStats?.averageRating?.toString() || '',
        'agent:review_count':
          feedbackStats?.totalFeedback?.toString() ||
          profile.agentFeedbacks?.length.toString() ||
          '0',
        'agent:joined_date': profile.joinedAt || '',
      },
    };
  } catch (error) {
    console.error('Error generating seller metadata:', error);
    return {
      title: 'Môi giới bất động sản - RevoLand',
      description: 'Khám phá các chuyên viên bất động sản chuyên nghiệp tại RevoLand',
    };
  }
}

function generateSEOTitle(profile: any, feedbackStats?: any): string {
  const parts: string[] = [];

  // 1. Agent name
  parts.push(profile.name || 'Chuyên viên bất động sản');

  // 2. Company if available
  if (profile.companyName) {
    parts.push(`- ${profile.companyName}`);
  }

  // 3. Experience if available
  if (profile.yearsOfExperience) {
    parts.push(`- ${profile.yearsOfExperience} năm kinh nghiệm`);
  }

  // 4. Rating if available
  if (feedbackStats?.averageRating && feedbackStats.averageRating > 0) {
    parts.push(`- ${feedbackStats.averageRating.toFixed(1)} ⭐`);
  }

  // 5. End with RevoLand
  const title = parts.join(' ') + ' | RevoLand';

  // Ensure title doesn't exceed recommended length (60 characters for optimal SEO)
  return title.length > 60 ? `${title.substring(0, 57)}...` : title;
}

function generateSEODescription(profile: any, feedbackStats?: any): string {
  const parts: string[] = [];

  // Start with professional introduction
  parts.push(`Liên hệ với ${profile.name || 'chuyên viên bất động sản'}`);

  // Add company info
  if (profile.companyName) {
    parts.push(`tại ${profile.companyName}`);
  }

  // Add experience and specialization
  const details: string[] = [];

  if (profile.yearsOfExperience) {
    details.push(`${profile.yearsOfExperience} năm kinh nghiệm`);
  }

  if (profile.fieldOfWorks) {
    details.push(`chuyên về ${profile.fieldOfWorks}`);
  }

  if (profile.serviceLocations) {
    details.push(`phục vụ khu vực ${profile.serviceLocations}`);
  }

  if (details.length > 0) {
    parts.push(`- ${details.join(', ')}`);
  }

  // Add properties and rating info
  const stats: string[] = [];

  if (profile.properties?.length > 0) {
    stats.push(`${profile.properties.length} bất động sản`);
  }

  if (feedbackStats?.averageRating && feedbackStats.averageRating > 0) {
    stats.push(
      `${feedbackStats.averageRating.toFixed(1)} ⭐ (${feedbackStats.totalFeedback || profile.agentFeedbacks?.length || 0} đánh giá)`
    );
  }

  if (stats.length > 0) {
    parts.push(`- Hiện có ${stats.join(', ')}.`);
  }

  // Add call to action
  parts.push('Tư vấn miễn phí, hỗ trợ mua bán và cho thuê bất động sản chuyên nghiệp.');

  const description = parts.join(' ');

  // Ensure description doesn't exceed recommended length (160 characters for optimal SEO)
  return description.length > 160 ? `${description.substring(0, 157)}...` : description;
}

function generateAgentKeywords(profile: any): string[] {
  const keywords: string[] = [];

  // Add experience level keywords
  if (profile.yearsOfExperience) {
    if (profile.yearsOfExperience >= 10) {
      keywords.push('môi giới bất động sản giàu kinh nghiệm', 'chuyên gia bất động sản');
    } else if (profile.yearsOfExperience >= 5) {
      keywords.push(
        'môi giới bất động sản có kinh nghiệm',
        'chuyên viên bất động sản chuyên nghiệp'
      );
    } else {
      keywords.push('môi giới bất động sản trẻ', 'chuyên viên bất động sản năng động');
    }
  }

  // Add location-based keywords
  if (profile.serviceLocations) {
    const locations = profile.serviceLocations.split(',').map((loc: string) => loc.trim());
    locations.forEach((location: string) => {
      keywords.push(`môi giới bất động sản ${location}`, `agent bất động sản ${location}`);
    });
  }

  // Add field of work keywords
  if (profile.fieldOfWorks) {
    keywords.push(`chuyên viên ${profile.fieldOfWorks}`, `môi giới ${profile.fieldOfWorks}`);
  }

  // Add company-related keywords
  if (profile.companyName) {
    keywords.push(`${profile.companyName} agent`, `nhân viên ${profile.companyName}`);
  }

  return keywords;
}

export default async function SellerProfilePage({ params }: SellerProfilePageProps) {
  return <SellerProfileClient sellerId={params.id} />;
}
