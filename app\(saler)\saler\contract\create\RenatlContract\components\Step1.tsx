'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { partyASchema, PartyAInput } from '../schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface Step1Props {
  data: PartyAInput;
  onNext: (data: PartyAInput) => void;
}

const Step1: React.FC<Step1Props> = ({ data, onNext }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<PartyAInput>({
    resolver: zodResolver(partyASchema),
    defaultValues: data,
    mode: 'onChange',
  });

  const onSubmit = (formData: PartyAInput) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Bước 1: Thông tin Bên A (Bên cho thuê)
        </CardTitle>
        <CardDescription className="text-center">
          Vui lòng nhập đầy đủ thông tin của bên cho thuê
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Tên *
              </Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Nhập tên đầy đủ"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium">
                Số điện thoại *
              </Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="Ví dụ: 0901234567"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-sm text-red-500">{errors.phone.message}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="text-sm font-medium">
              Địa chỉ *
            </Label>
            <Input
              id="address"
              {...register('address')}
              placeholder="Nhập địa chỉ đầy đủ"
              className={errors.address ? 'border-red-500' : ''}
            />
            {errors.address && <p className="text-sm text-red-500">{errors.address.message}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fax" className="text-sm font-medium">
                Fax
              </Label>
              <Input id="fax" {...register('fax')} placeholder="Số fax (không bắt buộc)" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="taxCode" className="text-sm font-medium">
                Mã số thuế
              </Label>
              <Input
                id="taxCode"
                {...register('taxCode')}
                placeholder="Mã số thuế (không bắt buộc)"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="accountNumber" className="text-sm font-medium">
                Số tài khoản*
              </Label>
              <Input
                id="accountNumber"
                {...register('accountNumber')}
                placeholder="Số tài khoản ngân hàng"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email*
              </Label>
              <Input
                id="email"
                {...register('email')}
                placeholder="Nhập email"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="representative" className="text-sm font-medium">
                Người đại diện *
              </Label>
              <Input
                id="representative"
                {...register('representative')}
                placeholder="Tên người đại diện"
                className={errors.representative ? 'border-red-500' : ''}
              />
              {errors.representative && (
                <p className="text-sm text-red-500">{errors.representative.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="birthYear" className="text-sm font-medium">
                Năm sinh *
              </Label>
              <Input
                id="birthYear"
                {...register('birthYear')}
                placeholder="Ví dụ: 1980"
                className={errors.birthYear ? 'border-red-500' : ''}
              />
              {errors.birthYear && (
                <p className="text-sm text-red-500">{errors.birthYear.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="position" className="text-sm font-medium">
              Chức vụ *
            </Label>
            <Input
              id="position"
              {...register('position')}
              placeholder="Ví dụ: Giám đốc, Chủ sở hữu..."
              className={errors.position ? 'border-red-500' : ''}
            />
            {errors.position && <p className="text-sm text-red-500">{errors.position.message}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="idNumber" className="text-sm font-medium">
                Số CMND/CCCD *
              </Label>
              <Input
                id="idNumber"
                {...register('idNumber')}
                placeholder="9 hoặc 12 số"
                className={errors.idNumber ? 'border-red-500' : ''}
              />
              {errors.idNumber && <p className="text-sm text-red-500">{errors.idNumber.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="idIssuedDate" className="text-sm font-medium">
                Ngày cấp *
              </Label>
              <Input
                id="idIssuedDate"
                type="date"
                {...register('idIssuedDate')}
                className={errors.idIssuedDate ? 'border-red-500' : ''}
              />
              {errors.idIssuedDate && (
                <p className="text-sm text-red-500">{errors.idIssuedDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="idIssuedPlace" className="text-sm font-medium">
                Nơi cấp *
              </Label>
              <Input
                id="idIssuedPlace"
                {...register('idIssuedPlace')}
                placeholder="Ví dụ: CA TP.HCM"
                className={errors.idIssuedPlace ? 'border-red-500' : ''}
              />
              {errors.idIssuedPlace && (
                <p className="text-sm text-red-500">{errors.idIssuedPlace.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={!isValid}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              Tiếp theo
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default Step1;
