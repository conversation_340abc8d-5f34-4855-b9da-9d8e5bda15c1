'use client';
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import {
  Map,
  AdvancedMarker,
  InfoWindow,
  useAdvancedMarkerRef,
  useMap,
} from '@vis.gl/react-google-maps';
import Image from 'next/image';
import { HomeIcon, School, Utensils, Hospital, Store, Leaf, Bike, Coffee } from 'lucide-react';
import { useIsMobile } from '@/hooks/useMobile';
import { NearbyPlaces } from './NearbyPlaces';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

function PropertyMarker({
  position,
  onClick,
}: {
  position: google.maps.LatLngLiteral;
  onClick: () => void;
}) {
  const [markerRef] = useAdvancedMarkerRef();
  return (
    <AdvancedMarker ref={markerRef} position={position} onClick={onClick}>
      <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
        <HomeIcon className="w-5 h-5 text-white" />
      </div>
    </AdvancedMarker>
  );
}

function PropertyInfoWindow({
  position,
  title,
  address,
  imageUrl,
  onClose,
}: {
  position: google.maps.LatLngLiteral;
  title: string;
  address: string;
  imageUrl: string;
  onClose: () => void;
}) {
  return (
    <InfoWindow
      position={position}
      onCloseClick={onClose}
      headerDisabled
      shouldFocus={false}
      disableAutoPan={true}
      pixelOffset={[0, -50]}
    >
      <div className="bg-white flex items-center gap-3 rounded-lg max-w-96 shadow-lg">
        <div className="w-16 h-16 md:w-24 md:h-16 flex-shrink-0">
          <Image
            src={imageUrl || '/placeholder.svg'}
            alt={title}
            width={96}
            height={64}
            className="w-full h-full object-cover rounded-md"
          />
        </div>
        <div className="flex flex-col">
          <h3 className="font-medium text-sm md:text-base mb-1 truncate">{title}</h3>
          <p className="text-xs text-gray-600">{address}</p>
        </div>
      </div>
    </InfoWindow>
  );
}

function RouteDrawer({
  origin,
  destination,
}: {
  origin: google.maps.LatLngLiteral;
  destination: google.maps.LatLngLiteral | null;
}) {
  const map = useMap();
  const directionsRendererRef = useRef<google.maps.DirectionsRenderer | null>(null);

  useEffect(() => {
    if (!map || !origin) return;

    if (!directionsRendererRef.current) {
      directionsRendererRef.current = new google.maps.DirectionsRenderer({
        suppressMarkers: true, // ✅ no A/B markers
      });
      directionsRendererRef.current.setMap(map);
    }

    if (!destination) {
      directionsRendererRef.current.setDirections({
        routes: [],
      } as unknown as google.maps.DirectionsResult);
      return;
    }

    const directionsService = new google.maps.DirectionsService();
    directionsService.route(
      {
        origin,
        destination,
        travelMode: google.maps.TravelMode.DRIVING,
      },
      (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          directionsRendererRef.current?.setDirections(result);
        } else {
          console.error('Directions request failed:', status);
        }
      }
    );
  }, [map, origin, destination]);

  useEffect(() => {
    return () => {
      if (directionsRendererRef.current) {
        directionsRendererRef.current.setMap(null);
        directionsRendererRef.current = null;
      }
    };
  }, []);

  return null;
}

export default function PropertyMap({
  latitude,
  longitude,
  address,
  title,
  imageUrl,
}: {
  latitude: number;
  longitude: number;
  address: string;
  title: string;
  imageUrl: string;
}) {
  const isMobile = useIsMobile();
  const [showInfoWindow, setShowInfoWindow] = useState(false);
  const [selectedType, setSelectedType] = useState<
    'school' | 'restaurant' | 'hospital' | 'supermarket' | 'park' | 'cafe'
  >('school');
  const [placesList, setPlacesList] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedPlaceId, setSelectedPlaceId] = useState<string | null>(null);
  const [routeDestination, setRouteDestination] = useState<google.maps.LatLngLiteral | null>(null);
  const itemsPerPage = 6;
  const [travelInfo, setTravelInfo] = useState<
    Record<string, { distance: string; duration: string }>
  >({});

  const typeTabs = [
    { label: 'Trường học', value: 'school' as const, Icon: School },
    { label: 'Nhà hàng', value: 'restaurant' as const, Icon: Utensils },
    { label: 'Bệnh viện', value: 'hospital' as const, Icon: Hospital },
    { label: 'Siêu thị', value: 'supermarket' as const, Icon: Store },
    { label: 'Công viên', value: 'park' as const, Icon: Leaf },
    { label: 'Cafe', value: 'cafe' as const, Icon: Coffee },
  ];
  const tabRefs = useRef<Array<HTMLButtonElement | null>>([]);
  const [underlineStyle, setUnderlineStyle] = useState<{ left: number; width: number }>({
    left: 0,
    width: 0,
  });

  const position = { lat: latitude, lng: longitude };

  // Get the map instance
  const map = useMap();

  const handlePlaceClick = (place: any) => {
    const destinationLatLng = {
      lat: place.geometry.location.lat(),
      lng: place.geometry.location.lng(),
    };
    setRouteDestination(destinationLatLng);
    setSelectedPlaceId(place.place_id);

    // Re-fetch for the clicked place to ensure it's up-to-date, though it should already be there
    if (!map) return; // Ensure map is loaded before making service calls

    const directionsService = new google.maps.DirectionsService();
    directionsService.route(
      {
        origin: position,
        destination: destinationLatLng,
        travelMode: google.maps.TravelMode.DRIVING,
      },
      (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          const leg = result.routes[0].legs[0];
          setTravelInfo(prev => ({
            ...prev,
            [place.place_id]: {
              distance: leg.distance?.text || '',
              duration: leg.duration?.text || '',
            },
          }));
        } else {
          console.error('Directions request failed:', status);
        }
      }
    );
  };

  useEffect(() => {
    setShowInfoWindow(!isMobile);
  }, [isMobile]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedType]);

  useEffect(() => {
    setSelectedPlaceId(null);
    setRouteDestination(null);
  }, [selectedType]);

  const totalPages = Math.ceil(placesList.length / itemsPerPage);
  const paginatedPlaces = placesList.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // New useEffect to fetch distance and time for all paginated places
  useEffect(() => {
    if (!map) return; // Ensure map is loaded before making service calls

    const directionsService = new google.maps.DirectionsService();

    paginatedPlaces.forEach(place => {
      // Only fetch if we don't already have the info for this place
      if (!travelInfo[place.place_id]) {
        const destinationLatLng = {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng(),
        };

        directionsService.route(
          {
            origin: position,
            destination: destinationLatLng,
            travelMode: google.maps.TravelMode.DRIVING,
          },
          (result, status) => {
            if (status === google.maps.DirectionsStatus.OK && result) {
              const leg = result.routes[0].legs[0];
              setTravelInfo(prev => ({
                ...prev,
                [place.place_id]: {
                  distance: leg.distance?.text || '',
                  duration: leg.duration?.text || '',
                },
              }));
            } else {
              console.error('Directions request failed:', status);
            }
          }
        );
      }
    });
  }, [placesList, selectedType, travelInfo, map, currentPage]);

  const handleMarkerClick = useCallback(() => {
    setShowInfoWindow(prev => !prev);
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setShowInfoWindow(false);
  }, []);

  useLayoutEffect(() => {
    const idx = typeTabs.findIndex(t => t.value === selectedType);
    const el = tabRefs.current[idx];
    if (el) {
      setUnderlineStyle({ left: el.offsetLeft, width: el.offsetWidth });
    }
  }, [selectedType]);

  return (
    <div className="flex flex-col gap-4">
      <div className="w-full h-[300px] md:h-[560px] rounded-xl overflow-hidden border-2 border-zinc-200 relative">
        <Map
          defaultCenter={position}
          defaultZoom={15}
          mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
          mapTypeControl={false}
          fullscreenControl={false}
          streetViewControl={false}
          className="w-full h-full"
        >
          <PropertyMarker position={position} onClick={handleMarkerClick} />
          {showInfoWindow && (
            <PropertyInfoWindow
              position={position}
              title={title}
              address={address}
              imageUrl={imageUrl}
              onClose={handleInfoWindowClose}
            />
          )}
          <NearbyPlaces
            position={position}
            radius={1200}
            types={[selectedType]}
            onPlacesUpdate={places => setPlacesList(places)}
            onNavigateTo={handlePlaceClick}
          />
          {routeDestination && <RouteDrawer origin={position} destination={routeDestination} />}
        </Map>
      </div>

      {/* Tabs under the map, above the list */}
      <div className="bg-white">
        <Tabs
          value={selectedType}
          onValueChange={v => setSelectedType(v as any)}
          className="w-full"
        >
          <TabsList className="bg-background sticky top-0 z-20 rounded-none p-0 w-full flex overflow-x-auto">
            {typeTabs.map((tab, index) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                ref={el => {
                  tabRefs.current[index] = el;
                }}
                className="text-xs md:text-sm bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none md:flex-1 flex-none inline-flex items-center justify-center gap-2 px-3 py-2 whitespace-nowrap"
              >
                <tab.Icon className="h-4 w-4" />
                {tab.label}
              </TabsTrigger>
            ))}
            <motion.div
              className="bg-primary absolute bottom-0 z-20 h-0.5"
              layoutId="underline"
              style={{ left: underlineStyle.left, width: underlineStyle.width }}
              transition={{ type: 'spring', stiffness: 400, damping: 40 }}
            />
          </TabsList>
          {typeTabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="max-md:pt-2 md:p-4">
              {/* no extra content, tabs just control the list below */}
              <h3 className="font-semibold mb-2 md:mb-3 capitalize text-sm md:text-base">
                {selectedType === 'school'
                  ? 'Trường học'
                  : selectedType === 'restaurant'
                    ? 'Nhà hàng'
                    : selectedType === 'hospital'
                      ? 'Bệnh viện'
                      : selectedType === 'supermarket'
                        ? 'Siêu thị'
                        : selectedType === 'park'
                          ? 'Công viên'
                          : 'Cafe'}{' '}
                gần đây
              </h3>
              {placesList.length === 0 ? (
                <p className="text-sm text-gray-500">Không có {selectedType} gần đây.</p>
              ) : (
                <>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {paginatedPlaces.map(place => {
                      const isSelected = selectedPlaceId === place.place_id;
                      const info = travelInfo[place.place_id];
                      return (
                        <li
                          key={place.place_id}
                          className={`flex items-center justify-between border p-3 rounded-md cursor-pointer transition-colors
                  ${isSelected ? 'bg-red-100 border-red-500' : 'hover:bg-gray-50'}`}
                          onClick={() => handlePlaceClick(place)}
                        >
                          <div className="flex flex-col flex-1 min-w-0">
                            <span className="font-medium text-sm md:text-base truncate">
                              {place.name}
                            </span>
                            <span className="text-[0.7rem] md:text-xs text-gray-600 truncate">
                              {place.vicinity ?? place.formatted_address}
                            </span>
                            {info && (
                              <span
                                className={`mt-1 text-xs md:text-sm ${isSelected ? ' text-red-700 font-bold' : ' text-gray-700'}`}
                              >
                                {info.distance} • {info.duration}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center shrink-0 whitespace-nowrap gap-3 md:gap-4 pl-3 md:pl-4">
                            <Bike className="h-4 w-4 text-gray-500" />
                            {isSelected && (
                              <Badge
                                variant="default"
                                className="bg-red-500 text-white text-[0.65rem] font-semibold"
                              >
                                Đã chọn
                              </Badge>
                            )}
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                  {totalPages > 1 && (
                    <div className="mt-2 md:mt-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              href="#prev"
                              className="rounded-full text-xs md:text-sm"
                              onClick={e => {
                                e.preventDefault();
                                setCurrentPage(p => Math.max(p - 1, 1));
                              }}
                            />
                          </PaginationItem>
                          {(() => {
                            const pagesToShow = 5;
                            const start = Math.max(
                              1,
                              Math.min(currentPage - 2, totalPages - pagesToShow + 1)
                            );
                            const end = Math.min(totalPages, start + pagesToShow - 1);
                            const pages = Array.from(
                              { length: end - start + 1 },
                              (_, i) => start + i
                            );
                            return pages.map(page => (
                              <PaginationItem key={page} className="hidden sm:block">
                                <PaginationLink
                                  href={`#${page}`}
                                  isActive={page === currentPage}
                                  className="rounded-full text-xs md:text-sm"
                                  onClick={e => {
                                    e.preventDefault();
                                    setCurrentPage(page);
                                  }}
                                >
                                  {page}
                                </PaginationLink>
                              </PaginationItem>
                            ));
                          })()}
                          <PaginationItem>
                            <PaginationNext
                              href="#next"
                              className="rounded-full text-xs md:text-sm"
                              onClick={e => {
                                e.preventDefault();
                                setCurrentPage(p => Math.min(p + 1, totalPages));
                              }}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
