'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { contractClausesarticle4to6Schema } from '../schemas';

interface Step4Props {
  data: { article4to6?: string };
  onNext: (data: { article4to6: string }) => void;
  onBack: () => void;
}

const defaultArticle4to6 = `ĐIỀU 4: QUYỀN VÀ NGHĨA VỤ CỦA BÊN A

4.1. Quyền của bên A:
a) <PERSON>êu cầu bên B trả đủ tiền thuê nhà theo đúng thỏa thuận đã cam kết;
b) <PERSON><PERSON><PERSON> cầu bên B có trách nhiệm sửa chữa các hư hỏng và bồi thường thiệt hại do lỗi của bên B gây ra (nếu có);
c) Yêu cầu bên B thanh toán đủ số tiền thuê căn hộ (đối với thời gian đã thuê) và giao lại căn hộ trong các trường hợp các bên thỏa thuận chấm dứt hợp đồng thuê căn hộ trước thời hạn;
d) Bảo trì, cải tạo căn hộ;
e) Đơn phương chấm dứt thực hiện hợp đồng thuê khi bên B có một trong các hành vi sau đây:
   - Không trả tiền thuê căn hộ theo thỏa thuận trong hợp đồng liên tiếp trong ba tháng trở lên mà không có lý do chính đáng;
   - Sử dụng căn hộ không đúng mục đích như đã thoả thuận;
   - Cố ý làm hư hỏng căn hộ cho thuê;
   - Sửa chữa, cải tạo, đổi căn hộ đang thuê hoặc cho người khác thuê lại căn hộ đang thuê mà không có sự đồng ý của bên A;
   - Làm mất trật tự, vệ sinh môi trường, ảnh hưởng nghiêm trọng đến sinh hoạt của những người xung quanh.

4.2. Nghĩa vụ của bên A:
a) Giao căn hộ và trang thiết bị gắn liền với căn hộ (nếu có) cho bên B đúng thời gian quy định;
b) Thông báo cho bên B biết các quy định về quản lý sử dụng căn hộ;
c) Bảo đảm cho bên B sử dụng ổn định căn hộ trong thời hạn thuê;
d) Trả lại số tiền thuê căn hộ mà bên B đã trả trước trong trường hợp các bên thỏa thuận chấm dứt hợp đồng thuê căn hộ trước thời hạn;
e) Bảo trì, quản lý căn hộ cho thuê theo quy định của pháp luật về quản lý sử dụng nhà ở;
f) Hướng dẫn, đề nghị bên B thực hiện đúng các quy định về quản lý nhân khẩu;

ĐIỀU 5: QUYỀN VÀ NGHĨA VỤ CỦA BÊN B

5.1. Quyền của bên B:
a) Nhận căn hộ và trang thiết bị gắn liền với căn hộ (nếu có) theo đúng thỏa thuận;
b) Yêu cầu bên A sửa chữa kịp thời các hư hỏng về căn hộ;
c) Yêu cầu bên A trả lại số tiền thuê căn hộ mà bên B đã nộp trước trong trường hợp chấm dứt hợp đồng thuê căn hộ trước thời hạn;
d) Được đổi căn hộ đang thuê với người khác hoặc cho thuê lại (nếu có thỏa thuận);

5.2. Nghĩa vụ của bên B:
a) Trả đủ tiền thuê căn hộ theo đúng thời hạn đã cam kết trong hợp đồng;
b) Sử dụng căn hộ đúng mục đích; có trách nhiệm sửa chữa phần hư hỏng do mình gây ra;
c) Chấp hành đầy đủ các quy định về quản lý sử dụng căn hộ;
d) Không được chuyển nhượng hợp đồng thuê căn hộ hoặc cho người khác thuê lại, trừ trường hợp được bên A đồng ý;
e) Chấp hành các quy định về giữ gìn vệ sinh môi trường và an ninh trật tự trong khu vực cư trú;

ĐIỀU 6: QUYỀN VÀ NGHĨA VỤ VỀ QUẢN LÝ VÀ SỬA CHỮA PHẦN SỞ HỮU CHUNG

6.1. Bên A có quyền và nghĩa vụ quản lý, sử dụng và khai thác phần sở hữu chung theo quy định của pháp luật;
6.2. Bên B có nghĩa vụ đóng góp chi phí quản lý vận hành chung của tòa nhà (nếu có) theo quy định của Ban quản trị hoặc đơn vị quản lý tòa nhà.`;

const Step4: React.FC<Step4Props> = ({ data, onNext, onBack }) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<{ article4to6: string }>({
    resolver: zodResolver(contractClausesarticle4to6Schema),
    defaultValues: {
      article4to6: data.article4to6 || defaultArticle4to6,
    },
    mode: 'onChange',
  });

  const onSubmit = (formData: { article4to6: string }) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Bước 4: Điều khoản quyền và nghĩa vụ
        </CardTitle>
        <CardDescription className="text-center">
          Thiết lập các điều khoản về quyền và nghĩa vụ của các bên
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="article4to6" className="text-sm font-medium">
                Điều khoản 4-6: Quyền và nghĩa vụ của các bên *
              </Label>
              <Textarea
                id="article4to6"
                {...register('article4to6')}
                rows={15}
                className="mt-2"
                placeholder="Nhập điều khoản 4-6..."
              />
              {errors.article4to6 && (
                <p className="text-red-500 text-sm mt-1">{errors.article4to6.message}</p>
              )}
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">💡 Hướng dẫn:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Bạn có thể chỉnh sửa nội dung điều khoản theo nhu cầu</li>
                <li>• ĐIỀU 4: QUYỀN VÀ NGHĨA VỤ CỦA BÊN A</li>
                <li>• ĐIỀU 5: QUYỀN VÀ NGHĨA VỤ CỦA BÊN B</li>
                <li>• ĐIỀU 6: QUYỀN VÀ NGHĨA VỤ VỀ QUẢN LÝ VÀ SỬA CHỮA PHẦN SỞ HỮU CHUNG</li>
              </ul>
            </div>
          </div>

          <div className="flex justify-between pt-6">
            <Button type="button" variant="outline" onClick={onBack}>
              <ChevronLeft className="mr-2 h-4 w-4" />
              Quay lại
            </Button>
            <Button type="submit" disabled={!isValid}>
              Tiếp theo
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default Step4;
