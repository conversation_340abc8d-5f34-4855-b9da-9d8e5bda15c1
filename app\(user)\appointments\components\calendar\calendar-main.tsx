// components/calendar/calendar-main.tsx
'use client';

import { CalendarHeader } from './calendar-header';
import { CalendarViewSwitcher } from './calendar-view-switcher';
import { MonthView } from './month-view';
import { WeekView } from './week-view';
import { DayView } from './day-view';

import { appointmentToCalendarEvent } from './utils';
import { CalendarEvent } from './types';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCalendar } from './calendar-context';
import { useGetUserAppointments } from '@/hooks/useAppointment';
import { BookAppointmentDialog } from '../appointment/book-appointment-dialog';
import { EventDetailsDialog } from '../appointment/appointment-details-dialog';

export function CalendarMain() {
  const {
    currentView,
    selectedDate,
    currentMonth,
    setSelectedDate,
    isBookDialogOpen,
    setIsBookDialogOpen,
    bookingPropertyId,
    setBookingPropertyId,
    // Event details dialog state
    selectedEvent,
    setSelectedEvent,
    isEventDetailsOpen,
    setIsEventDetailsOpen,
  } = useCalendar();

  // Fetch appointments using React Query
  const {
    data: appointments = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useGetUserAppointments();

  // Convert appointments to calendar events
  const events: CalendarEvent[] = appointments.map(appointmentToCalendarEvent);

  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event);
    setIsEventDetailsOpen(true);
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    setIsBookDialogOpen(true);
  };

  const handleBookAppointment = (date: Date, propertyId?: string) => {
    setSelectedDate(date);
    setBookingPropertyId(propertyId);
    setIsBookDialogOpen(true);
  };

  const renderCalendarView = () => {
    if (isLoading) {
      return <CalendarSkeleton view={currentView} />;
    }

    if (error) {
      return (
        <div className="flex-1 flex items-center justify-center p-4 sm:p-8">
          <Alert className="max-w-md w-full">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="mt-2">
              <p className="mb-3">Không thể tải dữ liệu lịch hẹn.</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isRefetching}
                className="w-full sm:w-auto"
              >
                {isRefetching ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Đang tải...
                  </>
                ) : (
                  'Thử lại'
                )}
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    switch (currentView) {
      case 'day':
        return (
          <DayView events={events} selectedDate={selectedDate} onEventClick={handleEventClick} />
        );
      case 'week':
        return <WeekView events={events} onEventClick={handleEventClick} />;
      case 'month':
        return (
          <MonthView
            events={events}
            currentMonth={currentMonth}
            selectedDate={selectedDate}
            onDateClick={handleDateClick}
            onEventClick={handleEventClick}
          />
        );
      case 'year':
        return (
          <div className="flex-1 flex items-center justify-center text-muted-foreground p-4">
            <div className="text-center">
              <p className="text-sm sm:text-base">Chế độ xem năm sẽ sớm được cập nhật...</p>
            </div>
          </div>
        );
      default:
        return (
          <MonthView
            events={events}
            currentMonth={currentMonth}
            selectedDate={selectedDate}
            onDateClick={handleDateClick}
            onEventClick={handleEventClick}
          />
        );
    }
  };

  return (
    <div className="flex flex-col h-full bg-background rounded-lg border">
      <CalendarHeader />

      {/* Controls Section - Responsive */}
      <div className="p-2 sm:p-4 border-b">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-2">
          {/* View Switcher */}
          <div className="w-full sm:w-auto">
            <CalendarViewSwitcher />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between w-full sm:w-auto gap-2">
            {/* Loading Indicator */}
            {isRefetching && (
              <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                <span className="hidden sm:inline">Đang cập nhật...</span>
                <span className="sm:hidden">Đang tải...</span>
              </div>
            )}

            {/* Book Button */}
            <Button
              onClick={() => handleBookAppointment(selectedDate)}
              size="sm"
              className="flex-shrink-0"
            >
              <Plus className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Đặt lịch hẹn</span>
              <span className="sm:hidden">Đặt lịch</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="flex-1 flex flex-col overflow-hidden">{renderCalendarView()}</div>

      {/* Book Appointment Dialog */}
      <BookAppointmentDialog
        open={isBookDialogOpen}
        onOpenChange={setIsBookDialogOpen}
        selectedDate={selectedDate}
        propertyId={bookingPropertyId}
      />

      {/* Event Details Dialog */}
      <EventDetailsDialog
        open={isEventDetailsOpen}
        onOpenChange={setIsEventDetailsOpen}
        event={selectedEvent}
      />
    </div>
  );
}

// Loading skeleton component - Responsive
function CalendarSkeleton({ view }: { view: string }) {
  if (view === 'month') {
    return (
      <div className="flex-1 p-2 sm:p-4">
        {/* Header skeleton */}
        <div className="grid grid-cols-7 gap-1 sm:gap-2 mb-2 sm:mb-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <Skeleton key={i} className="h-6 sm:h-8" />
          ))}
        </div>
        {/* Calendar grid skeleton */}
        <div className="grid grid-cols-7 gap-1 sm:gap-2">
          {Array.from({ length: 35 }).map((_, i) => (
            <Skeleton key={i} className="h-16 sm:h-24" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-2 sm:p-4">
      <Skeleton className="h-full w-full" />
    </div>
  );
}
