'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

// Type declaration for Zalo SDK
declare global {
  interface Window {
    ZaloSocialSDK?: {
      reload: () => void;
      share: (options: { href: string; oaId: string }) => void;
    };
  }
}

export function ZaloSdkProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  useEffect(() => {
    // Reload Zalo SDK when pathname changes
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined' && window.ZaloSocialSDK) {
        try {
          console.log('Reloading Zalo SDK for path:', pathname);
          window.ZaloSocialSDK.reload();
        } catch (error) {
          console.log('Zalo SDK reload error:', error);
        }
      }
    }, 500); // Give time for DOM to update

    return () => clearTimeout(timer);
  }, [pathname]);

  return <>{children}</>;
}
