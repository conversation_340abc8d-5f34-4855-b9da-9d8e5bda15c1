'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useState } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { GoogleAuthButton } from '@/components/auth/googleAuthButton';
import { Separator } from '@/components/ui/separator';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { LoginOTPDialog } from '@/components/auth/LoginOTPDialog';
import { FacebookAuthButton } from '@/components/auth/facebookAuthButton';

const loginSchema = z.object({
  phoneNumber: z
    .string()
    .min(1, 'S<PERSON> điện thoại là bắt buộc')
    .refine(
      value => {
        // Loại bỏ khoảng trắng và dấu gạch ngang
        const cleanPhone = value.replace(/[\s-]/g, '');
        // Regex cho số điện thoại Việt Nam
        const phoneRegex = /^(\+84|84|0)[35789][0-9]{8}$/;
        return phoneRegex.test(cleanPhone);
      },
      {
        message: 'Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam hợp lệ',
      }
    ),
  password: z.string().min(1, 'Mật khẩu là bắt buộc'),
});

export function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const { login, isLoading, needsOtpVerification, loginVerifyKey, clearLoginOtpState } = useAuth();
  const [googleError, setGoogleError] = useState<string | null>(null);
  const [facebookError, setFacebookError] = useState<string | null>(null);

  const handleGoogleError = (error: string | null) => {
    setFacebookError(null);
    setGoogleError(error);
  };

  const handleFacebookError = (error: string | null) => {
    setGoogleError(null);
    setFacebookError(error);
  };

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(loginSchema),
    mode: 'onChange',
  });

  // Format phone number while typing
  const formatPhoneNumber = (value: string) => {
    // Loại bỏ tất cả ký tự không phải số
    const numbersOnly = value.replace(/[^\d]/g, '');

    // Nếu bắt đầu bằng 84, thêm +
    if (numbersOnly.startsWith('84')) {
      return '+' + numbersOnly;
    }

    // Nếu bắt đầu bằng 0, giữ nguyên
    if (numbersOnly.startsWith('0')) {
      return numbersOnly;
    }

    // Nếu không bắt đầu bằng 0 hoặc 84, thêm 0 vào đầu
    if (numbersOnly.length > 0 && !numbersOnly.startsWith('0') && !numbersOnly.startsWith('84')) {
      return '0' + numbersOnly;
    }

    return numbersOnly;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phoneNumber', formatted, { shouldValidate: true });
  };

  const onSubmit = async (data: z.infer<typeof loginSchema>) => {
    try {
      // Chuẩn hóa số điện thoại trước khi gửi
      const cleanPhone = data.phoneNumber.replace(/[\s-]/g, '');

      // Add rememberMe to the data before sending to login
      const loginData = {
        keyLogin: cleanPhone, // Sử dụng keyLogin để tương thích với API hiện tại
        password: data.password,
        rememberMe,
      };
      login(loginData);
    } catch (error) {
      toast.error('Đã có lỗi xảy ra khi đăng nhập. Vui lòng thử lại sau.');
    }
  };

  const handleCloseOtpDialog = () => {
    clearLoginOtpState();
  };

  return (
    <>
      <div className="flex flex-col items-center justify-center">
        <div className="w-full space-y-6 rounded-xl">
          <div className="flex flex-col items-center gap-2 text-center">
            <h1 className="text-xl md:text-2xl font-bold tracking-tight text-red-500">
              Đăng nhập tài khoản của bạn
            </h1>
            <p className="text-balance text-xs md:text-sm text-muted-foreground">
              Nhập số điện thoại và mật khẩu để đăng nhập
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phoneNumber" className="text-xs md:text-sm">
                  Số điện thoại
                  <span className="text-destructive"> *</span>
                </Label>
                <Input
                  id="phoneNumber"
                  {...register('phoneNumber')}
                  onChange={handlePhoneChange}
                  type="text"
                  placeholder="Nhập số điện thoại (VD: 0901234567)"
                  className={cn(
                    errors.phoneNumber && 'border-destructive text-xs md:text-sm',
                    'placeholder:text-xs md:placeholder:text-sm text-xs md:text-sm'
                  )}
                  aria-describedby="phoneNumber-error"
                />
                {errors.phoneNumber && (
                  <p className="text-xs text-destructive" id="phoneNumber-error">
                    {errors.phoneNumber.message}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  Hỗ trợ định dạng: 0901234567, +84901234567, 84901234567
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-xs md:text-sm">
                    Mật khẩu
                    <span className="text-destructive"> *</span>
                  </Label>
                  <Link
                    href="/forgot-password"
                    className="text-xs md:text-sm text-primary hover:text-primary/90 hover:underline"
                  >
                    Quên mật khẩu?
                  </Link>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Nhập mật khẩu"
                    className={cn(
                      errors.password && 'border-destructive pr-10 text-xs md:text-sm',
                      'placeholder:text-xs md:placeholder:text-sm text-xs md:text-sm'
                    )}
                    aria-describedby="password-error"
                  />

                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1 h-8 w-8 px-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                    <span className="sr-only">
                      {showPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
                    </span>
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-xs text-destructive" id="password-error">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rememberMe"
                  checked={rememberMe}
                  onCheckedChange={checked => setRememberMe(!!checked)}
                  className="data-[state=checked]:bg-red-500 data-[state=checked]:border-red-500"
                />
                <Label
                  htmlFor="rememberMe"
                  className="text-xs md:text-sm font-normal cursor-pointer"
                >
                  Ghi nhớ đăng nhập
                </Label>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-red-500 hover:bg-red-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
                </>
              ) : (
                'Đăng nhập bằng số điện thoại'
              )}
            </Button>
          </form>

          <div className="relative flex items-center justify-center">
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
            <span className="px-2 text-xs md:text-sm text-muted-foreground">
              Hoặc đăng nhập với
            </span>
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
          </div>

          <div className="flex justify-center w-full">
            <div className="space-y-4 w-full max-w-[400px]">
              <FacebookAuthButton mode="login" onError={handleFacebookError} />
              <GoogleAuthButton mode="login" onError={handleGoogleError} />

              {(googleError || facebookError) && (
                <div className="rounded-md bg-destructive/10 p-2 text-center text-xs md:text-sm text-destructive">
                  {googleError || facebookError}
                </div>
              )}
            </div>
          </div>

          <div className="text-center">
            <p className="text-xs md:text-sm text-muted-foreground">
              Không có tài khoản?{' '}
              <Link href="/register" className="text-primary text-red-500 hover:underline">
                Đăng ký
              </Link>
            </p>
          </div>

          <div className="text-balance text-center text-xs md:text-sm text-muted-foreground">
            Bằng cách tiếp tục, bạn đồng ý với{' '}
            <Link href="/legal/terms-of-agreement" className="text-primary hover:underline">
              Điều khoản, điều kiện
            </Link>{' '}
            và{' '}
            <Link href="/legal/privacy-policy" className="text-primary hover:underline">
              Chính sách bảo mật
            </Link>
            .
          </div>
        </div>
      </div>

      {needsOtpVerification && loginVerifyKey && (
        <LoginOTPDialog
          isOpen={needsOtpVerification}
          onClose={handleCloseOtpDialog}
          verifyKey={loginVerifyKey}
        />
      )}
    </>
  );
}
