'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { useEffect, useState } from 'react';
import { MoreHorizontal, Pin, Trash2, Reply, FileText, Play } from 'lucide-react';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MessageStructure } from '@/lib/api/services/fetchChat';

type Props = {
  messages: MessageStructure[];
  chatUser: {
    id: string;
    name: string;
    avatarUrl: string;
  };
  onPin: (messageId: string) => void;
  onDelete: (messageId: string) => void;
  onReply: (message: MessageStructure) => void;
  openMenuMessageId: string | null;
  setOpenMenuMessageId: (id: string | null) => void;
  selectedMedia: { url: string; type: string } | null;
  setSelectedMedia: (media: { url: string; type: string } | null) => void;
};

export default function OtherMessage({
  messages,
  chatUser,
  onPin,
  onDelete,
  onReply,
  openMenuMessageId,
  setOpenMenuMessageId,
  setSelectedMedia,
}: Props) {
  const [renderMessage, setRenderMessage] = useState<React.ReactNode>(null);

  const renderAttachments = (attachments: MessageStructure['attachments']) => {
    if (!attachments || attachments.length === 0) return null;

    return (
      <div className="flex flex-col gap-2 mt-2">
        {attachments.map((attachment, index) => {
          if (!attachment.url || !attachment.type) return null;
          const fileType = attachment.type?.toLowerCase() || '';
          const isImage = fileType.startsWith('image/');
          const isVideo = fileType.startsWith('video/');
          const isPdfAttachment = fileType === 'application/pdf';
          const isFile = !isImage && !isVideo && !isPdfAttachment;

          if (isImage) {
            return (
              <div
                key={index}
                className="max-w-[150px] cursor-pointer"
                onClick={() => setSelectedMedia({ url: attachment.url, type: 'image' })}
              >
                <Image
                  src={attachment.url}
                  alt="Attachment"
                  width={300}
                  height={200}
                  style={{ width: 'auto', height: 'auto' }}
                  className="rounded-lg object-cover"
                />
              </div>
            );
          }

          if (isVideo) {
            return (
              <div
                key={index}
                className="max-w-[150px] cursor-pointer"
                onClick={() => setSelectedMedia({ url: attachment.url, type: 'video' })}
              >
                <video controls className="rounded-lg" src={attachment.url}>
                  Your browser does not support the video tag.
                </video>
              </div>
            );
          }

          if (isPdfAttachment) {
            return (
              <a
                key={index}
                href={attachment.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-blue-500 hover:text-blue-600 max-w-[250px]"
              >
                <FileText size={16} className="flex-shrink-0" />
                <span className="truncate">{attachment.name || 'View PDF'}</span>
              </a>
            );
          }

          if (isFile) {
            return (
              <a
                key={index}
                href={attachment.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-blue-500 hover:text-blue-600 max-w-[250px]"
              >
                <FileText size={16} className="flex-shrink-0" />
                <span className="truncate">{attachment.name || 'Download File'}</span>
              </a>
            );
          }

          return null;
        })}
      </div>
    );
  };

  const renderMessageWithActions = (
    message: MessageStructure,
    isFirst: boolean,
    isLast: boolean
  ) => {
    const hasAttachments = message.attachments && message.attachments.length > 0;
    const hasContent = message.content && message.content.trim();

    return (
      <div className="relative group">
        <div
          className={`bg-gray-100 items-center rounded-[10px] break-words text-[15px] w-fit px-[10px] py-[8px] ${
            isFirst ? 'rounded-tr-[20px]' : 'rounded-tr-[10px]'
          } ${isLast ? 'rounded-br-[20px]' : 'rounded-br-[10px]'} rounded-tl-lg rounded-bl-lg`}
        >
          {/* Reply message preview */}
          {message.replyToMessageId && (
            <div className="bg-blue-50 border-l-4 border-blue-500 rounded px-2 py-1 mb-2 text-xs">
              <div className="font-semibold text-blue-700">Trả lời tin nhắn</div>
              <div className="text-gray-600 truncate max-w-[200px]">
                {(() => {
                  const repliedMessage = message.replyToMessage;
                  if (!repliedMessage) return 'Tin nhắn đã bị xóa';

                  // Nếu có content, hiển thị content
                  if (repliedMessage.content) {
                    return repliedMessage.content;
                  }

                  // Nếu có attachments, hiển thị thumbnail
                  if (repliedMessage.attachments && repliedMessage.attachments.length > 0) {
                    const attachment = repliedMessage.attachments[0];
                    if (!attachment.url || !attachment.type) return 'File đính kèm';

                    const fileType = attachment.type.toLowerCase();
                    const isImage = fileType.startsWith('image/');
                    const isVideo = fileType.startsWith('video/');

                    if (isImage) {
                      return (
                        <Image
                          src={attachment.url}
                          alt="Attachment"
                          width={50}
                          height={50}
                          className="rounded object-cover"
                        />
                      );
                    }

                    if (isVideo) {
                      return (
                        <div className="relative w-[50px] h-[50px] bg-gray-200 rounded flex items-center justify-center">
                          <video
                            src={attachment.url}
                            className="w-full h-full object-cover rounded"
                            muted
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                            <Play size={16} className="text-white" />
                          </div>
                        </div>
                      );
                    }

                    return <FileText size={16} className="text-gray-500" />;
                  }

                  return 'Tin nhắn đã bị xóa';
                })()}
              </div>
            </div>
          )}

          {hasContent && (
            <p className="break-words whitespace-normal leading-[1.3]">{message.content}</p>
          )}
          {hasAttachments && renderAttachments(message.attachments)}
        </div>

        {/* Message Actions Menu */}
        <DropdownMenu
          open={openMenuMessageId === message.id}
          onOpenChange={open => setOpenMenuMessageId(open ? message.id : null)}
        >
          <DropdownMenuTrigger asChild>
            <button
              className="absolute -right-8 top-1/2 transform -translate-y-1/2 w-6 h-6 flex items-center justify-center rounded-full bg-white opacity-0 group-hover:opacity-100 transition-opacity shadow-sm"
              onClick={e => {
                e.stopPropagation();
                e.preventDefault();
                setOpenMenuMessageId(message.id);
              }}
            >
              <MoreHorizontal size={16} />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="right"
            align="center"
            className="z-[9999] rounded-xl shadow-lg bg-white min-w-[100px] py-2 px-0 border"
          >
            <DropdownMenuItem onClick={() => onPin(message.id)}>
              <Pin size={16} className="mr-2" />
              Ghim
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onReply(message)}>
              <Reply size={16} className="mr-2" />
              Trả lời
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                if (window.confirm('Bạn có chắc chắn muốn xóa tin nhắn này?')) {
                  onDelete(message.id);
                }
              }}
              className="text-red-600"
            >
              <Trash2 size={16} className="mr-2" />
              Xóa
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };

  useEffect(() => {
    if (!messages || messages.length === 0) {
      setRenderMessage(null);
      return;
    }

    if (messages.length === 1) {
      setRenderMessage(renderMessageWithActions(messages[0], true, true));
      return;
    }

    if (messages.length === 2) {
      setRenderMessage(
        <>
          <div key="first">{renderMessageWithActions(messages[0], true, false)}</div>
          <div key="second">{renderMessageWithActions(messages[1], false, true)}</div>
        </>
      );
      return;
    }

    setRenderMessage(
      <>
        <div key="first">{renderMessageWithActions(messages[0], true, false)}</div>

        {messages.slice(1, -1).map((msg, index) => (
          <div key={msg.id || `middle-${index}`}>{renderMessageWithActions(msg, false, false)}</div>
        ))}

        <div key="last">{renderMessageWithActions(messages[messages.length - 1], false, true)}</div>
      </>
    );
  }, [messages, openMenuMessageId]);

  return (
    <div className="flex items-end gap-2 w-[75%]">
      <div>
        <Avatar className="w-9 h-9">
          <AvatarImage
            src={
              chatUser.avatarUrl ||
              `https://ui-avatars.com/api/?name=${encodeURIComponent(chatUser.name || 'Anonymous')}&background=F3F4F6&color=000000`
            }
          />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
      </div>

      <div className="flex flex-col gap-1">{renderMessage}</div>
    </div>
  );
}
