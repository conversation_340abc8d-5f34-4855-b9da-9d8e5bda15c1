'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface PricingTier {
  duration: string;
  regular?: string;
  hotGold?: string;
  hotDiamond?: string;
}

interface ServicePackage {
  name: string;
  price: string;
  period: string;
  features: string[];
  badge?: string;
  popular?: boolean;
}

const individualPostingData: PricingTier[] = [
  { duration: '1 ngày', regular: '2000' },
  { duration: '7 ngày', regular: '8000' },
  { duration: '10 ngày', regular: '15000' },
  { duration: '15 ngày', regular: '25000' },
  { duration: '30 ngày', regular: '45000' },
];

const hotListingsData: PricingTier[] = [
  { duration: '1 ngày', hotGold: '20000', hotDiamond: '30000' },
  { duration: '7 ngày', hotGold: '80000', hotDiamond: '120000' },
  { duration: '10 ngày', hotGold: '110000', hotDiamond: '160000' },
  { duration: '15 ngày', hotGold: '180000', hotDiamond: '250000' },
  { duration: '30 ngày', hotGold: '300000', hotDiamond: '400000' },
];

const enterpriseHotListingsData: PricingTier[] = [
  { duration: '1 ngày', hotGold: '15000', hotDiamond: '25000' },
  { duration: '7 ngày', hotGold: '70000', hotDiamond: '100000' },
  { duration: '10 ngày', hotGold: '100000', hotDiamond: '140000' },
  { duration: '15 ngày', hotGold: '170000', hotDiamond: '230000' },
  { duration: '30 ngày', hotGold: '290000', hotDiamond: '380000' },
];

const smartLivingPackages: ServicePackage[] = [
  {
    name: 'Revoland Cơ bản',
    price: '199000',
    period: 'tháng',
    features: [
      'Đăng tin miễn phí 3 lần/tháng, được tối ưu bởi AI',
      'Theo dõi, phân loại và kết nối với khách hàng tiềm năng. Đặt lịch hẹn chỉ với vài thao tác.',
      'Báo cáo định giá tự động, đơn giản hóa quy trình tư vấn và giao dịch.',
    ],
  },
  {
    name: 'Revoland VIP',
    price: '499000',
    period: 'tháng',
    badge: 'Phổ biến',
    popular: true,
    features: [
      'Tăng độ tin cậy và hiển thị vượt trội với các tin đăng mang huy hiệu xác thực bởi AI.',
      'Đồng bộ dữ liệu, theo dõi hành trình khách hàng và tối ưu hiệu suất bán hàng.',
      'Nắm bắt hiệu quả chiến dịch và hành vi người dùng qua dashboard phân tích mạnh mẽ.',
      'Đội ngũ hỗ trợ chuyên nghiệp sẵn sàng đồng hành 24/7, giải quyết mọi vấn đề nhanh chóng.',
    ],
  },
];

const enterprisePackages: ServicePackage[] = [
  {
    name: 'Gói dành cho Nhóm Sales',
    price: '459000',
    period: 'tháng',
    features: [
      'Phù hợp nhóm 3–10 thành viên',
      'Tích hợp API linh hoạt với hệ thống hiện tại',
      'Hỗ trợ riêng theo yêu cầu',
    ],
  },
  {
    name: 'Gói dành cho Công ty bất động sản nhỏ',
    price: '399000',
    period: 'tháng',
    features: [
      'Phù hợp đội ngũ dưới 30 người',
      'Phân tích nâng cao, giúp ra quyết định nhanh chóng',
      'Hỗ trợ gắn nhãn thương hiệu riêng (white-label)',
    ],
    popular: true,
  },
  {
    name: 'Gói dành cho Công ty bất động sản lớn',
    price: '359000',
    period: 'tháng',
    features: [
      'Tối ưu cho doanh nghiệp trên 30 nhân sự',
      'Tích hợp Business Intelligence chuyên sâu',
      'Quản lý tài khoản riêng, chăm sóc chuyên biệt',
    ],
  },
];

const specialOffers = [
  {
    id: '6months',
    duration: '6',
    title: 'Gói 6 tháng',
    bonus: '+1',
    description: 'Thanh toán trước 6 tháng và được tặng thêm 1 tháng sử dụng miễn phí',
    badge: 'Tặng thêm 1 tháng',
  },
  {
    id: '12months',
    duration: '12',
    title: 'Gói 12 tháng',
    bonus: '+3',
    description: 'Thanh toán trước 1 năm và được tặng thêm 3 tháng sử dụng miễn phí',
    badge: 'Tặng thêm 3 tháng',
  },
];

export default function RevolandPricing() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="flex items-center p-6">
        <Button variant="ghost" className="mr-4" onClick={() => router.back()}>
          <ArrowLeft className="w-6 h-6" />
        </Button>
      </div>
      <div className="bg-white">
        <div className="max-w-6xl mx-auto px-8 py-24">
          <div className="text-center space-y-8">
            <h1 className="text-7xl font-extralight text-gray-900 tracking-tight leading-none">
              Báo giá & Hỗ trợ
            </h1>
            <p className="text-2xl text-gray-500 max-w-3xl mx-auto font-light leading-relaxed">
              Giải pháp toàn diện với mức giá minh bạch cho mọi quy mô doanh nghiệp
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-8 py-16">
        <Tabs defaultValue="individual" className="w-full">
          <div className="flex justify-center mb-20">
            <div className="bg-gray-100/80 backdrop-blur-sm rounded-2xl p-1.5 shadow-inner">
              <TabsList className="grid w-full grid-cols-4 bg-transparent gap-1 h-auto p-0">
                <TabsTrigger
                  value="individual"
                  className="relative px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-red-500 data-[state=active]:shadow-lg data-[state=active]:text-white data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-white/50"
                >
                  <span className="relative z-10">Gói cơ bản</span>
                </TabsTrigger>
                <TabsTrigger
                  value="hot"
                  className="relative px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-red-500 data-[state=active]:shadow-lg data-[state=active]:text-white data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-white/50"
                >
                  <span className="relative z-10">Tin nổi bật</span>
                </TabsTrigger>
                <TabsTrigger
                  value="smart"
                  className="relative px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-red-500 data-[state=active]:shadow-lg data-[state=active]:text-white data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-white/50"
                >
                  <span className="relative z-10">Gói dịch vụ</span>
                </TabsTrigger>
                <TabsTrigger
                  value="enterprise"
                  className="relative px-8 py-4 text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-red-500 data-[state=active]:shadow-lg data-[state=active]:text-white data-[state=inactive]:hover:text-gray-900 data-[state=inactive]:hover:bg-white/50"
                >
                  <span className="relative z-10">Doanh nghiệp</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          <TabsContent value="individual" className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-light text-gray-900">Gói đăng tin cơ bản</h2>
              <p className="text-xl text-gray-500 max-w-2xl mx-auto">
                Mức giá minh bạch và linh hoạt cho mọi nhu cầu đăng tin
              </p>
            </div>

            <Card className="border-0 shadow-lg rounded-3xl overflow-hidden ring-2 ring-red-500">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="border-0">
                      <TableHead className="text-gray-900 py-6 px-8 text-left">
                        Thời gian sử dụng
                      </TableHead>
                      <TableHead className="text-gray-900 py-6 px-8 text-right">Mức phí</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {individualPostingData.map((item, index) => (
                      <TableRow
                        key={index}
                        className="border-0 hover:bg-gray-50/30 transition-colors"
                      >
                        <TableCell className="py-6 px-8 font-medium text-gray-800">
                          {item.duration}
                        </TableCell>
                        <TableCell className="py-6 px-8 text-right">
                          <Badge>{formatCurrency(item.regular)}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="hot" className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-light text-gray-900">Tin nổi bật</h2>
              <p className="text-xl text-gray-500 max-w-2xl mx-auto">
                Tăng cường hiệu quả với gói tin ưu tiên hiển thị
              </p>
            </div>

            <Card className="border-0 shadow-lg rounded-3xl overflow-hidden ring-2 ring-red-500">
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50/50 border-0">
                      <TableHead className="font-medium text-gray-900 py-6 px-8">
                        Thời gian
                      </TableHead>
                      <TableHead className="font-medium text-gray-900 py-6 px-8 text-center">
                        Tin hàng hot vàng
                      </TableHead>
                      <TableHead className="font-medium text-gray-900 py-6 px-8 text-center">
                        Tin Hàng Hot Kim cương
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {hotListingsData.map((item, index) => (
                      <TableRow
                        key={index}
                        className="border-0 hover:bg-gray-50/30 transition-colors"
                      >
                        <TableCell className="py-6 px-8 font-medium text-gray-800">
                          {item.duration}
                        </TableCell>
                        <TableCell className="py-6 px-8 text-center">
                          <Badge className="bg-gray-100 text-black hover:bg-gray-200">
                            {formatCurrency(item.hotGold)}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-6 px-8 text-center">
                          <Badge>{formatCurrency(item.hotDiamond)}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="smart" className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-light text-gray-900">Gói dịch vụ toàn diện</h2>
              <p className="text-xl text-gray-500 max-w-2xl mx-auto">
                Giải pháp thông minh với công nghệ AI tiên tiến
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 items-stretch">
              {smartLivingPackages.map((pkg, index) => (
                <Card
                  key={index}
                  className={`relative bg-white border-0 shadow-lg rounded-3xl overflow-hidden h-full ${pkg.popular ? 'ring-2 ring-red-500' : ''}`}
                >
                  {pkg.badge && (
                    <Badge className="absolute top-6 right-6 bg-red-500">{pkg.badge}</Badge>
                  )}
                  <CardContent className="p-8 h-full">
                    <div className="flex flex-col h-full">
                      <div className="space-y-2 mb-6">
                        <h3 className="text-2xl font-light text-gray-900">{pkg.name}</h3>
                        <div className="flex items-baseline">
                          <span className="text-4xl font-light text-gray-900">
                            {formatCurrency(pkg.price)}
                          </span>
                          <span className="text-gray-500 ml-2">/{pkg.period}</span>
                        </div>
                      </div>

                      <ul className="space-y-4 flex-grow mb-8">
                        {pkg.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start gap-3">
                            <div className="w-1.5 h-1.5 bg-gray-900 rounded-full mt-3 flex-shrink-0"></div>
                            <span className="text-gray-700 leading-relaxed">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <Button
                        className={`w-full h-12 rounded-2xl font-medium mt-auto ${pkg.popular ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'}`}
                      >
                        Bắt đầu ngay
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="enterprise" className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-4xl font-light text-gray-900">Giải pháp doanh nghiệp</h2>
              <p className="text-xl text-gray-500 max-w-2xl mx-auto">
                Tùy chỉnh hoàn toàn theo nhu cầu tổ chức của bạn
              </p>
            </div>

            <div className="grid lg:grid-cols-3 gap-6 items-stretch">
              {enterprisePackages.map((pkg, index) => (
                <Card
                  key={index}
                  className={`bg-white border-0 shadow-lg rounded-3xl h-full ${pkg.popular ? 'ring-2 ring-red-500' : ''}`}
                >
                  <CardContent className="p-8 h-full">
                    <div className="flex flex-col h-full">
                      <div className="space-y-2 mb-6">
                        <h3 className="text-xl font-medium text-gray-900">{pkg.name}</h3>
                        <div className="flex items-baseline">
                          <span className="text-3xl font-light text-gray-900">
                            {formatCurrency(pkg.price)}
                          </span>
                          <span className="text-sm text-gray-500 ml-2">/{pkg.period}</span>
                        </div>
                      </div>

                      <ul className="space-y-3 flex-grow mb-8">
                        {pkg.features.map((feature, idx) => (
                          <li key={idx} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-gray-900 rounded-full"></div>
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <Button
                        className={`w-full h-12 rounded-2xl font-medium mt-auto ${pkg.popular ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-100 hover:bg-gray-200 text-gray-900'}`}
                      >
                        Liên hệ tư vấn
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Enterprise Hot Listings */}
            <Card className="border-0 shadow-lg rounded-3xl overflow-hidden ring-2 ring-red-500">
              <CardHeader className="p-8 pb-0">
                <div className="text-center space-y-2">
                  <CardTitle className="text-2xl font-light text-gray-900">
                    Bảng giá ưu đãi doanh nghiệp
                  </CardTitle>
                  <CardDescription className="text-gray-500">
                    Mức giá đặc biệt cho khách hàng doanh nghiệp
                  </CardDescription>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50/50 border-0">
                      <TableHead className="font-medium text-gray-900 py-6 px-8">
                        Thời gian
                      </TableHead>
                      <TableHead className="font-medium text-gray-900 py-6 px-8 text-center">
                        Tin hàng hot vàng
                      </TableHead>
                      <TableHead className="font-medium text-gray-900 py-6 px-8 text-center">
                        Tin Hàng Hot Kim cương
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {enterpriseHotListingsData.map((item, index) => (
                      <TableRow
                        key={index}
                        className="border-0 hover:bg-gray-50/30 transition-colors"
                      >
                        <TableCell className="py-6 px-8 font-medium text-gray-800">
                          {item.duration}
                        </TableCell>
                        <TableCell className="py-6 px-8 text-center">
                          <Badge className="bg-gray-100 text-black hover:bg-gray-200">
                            {formatCurrency(item.hotGold)}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-6 px-8 text-center">
                          <Badge>{formatCurrency(item.hotDiamond)}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Combined Special Offers & Quality Commitments Section */}
      <div className="max-w-6xl mx-auto px-8 py-24 space-y-32">
        {/* Special Offers */}
        <div className="text-center space-y-16">
          <div className="space-y-4">
            <h2 className="text-4xl font-light text-gray-900">Chính sách ưu đãi</h2>
            <p className="text-2xl text-gray-500 max-w-3xl mx-auto font-light leading-relaxed">
              Cam kết mang lại giá trị tối ưu cho mọi khách hàng
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
            {specialOffers.map(offer => (
              <div key={offer.id} className="group">
                <Card className="bg-white border border-gray-200 rounded-3xl shadow-sm hover:shadow-2xl transition-all duration-700 overflow-hidden group-hover:border-gray-300 ring-2 ring-red-500">
                  <CardContent className="p-12 text-center">
                    <div className="space-y-8">
                      <div className="relative inline-block">
                        <div className="w-24 h-24 bg-red-500 rounded-3xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-105 transition-transform duration-500">
                          <div className="text-center">
                            <div className="text-3xl font-bold text-white">{offer.duration}</div>
                            <div className="text-xs text-gray-300 -mt-1">tháng</div>
                          </div>
                        </div>
                        <div className="absolute -top-3 -right-3 w-10 h-10 bg-red-700 text-white rounded-full flex items-center justify-center shadow-lg">
                          <span className="text-sm font-bold">{offer.bonus}</span>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <h3 className="text-2xl font-light text-gray-900">{offer.title}</h3>
                        <Badge className="text-sm p-4 bg-white hover:ring-2 hover:ring-red-500 hover:bg-white text-red-500">
                          {offer.badge}
                        </Badge>
                      </div>
                      <p className="text-gray-600 leading-relaxed">{offer.description}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {/* <div className="text-center space-y-16">
            <div className="space-y-4">
              <h2 className="text-4xl font-extralight text-gray-900 tracking-tight">Cam kết chất lượng</h2>
              <p className="text-2xl text-gray-500 max-w-3xl mx-auto font-light leading-relaxed">
                Những giá trị cốt lõi và tiêu chuẩn dịch vụ hàng đầu
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {qualityCommitments.map((commitment) => {
                const IconComponent = commitment.icon;
                return (
                  <Card key={commitment.id} className="group border border-gray-200 shadow-sm hover:shadow-xl transition-all duration-500 hover:-translate-y-3 ring-2 ring-red-500">
                    <CardContent className="p-10 text-center">
                      <div className="space-y-6">
                        <div className="w-20 h-20 bg-red-500 rounded-3xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                          <IconComponent className="w-10 h-10 text-white" />
                        </div>
                        <div className="space-y-3">
                          <h3 className="text-xl font-semibold text-gray-900">{commitment.title}</h3>
                          <p className="text-sm text-gray-600 leading-relaxed">
                            {commitment.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div> */}
      </div>
    </div>
  );
}
