import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { propertyService } from '@/lib/api/services/fetchProperty';
import { Toaster } from '@/components/ui/sonner';

const inter = Inter({ subsets: ['latin'] });

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const response = await propertyService.getProperty(params.id);
    const property = response.data;

    return {
      title: `${property.title} | ${property.type} in ${property.location.city}`,
      description: property.description,
      openGraph: {
        title: `${property.title} | ${property.type} in ${property.location.city}`,
        description: property.description,
        images: property.imageUrls[0] ? [property.imageUrls[0]] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${property.title} | ${property.type} in ${property.location.city}`,
        description: property.description,
        images: property.imageUrls[0] ? [property.imageUrls[0]] : [],
      },
    };
  } catch (error) {
    return {
      title: 'Property Details | Revo Land',
      description: 'View detailed information about this property.',
    };
  }
}

export default function PropertyDetailLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className={inter.className}>
      {children}
      <Toaster />
    </div>
  );
}
