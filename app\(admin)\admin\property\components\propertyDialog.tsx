'use client';

import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>Content,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Property, PropertyStatus, TransactionHistory } from '@/lib/api/services/fetchProperty';
import {
  FileText,
  ExternalLink,
  Info,
  User,
  Home,
  DollarSign,
  ParkingSquare,
  Calendar,
  ClipboardList,
  MessageSquare,
  Phone,
  Clock,
  Building2,
  Warehouse,
  Car,
  ArrowUpDown,
  Waves,
  Dumbbell,
  Shield,
  Wind,
  Building,
  Trees,
  PlayCircle,
  Power,
  Check,
  X,
} from 'lucide-react';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import Image from 'next/image';
import { formatDate } from '@/utils/dates/formatDate';
import React from 'react';

interface PropertyDialogProps {
  property: Property;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PropertyDialog({ property, open }: PropertyDialogProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Reset selected image when dialog closes
  useEffect(() => {
    if (!open) {
      setSelectedImage(null);
    }
  }, [open]);

  // Function to get file extension
  const getFileExtension = (url: string) => {
    const parts = url.split('.');
    return parts[parts.length - 1].toLowerCase();
  };

  // Function to get file name from URL
  const getFileName = (url: string) => {
    const parts = url.split('/');
    return parts[parts.length - 1];
  };

  // Check if document is an image
  const isImageFile = (url: string) => {
    const ext = getFileExtension(url);
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext);
  };

  const isExcelFile = (url: string) => {
    const ext = getFileExtension(url);
    return ['xlsx', 'xls'].includes(ext);
  };

  return (
    <DialogContent
      className="max-w-screen h-screen overflow-y-auto"
      onInteractOutside={e => e.preventDefault()}
    >
      <DialogHeader>
        <DialogTitle className="text-xl">{property.title}</DialogTitle>
        <DialogDescription className="flex items-center gap-2">
          <span className="px-2 py-1 rounded text-sm font-bold">
            Property Code: {property.code}
          </span>
          <span
            className={`px-2 py-1 rounded text-sm ${
              property.status === PropertyStatus.AVAILABLE
                ? 'bg-green-100 text-green-700'
                : property.status === PropertyStatus.SOLD
                  ? 'bg-red-100 text-red-700'
                  : property.status === PropertyStatus.RENTED
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700'
            }`}
          >
            {property.status}
          </span>
        </DialogDescription>
      </DialogHeader>

      {/* Large Image Preview */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-[95%] max-h-[95%]">
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-70 transition-all"
              onClick={e => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
            >
              ✕
            </button>
            <Image
              src={selectedImage}
              alt="Large preview"
              width={1200}
              height={800}
              className="max-w-full max-h-[90vh] object-contain"
            />
          </div>
        </div>
      )}

      {/* Images Gallery - More prominent and at the top */}
      {property.imageUrls && property.imageUrls.length > 0 && (
        <div className="mb-6">
          <div className="grid grid-cols-4 gap-2 min-h-[450px]">
            {/* Main large image */}
            <div
              className="col-span-2 row-span-2 relative rounded-lg overflow-hidden shadow-md cursor-pointer"
              onClick={() =>
                property.imageUrls && property.imageUrls.length > 0
                  ? setSelectedImage(property.imageUrls[0])
                  : null
              }
            >
              <Image
                src={property.imageUrls?.[0]}
                alt={`Main property image`}
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-opacity flex items-center justify-center">
                <span className="text-white text-sm font-medium px-3 py-1 bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity">
                  View
                </span>
              </div>
            </div>

            {/* Smaller thumbnails */}
            {property.imageUrls.slice(1, 5).map((url: string, index: number) => (
              <div
                key={index}
                className="relative cursor-pointer overflow-hidden rounded-lg shadow-sm"
                onClick={() => setSelectedImage(url)}
              >
                <Image
                  src={url}
                  alt={`Property image ${index + 2}`}
                  fill
                  className="object-cover hover:scale-110 transition-transform duration-300"
                  sizes="(max-width: 768px) 50vw, 25vw"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tabbed navigation for better organization */}
      <Tabs defaultValue="details" className="mb-4 border-b">
        <TabsList className="flex space-x-8 w-full">
          <TabsTrigger value="details" className="px-4 py-2 flex items-center gap-2">
            <Info className="w-4 h-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="owner" className="px-4 py-2 flex items-center gap-2">
            <User className="w-4 h-4" />
            Owner
          </TabsTrigger>
          <TabsTrigger value="features" className="px-4 py-2 flex items-center gap-2">
            <Home className="w-4 h-4" />
            Features
          </TabsTrigger>
          <TabsTrigger value="pricing" className="px-4 py-2 flex items-center gap-2">
            <DollarSign className="w-4 h-4" />
            Pricing
          </TabsTrigger>
          <TabsTrigger value="documents" className="px-4 py-2 flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Documents
          </TabsTrigger>
          {property.imageUrls && property.imageUrls.length > 5 && (
            <TabsTrigger value="gallery" className="px-4 py-2 flex items-center gap-2">
              <ParkingSquare className="w-4 h-4" />
              Gallery
            </TabsTrigger>
          )}
          {property.transactionHistory && property.transactionHistory.length > 0 && (
            <TabsTrigger value="history" className="px-4 py-2 flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              History
            </TabsTrigger>
          )}
        </TabsList>

        {/* Tab Content */}
        <TabsContent value="details">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <ClipboardList className="w-5 h-5 mr-2" />
                Basic Information
              </h3>
              <div className="grid grid-cols-5 gap-4">
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Property Code</p>
                  <p className="text-lg font-bold">{property.code}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Type</p>
                  <p className="text-lg font-bold">{property.type}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Status</p>
                  <p className="text-lg font-bold">{property.status}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Year Built</p>
                  <p className="text-lg font-bold">{property.yearBuilt || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Address</p>
                  <p className="text-base font-bold">
                    {property.location.address}, {property.location.ward},{' '}
                    {property.location.district}, {property.location.city}
                  </p>
                </div>
              </div>
            </div>

            {/* Description */}
            {property.description && (
              <div className="grid grid-cols-2 gap-4 space-y-3 p-4 rounded-lg">
                <div>
                  <h3 className="text-lg font-bold flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2" />
                    Description
                  </h3>
                  <p className="text-sm col-span-2 font-bold py-2">{property.description}</p>
                </div>
                <div className="relative h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">Location map placeholder</p>
                </div>
              </div>
            )}

            {/* Contact Information */}
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <Phone className="w-5 h-5 mr-2" />
                Contact Information
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Contact Name</p>
                  <p>{property.contactName}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Contact Phone</p>
                  <p>{property.contactPhone}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Contact Email</p>
                  <p>{property.contactEmail || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Dates */}
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Dates
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Created At</p>
                  <p>{formatDate(property.createdAt)}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Updated At</p>
                  <p>{formatDate(property.updatedAt)}</p>
                </div>
                {/* <div>
                  <p className="text-sm font-bold text-muted-foreground">Admin Note</p>
                  <p>{property.adminNote || 'N/A'}</p>
                </div> */}
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Owner Tab */}
        <TabsContent value="owner">
          <div className="space-y-3 p-4 rounded-lg">
            <h3 className="text-lg font-bold flex items-center">
              <User className="w-5 h-5 mr-2" />
              Owner Information
            </h3>
            {/* <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-bold text-muted-foreground">Name</p>
                <p>{property.owner.name}</p>
              </div>
              <div>
                <p className="text-sm font-bold text-muted-foreground">Phone</p>
                <p>{property.owner.phone}</p>
              </div>
              <div>
                <p className="text-sm font-bold text-muted-foreground">Email</p>
                <p>{property.owner.email || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-bold text-muted-foreground">Address</p>
                <p>{property.owner.address || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-bold text-muted-foreground">Nationality</p>
                <p>{property.owner.nationality || 'N/A'}</p>
              </div>
            </div> */}
          </div>
        </TabsContent>

        {/* Features Tab */}
        <TabsContent value="features">
          <div className="space-y-6">
            {/* Property Details */}
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <Building2 className="w-5 h-5 mr-2" />
                Property Details
              </h3>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Bedrooms</p>
                  <p className="text-lg font-bold">{property.propertyDetails?.bedrooms || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Bathrooms</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.bathrooms || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Living Rooms</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.livingRooms || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Kitchens</p>
                  <p className="text-lg font-bold">{property.propertyDetails?.kitchens || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Land Area</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.landArea
                      ? `${property.propertyDetails.landArea} m²`
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Building Area</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.buildingArea
                      ? `${property.propertyDetails.buildingArea} m²`
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Number of Floors</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.numberOfFloors || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Floor Number</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.floorNumber || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Orientation</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.apartmentOrientation || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Furnished</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.furnished ? 'Yes' : 'No'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-bold text-muted-foreground">Has Basement</p>
                  <p className="text-lg font-bold">
                    {property.propertyDetails?.hasBasement ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>
            </div>

            {/* Amenities with improved visual layout */}
            {property.amenities && (
              <div className="space-y-3 p-4 rounded-lg">
                <h3 className="text-lg font-bold flex items-center">
                  <Warehouse className="w-5 h-5 mr-2" />
                  Amenities
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {property.amenities.parking !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Car className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Parking</span>
                      </div>
                      {property.amenities.parking ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.elevator !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <ArrowUpDown className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Elevator</span>
                      </div>
                      {property.amenities.elevator ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.swimmingPool !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Waves className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Swimming Pool</span>
                      </div>
                      {property.amenities.swimmingPool ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.gym !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Dumbbell className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Gym</span>
                      </div>
                      {property.amenities.gym ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.securitySystem !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Shield className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Security System</span>
                      </div>
                      {property.amenities.securitySystem ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.airConditioning !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Wind className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Air Conditioning</span>
                      </div>
                      {property.amenities.airConditioning ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.balcony !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Building className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Balcony</span>
                      </div>
                      {property.amenities.balcony ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.garden !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Trees className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Garden</span>
                      </div>
                      {property.amenities.garden ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.playground !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <PlayCircle className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Playground</span>
                      </div>
                      {property.amenities.playground ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                  {property.amenities.backupGenerator !== undefined && (
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:border-red-200 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-50 rounded-lg">
                          <Power className="w-5 h-5 text-red-600" />
                        </div>
                        <span className="font-bold">Backup Generator</span>
                      </div>
                      {property.amenities.backupGenerator ? (
                        <Check className="w-5 h-5 text-green-500" />
                      ) : (
                        <X className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Pricing Tab */}
        <TabsContent value="pricing">
          <div className="space-y-6">
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Price Details
              </h3>
              <div className="grid grid-cols-1 gap-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 rounded-lg border border-gray-200 shadow-sm">
                    <p className="text-sm font-bold text-muted-foreground">Sale Price</p>
                    <p className="text-2xl font-bold">
                      {property.priceDetails?.salePrice !== undefined
                        ? formatCurrency(
                            property.priceDetails.salePrice,
                            property.priceDetails?.currency
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div className="p-4 rounded-lg border border-gray-200 shadow-sm">
                    <p className="text-sm font-bold text-muted-foreground">Rental Price</p>
                    <p className="text-2xl font-bold">
                      {property.priceDetails?.rentalPrice !== undefined
                        ? formatCurrency(
                            property.priceDetails.rentalPrice,
                            property.priceDetails?.currency
                          )
                        : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-bold text-muted-foreground">Price Per m²</p>
                    <p>
                      {property.priceDetails?.pricePerSquareMeter
                        ? formatCurrency(
                            property.priceDetails.pricePerSquareMeter,
                            property.priceDetails?.currency
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-bold text-muted-foreground">Deposit Amount</p>
                    <p>
                      {property.priceDetails?.depositAmount
                        ? formatCurrency(
                            property.priceDetails.depositAmount,
                            property.priceDetails?.currency
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-bold text-muted-foreground">Maintenance Fee</p>
                    <p>
                      {property.priceDetails?.maintenanceFee
                        ? formatCurrency(
                            property.priceDetails.maintenanceFee,
                            property.priceDetails?.currency
                          )
                        : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-bold text-muted-foreground">Payment Methods</p>
                    <p>{property.priceDetails?.paymentMethods}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents">
          <div className="space-y-3 p-4 rounded-lg">
            <h3 className="text-lg font-bold flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Legal Documents
            </h3>
            {property.legalDocumentUrls && property.legalDocumentUrls.length > 0 ? (
              <div className="grid grid-cols-1 gap-3">
                {property.legalDocumentUrls.map((url: string, index: number) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-md border border-gray-200 hover:border-blue-300 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-blue-100 p-2 rounded">
                        <FileText className="h-5 w-5 text-blue-600" />
                      </div>
                      <span className="truncate max-w-md">{getFileName(url)}</span>
                    </div>
                    <div className="flex gap-2">
                      {isExcelFile(url) && (
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 flex items-center gap-1 transition-colors"
                        >
                          View <ExternalLink className="h-3 w-3" />
                        </a>
                      )}
                      {isImageFile(url) && (
                        <button
                          onClick={() => setSelectedImage(url)}
                          className="text-sm px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                        >
                          Preview
                        </button>
                      )}
                      <a
                        href={url}
                        download
                        className="text-sm px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                      >
                        Download
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">No documents available</div>
            )}
          </div>
        </TabsContent>

        {/* Gallery Tab */}
        {property.imageUrls && property.imageUrls.length > 5 && (
          <TabsContent value="gallery">
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <ParkingSquare className="w-5 h-5 mr-2" />
                Gallery
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {property.imageUrls.map((url: string, index: number) => (
                  <div
                    key={index}
                    className="relative cursor-pointer overflow-hidden rounded-lg shadow-sm"
                    onClick={() => setSelectedImage(url)}
                  >
                    <Image
                      src={url}
                      alt={`Property image ${index + 1}`}
                      fill
                      className="object-cover hover:scale-110 transition-transform duration-300"
                      sizes="(max-width: 768px) 50vw, 25vw"
                    />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        )}

        {/* History Tab */}
        {property.transactionHistory && property.transactionHistory.length > 0 && (
          <TabsContent value="history">
            <div className="space-y-3 p-4 rounded-lg">
              <h3 className="text-lg font-bold flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Transaction History
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {property.transactionHistory.map(
                  (transaction: TransactionHistory, index: number) => (
                    <div key={index} className="border p-3 rounded-md">
                      <div className="grid grid-cols-3 gap-2">
                        <div>
                          <p className="text-sm font-bold text-muted-foreground">
                            Transaction Date
                          </p>
                          <p>{transaction.transactionDate}</p>
                        </div>
                        <div>
                          <p className="text-sm font-bold text-muted-foreground">
                            Transaction Type
                          </p>
                          <p>{transaction.transactionType}</p>
                        </div>
                        <div>
                          <p className="text-sm font-bold text-muted-foreground">Price</p>
                          <p>
                            {formatCurrency(transaction.price, property.priceDetails?.currency)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-bold text-muted-foreground">Buyer</p>
                          <p>{transaction.buyer.id}</p>
                        </div>
                        <div>
                          <p className="text-sm font-bold text-muted-foreground">Seller</p>
                          <p>{transaction.seller.id}</p>
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </DialogContent>
  );
}
