import React, { useState } from 'react';
import { User, ArrowRight, Building2, Users, Menu } from 'lucide-react';

interface MainContentProps {
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
}

const MainContent: React.FC<MainContentProps> = ({ sidebarCollapsed, onToggleSidebar }) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const options = [
    {
      id: 'work',
      title: 'Kinh doanh',
      icon: Building2,
      description: 'Quản lý bất động sản, khách hàng và giao dịch',
      color: 'from-red-500 to-orange-500',
    },
    {
      id: 'personal',
      title: 'Cá nhân',
      icon: User,
      description: 'Tổ chức công việc và mục tiêu cá nhân',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      id: 'team',
      title: 'Nhó<PERSON>',
      icon: Users,
      description: '<PERSON><PERSON><PERSON> hợ<PERSON> làm việc nhóm và dự án',
      color: 'from-green-500 to-emerald-500',
    },
  ];

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white flex flex-col">
      {/* Mobile Header - Only show when sidebar is collapsed */}
      {sidebarCollapsed && (
        <div className="lg:hidden bg-white border-b border-gray-100 p-4">
          <div className="flex items-center justify-between">
            <button
              onClick={onToggleSidebar}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Menu size={24} />
            </button>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-orange-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="text-gray-900 font-bold text-lg">Revoland</span>
            </div>
            <div className="w-10"></div> {/* Spacer for centering */}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="max-w-3xl w-full">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25">
                <span className="text-white font-bold text-2xl">R</span>
              </div>
              <h1 className="text-4xl font-bold text-gray-900">Revoland</h1>
            </div>
            <p className="text-gray-600 text-xl mb-3">Chào mừng, An San!</p>
            <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-orange-500 rounded-full mx-auto"></div>
          </div>

          {/* Main Question */}
          <div className="text-center mb-16">
            <h2 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Bạn muốn sử dụng Revoland cho mục đích gì?
            </h2>
            <p className="text-gray-600 text-xl max-w-2xl mx-auto leading-relaxed">
              Chọn mục đích sử dụng chính để chúng tôi thiết lập workspace phù hợp nhất cho bạn
            </p>
          </div>

          {/* Options */}
          <div className="grid gap-6 mb-12">
            {options.map(option => (
              <button
                key={option.id}
                onClick={() => setSelectedOption(option.id)}
                className={`group relative p-8 rounded-2xl border-2 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl ${
                  selectedOption === option.id
                    ? 'border-red-500 bg-red-50 shadow-xl shadow-red-500/10'
                    : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-lg'
                }`}
              >
                <div className="flex items-center gap-6">
                  <div className={`p-4 rounded-2xl bg-gradient-to-br ${option.color} shadow-lg`}>
                    <option.icon size={32} className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{option.title}</h3>
                    <p className="text-gray-600 text-lg">{option.description}</p>
                  </div>
                  <div
                    className={`transition-all duration-300 ${
                      selectedOption === option.id
                        ? 'text-red-500 opacity-100 transform translate-x-0'
                        : 'text-gray-400 opacity-0 transform translate-x-4'
                    }`}
                  >
                    <ArrowRight size={24} />
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Continue Button */}
          <div className="text-center">
            <button
              disabled={!selectedOption}
              className={`px-12 py-4 rounded-2xl font-bold text-lg transition-all duration-300 ${
                selectedOption
                  ? 'bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-xl shadow-red-500/25 hover:shadow-red-500/40 transform hover:scale-105'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              Tiếp tục với {selectedOption && options.find(o => o.id === selectedOption)?.title}
            </button>
          </div>

          {/* Progress Indicator */}
          <div className="mt-16 flex justify-center">
            <div className="flex gap-3">
              <div className="w-3 h-3 bg-red-500 rounded-full shadow-lg shadow-red-500/50"></div>
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainContent;
