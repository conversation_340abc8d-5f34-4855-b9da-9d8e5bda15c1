'use client';
import React, { useState } from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TooltipProvider } from '@/components/ui/tooltip';
import {
  Calendar,
  User,
  ArrowRight,
  Trash2,
  Filter,
  Search,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Activity,
  Clock,
} from 'lucide-react';

import { DealLogSearchParams, DealStatus } from '@/lib/api/services/fetchDeal';
import { UserInfoTooltip } from '@/components/UserInfoTooltip';
import { useDealLogs } from '@/hooks/useDeals';

// Inline status configuration
const statusConfig = {
  [DealStatus.New]: {
    label: 'New',
    color: '#3B82F6',
    className: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  [DealStatus.Contacted]: {
    label: 'Contacted',
    color: '#F59E0B',
    className: 'bg-amber-100 text-amber-800 border-amber-200',
  },
  [DealStatus.Negotiation]: {
    label: 'Negotiation',
    color: '#8B5CF6',
    className: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  [DealStatus.Closing]: {
    label: 'Closing',
    color: '#EF4444',
    className: 'bg-red-100 text-red-800 border-red-200',
  },
  [DealStatus.Won]: {
    label: 'Won',
    color: '#10B981',
    className: 'bg-green-100 text-green-800 border-green-200',
  },
  [DealStatus.Lost]: {
    label: 'Lost',
    color: '#6B7280',
    className: 'bg-gray-100 text-gray-800 border-gray-200',
  },
} as const;

interface DealLogsProps {
  dealId: string;
  className?: string;
}

export default function DealLogs({ dealId, className = '' }: DealLogsProps) {
  const [params, setParams] = useState<DealLogSearchParams>({
    pageNumber: 1,
    pageSize: 10,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { logs, isLoading, isError, count, page, totalPages, refetch, isFetching } = useDealLogs(
    dealId,
    params
  );

  const handlePageChange = (newPage: number) => {
    setParams(prev => ({ ...prev, pageNumber: newPage }));
  };

  const handleFilterChange = (
    key: keyof DealLogSearchParams,
    value: string | number | undefined
  ) => {
    setParams(prev => ({
      ...prev,
      [key]: value,
      pageNumber: 1,
    }));
  };

  const handleSearch = () => {
    setParams(prev => ({
      ...prev,
      triggeredBy: searchTerm || undefined,
      pageNumber: 1,
    }));
  };

  const clearFilters = () => {
    setParams({
      pageNumber: 1,
      pageSize: 10,
    });
    setSearchTerm('');
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      className: 'bg-gray-100 text-gray-800',
    };
    return (
      <Badge className={`${config.className} whitespace-nowrap flex-shrink-0`}>
        {config.label}
      </Badge>
    );
  };

  if (isLoading && !logs.length) {
    return (
      <div className={`flex items-center justify-center p-12 ${className}`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-blue-200 rounded-full animate-spin border-t-blue-600"></div>
            <Activity className="w-5 h-5 text-blue-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <div className="text-center">
            <p className="text-gray-600 font-medium">Loading deal history</p>
            <p className="text-gray-500 text-sm">Please wait while we fetch the activity logs</p>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className={`p-8 text-center ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
            <Activity className="w-6 h-6 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-red-900 mb-2">Failed to load deal history</h3>
          <p className="text-red-700 mb-4">
            We encountered an error while fetching the activity logs.
          </p>
          <Button onClick={() => refetch()} className="bg-red-600 hover:bg-red-700 text-white">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Activity History</h2>
            </div>
            <Badge variant="outline" className="text-sm">
              {count} {count === 1 ? 'activity' : 'activities'}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? 'bg-blue-50 border-blue-200' : ''}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
            <Button variant="ghost" size="sm" onClick={() => refetch()} disabled={isFetching}>
              <RefreshCw className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card className="p-6 bg-gray-50 border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search by triggered by */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Search User</label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Search by user..."
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="flex-1"
                  />
                  <Button size="sm" onClick={handleSearch}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Select
                  value={params.status || ''}
                  onValueChange={value => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {Object.entries(statusConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: config.color }}
                          />
                          <span>{config.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Page Size */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Items per page</label>
                <Select
                  value={params.pageSize?.toString() || '10'}
                  onValueChange={value => handleFilterChange('pageSize', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </Card>
        )}

        {/* Logs List */}
        {logs.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <Calendar className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No activity found</h3>
                <p className="text-gray-600">This deal does not have any activity logs yet.</p>
              </div>
            </div>
          </Card>
        ) : (
          <div className="space-y-4">
            {logs.map(log => (
              <Card key={log.id} className="p-6 hover:shadow-md transition-shadow bg-white">
                <div className="flex items-start space-x-4">
                  {/* Icon */}
                  <div className="flex-shrink-0">
                    {log.isDeleteAction ? (
                      <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                        <Trash2 className="h-5 w-5 text-red-600" />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <ArrowRight className="h-5 w-5 text-blue-600" />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {log.isDeleteAction ? (
                          <Badge variant="destructive" className="text-xs">
                            DELETED
                          </Badge>
                        ) : (
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(log.fromStatus)}
                            <ArrowRight className="h-3 w-3 text-gray-400" />
                            {getStatusBadge(log.toStatus)}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatDate(log.timestamp)}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <User className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      <UserInfoTooltip
                        userInfo={{
                          name: log.triggeredBy.name || '',
                          email: log.triggeredBy.email || '',
                          phone: log.triggeredBy.phone || '',
                        }}
                      >
                        <span className="font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer transition-colors">
                          {log.triggeredBy.name}
                        </span>
                      </UserInfoTooltip>

                      {!log.isDeleteAction && (
                        <span className="text-gray-600 text-sm">
                          moved this deal from <span className="font-medium">{log.fromStatus}</span>{' '}
                          to <span className="font-medium">{log.toStatus}</span>
                        </span>
                      )}

                      {log.isDeleteAction && (
                        <span className="text-red-600 font-medium text-sm">deleted this deal</span>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {(page - 1) * (params.pageSize || 10) + 1} to{' '}
              {Math.min(page * (params.pageSize || 10), count)} of {count} activities
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1 || isFetching}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = i + 1;
                  return (
                    <Button
                      key={pageNum}
                      variant={page === pageNum ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      disabled={isFetching}
                      className="w-10 h-8"
                    >
                      {pageNum}
                    </Button>
                  );
                })}

                {totalPages > 5 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant={page === totalPages ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handlePageChange(totalPages)}
                      disabled={isFetching}
                      className="w-10 h-8"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages || isFetching}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
