'use client';

import { useState, useRef, useEffect } from 'react';
import { Search } from 'lucide-react';
import { usePropertiesBySaler } from '@/hooks/useProperty';
import { Property } from '@/lib/api/services/fetchProperty';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';

export default function SearchProperties() {
  const [searchValue, setSearchValue] = useState('');
  const [searchTerm, setSearchTerm] = useState<string | ''>('');
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Gọi useProperties với searchTerm
  const { properties, isLoading, isError, error } = usePropertiesBySaler(
    showDropdown && searchTerm ? { searchTerm } : undefined
  );

  const handleSearch = () => {
    if (searchValue.trim()) {
      setSearchTerm(searchValue.trim());
      setShowDropdown(true);
    } else {
      setSearchTerm('');
      setShowDropdown(false);
    }
  };

  // Đóng dropdown khi click ra ngoài
  useEffect(() => {
    if (!showDropdown) return;
    const handleClick = (e: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(e.target as Node)
      ) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [showDropdown]);

  // Ẩn dropdown khi xóa searchValue
  useEffect(() => {
    if (!searchValue) {
      setShowDropdown(false);
      setSearchTerm('');
    }
  }, [searchValue]);

  return (
    <section className="w-[60%] bg-card sm:px-4 lg:px-8 flex flex-col items-center mb-4 relative">
      <div className="w-full flex flex-col sm:flex-row gap-2 items-center relative">
        <div className="relative w-full">
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500 pointer-events-none">
            <Search className="h-5 w-5" />
          </span>
          <input
            ref={inputRef}
            type="text"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            placeholder="Tìm kiếm bất động sản..."
            className="flex-1 border rounded-md px-10 py-2 text-base focus:outline-none transition w-full text-black dark:text-white bg-white dark:bg-sidebar"
            onKeyDown={e => {
              if (e.key === 'Enter') handleSearch();
            }}
            onFocus={() => {
              if (searchTerm && properties && properties.length > 0) setShowDropdown(true);
            }}
          />
        </div>
        {/* Dropdown kết quả search */}
        {showDropdown && (
          <div
            ref={dropdownRef}
            className="absolute left-0 top-full mt-1 w-full z-50 bg-sidebar border rounded-xl shadow-lg max-h-80 overflow-y-auto scrollbar-none"
            style={{ scrollbarWidth: 'none' }}
          >
            <style>{`
              .scrollbar-none::-webkit-scrollbar { display: none; }
            `}</style>
            {isLoading && (
              <ul className="divide-y divide-white/10 animate-pulse">
                {Array.from({ length: 4 }).map((_, idx) => (
                  <li key={idx} className="py-2 px-4 flex flex-col gap-2">
                    <Skeleton className="h-4 w-1/2 rounded" />
                    <Skeleton className="h-3 w-1/3 rounded" />
                    <Skeleton className="h-3 w-2/3 rounded" />
                  </li>
                ))}
              </ul>
            )}
            {isError && (
              <div className="text-red-600 py-4 text-center">
                {error?.message || 'Đã xảy ra lỗi'}
              </div>
            )}
            {!isLoading && properties && properties.length > 0 && (
              <ul className="divide-y divide-white/10">
                {properties.map((property: Property) => (
                  <li
                    key={property.id}
                    className="py-2 px-4 cursor-pointer flex items-center justify-between gap-2"
                  >
                    <div>
                      <div className="font-semibold">
                        {property.name} -{' '}
                        {property.transactionType === 'ForRent'
                          ? 'Cho thuê'
                          : property.transactionType === 'ForSale'
                            ? 'Bán nhà'
                            : 'Dự án'}
                      </div>
                      <div className="text-sm">Mô tả: {property.title || 'Không'}</div>
                      <div className="text-sm">Địa chỉ: {property.location?.address}</div>
                    </div>
                    <Link
                      href={`/saler/contract/create?id=${property.id}`}
                      className="px-3 py-1 rounded bg-black text-white text-sm font-medium whitespace-nowrap dark:bg-white dark:text-black transition-colors"
                      title="Tạo hợp đồng cho bất động sản này"
                    >
                      Tạo hợp đồng
                    </Link>
                  </li>
                ))}
              </ul>
            )}
            {!isLoading && properties && properties.length === 0 && !isError && (
              <div className="text-center text-gray-500 py-4">
                Không tìm thấy bất động sản phù hợp.
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
}
