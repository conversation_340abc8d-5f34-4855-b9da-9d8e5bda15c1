'use client';

import { ArrowLef<PERSON>, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PaginationControlProps {
  currentPage: number;
  totalPages: number;
  onPrev: () => void;
  onNext: () => void;
}

export default function PaginationControl({
  currentPage,
  totalPages,
  onPrev,
  onNext,
}: PaginationControlProps) {
  return (
    <div className="flex justify-between items-center mt-8">
      <div className="text-sm text-gray-400">
        {String(currentPage).padStart(2, '0')} of {String(totalPages).padStart(2, '0')}
      </div>
      <div className="flex space-x-2">
        <Button
          size="icon"
          variant="outline"
          onClick={onPrev}
          disabled={currentPage <= 1}
          className="rounded-full bg-transparent border-gray-700 hover:bg-gray-800 hover:text-white"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Button
          size="icon"
          variant="outline"
          onClick={onNext}
          disabled={currentPage >= totalPages}
          className="rounded-full bg-transparent border-gray-700 hover:bg-gray-800 hover:text-white"
        >
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
