'use client';
import React from 'react';
import SearchProperties from './components/SearchProperties';
import { SiteHeader } from '@/components/common/siteHeader';
import { DataTable } from '@/components/ui/data-table';
import { columns } from './components/colums';
import { useContract } from '@/hooks/useContract';
// import { ContractFilterParams } from '@/lib/api/services/fetchContract';

const RentalContractPage = () => {
  const listContract = useContract();
  // const quertyParams: ContractFilterParams = {
  //   pageNumber: 1, // Default to the first page
  //   pageSize: 10, // Default page size
  // };
  const { data: contracts } = listContract.useGetAllContractsBySaler();
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
        <SiteHeader title="Tạo hợp đồng thuê căn hộ" />
      </header>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        <div className="w-full h-full">
          <div className="h-full flex flex-col">
            <div className="flex-1 overflow-auto scrollbar-hide">
              <div className="p-4 lg:p-6">
                <SearchProperties />
              </div>
              <div className="p-4 lg:p-6">
                {/* This is where the contract list or table will be rendered */}
                <DataTable columns={columns} data={contracts?.data.data || []} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RentalContractPage;
