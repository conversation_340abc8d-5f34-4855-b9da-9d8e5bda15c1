'use client';

import React from 'react';
import Messenger from './components/Messenger';

export default function MessengerPage() {
  return (
    // <div className="flex h-screen bg-background">
    //   {/* Sidebar - hiển thị danh sách conversations */}
    //   <div
    //     className={`${showSidebar ? 'flex' : 'hidden'} md:flex md:w-80 lg:w-96 flex-col border-r border-border`}
    //   >
    //     <ConversationSidebar
    //       conversations={conversations}
    //       selectedConversationId={selectedConversation?.id}
    //       onSelectConversation={selectConversation}
    //       isLoading={isLoadingConversations}
    //     />
    //   </div>

    //   {/* Chat area - hiển thị khi đã chọn conversation */}
    //   {selectedConversation ? (
    //     <ChatWindow
    //       conversation={selectedConversation}
    //       messages={messages}
    //       pinnedMessages={pinnedMessages}
    //       isLoading={isLoadingMessages}
    //       isSending={isSending}
    //       hasMoreMessages={hasMoreMessages}
    //       onBack={handleBackToConversations}
    //       onSendMessage={handleSendMessage}
    //       onLoadMore={loadMoreMessages}
    //       onRecall={recallMessage}
    //       onPin={pinMessage}
    //       onUnpin={unpinMessage}
    //       onMediaSelect={handleMediaPreview}
    //     />
    //   ) : (
    //     /* Empty state khi chưa chọn conversation */
    //     <div className="flex-1 flex items-center justify-center">
    //       <div className="text-center">
    //         <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
    //           <svg
    //             className="w-8 h-8 text-muted-foreground"
    //             fill="none"
    //             stroke="currentColor"
    //             viewBox="0 0 24 24"
    //           >
    //             <path
    //               strokeLinecap="round"
    //               strokeLinejoin="round"
    //               strokeWidth={2}
    //               d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
    //             />
    //           </svg>
    //         </div>
    //         <h3 className="text-lg font-medium mb-2">Chọn cuộc trò chuyện</h3>
    //         <p className="text-muted-foreground">
    //           Chọn một cuộc trò chuyện từ danh sách để bắt đầu nhắn tin
    //         </p>
    //       </div>
    //     </div>
    //   )}

    //   {/* Media preview modal */}
    //   <MediaPreviewModal
    //     isOpen={mediaPreview.isOpen}
    //     files={mediaPreview.files}
    //     onClose={closeMediaPreview}
    //     onSend={(content: string, files: File[]) => {
    //       handleSendMessage(content, files);
    //       closeMediaPreview();
    //     }}
    //   />
    // </div>
    <div className="flex h-screen bg-background">
      <Messenger />
    </div>
  );
}
