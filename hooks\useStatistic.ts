import { useQuery } from '@tanstack/react-query';
import {
  getStaticService,
  TopKpisResponse,
  TopKpisFilters,
  TodayTaskResponse,
} from '@/lib/api/services/fetchStatistic';

/**
 * Hook for fetching top KPIs statistics with optional date filters
 * @param filters - Optional date range filters (from/to)
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useStatisticTopKpis = (filters?: TopKpisFilters, enabled: boolean = true) => {
  return useQuery<TopKpisResponse, Error>({
    queryKey: ['statistic', 'top-kpis', filters ? JSON.stringify(filters) : null],
    queryFn: () => getStaticService.getStatic(filters),
    enabled,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    retry: 2,
    select: (data: TopKpisResponse) => ({
      status: data.status,
      message: data.message,
      code: data.code,
      data: data.data,
    }),
  });
};

/**
 * Hook for fetching today's tasks and deal values by property
 * @param enabled - Whether the query should be enabled (default: true)
 */
export const useStatisticTodayTask = (enabled: boolean = true) => {
  return useQuery<TodayTaskResponse, Error>({
    queryKey: ['statistic', 'today-task'],
    queryFn: () => getStaticService.getTodayTask(),
    enabled,
    staleTime: 1 * 60 * 1000,
    gcTime: 3 * 60 * 1000,
    refetchOnWindowFocus: true,
    retry: 2,
    select: (data: TodayTaskResponse) => ({
      status: data.status,
      message: data.message,
      code: data.code,
      data: data.data,
    }),
  });
};

/**
 * Combined hook for all statistic data with conditional fetching
 * @param options - Configuration for which queries to enable
 */
export const useStatistic = (options?: {
  enableTopKpis?: boolean;
  enableTodayTask?: boolean;
  topKpisFilters?: TopKpisFilters;
}) => {
  const { enableTopKpis = true, enableTodayTask = true, topKpisFilters } = options || {};

  const topKpis = useStatisticTopKpis(topKpisFilters, enableTopKpis);
  const todayTask = useStatisticTodayTask(enableTodayTask);

  return {
    topKpis,
    todayTask,
    isLoading: topKpis.isLoading || todayTask.isLoading,
    isError: topKpis.isError || todayTask.isError,
    error: topKpis.error || todayTask.error,
    // Extracted data for easier access
    topKpisData: topKpis.data,
    todayTaskData: todayTask.data,
  };
};

// Re-export types for convenience
export type { TopKpisResponse, TopKpisFilters, TodayTaskResponse };
export { StatusOfDeal } from '@/lib/api/services/fetchStatistic';
