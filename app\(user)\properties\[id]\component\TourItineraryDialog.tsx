'use client';

import { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, <PERSON><PERSON><PERSON>ck, CirclePlus, User } from 'lucide-react';
import { useUserTours } from '@/hooks/useTour'; // Import the new hook
import { CreateTourForm } from './CreateTourForm';
import type { Property } from '@/lib/api/services/fetchProperty';
import { useRouter } from 'next/navigation';
import { TourTable } from '@/components/TourTable';

interface TourItineraryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  property?: Property;
}

export default function TourItineraryDialog({
  isOpen,
  onClose,
  property,
}: TourItineraryDialogProps) {
  const router = useRouter();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const { tours, isLoading, isError, error, refetch } = useUserTours(); // Use the hook

  useEffect(() => {
    if (isOpen) {
      setShowCreateForm(false); // Always show tour list first when dialog opens
      refetch(); // Manually refetch when dialog opens
    }
  }, [isOpen, refetch]);

  const handleFormSuccess = () => {
    setShowCreateForm(false); // Switch back to tour list view
    refetch(); // Re-fetch tours to show the newly created one
  };

  const handleCloseDialog = () => {
    setShowCreateForm(false); // Reset view when closing
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseDialog}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-scroll scrollbar-hide">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarCheck className="h-5 w-5" />
            {showCreateForm ? 'Tạo Lịch Trình Tour Mới' : 'Lịch Trình Xem Nhà Của Bạn'}
          </DialogTitle>
          <DialogDescription>
            {showCreateForm
              ? 'Lên lịch trình đi xem nhiều bất động sản đã lưu.'
              : 'Xem và quản lý các lịch trình xem nhà đã tạo.'}
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          {showCreateForm ? (
            <CreateTourForm
              onSuccess={handleFormSuccess}
              onBack={() => setShowCreateForm(false)}
              property={property}
            />
          ) : (
            <div className="grid gap-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Đang tải tour của bạn...</span>
                </div>
              ) : isError ? (
                <div className="text-center text-red-500 py-8">
                  <p>{error?.message || 'Không thể tải danh sách tour.'}</p>
                  <Button onClick={() => refetch()} className="mt-4">
                    Thử lại
                  </Button>
                </div>
              ) : (
                <>
                  {tours && tours.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-lg text-muted-foreground mb-4">
                        Bạn chưa có lịch trình tour nào.
                      </p>
                      <Button
                        onClick={() => setShowCreateForm(true)}
                        className="bg-red-600 text-white hover:bg-red-700"
                      >
                        Tạo Lịch Trình Mới Ngay
                      </Button>
                    </div>
                  ) : (
                    <>
                      <div className="flex justify-end ">
                        <Button
                          onClick={() => setShowCreateForm(true)}
                          className="mb-4 self-end bg-red-600 text-white hover:bg-red-700"
                        >
                          <CirclePlus />
                          Tạo Lịch Trình Mới
                        </Button>
                        <Button
                          onClick={() => router.push(`/myrevo?tab=tour-planner`)}
                          className="mb-4 ml-4 self-end bg-red-600 text-white hover:bg-red-700"
                        >
                          <User />
                          Lịch Trình Của Bạn
                        </Button>
                      </div>
                      <TourTable disabled={isLoading} isDialog={true} />
                    </>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
