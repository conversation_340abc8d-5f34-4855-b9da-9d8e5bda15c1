import React, { useState } from 'react';
import {
  ArrowLeft,
  Users,
  Target,
  DollarSign,
  Calendar,
  Eye,
  Star,
  BarChart3,
  <PERSON><PERSON>hart,
  Clock,
  CheckCircle,
  Building2,
  FileText,
  Download,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Crown,
  Shield,
} from 'lucide-react';

interface CompanyDashboardProps {
  onBack: () => void;
}

interface KPIData {
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

interface Agent {
  id: string;
  name: string;
  avatar: string;
  role: string;
  deals: number;
  revenue: number;
  rating: number;
  status: 'active' | 'busy' | 'offline';
}

interface Lead {
  id: string;
  name: string;
  property: string;
  value: number;
  status: 'hot' | 'warm' | 'cold';
  agent: string;
  lastContact: string;
  source: string;
}

const CompanyDashboard: React.FC<CompanyDashboardProps> = ({ onBack }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  const kpiData: KPIData[] = [
    {
      title: 'Tổng Leads',
      value: 1247,
      change: 12.5,
      changeType: 'increase',
      icon: Users,
      color: 'from-blue-500 to-cyan-500',
      description: 'Khách hàng tiềm năng mới',
    },
    {
      title: 'Tỷ lệ Chuyển đổi',
      value: '24.8%',
      change: 3.2,
      changeType: 'increase',
      icon: Target,
      color: 'from-green-500 to-emerald-500',
      description: 'Leads thành giao dịch',
    },
    {
      title: 'Doanh thu',
      value: '₫45.2B',
      change: -2.1,
      changeType: 'decrease',
      icon: DollarSign,
      color: 'from-purple-500 to-pink-500',
      description: 'Tổng doanh thu tháng này',
    },
    {
      title: 'Giao dịch',
      value: 89,
      change: 8.7,
      changeType: 'increase',
      icon: CheckCircle,
      color: 'from-orange-500 to-red-500',
      description: 'Giao dịch hoàn thành',
    },
    {
      title: 'Lịch hẹn',
      value: 156,
      change: 15.3,
      changeType: 'increase',
      icon: Calendar,
      color: 'from-indigo-500 to-purple-500',
      description: 'Cuộc hẹn trong tuần',
    },
    {
      title: 'Đánh giá TB',
      value: '4.8/5',
      change: 0.2,
      changeType: 'increase',
      icon: Star,
      color: 'from-yellow-500 to-orange-500',
      description: 'Đánh giá từ khách hàng',
    },
  ];

  const topAgents: Agent[] = [
    {
      id: '1',
      name: 'Nguyễn Văn A',
      avatar: 'NVA',
      role: 'Senior Agent',
      deals: 25,
      revenue: 2500000000,
      rating: 4.9,
      status: 'active',
    },
    {
      id: '2',
      name: 'Trần Thị B',
      avatar: 'TTB',
      role: 'Team Leader',
      deals: 32,
      revenue: 3200000000,
      rating: 4.8,
      status: 'busy',
    },
    {
      id: '3',
      name: 'Lê Văn C',
      avatar: 'LVC',
      role: 'Agent',
      deals: 18,
      revenue: 1800000000,
      rating: 4.7,
      status: 'active',
    },
    {
      id: '4',
      name: 'Phạm Thị D',
      avatar: 'PTD',
      role: 'Junior Agent',
      deals: 12,
      revenue: 1200000000,
      rating: 4.6,
      status: 'offline',
    },
  ];

  const recentLeads: Lead[] = [
    {
      id: '1',
      name: 'Nguyễn Minh Hoàng',
      property: 'Căn hộ Vinhomes Central Park',
      value: 5200000000,
      status: 'hot',
      agent: 'Nguyễn Văn A',
      lastContact: '2 giờ trước',
      source: 'Website',
    },
    {
      id: '2',
      name: 'Lê Thị Mai',
      property: 'Biệt thự Phú Mỹ Hưng',
      value: 12000000000,
      status: 'warm',
      agent: 'Trần Thị B',
      lastContact: '1 ngày trước',
      source: 'Facebook',
    },
    {
      id: '3',
      name: 'Trần Văn Nam',
      property: 'Chung cư The Manor',
      value: 3800000000,
      status: 'cold',
      agent: 'Lê Văn C',
      lastContact: '3 ngày trước',
      source: 'Giới thiệu',
    },
    {
      id: '4',
      name: 'Phạm Thị Lan',
      property: 'Nhà phố Thảo Điền',
      value: 8500000000,
      status: 'hot',
      agent: 'Phạm Thị D',
      lastContact: '30 phút trước',
      source: 'Zalo',
    },
  ];

  const periods = [
    { id: 'week', label: 'Tuần này' },
    { id: 'month', label: 'Tháng này' },
    { id: 'quarter', label: 'Quý này' },
    { id: 'year', label: 'Năm này' },
  ];

  const statusConfig = {
    hot: { label: 'Nóng', color: 'bg-red-100 text-red-800', dot: 'bg-red-500' },
    warm: { label: 'Ấm', color: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-500' },
    cold: { label: 'Lạnh', color: 'bg-blue-100 text-blue-800', dot: 'bg-blue-500' },
  };

  const agentStatusConfig = {
    active: { label: 'Hoạt động', color: 'bg-green-100 text-green-800', dot: 'bg-green-500' },
    busy: { label: 'Bận', color: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-500' },
    offline: { label: 'Offline', color: 'bg-gray-100 text-gray-800', dot: 'bg-gray-500' },
  };

  const roleIcons = {
    'Team Leader': Crown,
    'Senior Agent': Shield,
    Agent: Users,
    'Junior Agent': Eye,
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      notation: 'compact',
      maximumFractionDigits: 1,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Công ty</h1>
              <p className="text-gray-600">Tổng quan hiệu suất kinh doanh</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <select
              value={selectedPeriod}
              onChange={e => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 bg-white border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
            >
              {periods.map(period => (
                <option key={period.id} value={period.id}>
                  {period.label}
                </option>
              ))}
            </select>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <Download size={16} />
              <span className="text-sm font-medium">Xuất báo cáo</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl border border-gray-200 transition-all">
              <RefreshCw size={16} />
              <span className="text-sm font-medium">Làm mới</span>
            </button>
          </div>
        </div>
      </div>

      <div className="p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {kpiData.map((kpi, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div
                      className={`w-12 h-12 bg-gradient-to-br ${kpi.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <kpi.icon size={24} className="text-white" />
                    </div>
                    <div
                      className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                        kpi.changeType === 'increase'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {kpi.changeType === 'increase' ? (
                        <ArrowUpRight size={12} />
                      ) : (
                        <ArrowDownRight size={12} />
                      )}
                      <span>{Math.abs(kpi.change)}%</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-gray-600 text-sm font-medium">{kpi.title}</h3>
                    <p className="text-3xl font-bold text-gray-900">{kpi.value}</p>
                    <p className="text-gray-500 text-sm">{kpi.description}</p>
                  </div>
                </div>

                <div
                  className={`h-1 bg-gradient-to-r ${kpi.color} opacity-60 group-hover:opacity-100 transition-opacity duration-300`}
                ></div>
              </div>
            ))}
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Revenue Chart */}
            <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Doanh thu theo tháng</h3>
                    <p className="text-gray-600 text-sm">Biểu đồ doanh thu 6 tháng gần nhất</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all">
                      <BarChart3 size={18} />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all">
                      <PieChart size={18} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {/* Simplified Chart Representation */}
                <div className="space-y-4">
                  {[
                    { month: 'T7', value: 38, amount: '₫38.2B' },
                    { month: 'T8', value: 42, amount: '₫42.1B' },
                    { month: 'T9', value: 35, amount: '₫35.8B' },
                    { month: 'T10', value: 48, amount: '₫48.3B' },
                    { month: 'T11', value: 52, amount: '₫52.7B' },
                    { month: 'T12', value: 45, amount: '₫45.2B' },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center gap-4">
                      <span className="text-sm font-medium text-gray-600 w-8">{item.month}</span>
                      <div className="flex-1 bg-gray-100 rounded-full h-3 overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${item.value}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-semibold text-gray-900 w-20 text-right">
                        {item.amount}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Lead Sources */}
            <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-100">
                <h3 className="text-lg font-bold text-gray-900">Nguồn Leads</h3>
                <p className="text-gray-600 text-sm">Phân bố nguồn khách hàng tiềm năng</p>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {[
                    {
                      source: 'Website',
                      count: 456,
                      percentage: 36.6,
                      color: 'from-blue-500 to-cyan-500',
                    },
                    {
                      source: 'Facebook',
                      count: 324,
                      percentage: 26.0,
                      color: 'from-indigo-500 to-blue-500',
                    },
                    {
                      source: 'Giới thiệu',
                      count: 287,
                      percentage: 23.0,
                      color: 'from-green-500 to-emerald-500',
                    },
                    {
                      source: 'Zalo',
                      count: 180,
                      percentage: 14.4,
                      color: 'from-yellow-500 to-orange-500',
                    },
                  ].map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-900">{item.source}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">{formatNumber(item.count)}</span>
                          <span className="text-sm font-semibold text-gray-900">
                            {item.percentage}%
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2 overflow-hidden">
                        <div
                          className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Top Agents & Recent Leads */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Top Agents */}
            <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Top Agents</h3>
                    <p className="text-gray-600 text-sm">Nhân viên xuất sắc nhất tháng</p>
                  </div>
                  <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                    Xem tất cả
                  </button>
                </div>
              </div>

              <div className="divide-y divide-gray-100">
                {topAgents.map((agent, index) => {
                  const RoleIcon = roleIcons[agent.role as keyof typeof roleIcons] || Users;
                  const statusInfo = agentStatusConfig[agent.status];

                  return (
                    <div key={agent.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold flex-shrink-0">
                            {agent.avatar}
                          </div>
                          <div
                            className={`absolute -bottom-1 -right-1 w-4 h-4 ${statusInfo.dot} rounded-full border-2 border-white`}
                          ></div>
                          {index < 3 && (
                            <div className="absolute -top-2 -left-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs font-bold">{index + 1}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-gray-900">{agent.name}</h4>
                            <span
                              className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}
                            >
                              {statusInfo.label}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mb-2">
                            <RoleIcon size={14} className="text-gray-400" />
                            <span className="text-sm text-gray-600">{agent.role}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>{agent.deals} giao dịch</span>
                            <span>{formatCurrency(agent.revenue)}</span>
                            <div className="flex items-center gap-1">
                              <Star size={12} className="text-yellow-500 fill-current" />
                              <span>{agent.rating}</span>
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="w-16 h-2 bg-gray-100 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                              style={{ width: `${(agent.deals / 35) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Recent Leads */}
            <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Leads Gần đây</h3>
                    <p className="text-gray-600 text-sm">Khách hàng tiềm năng mới nhất</p>
                  </div>
                  <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                    Xem tất cả
                  </button>
                </div>
              </div>

              <div className="divide-y divide-gray-100">
                {recentLeads.map(lead => {
                  const statusInfo = statusConfig[lead.status];

                  return (
                    <div key={lead.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-semibold text-sm flex-shrink-0">
                          {lead.name.charAt(0)}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-gray-900 truncate">{lead.name}</h4>
                            <span
                              className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusInfo.color} flex items-center gap-1`}
                            >
                              <div className={`w-2 h-2 ${statusInfo.dot} rounded-full`}></div>
                              {statusInfo.label}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2 truncate">{lead.property}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Agent: {lead.agent}</span>
                            <span>{lead.lastContact}</span>
                            <span>{lead.source}</span>
                          </div>
                        </div>

                        <div className="text-right">
                          <p className="text-sm font-semibold text-gray-900">
                            {formatCurrency(lead.value)}
                          </p>
                          <div className="flex items-center gap-1 mt-1">
                            <Clock size={12} className="text-gray-400" />
                            <span className="text-xs text-gray-500">{lead.lastContact}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-bold text-gray-900">Thao tác nhanh</h3>
              <p className="text-gray-600 text-sm">Các tác vụ thường dùng</p>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[
                  { icon: Users, label: 'Thêm Lead', color: 'from-blue-500 to-cyan-500' },
                  { icon: Calendar, label: 'Đặt lịch hẹn', color: 'from-green-500 to-emerald-500' },
                  { icon: FileText, label: 'Tạo báo cáo', color: 'from-purple-500 to-pink-500' },
                  { icon: Building2, label: 'Thêm BDS', color: 'from-orange-500 to-red-500' },
                ].map((action, index) => (
                  <button
                    key={index}
                    className="flex flex-col items-center gap-3 p-6 rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all group"
                  >
                    <div
                      className={`w-12 h-12 bg-gradient-to-br ${action.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <action.icon size={24} className="text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-900">{action.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDashboard;
