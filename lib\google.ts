'use server';

import { Client } from '@googlemaps/google-maps-services-js';

const client = new Client();

export const autocomplete = async (input: string) => {
  if (!input) return [];

  try {
    const response = await client.textSearch({
      params: {
        query: input,
        key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
      },
    });

    return response.data.results || [];
  } catch (error) {
    console.error('Error in autocomplete:', error);
    return [];
  }
};

export const getPlaceDetails = async (placeId: string) => {
  try {
    const response = await client.placeDetails({
      params: {
        place_id: placeId,
        fields: ['geometry', 'formatted_address'],
        key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
      },
    });

    return response.data.result;
  } catch (error) {
    console.error('Error fetching place details:', error);
    return null;
  }
};

export const calculateBoundingBox = (lat: number, lng: number, radiusInDegrees: number = 0.05) => {
  return {
    swLatitude: lat - radiusInDegrees,
    neLatitude: lat + radiusInDegrees,
    swLongitude: lng - radiusInDegrees,
    neLongitude: lng + radiusInDegrees,
  };
};
