'use client';

import { UnifiedAccountSettings } from '@/components/account-settings';
import { SiteHeader } from '@/components/common/siteHeader';

export default function ProfilePage() {
  return (
    <div className="flex flex-col h-full">
      <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
        <SiteHeader title="Hồ sơ cá nhân" />
      </header>
      <main className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 overflow-auto scrollbar-hide">
          <div className="p-4 lg:p-6">
            <UnifiedAccountSettings />
          </div>
        </div>
      </main>
    </div>
  );
}
