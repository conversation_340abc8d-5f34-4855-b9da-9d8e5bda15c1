import Image from 'next/image';
import { Property, TransactionType, PropertyType } from '@/lib/api/services/fetchProperty';

import {
  MapPin,
  Bed,
  Bath,
  Maximize,
  Heart,
  Share2,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { MouseEvent, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface PropertyCardProps {
  property: Property;
  priority?: boolean;
  onHover?: (propertyId: string | null) => void;
  size?: 'sm' | 'md';
}

const getPropertyTypeText = (type: PropertyType, transactionType: TransactionType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
  };

  const transactionText = transactionType === TransactionType.FOR_SALE ? 'bán' : 'cho thuê';
  return `${typeMap[type as keyof typeof typeMap]} ${transactionText}`;
};
const getPropertyType = (type: PropertyType) => {
  const typeMap = {
    [PropertyType.APARTMENT]: 'Chung cư',
    [PropertyType.LAND_PLOT]: 'Nhà Đất',
    [PropertyType.VILLA]: 'Biệt thự',
    [PropertyType.SHOP_HOUSE]: 'Nhà phố',
  };

  return `${typeMap[type as keyof typeof typeMap]}`;
};

const formatPrice = (price: number, transactionType: TransactionType) => {
  if (transactionType === TransactionType.FOR_RENT) {
    if (price >= 1000000) {
      return (
        <span>
          {(price / 1000000).toFixed(1)} triệu
          <span className="text-sm text-muted-foreground font-light"> /tháng</span>
        </span>
      );
    }
    return (
      <span>
        {(price / 1000).toFixed(1)} nghìn
        <span className="text-sm text-muted-foreground font-light"> /tháng</span>
      </span>
    );
  } else {
    if (price >= 1000000000) {
      return <span>{(price / 1000000000).toFixed(1)} tỷ</span>;
    } else if (price >= 1000000) {
      return <span>{(price / 1000000).toFixed(1)} triệu</span>;
    } else {
      return <span>{(price / 1000).toFixed(1)} nghìn</span>;
    }
  }
};

export function PropertyCardAuth({
  property,
  priority = false,
  onHover,
  size = 'md',
}: PropertyCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [preloadedImages, setPreloadedImages] = useState<number[]>([0]);
  const [isImageLoading, setIsImageLoading] = useState(true);

  // Handle hover state changes
  const handleMouseEnter = () => {
    setIsHovered(true);
    onHover?.(property.id);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    onHover?.(null);
  };

  // Preload next and previous images
  useEffect(() => {
    const preloadImage = (index: number) => {
      if (index >= 0 && index < property.imageUrls.length && !preloadedImages.includes(index)) {
        const img = new window.Image();
        img.src = property.imageUrls[index];
        setPreloadedImages(prev => [...prev, index]);
      }
    };

    // Preload next image
    const nextIndex = (currentImageIndex + 1) % property.imageUrls.length;
    preloadImage(nextIndex);

    // Preload previous image
    const prevIndex =
      currentImageIndex === 0 ? property.imageUrls.length - 1 : currentImageIndex - 1;
    preloadImage(prevIndex);
  }, [currentImageIndex, property.imageUrls, preloadedImages]);

  const nextImage = (e: MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === property.imageUrls.length - 1 ? 0 : prev + 1));
  };

  const prevImage = (e: MouseEvent) => {
    e.preventDefault();
    setIsImageLoading(true);
    setCurrentImageIndex(prev => (prev === 0 ? property.imageUrls.length - 1 : prev - 1));
  };

  return (
    <div
      className="overflow-hidden transition-all duration-300 flex flex-col  "
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Image Section with Badge */}
      <div className="relative aspect-square size-full mb-2 group">
        {/* Skeleton Loading */}
        {isImageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-xl" />
        )}

        {/* Main Image */}
        <Image
          src={property.imageUrls[currentImageIndex] || '/placeholder-property.jpg'}
          alt={property.title}
          fill
          priority={priority}
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className={cn(
            'object-cover rounded-2xl transition-opacity duration-300',
            isImageLoading ? 'opacity-0' : 'opacity-100'
          )}
          onLoad={() => setIsImageLoading(false)}
        />

        {/* Preload Hidden Images */}
        {property.imageUrls.map(
          (url, index) =>
            index !== currentImageIndex && (
              <Image
                key={index}
                src={url}
                alt={`${property.title} - Image ${index + 1}`}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="hidden"
                priority={index === 0}
              />
            )
        )}
        {/* Navigation Buttons */}
        {isHovered && property.imageUrls.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
              onClick={prevImage}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white/90 rounded-full h-8 w-8 z-10"
              onClick={nextImage}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </>
        )}
        <div className="absolute top-4 left-4 flex gap-2">
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className={cn(
                ' bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10',
                size === 'sm' && 'text-[10px] px-2 py-0.5'
              )}
            >
              {property.transactionType === TransactionType.FOR_SALE ? 'Bán' : 'Cho thuê'}
            </Badge>
            <Badge
              variant="outline"
              className={cn(
                ' bg-white/90 backdrop-blur-sm hover:bg-white/90 z-10',
                size === 'sm' && 'text-[10px] px-2 py-0.5'
              )}
            >
              {getPropertyType(property.type)}
            </Badge>
          </div>
        </div>
        {/* Image Counter */}
        {property.imageUrls.length > 1 && (
          <div
            className={cn(
              'absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full z-10',
              size === 'sm' && 'text-[10px]'
            )}
          >
            {currentImageIndex + 1}/{property.imageUrls.length}
          </div>
        )}
      </div>

      <div className="px-1">
        {/* Price and Actions Section */}
        <div className="flex justify-between items-center">
          <div>
            <p className={cn('text-xl font-semibold', size === 'sm' && 'text-base')}>
              {formatPrice(
                property.transactionType === TransactionType.FOR_SALE
                  ? property.priceDetails.salePrice || 0
                  : property.priceDetails.rentalPrice || 0,
                property.transactionType
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              className={cn('hover:text-red-600 transition-colors')}
            >
              <Heart className={cn('size-5')} />
            </Button>
            <Button variant="ghost" size="icon" className="hover:text-blue-600">
              <Share2 className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Property Details Section */}
        <div
          className={cn(
            'flex justify-between items-center mb-2 text-foreground text-sm',
            size === 'sm' && 'text-xs'
          )}
        >
          <div className="flex gap-4">
            <div className="flex items-center">
              <Bed className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
              <span>{property.propertyDetails.bedrooms}</span>
            </div>
            <div className="flex items-center">
              <Bath className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
              <span>{property.propertyDetails.bathrooms}</span>
            </div>
            <div className="flex items-center">
              <Maximize className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
              <span>{property.propertyDetails.buildingArea} m²</span>
            </div>
          </div>
          <div className={cn('text-sm text-muted-foreground', size === 'sm' && 'text-xs')}>
            {getPropertyTypeText(property.type, property.transactionType)}
          </div>
        </div>

        {/* Location Section */}
        <div className="mb-2">
          <div
            className={cn(
              'flex items-center text-xs text-muted-foreground',
              size === 'sm' && 'text-[10px]'
            )}
          >
            <MapPin className={cn('size-4 mr-1', size === 'sm' && 'size-3')} />
            <span>
              {property.location?.city || 'Location unavailable'}, {property.location?.district}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
