import { TransactionType } from '@/lib/api/services/fetchProperty';

enum Currency {
  VND = 'VND',
  USD = 'USD',
}

/**
 * Format a number as currency
 * @param amount The amount to format
 * @param currency The currency code
 * @returns The formatted currency string
 */
export function formatCurrency(
  amount: number | string | null | undefined,
  currency: Currency | string = Currency.VND
): string {
  if (amount === null || amount === undefined || amount === '' || isNaN(Number(amount)))
    return '0 đ'; // FIXED: always return valid string

  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  const currencyCode = currency || Currency.VND;

  try {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numericAmount);
  } catch (error) {
    return `${numericAmount.toLocaleString()} ${currencyCode}`;
  }
}

/**
 * Format a number into a readable price string for rent or sale.
 * @param price The numeric price value
 * @param transactionType FOR_RENT or FOR_SALE
 * @returns Formatted price string
 */
export function formatPrice(
  price: number | string | null | undefined,
  transactionType: TransactionType
): string {
  if (price === null || price === undefined || price === '') return '-';

  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
  if (isNaN(numericPrice)) return '-';

  const suffixRent = ' /tháng';

  if (transactionType === TransactionType.FOR_RENT) {
    if (numericPrice >= 1_000_000) {
      return `${(numericPrice / 1_000_000).toFixed(1)} triệu${suffixRent}`;
    } else {
      return `${(numericPrice / 1_000).toFixed(1)} nghìn${suffixRent}`;
    }
  } else {
    if (numericPrice >= 1_000_000_000) {
      return `${(numericPrice / 1_000_000_000).toFixed(1)} tỷ`;
    } else if (numericPrice >= 1_000_000) {
      return `${(numericPrice / 1_000_000).toFixed(1)} triệu`;
    } else {
      return `${(numericPrice / 1_000).toFixed(1)} nghìn`;
    }
  }
}
