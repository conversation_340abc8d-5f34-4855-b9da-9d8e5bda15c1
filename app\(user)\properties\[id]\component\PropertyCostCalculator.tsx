import { useState } from 'react';
import {
  Zap,
  Droplet,
  Bike,
  Car,
  Info,
  Calculator,
  Calendar,
  Settings,
  PiggyBank,
  ListChecks,
  DollarSign,
} from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';

import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import {
  PieChart,
  Pie,
  BarChart,
  Bar,
  XAxis,
  CartesianGrid,
  LabelList,
  Label,
  Cell,
} from 'recharts';

import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { formatShort } from '@/utils/numbers/formatShort';
import {
  calculateElectricityCost,
  calculateWaterCost,
} from '@/utils/numbers/electricityAndWaterCost';

import { PriceDetail } from '@/lib/api/services/fetchProperty';

interface PropertyCostCalculatorProps {
  propertyData: PriceDetail;
}

export default function PropertyCostCalculator({ propertyData }: PropertyCostCalculatorProps) {
  const { rentalPrice = 0, depositAmount, maintenanceFee = 0 } = propertyData;

  const bikeFee = 150_000;
  const carFee = 1_500_000;

  const [months, setMonths] = useState(6);
  const [kwh, setKwh] = useState(0);
  const [m3, setM3] = useState(0);
  const [bikes, setBikes] = useState(0);
  const [cars, setCars] = useState(0);
  const [managementFee, setManagementFee] = useState(maintenanceFee);
  const [deposit, setDeposit] = useState(depositAmount ?? rentalPrice * 2);
  const [showResult, setShowResult] = useState(false);

  const [result, setResult] = useState({
    elec: 0,
    water: 0,
    bike: 0,
    car: 0,
    management: 0,
    monthly: 0,
    total: 0,
  });

  const validateNonNeg = (v: number) => (v < 0 ? 0 : v);

  const handleCalculate = () => {
    if (months < 1 || kwh < 1 || m3 < 1) {
      alert('Nhập tháng, điện, nước ≥ 1');
      return;
    }

    const elec = calculateElectricityCost(kwh);
    const water = calculateWaterCost(m3);
    const bikeCost = bikeFee * bikes;
    const carCost = carFee * cars;
    const monthly = rentalPrice + elec + water + managementFee + bikeCost + carCost;
    const total = monthly * months + deposit;

    setResult({
      elec,
      water,
      bike: bikeCost,
      car: carCost,
      management: managementFee,
      monthly,
      total,
    });
    setShowResult(true);
  };

  const renderTooltip = (txt: string) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className="w-4 h-4 text-neutral-700 cursor-pointer" />
        </TooltipTrigger>
        <TooltipContent>{txt}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  const pieDataRaw = [
    { name: 'Tiền thuê', value: rentalPrice },
    { name: 'Điện', value: result.elec },
    { name: 'Nước', value: result.water },
    ...(managementFee > 0 ? [{ name: 'Quản lý', value: result.management }] : []),
    ...(bikes > 0 ? [{ name: 'Xe máy', value: result.bike }] : []),
    ...(cars > 0 ? [{ name: 'Ô tô', value: result.car }] : []),
  ];

  const pieData = [...pieDataRaw].sort((a, b) => b.value - a.value);

  const barData = [
    { name: 'T.Thuê', value: rentalPrice },
    { name: 'Điện', value: result.elec },
    { name: 'Nước', value: result.water },
    ...(managementFee > 0 ? [{ name: 'Phí QL', value: result.management }] : []),
    ...(bikes > 0 ? [{ name: 'Xe máy', value: result.bike }] : []),
    ...(cars > 0 ? [{ name: 'Ô tô', value: result.car }] : []),
  ];

  const LegendItem = ({ name, color }: { name: string; color: string }) => (
    <div className="flex items-center gap-1">
      <div className="w-3 h-3 rounded-sm shrink-0" style={{ backgroundColor: color }} />
      <span className="text-sm text-neutral-800 whitespace-nowrap">{name}</span>
    </div>
  );

  const renderLegend = () => {
    const count = pieData.length;
    const base = (i: number) => `hsl(0, 83%, ${45 + i * 8}%)`;

    if (count <= 3) {
      return (
        <div className="mt-4 flex justify-center gap-4 flex-wrap">
          {pieData.map((d, i) => (
            <LegendItem key={d.name} name={d.name} color={base(i)} />
          ))}
        </div>
      );
    }

    if (count === 4) {
      return (
        <div className="mt-4 flex justify-center">
          <div
            className="grid gap-x-6 gap-y-2"
            style={{ gridTemplateColumns: 'repeat(2, max-content)' }}
          >
            {pieData.map((d, i) => (
              <LegendItem key={d.name} name={d.name} color={base(i)} />
            ))}
          </div>
        </div>
      );
    }

    if (count === 5) {
      const row1 = pieData.slice(0, 3);
      const row2 = pieData.slice(3);
      return (
        <div className="mt-4 flex flex-col items-center space-y-2">
          <div className="flex gap-4">
            {row1.map((d, i) => (
              <LegendItem key={d.name} name={d.name} color={base(i)} />
            ))}
          </div>
          <div className="flex gap-4">
            {row2.map((d, i) => (
              <LegendItem key={d.name} name={d.name} color={base(i + 3)} />
            ))}
          </div>
        </div>
      );
    }

    return (
      <div className="mt-4 grid grid-cols-3 gap-x-8 gap-y-2 justify-center mx-auto">
        {pieData.map((d, i) => (
          <LegendItem key={d.name} name={d.name} color={base(i)} />
        ))}
      </div>
    );
  };

  return (
    <div id="price" className="mb-4 md:mb-8">
      {/* Header */}
      {/* Form */}
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:p-4">
          <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
            <Calculator className="size-4 md:size-5" />
            Tính toán chi phí thuê nhà (dự tính)
          </CardTitle>
          <CardDescription className="text-xs md:text-sm">
            Nhập thông tin để tính toán chi phí thuê nhà
          </CardDescription>
        </CardHeader>
        <CardContent className="max-md:p-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            {[
              {
                icon: <Calendar className="w-4 h-4 text-neutral-700" />,
                label: 'Số tháng thuê',
                value: months,
                onChange: (v: number) => setMonths(validateNonNeg(v)),
                id: 'months',
                helper: 'Nhập số tháng thuê',
              },
              {
                icon: <DollarSign className="w-4 h-4 text-neutral-700" />,
                label: 'Tiền thuê (tháng)',
                value: rentalPrice,
                onChange: () => {},
                id: 'rent',
                helper: 'Cố định',
                readOnly: true,
              },
              {
                icon: <Zap className="w-4 h-4 text-neutral-700" />,
                label: 'Điện (kWh)',
                value: kwh,
                onChange: (v: number) => setKwh(validateNonNeg(v)),
                id: 'kwh',
                helper: 'Tính theo bậc thang EVN + Thuế GTGT 8%',
              },
              {
                icon: <Droplet className="w-4 h-4 text-neutral-700" />,
                label: 'Nước (m³)',
                value: m3,
                onChange: (v: number) => setM3(validateNonNeg(v)),
                id: 'm3',
                helper: 'Tính theo bậc thang + VAT + phí môi trường',
              },
              {
                icon: <Bike className="w-4 h-4 text-neutral-700" />,
                label: 'Xe máy',
                value: bikes,
                onChange: (v: number) => setBikes(validateNonNeg(v)),
                id: 'bike',
                helper: 'Phí: 150.000đ/tháng (tham khảo)',
              },
              {
                icon: <Car className="w-4 h-4 text-neutral-700" />,
                label: 'Ô tô',
                value: cars,
                onChange: (v: number) => setCars(validateNonNeg(v)),
                id: 'car',
                helper: 'Phí: 1.500.000đ/tháng (tham khảo)',
              },
              {
                icon: <Settings className="w-4 h-4 text-neutral-700" />,
                label: 'Phí quản lý',
                value: managementFee,
                onChange: (v: number) => setManagementFee(validateNonNeg(v)),
                id: 'manage',
                helper: 'Tùy chỉnh',
              },
              {
                icon: <PiggyBank className="w-4 h-4 text-neutral-700" />,
                label: 'Tiền đặt cọc',
                value: deposit,
                onChange: (v: number) => setDeposit(validateNonNeg(v)),
                id: 'deposit',
                helper: 'Mặc định 2 tháng tiền thuê (có thể thay đổi)',
              },
            ].map(item => (
              <div key={item.id}>
                <label className="font-medium flex items-center gap-2 mb-1">
                  {item.icon}
                  {item.label}
                </label>
                <Input
                  type="number"
                  min={['months', 'kwh', 'm3'].includes(item.id) ? 1 : 0}
                  value={item.value}
                  onChange={e => item.onChange(+e.target.value)}
                  readOnly={item.readOnly}
                />
                <p className="text-xs text-muted-foreground mt-1">{item.helper}</p>
              </div>
            ))}
          </div>
          <div className="flex justify-end mb-6">
            <Button
              onClick={handleCalculate}
              className="bg-neutral-700 text-white hover:bg-black transition"
            >
              Tính toán
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Kết quả */}
      {showResult && (
        <Card>
          <CardHeader className="max-md:p-4">
            <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
              <ListChecks className="w-5 h-5 text-neutral-700" />
              Kết quả tính toán
            </CardTitle>
            <CardDescription className="text-xs md:text-sm">
              Chi tiết chi phí thuê nhà
            </CardDescription>
          </CardHeader>
          <CardContent className="max-md:p-4">
            <Accordion type="multiple" defaultValue={['monthly', 'total']}>
              <AccordionItem value="monthly">
                <AccordionTrigger className="px-6 py-4 hover:bg-neutral-100">
                  <span className="font-medium text-neutral-900">Tổng chi phí hàng tháng</span>
                  <span className="ml-auto text-lg font-semibold text-neutral-900 pr-4">
                    {formatCurrency(result.monthly)}
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="flex flex-col">
                      <CardHeader className="flex flex-col items-center text-center pb-2">
                        <CardTitle className="text-base md:text-lg">Phân bổ chi phí</CardTitle>
                        <CardDescription>Trên tổng chi phí hàng tháng</CardDescription>
                      </CardHeader>
                      <CardContent className="flex-col items-center">
                        <ChartContainer config={{}} className="w-full h-64 -translate-x-2">
                          <PieChart>
                            <ChartTooltip
                              content={({ active, payload }) =>
                                active && payload?.length ? (
                                  <div className="bg-white rounded-md shadow px-3 py-2 text-sm flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-sm"
                                      style={{ backgroundColor: payload[0]?.payload.fill }}
                                    />
                                    <span>{payload[0]?.name}</span>
                                    <span className="ml-2 font-semibold">
                                      {(() => {
                                        const value = (payload && payload[0]?.value) ?? 0;
                                        return formatCurrency(Number(value));
                                      })()}
                                    </span>
                                  </div>
                                ) : null
                              }
                            />
                            <Pie
                              data={pieData}
                              dataKey="value"
                              nameKey="name"
                              innerRadius="60%"
                              outerRadius="90%"
                              paddingAngle={0}
                              stroke="none"
                              isAnimationActive
                              label={({
                                cx = 0,
                                cy = 0,
                                midAngle = 0,
                                outerRadius = 0,
                                percent = 0,
                              }) => {
                                if (percent < 0.015) return null;
                                const RADIAN = Math.PI / 180;
                                const radius = outerRadius + 12;
                                const x = cx + radius * Math.cos(-midAngle * RADIAN);
                                const y = cy + radius * Math.sin(-midAngle * RADIAN);
                                return (
                                  <text
                                    x={x}
                                    y={y}
                                    fill="#dc2626"
                                    textAnchor="middle"
                                    dominantBaseline="central"
                                    fontSize={12}
                                    fontWeight={500}
                                  >
                                    {(percent * 100).toFixed(0)}%
                                  </text>
                                );
                              }}
                              labelLine={false}
                            >
                              {pieData.map((entry, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={`hsl(0, 83%, ${45 + index * 8}%)`}
                                  stroke="#ffffff"
                                  strokeWidth={1}
                                />
                              ))}
                              <Label
                                content={({ viewBox }) => {
                                  if (!viewBox || !('cx' in viewBox && 'cy' in viewBox))
                                    return null;
                                  const { cx, cy } = viewBox as { cx: number; cy: number };
                                  return (
                                    <text
                                      x={cx}
                                      y={cy}
                                      textAnchor="middle"
                                      dominantBaseline="middle"
                                    >
                                      <tspan
                                        x={cx}
                                        y={cy}
                                        className="fill-foreground text-[22px] font-bold"
                                      >
                                        {formatCurrency(result.monthly)}
                                      </tspan>
                                      <tspan
                                        x={cx}
                                        y={cy + 20}
                                        className="fill-muted-foreground text-sm"
                                      >
                                        Hàng tháng
                                      </tspan>
                                    </text>
                                  );
                                }}
                              />
                            </Pie>
                          </PieChart>
                        </ChartContainer>
                        {/* Legend */}
                        {renderLegend()}
                      </CardContent>
                    </Card>

                    <Card className="flex flex-col">
                      <CardHeader className="flex flex-col items-center text-center">
                        <CardTitle className="text-base md:text-lg">So sánh khoản chi</CardTitle>
                        <CardDescription>Theo tháng</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ChartContainer
                          config={{ value: { label: 'VNĐ', color: '#dc2626' } }}
                          className="w-full h-[300px]"
                        >
                          <BarChart
                            data={barData}
                            margin={{ top: 20, bottom: 0, left: 0, right: 0 }}
                          >
                            <CartesianGrid vertical={false} strokeDasharray="3 3" />
                            <XAxis
                              dataKey="name"
                              tickLine={false}
                              axisLine={false}
                              tick={{ fontSize: 12 }}
                            />

                            {/*Hover tooltip*/}
                            <ChartTooltip
                              content={({ active, payload }) =>
                                active && payload?.length ? (
                                  <div className="bg-white rounded-md shadow px-3 py-2 text-sm flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-sm"
                                      style={{
                                        backgroundColor: payload[0]?.payload.fill ?? '#dc2626',
                                      }}
                                    />
                                    <span className="text-muted-foreground">
                                      {payload[0]?.payload?.name}
                                    </span>
                                    <span className="ml-auto font-semibold">
                                      {formatCurrency(Number(payload[0]?.value ?? 0))}
                                    </span>
                                  </div>
                                ) : null
                              }
                            />
                            <Bar dataKey="value" fill="#dc2626" radius={4}>
                              <LabelList
                                dataKey="value"
                                position="top"
                                formatter={(v: number) => formatShort(v)}
                                fontSize={12}
                                fill="#333"
                              />
                            </Bar>
                          </BarChart>
                        </ChartContainer>
                      </CardContent>
                    </Card>
                  </div>
                  <ul className="mt-6 space-y-2 text-sm text-neutral-800">
                    <li className="flex justify-between">
                      <span>Tiền thuê</span>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(rentalPrice)}</span>
                        {renderTooltip('Tiền thuê 1 tháng')}
                      </div>
                    </li>
                    <li className="flex justify-between">
                      <span>Điện</span>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(result.elec)}</span>
                        {renderTooltip('Tính theo bậc thang ENV + thuế GTGT 8%')}
                      </div>
                    </li>
                    <li className="flex justify-between">
                      <span>Nước</span>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(result.water)}</span>
                        {renderTooltip('Tính theo bậc thang + thuế VAT và phí môi trường')}
                      </div>
                    </li>
                    {managementFee > 0 && (
                      <li className="flex justify-between">
                        <span>Phí quản lý</span>
                        <div className="flex items-center gap-1">
                          <span>{formatCurrency(managementFee)}</span>
                          {renderTooltip('Tùy chỉnh')}
                        </div>
                      </li>
                    )}
                    {bikes > 0 && (
                      <li className="flex justify-between">
                        <span>Phí gửi xe máy</span>
                        <div className="flex items-center gap-1">
                          <span>{formatCurrency(result.bike)}</span>
                          {renderTooltip(`${bikes} × ${formatCurrency(bikeFee)}`)}
                        </div>
                      </li>
                    )}
                    {cars > 0 && (
                      <li className="flex justify-between">
                        <span>Phí gửi ô tô</span>
                        <div className="flex items-center gap-1">
                          <span>{formatCurrency(result.car)}</span>
                          {renderTooltip(`${cars} × ${formatCurrency(carFee)}`)}
                        </div>
                      </li>
                    )}
                  </ul>
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="total">
                <AccordionTrigger className="px-6 py-4 hover:bg-neutral-100">
                  <span className="font-medium text-neutral-900">Tổng hợp đồng</span>
                  <span className="ml-auto text-lg font-semibold text-neutral-900 pr-4">
                    {formatCurrency(result.total)}
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4 text-sm space-y-3 text-neutral-800">
                  <div className="flex justify-between">
                    <span>Tổng chi phí thuê nhà ({months} tháng)</span>
                    <div className="flex items-center gap-1">
                      <span>{formatCurrency(result.monthly * months)}</span>
                      {renderTooltip(`Tổng chi phí hàng tháng × ${months}`)}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span>Tiền đặt cọc</span>
                    <div className="flex items-center gap-1">
                      <span>{formatCurrency(deposit)}</span>
                      {renderTooltip(
                        depositAmount
                          ? 'Được hoàn trả khi hết hợp đồng'
                          : 'Mặc định = 2 tháng × tiền thuê'
                      )}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
