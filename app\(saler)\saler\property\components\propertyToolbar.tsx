import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Search, Grid, List, Upload, Plus, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { PropertySearchParams } from '@/lib/api/services/fetchProperty';
import Link from 'next/link';

interface PropertyToolbarProps {
  searchParams: Partial<PropertySearchParams>;
  onFilterChange: (params: PropertySearchParams) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  totalItems: number;
  isLoading?: boolean;
  hasFilters?: boolean;
  onToggleFilters?: () => void;
  onMobileFilterOpen?: () => void;
}

export const PropertyToolbar = forwardRef<{ openMobileFilters: () => void }, PropertyToolbarProps>(
  (
    {
      searchParams,
      onFilterChange,
      viewMode,
      onViewModeChange,
      totalItems,
      isLoading = false,
      hasFilters = false,
      onToggleFilters,
      onMobileFilterOpen,
    },
    ref
  ) => {
    const [searchQuery, setSearchQuery] = useState(searchParams.searchTerm || '');

    useImperativeHandle(ref, () => ({
      openMobileFilters: () => {
        onMobileFilterOpen?.();
      },
    }));

    // const sortOptions: SortOption[] = [
    // {
    //   value: 'default',
    //   label: t('properties.sort.default'),
    //   field: 'createdAt',
    //   direction: 'desc',
    // },
    // {
    //   value: 'price_asc',
    //   label: t('properties.sort.price_low_high'),
    //   field: 'salePrice',
    //   direction: 'asc',
    // },
    // {
    //   value: 'price_desc',
    //   label: t('properties.sort.price_high_low'),
    //   field: 'salePrice',
    //   direction: 'desc',
    // },
    // {
    //   value: 'area_asc',
    //   label: t('properties.sort.area_small_large'),
    //   field: 'buildingArea',
    //   direction: 'asc',
    // },
    // {
    //   value: 'area_desc',
    //   label: t('properties.sort.area_large_small'),
    //   field: 'buildingArea',
    //   direction: 'desc',
    // },
    // {
    //   value: 'date_newest',
    //   label: t('properties.sort.newest_first'),
    //   field: 'createdAt',
    //   direction: 'desc',
    // },
    // {
    //   value: 'date_oldest',
    //   label: t('properties.sort.oldest_first'),
    //   field: 'createdAt',
    //   direction: 'asc',
    // },
    // ];

    //   const getCurrentSortValue = () => {
    //     const sortBy = searchParams.sortBy;
    //     const sortOrder = searchParams.sortOrder || 'desc';

    //     if (!sortBy) return 'default';

    //     const option = sortOptions.find(
    //       opt => opt.field === sortBy && opt.direction === sortOrder
    //     );
    //     return option?.value || 'default';
    //   };

    const handleSearchSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      onFilterChange({
        ...searchParams,
        searchTerm: searchQuery.trim() || undefined,
        pageNumber: 1,
      });
    };

    const handleSearchChange = (value: string) => {
      setSearchQuery(value);
      // Debounced search could be implemented here
    };

    //   const handleSortChange = (value: string) => {
    //     const option = sortOptions.find(opt => opt.value === value);
    //     if (!option) return;

    //     onFilterChange({
    //       ...searchParams,
    //       sortBy: option.field,
    //       sortOrder: option.direction,
    //       pageNumber: 1,
    //     });
    //   };

    const clearSearch = () => {
      setSearchQuery('');
      onFilterChange({
        ...searchParams,
        searchTerm: undefined,
        pageNumber: 1,
      });
    };

    const handleFilterClick = () => {
      // Check if we're on mobile (you can use a more sophisticated detection)
      const isMobile = window.innerWidth < 1024;
      if (isMobile) {
        onMobileFilterOpen?.();
      } else {
        onToggleFilters?.();
      }
    };

    return (
      <div className="flex items-center justify-between gap-2 lg:gap-3">
        {/* Left side - Search and View Controls */}
        <div className="flex items-center gap-2 lg:gap-3 flex-1 min-w-0">
          {/* Search */}
          <div className="flex-1 max-w-sm min-w-0">
            <form onSubmit={handleSearchSubmit} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm bất động sản..."
                value={searchQuery}
                onChange={e => handleSearchChange(e.target.value)}
                className="pl-10 pr-10 h-9"
                disabled={isLoading}
              />
              {searchQuery && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted"
                >
                  ×
                </Button>
              )}
            </form>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-md p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="h-7 px-2 text-xs"
            >
              <Grid className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="h-7 px-2 text-xs"
            >
              <List className="h-3 w-3" />
            </Button>
          </div>

          {/* Filter Toggle Button */}
          <Button
            variant={hasFilters ? 'default' : 'outline'}
            size="sm"
            onClick={handleFilterClick}
            className="flex items-center gap-1 lg:gap-2 h-9"
          >
            <Filter className="h-3 w-3" />
            <span className="hidden sm:inline">Bộ lọc</span>
            {hasFilters && (
              <Badge variant="secondary" className="ml-1 px-1 py-0 text-xs">
                {
                  Object.keys(searchParams).filter(
                    key =>
                      key !== 'pageNumber' &&
                      key !== 'pageSize' &&
                      searchParams[key as keyof PropertySearchParams] !== undefined
                  ).length
                }
              </Badge>
            )}
          </Button>

          {/* Results count */}
          <div className="text-sm text-muted-foreground hidden xl:block whitespace-nowrap">
            {isLoading ? (
              <span>Đang tải...</span>
            ) : (
              <span>
                {totalItems} {totalItems === 1 ? 'bất động sản' : 'bất động sản'}
              </span>
            )}
          </div>

          {/* Compact results count for smaller screens */}
          <div className="text-xs text-muted-foreground xl:hidden">
            {isLoading ? <span>...</span> : <span>{totalItems}</span>}
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {/* Import Properties Button */}
          <Button variant="outline" className="hidden md:flex h-9 text-sm">
            <Upload className="h-4 w-4 mr-2" />
            <span className="hidden lg:inline">Nhập bất động sản</span>
            <span className="lg:hidden">Nhập</span>
          </Button>

          {/* Add New Property Button */}
          <Link href="/saler/property/action">
            <Button className="h-9 text-sm">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Thêm mới</span>
              <span className="sm:hidden">Thêm</span>
            </Button>
          </Link>
        </div>

        {/* Active filters display - Show below toolbar if needed */}
        {/* {Object.keys(searchParams).filter(
        key =>
          key !== 'pageNumber' &&
          key !== 'pageSize' &&
          searchParams[key as keyof PropertySearchParams] !== undefined
      ).length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-background border-t shadow-sm flex items-center gap-2 flex-wrap z-10">
          <span className="text-sm text-muted-foreground font-medium">Bộ lọc đang áp dụng:</span>
          {searchParams.searchTerm && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Tìm kiếm: {searchParams.searchTerm}
              <Button
                onClick={() =>
                  onFilterChange({ ...searchParams, searchTerm: undefined, pageNumber: 1 })
                }
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          {searchParams.type && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Danh mục: {searchParams.type}
              <Button
                onClick={() => onFilterChange({ ...searchParams, type: undefined, pageNumber: 1 })}
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          {searchParams.status && searchParams.status !== PropertyStatus.AVAILABLE && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Trạng thái: {searchParams.status}
              <Button
                onClick={() =>
                  onFilterChange({ ...searchParams, status: undefined, pageNumber: 1 })
                }
                className="ml-1 hover:bg-muted-foreground/20 rounded h-3 w-3 p-0"
                variant="ghost"
              >
                ×
              </Button>
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange({ pageNumber: 1, pageSize: searchParams.pageSize })}
            className="ml-auto text-xs h-6 px-2"
          >
            Xóa tất cả
          </Button>
        </div>
      )} */}
      </div>
    );
  }
);

PropertyToolbar.displayName = 'PropertyToolbar';
