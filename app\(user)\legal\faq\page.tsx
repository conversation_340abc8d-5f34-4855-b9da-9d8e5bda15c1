'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Search,
  MessageCircle,
  AlertCircle,
  Shield,
  CreditCard,
  Users,
  Building,
  HelpCircle,
  ArrowLeft,
} from 'lucide-react';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: 'search-property',
    question: 'Làm thế nào để tìm kiếm bất động sản phù hợp trên Revoland?',
    answer:
      '<PERSON><PERSON><PERSON> có thể sử dụng các bộ lọc thông minh trên trang danh sách để tìm đúng sản phẩm theo nhu cầu như: <PERSON><PERSON><PERSON> h<PERSON>nh (c<PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>h<PERSON>, đ<PERSON><PERSON> n<PERSON>, v.v.), m<PERSON><PERSON>, di<PERSON><PERSON> t<PERSON>ch, s<PERSON> ph<PERSON>, vị trí cụ thể (phường, quận, thành phố), tình trạng pháp lý, hướng nhà, v.v. Trang web cũng hỗ trợ xem bất động sản trên bản đồ để bạn dễ hình dung vị trí.',
    category: 'general',
  },
  {
    id: 'create-account',
    question: 'Tôi có cần tạo tài khoản để tìm kiếm hoặc liên hệ người bán không?',
    answer:
      'Không cần. Bạn có thể truy cập và tìm kiếm bất động sản miễn phí, xem thông tin người đăng, liên hệ trực tiếp qua số điện thoại hoặc gửi tin nhắn nhanh. Tuy nhiên, nếu bạn muốn lưu tin yêu thích, theo dõi tin đăng, đăng tin bán/cho thuê, bạn cần tạo tài khoản.',
    category: 'general',
  },
  {
    id: 'post-listing',
    question: 'Làm sao để đăng tin bán hoặc cho thuê bất động sản?',
    answer:
      'Bạn cần tạo tài khoản và làm theo các bước: Vào mục Đăng tin → Chọn loại bất động sản, hình thức (bán/cho thuê) → Nhập đầy đủ thông tin (vị trí, diện tích, giá, mô tả chi tiết) → Tải hình ảnh/video → Xác nhận và thanh toán (nếu là tin VIP). Sau khi kiểm duyệt, tin của bạn sẽ hiển thị công khai trên nền tảng.',
    category: 'general',
  },
  {
    id: 'listing-types',
    question: 'Có những loại tin đăng nào? Khác biệt ra sao?',
    answer:
      'Tin thường: Hiển thị bình thường theo thời gian đăng. Tin VIP (VIP 1, 2, 3…): Được ưu tiên hiển thị trên đầu danh sách, tăng lượt xem và liên hệ. Tin VIP phù hợp nếu bạn cần bán/cho thuê nhanh chóng, tiếp cận khách hàng mục tiêu nhiều hơn.',
    category: 'general',
  },
  {
    id: 'technical-issues',
    question: 'Tôi gặp lỗi khi sử dụng trang web, phải làm sao?',
    answer:
      'Bạn có thể thử: Làm mới (refresh) trang, dùng trình duyệt khác hoặc chế độ ẩn danh. Nếu vẫn gặp sự cố, vui lòng liên hệ Hotline: 0968070478 hoặc Email: <EMAIL>',
    category: 'technical',
  },
  {
    id: 'verify-info',
    question: 'Làm sao để biết thông tin bất động sản có chính xác không?',
    answer:
      'Revoland luôn nỗ lực kiểm duyệt tin đăng, tuy nhiên bạn nên yêu cầu người bán cung cấp sổ hồng, giấy tờ liên quan, nên đi xem nhà thực tế, có thể liên hệ tư vấn pháp lý hoặc môi giới chuyên nghiệp để hỗ trợ.',
    category: 'general',
  },
  {
    id: 'moderation',
    question: 'Revoland có kiểm duyệt môi giới, tin giả không?',
    answer:
      'Có. Đội ngũ quản lý nội dung của Revoland thực hiện: Kiểm duyệt tin trước khi hiển thị, cảnh báo các tin có dấu hiệu spam hoặc lừa đảo, hỗ trợ xử lý khiếu nại giữa người mua và người bán. Bạn cũng có thể báo cáo tin sai phạm ngay dưới mỗi bài đăng.',
    category: 'general',
  },
  {
    id: 'broker-service',
    question: 'Tôi có thể làm môi giới trên Revoland không?',
    answer:
      "Có. Bạn có thể đăng ký tài khoản cá nhân hoặc công ty, đăng tin bán/cho thuê với số lượng lớn, được hỗ trợ các công cụ quảng bá, báo cáo hiệu suất. Revoland còn có chương trình 'Môi giới chuyên nghiệp' với nhiều quyền lợi nếu bạn hoạt động lâu dài và hiệu quả.",
    category: 'broker',
  },

  // Account & User Management
  {
    id: 'otp-issues',
    question: 'Tôi phải làm gì nếu không nhận được mã OTP?',
    answer:
      'Kiểm tra kết nối internet và sóng điện thoại, xem thư mục spam nếu là email. Nếu vẫn không nhận được, hãy thử yêu cầu lại sau 1 phút hoặc liên hệ CSKH.',
    category: 'account',
  },
  {
    id: 'change-contact',
    question: 'Tôi có thể thay đổi số điện thoại hoặc email tài khoản không?',
    answer:
      "Có, bạn có thể thay đổi tại mục 'Thông tin tài khoản'. Một số thông tin cần xác thực lại bằng OTP mới.",
    category: 'account',
  },
  {
    id: 'account-locked',
    question: 'Tôi bị khóa tài khoản, làm sao để mở lại?',
    answer:
      'Tài khoản bị khóa do vi phạm chính sách (spam, lừa đảo...). Gửi yêu cầu hỗ trợ qua Zalo hoặc email để được xem xét và mở khóa.',
    category: 'account',
  },

  {
    id: 'listing-rejected',
    question: 'Tin tôi bị từ chối duyệt, vì sao?',
    answer:
      'Nội dung chứa thông tin sai lệch, thiếu minh bạch; tiêu đề/quảng cáo sai quy định, thiếu hình ảnh; tin trùng lặp, không rõ pháp lý hoặc không mô tả đầy đủ.',
    category: 'listing',
  },
  {
    id: 'refresh-listing',
    question: 'Làm sao để làm mới hoặc gia hạn tin đăng?',
    answer:
      "Vào mục 'Tin của tôi' → Chọn 'Gia hạn' hoặc 'Làm mới'. Gia hạn sẽ kéo dài thời gian hiển thị. Làm mới sẽ đưa tin lên đầu danh sách trong thời gian ngắn.",
    category: 'listing',
  },

  {
    id: 'payment-methods',
    question: 'Revoland chấp nhận những hình thức thanh toán nào?',
    answer: 'Chuyển khoản ngân hàng và ví điện tử (Momo, VNPAY).',
    category: 'payment',
  },
  {
    id: 'vat-invoice',
    question: 'Tôi muốn xuất hóa đơn VAT thì làm sao?',
    answer:
      'Gửi thông tin xuất hóa đơn trong vòng 3 ngày từ ngày nạp tiền. Liên hệ bộ phận kế toán qua email: <EMAIL>',
    category: 'payment',
  },
  {
    id: 'payment-pending',
    question: 'Tôi nạp tiền nhưng chưa thấy cập nhật?',
    answer:
      'Vui lòng chờ từ 5–10 phút hoặc kiểm tra trạng thái giao dịch. Nếu vẫn chưa có, hãy gửi biên lai chuyển khoản đến CSKH để được hỗ trợ.',
    category: 'payment',
  },

  {
    id: 'become-broker',
    question: 'Làm sao để trở thành môi giới chuyên nghiệp trên Revoland?',
    answer:
      'Có tối thiểu 10 tin đăng hợp lệ trong 30 ngày gần nhất, đăng ký tài khoản môi giới, cập nhật hồ sơ cá nhân đầy đủ. Được cấp huy hiệu chuyên nghiệp nếu đủ điều kiện.',
    category: 'broker',
  },
  {
    id: 'bulk-listing',
    question: 'Tôi có thể đăng tin dự án/căn hộ số lượng lớn không?',
    answer:
      'Có. Liên hệ CSKH để được tư vấn gói dịch vụ dành cho môi giới hoặc doanh nghiệp. Hỗ trợ banner, PR, landing page dự án.',
    category: 'broker',
  },
];

const categories = [
  { id: 'all', name: 'Tất cả', icon: HelpCircle },
  { id: 'general', name: 'Câu hỏi chung', icon: MessageCircle },
  { id: 'account', name: 'Tài khoản', icon: Users },
  { id: 'listing', name: 'Đăng tin', icon: Building },
  { id: 'payment', name: 'Thanh toán', icon: CreditCard },
  { id: 'broker', name: 'Môi giới', icon: Shield },
  { id: 'technical', name: 'Kỹ thuật', icon: AlertCircle },
];

export default function FAQPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="flex items-center p-6">
        <Button variant="ghost" className="mr-4" onClick={() => router.back()}>
          <ArrowLeft className="w-6 h-6" />
        </Button>
      </div>
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-6xl mx-auto px-8 py-24">
          <div className="text-center space-y-8">
            <h1 className="text-7xl font-extralight text-gray-900 tracking-tight leading-none">
              Câu hỏi thường gặp
            </h1>
            <p className="text-2xl text-gray-500 max-w-3xl mx-auto font-light leading-relaxed">
              Tìm câu trả lời nhanh chóng cho những thắc mắc phổ biến về dịch vụ Revoland
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="max-w-6xl mx-auto px-8 py-16">
        <div className="space-y-8">
          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Tìm kiếm câu hỏi..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="pl-12 h-14 text-lg border-2 border-gray-200 rounded-2xl focus:border-red-500 focus:ring-0"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map(category => {
              const IconComponent = category.icon;
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`rounded-full px-6 py-3 transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'hover:bg-red-50 hover:border-red-500 hover:text-red-600'
                  }`}
                >
                  <IconComponent className="w-4 h-4 mr-2" />
                  {category.name}
                </Button>
              );
            })}
          </div>

          {searchTerm && (
            <div className="text-center">
              <Badge className="bg-red-50 text-red-600 hover:bg-red-50">
                Tìm thấy {filteredFAQs.length} kết quả cho &quot;{searchTerm}&quot;
              </Badge>
            </div>
          )}
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-8 pb-16">
        {filteredFAQs.length > 0 ? (
          <Accordion type="single" collapsible className="space-y-4">
            {filteredFAQs.map(faq => (
              <AccordionItem
                key={faq.id}
                value={faq.id}
                className="border border-gray-200 rounded-2xl px-6 data-[state=open]:ring-2 data-[state=open]:ring-red-500 data-[state=open]:border-red-500 transition-all duration-300"
              >
                <AccordionTrigger className="text-left hover:no-underline py-6 text-lg font-medium text-gray-900 hover:text-red-600 transition-colors">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 leading-relaxed pb-6 text-base">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-light text-gray-900 mb-4">Không tìm thấy kết quả</h3>
            <p className="text-gray-500 mb-6">
              Thử tìm kiếm với từ khóa khác hoặc liên hệ trực tiếp với chúng tôi
            </p>
            <Button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
              }}
              className="bg-red-500 hover:bg-red-600 text-white rounded-full px-8"
            >
              Xóa bộ lọc
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
