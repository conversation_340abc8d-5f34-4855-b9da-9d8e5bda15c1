import { ImageResponse } from 'next/og';
// App router includes @vercel/og.
// No need to install it.

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const img1 = searchParams.get('img1');
  const img2 = searchParams.get('img2');
  const img3 = searchParams.get('img3');
  const img4 = searchParams.get('img4');
  if (!img1 && !img2 && !img3 && !img4) {
    return new ImageResponse(<>Visit with &quot;?username=vercel&quot;</>, {
      width: 1200,
      height: 630,
    });
  }

  return new ImageResponse(
    (
      <div
        style={{
          display: 'flex',
          fontSize: 60,
          flexDirection: 'row',
          gridTemplateColumns: '1fr 1fr',
          gridTemplateRows: '1fr 1fr',
          background: '#f6f6f6',
          width: '100%',
          height: '100%',
          padding: '20px',
          gap: '10px',
        }}
      >
        <img width="256" height="256" src={`${img1}`} style={{}} />
        <img width="256" height="256" src={`${img2}`} style={{}} />
        <img width="256" height="256" src={`${img3}`} style={{}} />
        <img width="256" height="256" src={`${img4}`} style={{}} />
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}
