/**
 * Format thời gian xem property theo định dạng Việt Nam
 * @param viewedAt - ISO string thời gian xem
 * @returns Formatted string: "hôm nay", "hôm qua", hoặc "dd/MM/yyyy"
 */
export function formatViewedTime(viewedAt: string): string {
  const viewedDate = new Date(viewedAt);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Reset time to 00:00:00 for accurate date comparison
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const yesterdayStart = new Date(
    yesterday.getFullYear(),
    yesterday.getMonth(),
    yesterday.getDate()
  );
  const viewedStart = new Date(
    viewedDate.getFullYear(),
    viewedDate.getMonth(),
    viewedDate.getDate()
  );

  // So sánh ngày
  if (viewedStart.getTime() === todayStart.getTime()) {
    return 'Hôm nay';
  } else if (viewedStart.getTime() === yesterdayStart.getTime()) {
    return 'Hôm qua';
  } else {
    // Format: dd/MM/yyyy
    const day = viewedDate.getDate().toString().padStart(2, '0');
    const month = (viewedDate.getMonth() + 1).toString().padStart(2, '0');
    const year = viewedDate.getFullYear();
    return `${day}/${month}/${year}`;
  }
}

/**
 * Format thời gian xem property với giờ phút
 * @param viewedAt - ISO string thời gian xem
 * @returns Formatted string với giờ phút
 */
export function formatViewedTimeWithHour(viewedAt: string): string {
  const viewedDate = new Date(viewedAt);
  const hours = viewedDate.getHours().toString().padStart(2, '0');
  const minutes = viewedDate.getMinutes().toString().padStart(2, '0');
  const baseTime = formatViewedTime(viewedAt);

  return `${baseTime} lúc ${hours}:${minutes}`;
}

/**
 * Format thời gian xem property với chi tiết phút, giờ, ngày
 * @param viewedAt - ISO string thời gian xem
 * @returns Formatted string: "1p trước", "3 tiếng trước", "hôm qua", hoặc "14/07"
 */
export function formatViewedTimeDetailed(viewedAt: string): string {
  const viewedDate = new Date(viewedAt);
  const now = new Date();
  const diffInMs = now.getTime() - viewedDate.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // Nếu chưa đến 1 phút
  if (diffInMinutes < 1) {
    return 'Vừa xem';
  }

  // Nếu chưa đến 1 giờ
  if (diffInMinutes < 60) {
    return `${diffInMinutes}p trước`;
  }

  // Nếu chưa đến 1 ngày
  if (diffInHours < 24) {
    return `${diffInHours} tiếng trước`;
  }

  // Nếu chưa đến 7 ngày
  if (diffInDays < 7) {
    if (diffInDays === 1) {
      return 'Hôm qua';
    }
    return `${diffInDays} ngày trước`;
  }

  // Nếu trong cùng năm
  if (viewedDate.getFullYear() === now.getFullYear()) {
    const day = viewedDate.getDate().toString().padStart(2, '0');
    const month = (viewedDate.getMonth() + 1).toString().padStart(2, '0');
    return `${day}/${month}`;
  }

  // Nếu khác năm
  const day = viewedDate.getDate().toString().padStart(2, '0');
  const month = (viewedDate.getMonth() + 1).toString().padStart(2, '0');
  const year = viewedDate.getFullYear();
  return `${day}/${month}/${year}`;
}
