import { useState } from 'react';

import {
  ChevronDown,
  ChevronUp,
  Plus,
  Calendar,
  MoreHorizontal,
  Bold,
  Italic,
  Underline,
  Link,
  Paperclip,
  Loader2,
  Edit3,
  Save,
  X,
  MessageSquare,
  Clock,
} from 'lucide-react';
import { Deal, DealNote } from '@/lib/api/services/fetchDeal';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

import { toast } from 'sonner';
import { useUpdateDealNote, useUpdateDeal } from '@/hooks/useDeals';
import { formatDistanceToNow } from 'date-fns';

interface NotesSectionProps {
  deal: Deal;
}

const NotesSection = ({ deal }: NotesSectionProps) => {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
  });

  // Use your existing hook
  const updateDealMutation = useUpdateDeal();
  const updateNoteMutation = useUpdateDealNote();

  const handleAddNote = async () => {
    if (!newNote.title.trim() || !newNote.content.trim()) return;

    try {
      await updateDealMutation.mutateAsync({
        id: deal.id,
        note: {
          title: newNote.title,
          content: newNote.content,
        },
      });

      // Reset form on success
      setNewNote({ title: '', content: '' });
      setIsAddingNote(false);
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    }
  };

  const handleUpdateNote = async (
    dealNoteId: string,
    noteData: { title: string; content: string }
  ) => {
    try {
      await updateNoteMutation.mutateAsync({
        dealId: deal.id,
        dealNoteId,
        ...noteData,
      });

      toast.success('Note updated successfully');
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Note updated fail');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Notes</h2>
            </div>
            <Badge variant="outline" className="text-sm">
              {deal.notes?.length || 0} {(deal.notes?.length || 0) === 1 ? 'note' : 'notes'}
            </Badge>
          </div>

          <Button
            onClick={() => setIsAddingNote(!isAddingNote)}
            disabled={updateDealMutation.isPending}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Note
          </Button>
        </div>

        {/* Add New Note Form */}
        <Collapsible open={isAddingNote} onOpenChange={setIsAddingNote}>
          <CollapsibleContent>
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                <div className="flex items-center space-x-2 text-gray-700 mb-4">
                  <Edit3 className="h-4 w-4" />
                  <span className="font-medium">Create New Note</span>
                </div>

                {/* Note Title */}
                <div>
                  <Input
                    placeholder="Note title (e.g., Meeting Summary - July 12, 2024)"
                    value={newNote.title}
                    onChange={e => setNewNote(prev => ({ ...prev, title: e.target.value }))}
                    className="font-medium border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    disabled={updateDealMutation.isPending}
                  />
                </div>

                {/* Toolbar */}
                <div className="flex items-center gap-2 p-3 border rounded-lg bg-white">
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={updateDealMutation.isPending}
                    >
                      <Bold className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={updateDealMutation.isPending}
                    >
                      <Italic className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={updateDealMutation.isPending}
                    >
                      <Underline className="h-4 w-4" />
                    </Button>
                  </div>

                  <Separator orientation="vertical" className="h-6" />

                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={updateDealMutation.isPending}
                    >
                      <Link className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={updateDealMutation.isPending}
                    >
                      <Paperclip className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Note Content */}
                <Textarea
                  placeholder="Write your note here..."
                  value={newNote.content}
                  onChange={e => setNewNote(prev => ({ ...prev, content: e.target.value }))}
                  className="min-h-[120px] resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  disabled={updateDealMutation.isPending}
                />

                {/* Actions */}
                <div className="flex items-center justify-between pt-4">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Paperclip className="h-4 w-4" />
                    <span>Add attachments (coming soon)</span>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingNote(false);
                        setNewNote({ title: '', content: '' });
                      }}
                      disabled={updateDealMutation.isPending}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddNote}
                      disabled={
                        updateDealMutation.isPending ||
                        !newNote.title.trim() ||
                        !newNote.content.trim()
                      }
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {updateDealMutation.isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Add Note
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Existing Notes */}
      <div className="space-y-4">
        {deal.notes && deal.notes.length > 0 ? (
          deal.notes.map(note => (
            <NoteCard
              key={note.id}
              note={note}
              onUpdateNote={noteData => handleUpdateNote(note.id, noteData)}
              isUpdating={updateNoteMutation.isPending}
            />
          ))
        ) : (
          <Card className="p-12 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <MessageSquare className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No notes yet</h3>
                <p className="text-gray-600 mb-4">
                  Start documenting your conversations and important details about this deal.
                </p>
                <Button
                  onClick={() => setIsAddingNote(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Note
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

// Updated Note Card Component
interface NoteCardProps {
  note: DealNote;
  onUpdateNote: (noteData: { title: string; content: string }) => Promise<void>;
  isUpdating: boolean;
}

const NoteCard = ({ note, onUpdateNote, isUpdating }: NoteCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedNote, setEditedNote] = useState({
    title: note.title,
    content: note.content,
  });

  const handleSaveEdit = async () => {
    if (!editedNote.title.trim() || !editedNote.content.trim()) return;

    try {
      await onUpdateNote(editedNote);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating note:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditedNote({
      title: note.title,
      content: note.content,
    });
    setIsEditing(false);
  };

  const timeAgo = formatDistanceToNow(new Date(note.createdAt), { addSuffix: true });

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow bg-white">
      <div className="p-6 space-y-4">
        {/* Note Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-blue-100 text-blue-700 text-sm font-medium">
                  {note.createdBy?.charAt(0)?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 text-sm">
                    {note.createdBy || 'Unknown User'}
                  </span>
                  <span className="text-gray-400">•</span>
                  <span className="text-gray-500 text-sm">{timeAgo}</span>
                </div>
              </div>
            </div>

            {isEditing ? (
              <Input
                value={editedNote.title}
                onChange={e => setEditedNote(prev => ({ ...prev, title: e.target.value }))}
                className="font-semibold text-lg mb-3 border-gray-300 focus:border-blue-500"
                disabled={isUpdating}
              />
            ) : (
              <h3 className="font-semibold text-lg text-gray-900 mb-3">{note.title}</h3>
            )}
          </div>

          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setIsEditing(!isEditing)}
              disabled={isUpdating}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Note Preview */}
        {!isExpanded && !isEditing && (
          <div className="text-gray-700 line-clamp-3 text-sm leading-relaxed">{note.content}</div>
        )}

        {/* Expanded Content */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent>
            <div className="pt-4 border-t border-gray-100 space-y-4">
              {/* Full Content */}
              {isEditing ? (
                <div className="space-y-4">
                  <Textarea
                    value={editedNote.content}
                    onChange={e => setEditedNote(prev => ({ ...prev, content: e.target.value }))}
                    className="min-h-[120px] resize-none border-gray-300 focus:border-blue-500"
                    disabled={isUpdating}
                  />
                  <div className="flex gap-3 justify-end">
                    <Button variant="outline" onClick={handleCancelEdit} disabled={isUpdating}>
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveEdit}
                      disabled={
                        isUpdating || !editedNote.title.trim() || !editedNote.content.trim()
                      }
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isUpdating ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-700 whitespace-pre-wrap text-sm leading-relaxed">
                  {note.content}
                </div>
              )}

              {/* Metadata Footer */}
              {!isEditing && (
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Created {timeAgo}</span>
                    </div>

                    {note.updatedAt !== note.createdAt && (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>
                          Updated{' '}
                          {formatDistanceToNow(new Date(note.updatedAt), { addSuffix: true })}
                        </span>
                      </div>
                    )}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-700"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </Card>
  );
};

export default NotesSection;
