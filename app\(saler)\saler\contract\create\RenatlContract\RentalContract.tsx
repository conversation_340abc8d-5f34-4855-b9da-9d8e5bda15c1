'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { FileDown, Send, Save } from 'lucide-react';
import { toast } from 'sonner';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';

import Step1 from './components/Step1';
import Step2 from './components/Step2';
import Step3 from './components/Step3';
import Step4 from './components/Step4';
import Step5 from './components/Step5';
import WordLikeEditorAdvanced from './components/WordLikeEditorAdvanced';
import { FormData } from './types';
import {
  PartyAInput,
  PartyBInput,
  ContractTermsInput,
  ContractClausesArticle4to6Input,
  ContractClausesarticle7to10Input,
  formDataSchema,
} from './schemas';
import { generateRentalContractHtml } from './utils/rentalContractGenerator';
import { blobToFile, generateContractPDFFileName } from '../../utils/fileUtils';
import { Location, PriceDetail, PropertyDetail } from '@/lib/api/services/fetchProperty';
import { Tenant } from '@/lib/api/services/fetchTenant';
import { useTenant } from '@/hooks/useTenant';
import { useUploadPropertyLegalDocument } from '@/hooks/useAttachment';
import { useContract } from '@/hooks/useContract';
import { exportQuillContentToPDFWithSignatures } from '../../utils/pdfMakeExporter';
import { useRouter } from 'next/navigation';

interface RentalContractPageProps {
  propertyLocation: Location;
  propertyDetails?: PropertyDetail;
  priceDetails?: PriceDetail;
  legalDocumentUrls?: string[];
  propertyId?: string;
}

const RentalContractPage: React.FC<RentalContractPageProps> = ({
  propertyLocation,
  propertyDetails,
  priceDetails,
  propertyId,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [contractContent, setContractContent] = useState('');
  const [isContractEdited, setIsContractEdited] = useState(false);
  const [isUploadingPDF, setIsUploadingPDF] = useState(false);
  const [pdfUrls, setPdfUrls] = useState<string[]>([]); // State để lưu trữ URL của file PDF đã upload
  const [lastGeneratedContent, setLastGeneratedContent] = useState<string>(''); // Lưu trữ nội dung hợp đồng đã dùng để tạo PDF
  const tenantApi = useTenant();
  const RentalContract = useContract();
  const uploadContractPDFApi = useUploadPropertyLegalDocument();
  const [isLoadingSendContract, setIsLoadingSendContract] = useState(false);
  const router = useRouter();
  const initialFormData: FormData = {
    partyA: {
      name: '',
      address: '',
      phone: '',
      fax: '',
      taxCode: '',
      email: '',
      accountNumber: '',
      representative: '',
      birthYear: '',
      position: '',
      idNumber: '',
      idIssuedDate: '',
      idIssuedPlace: '',
      personalName: '',
      personalBirthYear: '',
      personalIdNumber: '',
      personalIdIssuedDate: '',
      personalIdIssuedPlace: '',
      personalAddress: '',
      personalPhone: '',
      ownershipType: '',
    },
    partyB: {
      name: '',
      idNumber: '',
      phone: '',
      birthDate: '',
      address: '',
      email: '',
      idVerification: [],
      leaseStartDate: '',
      leaseEndDate: '',
    },
    terms: {
      monthlyRent: priceDetails?.rentalPrice || 0,
      deposit: priceDetails?.depositAmount || 0,
      leaseStartDate: '',
      leaseEndDate: '',
      paymentMethod: '',
      paymentDay: 1,
      propertyAddress:
        propertyLocation?.address +
          ', ' +
          propertyLocation?.ward +
          ', ' +
          propertyLocation?.district +
          ', ' +
          propertyLocation?.city || '',
      propertyPurpose: '',
      propertyType: '',
      apartmentNumber: '',
      floor: '',
      area: propertyDetails?.buildingArea || 0,
      landArea: 0,
      sharedArea: 0,
      privateArea: 0,
      ownershipOrigin: '',
      ownershipRestrictions: '',
      facilities: '',
    },
    clausesArticle4to6: {
      article4to6: '',
    },
    clausesarticle7to10: {
      article7to10: '',
    },
  };

  const form = useForm<FormData>({
    resolver: zodResolver(formDataSchema),
    defaultValues: initialFormData,
  });

  const { watch } = form;
  const watchedData = watch();

  // Auto-generate contract content when form data changes
  useEffect(() => {
    if (!isContractEdited && watchedData) {
      const newContent = generateRentalContractHtml(watchedData);
      setContractContent(newContent);
    }
  }, [watchedData, isContractEdited]);

  const handleStep1Next = useCallback(
    (data: PartyAInput) => {
      form.setValue('partyA', data);
      setIsContractEdited(false);
      setCurrentStep(2);
    },
    [form]
  );

  const handleStep2Next = useCallback(
    (data: PartyBInput) => {
      form.setValue('partyB', data);
      form.setValue('terms.leaseStartDate', form.getValues('partyB').leaseStartDate);
      form.setValue('terms.leaseEndDate', form.getValues('partyB').leaseEndDate);
      setIsContractEdited(false);
      setCurrentStep(3);
    },
    [form]
  );

  const handleStep3Next = useCallback(
    (data: ContractTermsInput) => {
      form.setValue('terms', data);
      setIsContractEdited(false);
      setCurrentStep(4);
    },
    [form]
  );

  const handleStep4Next = useCallback(
    (data: ContractClausesArticle4to6Input) => {
      form.setValue('clausesArticle4to6', data);
      setIsContractEdited(false);
      setCurrentStep(5);
    },
    [form]
  );

  const handleStep5Next = useCallback(
    (data: ContractClausesarticle7to10Input) => {
      form.setValue('clausesarticle7to10', data);
      setIsContractEdited(false);
      setCurrentStep(6);
    },
    [form]
  );

  const handleBackStep = useCallback(() => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  }, []);

  const handleContractContentChange = useCallback((content: string) => {
    setContractContent(content);
    setIsContractEdited(true);
  }, []);

  const handleResetContract = useCallback(() => {
    const newContent = generateRentalContractHtml(watchedData);
    setContractContent(newContent);
    setIsContractEdited(false);
    setPdfUrls([]); // Reset pdfUrls khi khôi phục nội dung gốc
    setLastGeneratedContent('');
    toast.success('Đã khôi phục nội dung hợp đồng gốc');
  }, [watchedData]);

  const generateAndUploadPDF = useCallback(async (): Promise<string[]> => {
    try {
      setIsUploadingPDF(true);

      let pdfBlob: Blob;

      if (isContractEdited && contractContent) {
        // Generate PDF blob from edited HTML content
        const { exportQuillContentToPDFWithSignatures } = await import(
          '../../utils/pdfMakeExporter'
        );

        pdfBlob = (await exportQuillContentToPDFWithSignatures(contractContent, true)) as Blob;
      } else {
        // Generate PDF blob using structured data from form
        const newContent = generateRentalContractHtml(watchedData);
        pdfBlob = (await exportQuillContentToPDFWithSignatures(newContent, true)) as Blob;
      }

      if (!pdfBlob) {
        throw new Error('Failed to generate PDF blob');
      }

      // Convert blob to File object
      const fileName = generateContractPDFFileName('rental', propertyId);
      const pdfFile = blobToFile(pdfBlob, fileName);

      // Upload PDF to cloud
      const uploadResponse = await uploadContractPDFApi.mutateAsync({
        files: [pdfFile],
      });

      if (uploadResponse.status && uploadResponse.data && uploadResponse.data.length > 0) {
        const urls = uploadResponse.data.map(attachment => attachment.fileUrl);
        setLastGeneratedContent(
          isContractEdited ? contractContent : generateRentalContractHtml(watchedData)
        );
        setPdfUrls(urls);
        return urls;
      } else {
        throw new Error('Upload response không hợp lệ');
      }
    } catch (error) {
      console.error('Error generating and uploading PDF:', error);
      toast.error('Có lỗi xảy ra khi upload PDF hợp đồng');
      return [];
    } finally {
      setIsUploadingPDF(false);
    }
  }, [watchedData, propertyId, uploadContractPDFApi, isContractEdited, contractContent]);

  const getPdfUrls = useCallback(async (): Promise<string[]> => {
    // Kiểm tra xem nội dung hiện tại có khớp với nội dung đã dùng để tạo PDF trước đó không
    const currentContent = isContractEdited
      ? contractContent
      : generateRentalContractHtml(watchedData);
    if (pdfUrls.length > 0 && lastGeneratedContent === currentContent) {
      return pdfUrls;
    }
    // Nếu nội dung đã thay đổi hoặc chưa có URL, tạo và upload PDF mới
    return await generateAndUploadPDF();
  }, [
    pdfUrls,
    lastGeneratedContent,
    isContractEdited,
    contractContent,
    watchedData,
    generateAndUploadPDF,
  ]);

  const exportPDFWithPdfMakeStructured = useCallback(async () => {
    try {
      if (isContractEdited && contractContent) {
        // Export edited content using HTML exporter
        await exportQuillContentToPDFWithSignatures(contractContent, false);
        toast.success('Đã xuất PDF với nội dung đã chỉnh sửa thành công!');
      } else {
        // Export using structured data from form
        const newContent = generateRentalContractHtml(watchedData);
        await exportQuillContentToPDFWithSignatures(newContent, false);
        toast.success('Đã xuất PDF thành công!');
      }
      // Cập nhật pdfUrls nếu cần
      await getPdfUrls();
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Có lỗi xảy ra khi xuất PDF');
    }
  }, [watchedData, isContractEdited, contractContent, getPdfUrls]);

  const sendToParties = useCallback(async () => {
    setIsLoadingSendContract(true);
    try {
      const urls = await getPdfUrls();
      if (urls.length === 0) {
        toast.error('Không có file PDF để gửi');
        return;
      }
      // Giả sử có API để gửi hợp đồng
      // await sendContractToParties({ pdfUrls: urls, ...watchedData });
      const resOwner = await RentalContract.sendContractMutation.mutateAsync({
        contractLink: urls[0], // Giả sử chỉ gửi 1 link PDF
        email: watchedData.partyA.email,
      });
      const resTenant = await RentalContract.sendContractMutation.mutateAsync({
        contractLink: urls[0], // Giả sử chỉ gửi 1 link PDF
        email: watchedData.partyB.email,
      });
      if (!resOwner.status || !resTenant.status) {
        toast.error('Không thể gửi hợp đồng đến các bên');
        return;
      }
      toast.success('Đã gửi hợp đồng đến các bên thành công');
    } catch (error) {
      console.error('Error sending contract:', error);
      toast.error('Có lỗi xảy ra khi gửi hợp đồng');
    } finally {
      setIsLoadingSendContract(false);
    }
  }, [getPdfUrls]);

  const saveContract = useCallback(async () => {
    const tenantData = {
      name: watchedData.partyB.name,
      email: watchedData.partyB.email,
      phone: watchedData.partyB.phone,
      address: watchedData.partyB.address,
      leaseEndDate: watchedData.partyB.leaseEndDate,
      leaseStartDate: watchedData.partyB.leaseStartDate,
      idVerification: watchedData.partyB.idVerification || [],
    };
    try {
      // Step 1: Handle tenant creation/searchzz
      // const resTenant = await tenantApi.searchTenantsByPhone(watchedData.partyB.phone);
      let currentTenant: Tenant | null = null;
      // if (resTenant?.status) {
      //   currentTenant = resTenant.data;
      // } else {
      const resCreateTenant = await tenantApi.createTenant(tenantData);
      currentTenant = resCreateTenant.data;
      // }

      // Step 2: Get PDF URLs (reuse if available, otherwise generate and upload)
      const pdfUrls = await getPdfUrls();
      if (pdfUrls.length === 0) {
        throw new Error('Không có URL PDF để lưu hợp đồng');
      }

      // Step 3: Create contract data with PDF URLs
      const contractData = {
        propertyId: propertyId || '',
        tenantId: currentTenant?.id || '',
        contractStartDate: watchedData.partyB.leaseStartDate,
        contractEndDate: watchedData.partyB.leaseEndDate,
        paymentTerms: watchedData.terms.paymentMethod,
        curringRent: watchedData.terms.monthlyRent,
        rentDueDate: watchedData.partyB.leaseStartDate,
        depositAmount: watchedData.terms.deposit,
        pdfContractUrls: pdfUrls,
        content: contractContent || '',
      };

      // Step 4: Save contract
      await RentalContract.createMutationRental.mutateAsync(contractData);

      toast.success('Đã lưu hợp đồng và upload PDF thành công!');
      router.push('/saler/contract'); // Redirect to contract list after saving
    } catch (error) {
      console.error('Error saving contract:', error);
      toast.error('Có lỗi xảy ra khi lưu hợp đồng');
    }
  }, [
    contractContent,
    watchedData,
    propertyId,
    tenantApi,
    getPdfUrls,
    RentalContract.createMutationRental,
  ]);

  const getStepProgress = () => {
    return (currentStep / 6) * 100;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <Step1 data={watchedData.partyA} onNext={handleStep1Next} />;
      case 2:
        return <Step2 data={watchedData.partyB} onNext={handleStep2Next} onBack={handleBackStep} />;
      case 3:
        return <Step3 data={watchedData.terms} onNext={handleStep3Next} onBack={handleBackStep} />;
      case 4:
        return (
          <Step4
            data={watchedData.clausesArticle4to6 || { article4to6: '' }}
            onNext={handleStep4Next}
            onBack={handleBackStep}
          />
        );
      case 5:
        return (
          <Step5
            data={watchedData.clausesarticle7to10 || { article7to10: '' }}
            onNext={handleStep5Next}
            onBack={handleBackStep}
          />
        );
      case 6:
        return (
          <Card className="w-full">
            <div className="p-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-green-600 mb-2">Hoàn tất tạo hợp đồng</h2>
                <p className="text-gray-600">
                  Hợp đồng đã được tạo thành công. Bạn có thể xem lại, chỉnh sửa hoặc thực hiện các
                  thao tác bên dưới.
                </p>
              </div>

              <div className="flex flex-col gap-2 justify-center">
                <div className="flex flex-col gap-2 justify-center">
                  <Button
                    onClick={exportPDFWithPdfMakeStructured}
                    className="flex items-center gap-2 bg-indigo-200 hover:bg-indigo-700"
                    disabled={isLoadingSendContract}
                  >
                    <FileDown className="w-4 h-4" />
                    Xuất PDF
                  </Button>

                  <Button
                    onClick={sendToParties}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                  >
                    <Send className="w-4 h-4" />
                    Gửi đến các bên
                  </Button>

                  <Button
                    onClick={saveContract}
                    disabled={isUploadingPDF}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    {isUploadingPDF ? 'Đang upload PDF...' : 'Lưu hợp đồng'}
                  </Button>

                  {isContractEdited && (
                    <Button
                      onClick={handleResetContract}
                      variant="outline"
                      className="flex items-center gap-2 border-amber-300 text-amber-700 hover:bg-amber-100"
                    >
                      Khôi phục gốc
                    </Button>
                  )}
                </div>

                <div className="flex justify-between mt-6">
                  <Button onClick={handleBackStep} variant="outline" className="px-8 py-2">
                    Quay lại
                  </Button>
                  <Button
                    onClick={() => {
                      setCurrentStep(1);
                      form.reset(initialFormData);
                      setContractContent('');
                      setIsContractEdited(false);
                      setPdfUrls([]);
                      setLastGeneratedContent('');
                      toast.success('Đã tạo hợp đồng mới');
                    }}
                    variant="outline"
                    className="px-8 py-2"
                  >
                    Tạo hợp đồng mới
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Progress Bar */}
      <div className="flex-shrink-0 p-4 lg:p-6 border-b bg-background">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Bước {currentStep} / 6</span>
            <span className="text-sm">{Math.round(getStepProgress())}% hoàn thành</span>
          </div>
          <Progress value={getStepProgress()} className="h-2" />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        <div className="w-full h-full">
          <ResizablePanelGroup direction="horizontal" className="h-full w-full">
            {/* Form Panel */}
            <ResizablePanel minSize={40} className="flex-1">
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-auto scrollbar-hide">
                  <div className="p-4 lg:p-6">
                    <div className="max-w-4xl mx-auto">{renderStepContent()}</div>
                  </div>
                </div>
              </div>
            </ResizablePanel>

            {/* Contract Preview Panel */}
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={50} minSize={30} maxSize={70}>
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-auto scrollbar-hide">
                  <div className="p-4 lg:p-6">
                    <Card className="h-full">
                      <div className="p-4 h-full">
                        <WordLikeEditorAdvanced
                          formData={watchedData}
                          contractContent={contractContent}
                          onContentChange={handleContractContentChange}
                          editable={currentStep === 6}
                          onResetContract={handleResetContract}
                        />
                      </div>
                    </Card>
                  </div>
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </div>
  );
};

export default RentalContractPage;
