import Image from 'next/image';
import React from 'react';

export default function BuyAbilityInfoCard() {
  return (
    <div className="bg-white rounded-2xl shadow border border-gray-200 p-6 flex flex-col items-center w-full max-w-md">
      <div className="w-full flex items-center mb-6">
        <Image
          src="/zillow-logo.svg"
          alt="Zillow Home Loans"
          width={96}
          height={24}
          className="h-6 mr-2"
        />
        <img src="/zillow-logo.svg" alt="Zillow Home Loans" className="h-6 mr-2" />
        <span className="font-semibold text-gray-800 text-lg">
          Zillow Home Loans<sup className="ml-1 text-xs">SM</sup>
        </span>
      </div>
      <div className="grid grid-cols-3 gap-y-4 gap-x-6 w-full mb-6">
        <div className="text-2xl font-bold text-gray-900">$--</div>
        <div className="text-2xl font-bold text-gray-900">$--</div>
        <div className="text-2xl font-bold text-gray-900">$--</div>
        <div className="text-xs text-gray-500">Suggested target price</div>
        <div className="text-xs text-gray-500">
          BuyAbility<sup>SM</sup>
        </div>
        <div className="text-xs text-gray-500">Mo. payment</div>
        <div className="text-2xl font-bold text-gray-900 col-span-1">--%</div>
        <div className="text-2xl font-bold text-gray-900 col-span-1">--%</div>
        <div className="col-span-1"></div>
        <div className="text-xs text-gray-500 col-span-1">Today&apos;s rate</div>
        <div className="text-xs text-gray-500 col-span-1">APR</div>
        <div className="col-span-1"></div>
      </div>
      <button className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold rounded-xl py-3 text-lg mt-2 transition">
        Bắt đầu ngay
      </button>
    </div>
  );
}
