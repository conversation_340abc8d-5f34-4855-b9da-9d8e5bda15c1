'use client';

import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Star, Phone } from 'lucide-react';

interface SellerData {
  id: string;
  fullName: string;
  avatar: string;
  coverImage: string;
  title: string;
  company: string;
  rating: number;
  reviewCount: number;
  experience: string;
  phoneNumber: string;
}

interface SellerProfileHeaderProps {
  seller: SellerData;
}

function SellerProfileHeader({ seller }: SellerProfileHeaderProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
      <div className="relative bg-white rounded-2xl overflow-hidden">
        {/* Cover Image */}
        <div className="relative h-56 md:h-96 rounded-2xl w-full overflow-hidden">
          <Image
            src={
              seller.coverImage &&
              seller.coverImage !== '/images/default-cover.jpg' &&
              seller.coverImage !== 'default-cover.jpg'
                ? seller.coverImage
                : '/bg_hero.jpg'
            }
            alt="Cover"
            fill
            className="object-cover"
            priority
            onError={e => {
              const target = e.target as HTMLImageElement;
              if (target.src !== '/bg_hero.jpg') {
                target.src = '/bg_hero.jpg';
              }
            }}
          />
          {/* Subtle overlay */}
          {/* <div className="absolute inset-0 bg-gradient-to-b from-black/10 to-black/30" /> */}
        </div>

        {/* Profile Content */}
        <div className="relative px-4 sm:px-6 lg:px-8 pt-4">
          {/* Content area with avatar and info side by side */}
          <div className="flex flex-col md:flex-row md:items-start md:space-x-6">
            {/* Avatar - larger size with partial overlap */}
            <div className="relative flex-shrink-0 -mt-16 mb-1">
              <Avatar className="size-32 sm:size-36 md:size-48 lg:size-52 border-4 border-white">
                <AvatarImage
                  src={
                    seller.avatar &&
                    seller.avatar !== '/images/default-avatar.jpg' &&
                    seller.avatar !== 'default-avatar.jpg'
                      ? seller.avatar
                      : 'https://images.unsplash.com/photo-1585495898471-0fa227b7f193?q=80&w=1752&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
                  }
                  alt={seller.fullName}
                  className="object-cover"
                />
                <AvatarFallback className="bg-primary text-white text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold">
                  {getInitials(seller.fullName)}
                </AvatarFallback>
              </Avatar>
              {/* Verified badge */}
              {/* <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-2">
                <Award className="w-6 h-6 text-white" />
              </div> */}
            </div>

            {/* Right side - All Profile Info */}
            <div className="flex-1 flex flex-col md:flex-row md:justify-between md:items-start md:h-48">
              {/* Profile Info - aligned with avatar height */}
              <div className="flex-1 md:pr-8 flex flex-col justify-center">
                <h1 className="text-xl sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                  {seller.fullName}
                </h1>
                <p className="text-gray-600 text-xs md:text-sm mb-3">{seller.title}</p>

                {/* Rating */}
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < Math.floor(seller.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
                      />
                    ))}
                  </div>
                  <span className="text-xs md:text-sm text-gray-600">
                    ({seller.reviewCount} Đánh giá)
                  </span>
                </div>

                {/* Contact Information - on same row */}
                <div className="flex flex-col md:flex-row md:space-x-6 space-y-2 md:space-y-0 text-xs md:text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Phone className="size-3 md:size-4" />
                    <span>Điện thoại: {seller.phoneNumber}</span>
                  </div>
                  {/* <div className="flex items-center bg-gray-100 rounded-full p-2 space-x-2">
                    <Building2 className="w-4 h-4" />
                    <span>{seller.company}</span>
                  </div> */}
                </div>
              </div>

              {/* Action buttons - aligned to center of profile info */}
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 mt-6 md:mt-0 md:min-w-[280px] md:self-center">
                <Button variant="outline" className="flex-1 px-6 py-2">
                  Nhắn tin
                </Button>
                <Button className="flex-1 bg-black hover:bg-gray-800 text-white px-6 py-2">
                  Liên hệ
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SellerProfileHeader;
