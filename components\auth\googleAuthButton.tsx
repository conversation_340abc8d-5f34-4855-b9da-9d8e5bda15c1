import { useEffect, useCallback, useState, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { GoogleCredentialResponse } from '@/lib/api/services/fetchAuth';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface GoogleAuthButtonProps {
  mode: 'login' | 'register';
  onError: (error: string) => void;
  className?: string;
}

export function GoogleAuthButton({ mode, onError }: GoogleAuthButtonProps) {
  const { googleLogin, isLoading } = useAuth();
  const [isGoogleReady, setIsGoogleReady] = useState(false);
  const hiddenButtonRef = useRef<HTMLDivElement>(null);

  const handleGoogleAuth = useCallback(
    async (credentialResponse: GoogleCredentialResponse) => {
      const idToken = credentialResponse.credential;
      if (idToken) {
        googleLogin(idToken);
      } else {
        onError('Không nhận được token từ Google');
      }
    },
    [googleLogin, onError]
  );

  const initializeGoogle = useCallback(() => {
    interface GoogleWindow extends Window {
      google?: {
        accounts: {
          id: {
            initialize: (config: {
              client_id: string;
              callback: (response: GoogleCredentialResponse) => void;
              auto_select?: boolean;
              cancel_on_tap_outside?: boolean;
              context?: string;
            }) => void;
            renderButton: (
              element: HTMLElement | null,
              options: {
                type?: 'standard' | 'icon';
                theme?: 'outline' | 'filled_blue' | 'filled_black';
                size?: 'large' | 'medium' | 'small';
                text?: string;
                shape?: 'rectangular' | 'pill' | 'circle' | 'square';
                logo_alignment?: 'left' | 'center';
                width?: number;
                locale?: string;
              }
            ) => void;
          };
        };
      };
    }

    const googleWindow = window as unknown as GoogleWindow;
    if (googleWindow.google) {
      googleWindow.google.accounts.id.initialize({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
        callback: handleGoogleAuth,
        auto_select: false,
        cancel_on_tap_outside: true,
        context: 'signin',
      });

      // Render the hidden Google button
      if (hiddenButtonRef.current) {
        googleWindow.google.accounts.id.renderButton(hiddenButtonRef.current, {
          type: 'standard',
          theme: 'outline',
          size: 'large',
          text: mode === 'login' ? 'signin_with' : 'signup_with',
          shape: 'rectangular',
          logo_alignment: 'left',
          width: 200,
          locale: 'en',
        });
      }

      setIsGoogleReady(true);
    }
  }, [handleGoogleAuth, mode]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Remove any existing Google Sign-In script to prevent duplicates
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      );
      if (existingScript) {
        document.head.removeChild(existingScript);
      }

      // Add new script
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = initializeGoogle;
      document.head.appendChild(script);

      return () => {
        // Cleanup script when component unmounts
        const scriptToRemove = document.querySelector(
          'script[src="https://accounts.google.com/gsi/client"]'
        );
        if (scriptToRemove) {
          document.head.removeChild(scriptToRemove);
        }
      };
    }
  }, [initializeGoogle]);

  const handleClick = useCallback(() => {
    if (!isGoogleReady) {
      onError('Google SDK chưa sẵn sàng');
      return;
    }

    // Trigger the hidden Google button
    const googleButton = hiddenButtonRef.current?.querySelector(
      'div[role="button"]'
    ) as HTMLElement;
    if (googleButton) {
      googleButton.click();
    }
  }, [isGoogleReady, onError]);

  return (
    <>
      {/* Hidden Google button */}
      <div
        ref={hiddenButtonRef}
        style={{
          position: 'absolute',
          // left: '-9999px',
          visibility: 'hidden',
          pointerEvents: 'none',
        }}
      />

      {/* Custom styled button */}
      <Button
        type="button"
        variant="outline"
        className={`w-full`}
        onClick={handleClick}
        disabled={!isGoogleReady || isLoading}
      >
        {isLoading ? (
          <Loader2 className="size-5 animate-spin" />
        ) : (
          <img
            src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg"
            alt="Google Icon"
            className="size-5"
          />
        )}
        <span className="flex flex-1 justify-center">
          {isLoading
            ? 'Đang xử lý...'
            : mode === 'login'
              ? 'Đăng nhập với Google'
              : 'Đăng ký với Google'}
        </span>
      </Button>
    </>
  );
}
