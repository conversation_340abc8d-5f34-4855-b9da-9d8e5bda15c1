import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fetchTenant,
  TenantCreateData,
  TenantCreateResponse,
} from '@/lib/api/services/fetchTenant';

export function useTenant() {
  const queryClient = useQueryClient();

  // Query: L<PERSON>y tất cả tenant
  const getAllQuery = useQuery<TenantCreateResponse[], Error>({
    queryKey: ['tenants'],
    queryFn: fetchTenant.getAllTenant,
  });

  // Query: Lấy tenant theo id
  function useGetById(id: string) {
    return useQuery<TenantCreateResponse, Error>({
      queryKey: ['tenant', id],
      queryFn: () => fetchTenant.getTenantById(id),
      enabled: !!id,
    });
  }

  // Query: Lấy tenant theo phone
  // function useGetByPhone(phone: string) {
  //   return useQuery<TenantCreateResponse, Error>({
  //     queryKey: ['tenantByPhone', phone],
  //     queryFn: async () => {
  //       const res = await fetchTenant.getTenantByPhone(phone);
  //       if (!res) throw new Error('Không tìm thấy tenant');
  //       return res;
  //     },
  //     enabled: !!phone,
  //   });
  // }

  // Search tenants by phone
  // async function searchTenantsByPhone(phone: string): Promise<TenantCreateResponse | null> {
  //   if (!phone) return null;
  //   try {
  //     const response = await fetchTenant.getTenantByPhone(phone);
  //     if (!response) return null;
  //     return response;
  //   } catch (error) {
  //     console.error('Error searching tenants:', error);
  //     return null;
  //   }
  // }

  // Mutation: Tạo tenant
  const createMutation = useMutation<TenantCreateResponse, Error, TenantCreateData>({
    mutationFn: fetchTenant.createTenant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tenants'] });
    },
  });

  // Mutation: Update tenant
  const updateMutation = useMutation<
    TenantCreateResponse,
    Error,
    { id: string; data: TenantCreateData }
  >({
    mutationFn: ({ id, data }) => fetchTenant.updateTenantById(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tenants'] });
    },
  });

  // Hàm tiện dụng: tạo tenant, trả về data/error/loading
  async function createTenant(data: TenantCreateData) {
    const result = await createMutation.mutateAsync(data);
    return result;
  }

  return {
    getAllQuery,
    useGetById,
    // useGetByPhone,
    // searchTenantsByPhone,
    createMutation,
    updateMutation,
    createTenant, // tiện dụng cho form
  };
}
