import { Metadata } from 'next';
import { collectionService } from '@/lib/api/services/fetchCollection';
import CollectionDetailClient from './component/CollectionDetailClient';
import Footer from '@/components/Footer';

export interface CollectionDetailPageProps {
  params: { id: string };
}

export async function generateMetadata({ params }: CollectionDetailPageProps): Promise<Metadata> {
  try {
    const response = await collectionService.getCollectionById(params.id);
    const collection = response.data;
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://revoland.vn';

    if (!collection) {
      return {
        title: '<PERSON>ộ sưu tập không tồn tại - RevoLand',
        description: 'Không tìm thấy bộ sưu tập này',
        robots: {
          index: false,
          follow: false,
        },
      };
    }

    // Construct the collection URL
    const collectionUrl = `${baseUrl}/myrevo/collections/${collection.id}`;

    // Create SEO-friendly title and description
    const title = `${collection.name} - <PERSON><PERSON> sưu tập bất động sản | RevoLand`;
    const description = collection.description
      ? `${collection.description.substring(0, 160)}${collection.description.length > 160 ? '...' : ''} - Bộ sưu tập ${collection.itemCount} bất động sản chất lượng.`
      : `Khám phá bộ sưu tập ${collection.name} với ${collection.itemCount} bất động sản chất lượng được tuyển chọn.`;

    // Get the first image for Open Graph
    const mainImage = collection.collectionImage?.[0] || collection.thumbnailUrl || '/hero.jpg';

    return {
      title,
      description,
      openGraph: {
        type: 'website',
        title,
        description,
        images: [
          {
            url: mainImage,
            width: 1200,
            height: 630,
            alt: collection.name,
          },
        ],
        siteName: 'RevoLand',
        locale: 'vi_VN',
        url: collectionUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [mainImage],
        site: '@RevoLand',
      },
      alternates: {
        canonical: collectionUrl,
      },
      keywords: [
        collection.name,
        'bộ sưu tập bất động sản',
        'tuyển chọn bất động sản',
        'danh sách bất động sản',
        'RevoLand',
        'mua bán nhà đất',
        'cho thuê bất động sản',
        'bất động sản cao cấp',
      ],
      creator: 'RevoLand',
      publisher: 'RevoLand',
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'collection:name': collection.name,
        'collection:item_count': collection.itemCount.toString(),
        'collection:created_date': collection.createdAt,
        'collection:updated_date': collection.updatedAt,
      },
    };
  } catch (error) {
    console.error('Error generating collection metadata:', error);
    return {
      title: 'Bộ sưu tập bất động sản - RevoLand',
      description: 'Khám phá các bộ sưu tập bất động sản chất lượng tại RevoLand',
    };
  }
}

export default async function CollectionDetailPage({ params }: CollectionDetailPageProps) {
  return (
    <>
      <CollectionDetailClient collectionId={params.id} />
      <Footer />
    </>
  );
}
