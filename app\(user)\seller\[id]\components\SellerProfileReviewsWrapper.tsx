'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Star } from 'lucide-react';
import AgentReviewList from '../../../properties/[id]/component/AgentReviewList';

interface SellerProfileReviewsWrapperProps {
  sellerId: string;
}

/**
 * Wrapper component that uses the existing AgentReviewList component
 * for displaying seller reviews in a Card layout
 */
export default function SellerProfileReviewsWrapper({
  sellerId,
}: SellerProfileReviewsWrapperProps) {
  return (
    <Card id="feedbacks" className="mb-4 md:mb-8 w-full">
      <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
        <CardTitle className="flex items-center gap-2 text-base md:text-xl">
          <Star className="size-4 md:size-5" />
          <span><PERSON><PERSON>h gi<PERSON> của khách hàng</span>
        </CardTitle>
        <CardDescription className="text-xs md:text-base">
          Xem đánh giá và phản hồi từ khách hàng về chuyên viên bất động sản
        </CardDescription>
      </CardHeader>
      <CardContent className="max-md:px-4 max-md:pb-3 space-y-4">
        <AgentReviewList agentId={sellerId} hideHeader={true} />
      </CardContent>
    </Card>
  );
}
