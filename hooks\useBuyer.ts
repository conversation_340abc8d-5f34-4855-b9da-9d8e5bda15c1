import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fetchBuyer,
  BuyerCreateData,
  BuyerCreateResponse,
  RequestGetBuyerByPhone,
} from '@/lib/api/services/fetchBuyer';

export function useBuyer() {
  const queryClient = useQueryClient();

  // Query: L<PERSON>y tất cả buyer
  const getAllQuery = useQuery<BuyerCreateResponse[], Error>({
    queryKey: ['buyers'],
    queryFn: fetchBuyer.getAllBuyers,
  });

  // Query: Lấy buyer theo id
  function useGetById(id: string) {
    return useQuery<BuyerCreateResponse, Error>({
      queryKey: ['buyer', id],
      queryFn: () => fetchBuyer.getBuyerById(id),
      enabled: !!id,
    });
  }

  // Query: Lấy buyer theo phone
  function useGetByPhone(data: RequestGetBuyerByPhone) {
    return useQuery<BuyerCreateResponse, Error>({
      queryKey: ['buyerByPhone', data.phone, data.email],
      queryFn: async () => {
        const res = await fetchBuyer.getBuyerByPhone(data);
        if (!res) throw new Error('Không tìm thấy buyer');
        return res;
      },
      enabled: !!data.phone,
    });
  }

  // Mutation: Tạo buyer
  const createMutation = useMutation<BuyerCreateResponse, Error, BuyerCreateData>({
    mutationFn: fetchBuyer.createBuyer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buyers'] });
    },
  });

  // Mutation: Update buyer
  const updateMutation = useMutation<
    BuyerCreateResponse,
    Error,
    { id: string; data: BuyerCreateData }
  >({
    mutationFn: ({ id, data }) => fetchBuyer.updateBuyerById(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buyers'] });
    },
  });

  // Hàm tiện dụng: tạo buyer, trả về data/error/loading
  async function createBuyer(data: BuyerCreateData) {
    const result = await createMutation.mutateAsync(data);
    return result;
  }

  return {
    getAllQuery,
    useGetById,
    useGetByPhone,
    createMutation,
    updateMutation,
    createBuyer, // tiện dụng cho form
  };
}
