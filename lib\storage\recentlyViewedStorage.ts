import { Property } from '@/lib/api/services/fetchProperty';
import { RECENTLY_VIEWED_CONFIG } from '@/lib/config/recentlyViewed';

export interface RecentlyViewedStorageItem {
  id: string;
  propertyId: string;
  viewedAt: string;
  property?: Property;
}

/**
 * localStorage implementation for recently viewed properties
 * Handles CRUD operations with deduplication and validation
 */
export class RecentlyViewedStorage {
  private readonly STORAGE_KEY = RECENTLY_VIEWED_CONFIG.localStorageKey;

  /**
   * Retrieve all items from localStorage
   * Validates data structure and removes duplicates
   */
  getItems(): RecentlyViewedStorageItem[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];

      const parsed: RecentlyViewedStorageItem[] = JSON.parse(stored);

      // Validate and filter invalid items
      const validItems = parsed.filter(this.isValidStorageItem);

      // Remove duplicates by propertyId
      const uniqueItems = this.removeDuplicates(validItems);

      console.log(`📂 Storage loaded: ${uniqueItems.length} unique items`);
      return uniqueItems;
    } catch (error) {
      console.error('Failed to read from localStorage:', error);
      this.clearAll();
      return [];
    }
  }

  /**
   * Add property to recently viewed list
   * If exists, moves to top with updated timestamp
   */
  addItem(property: Property): void {
    if (!this.isValidProperty(property)) {
      throw new Error('Invalid property: missing required fields');
    }

    try {
      const currentItems = this.getItems();
      const existingIndex = currentItems.findIndex(item => item.propertyId === property.id);

      if (existingIndex !== -1) {
        console.log(`🔄 Property ${property.id} already exists, moving to top`);
        this.moveToTop(currentItems, existingIndex);
      } else {
        console.log(`✅ Adding new property: ${property.id}`);
        this.addNewItem(currentItems, property);
      }
    } catch (error) {
      console.error('Failed to add item to localStorage:', error);
      throw new Error('Unable to save to localStorage');
    }
  }

  /**
   * Remove property from recently viewed list
   */
  removeItem(propertyId: string): void {
    if (!propertyId?.trim()) {
      throw new Error('Invalid property ID: cannot be empty');
    }

    try {
      const currentItems = this.getItems();
      const filteredItems = currentItems.filter(item => item.propertyId !== propertyId);

      this.saveItems(filteredItems);
      console.log(`✅ Property ${propertyId} removed`);
    } catch (error) {
      console.error('Failed to remove item from localStorage:', error);
      throw new Error('Unable to remove from localStorage');
    }
  }

  /**
   * Clear all recently viewed items
   */
  clearAll(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('✅ All recently viewed items cleared');
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
      throw new Error('Unable to clear localStorage');
    }
  }

  // Validate storage item structure
  private isValidStorageItem = (item: any): item is RecentlyViewedStorageItem => {
    return (
      item &&
      typeof item.id === 'string' &&
      typeof item.propertyId === 'string' &&
      typeof item.viewedAt === 'string' &&
      (!item.property || typeof item.property === 'object')
    );
  };

  // Validate property has required fields
  private isValidProperty = (property: any): property is Property => {
    return property && typeof property.id === 'string' && property.id.trim().length > 0;
  };

  // Remove duplicates by propertyId
  private removeDuplicates = (items: RecentlyViewedStorageItem[]): RecentlyViewedStorageItem[] => {
    const seen = new Set<string>();
    return items.filter(item => {
      if (seen.has(item.propertyId)) {
        return false;
      }
      seen.add(item.propertyId);
      return true;
    });
  };

  // Move existing item to top with updated timestamp
  private moveToTop = (items: RecentlyViewedStorageItem[], existingIndex: number): void => {
    const existingItem = items[existingIndex];
    const filteredItems = items.filter(item => item.propertyId !== existingItem.propertyId);

    existingItem.viewedAt = new Date().toISOString();
    const updatedItems = [existingItem, ...filteredItems];

    this.saveItems(updatedItems);
    console.log(`✅ Property ${existingItem.propertyId} moved to top`);
  };

  // Add new item to the top
  private addNewItem = (currentItems: RecentlyViewedStorageItem[], property: Property): void => {
    const newItem: RecentlyViewedStorageItem = {
      id: `${property.id}-${Date.now()}`,
      propertyId: property.id,
      viewedAt: new Date().toISOString(),
      property,
    };

    const updatedItems = [newItem, ...currentItems];
    this.saveItems(updatedItems);
    console.log(`✅ Property ${property.id} added as new item`);
  };

  // Save items to localStorage
  private saveItems = (items: RecentlyViewedStorageItem[]): void => {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Failed to save items to localStorage:', error);
      throw new Error('Storage quota exceeded or localStorage unavailable');
    }
  };
}
