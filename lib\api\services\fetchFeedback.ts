import apiService from '../core';

export interface AgentFeedbackRequest {
  agentId: string;
  userId: string;
  rating: number;
  content: string;
}

export interface AgentFeedbackResponse {
  id: string;
  agentId: string;
  userId: string;
  username: string;
  fullname: string;
  avatar: string;
  rating: number;
  content: string;
  createdAt: string;
}

export interface FeedbackListResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    data: AgentFeedbackResponse[];
    totalCount: number;
    totalPages: number;
    pageSize: number;
    currentPage: number;
  };
}

export interface FeedbackStatsResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    totalFeedback: number;
    averageRating: number;
  };
}

export interface FeedbackActionResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface FeedbackListParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isDescending?: boolean;
}

export const feedbackService = {
  // Thêm feedback
  addFeedback: async (payload: AgentFeedbackRequest): Promise<FeedbackActionResponse> => {
    const res = await apiService.post<FeedbackActionResponse, AgentFeedbackRequest>(
      '/api/AgentFeedback/add-feedback',
      payload
    );
    return res.data;
  },

  // Cập nhật feedback
  updateFeedback: async (
    id: string,
    payload: { rating: number; content: string }
  ): Promise<FeedbackActionResponse> => {
    const res = await apiService.put<FeedbackActionResponse>(
      `/api/AgentFeedback/update-feedback/${id}`,
      payload
    );
    return res.data;
  },

  // Lấy danh sách feedback theo agentId
  getFeedbackByAgentId: async (
    agentId: string,
    params: FeedbackListParams
  ): Promise<FeedbackListResponse> => {
    const query = new URLSearchParams(params as Record<string, any>).toString();
    const res = await apiService.get<FeedbackListResponse>(
      `/api/AgentFeedback/get-feedback-by-AgentId/${agentId}?${query}`
    );
    return res.data;
  },

  // ✅ Lấy feedback chi tiết theo ID và agentId
  getFeedbackById: async (id: string, agentId: string): Promise<AgentFeedbackResponse> => {
    const res = await apiService.get<AgentFeedbackResponse>(
      `/api/AgentFeedback/${id}?agentId=${agentId}`
    );
    return res.data;
  },

  // Lấy thống kê feedback
  getFeedbackStats: async (agentId: string): Promise<FeedbackStatsResponse> => {
    const res = await apiService.get<FeedbackStatsResponse>(
      `/api/AgentFeedback/get-total-feedback-and-average-score/${agentId}`
    );
    return res.data;
  },

  // Xoá feedback của user hiện tại theo agentId
  deleteFeedback: async (agentId: string): Promise<FeedbackActionResponse> => {
    const res = await apiService.delete<FeedbackActionResponse>(
      `/api/AgentFeedback/delete-feedback/${agentId}`
    );
    return res.data;
  },
};

export default feedbackService;
