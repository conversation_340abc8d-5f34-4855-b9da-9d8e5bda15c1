'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Bookmark, BookmarkCheck } from 'lucide-react';
import { useAuth } from '@/lib/providers/authProvider';
import { useSaveSearch } from '@/hooks/useSavedSearches';
import { SaveSearchParams } from '@/lib/api/services/fetchSavedSearches';
import { toast } from 'sonner';

interface SaveSearchButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  onSave?: () => void;
  isSearching?: boolean;
}

export default function SaveSearchButton({
  className,
  variant = 'outline',
  size = 'default',
  onSave,
  isSearching = false,
}: SaveSearchButtonProps) {
  const { isAuthenticated } = useAuth();
  const searchParams = useSearchParams();
  const [isOpen, setIsOpen] = useState(false);
  const [searchName, setSearchName] = useState('');
  const saveSearchMutation = useSaveSearch();

  // Convert URL search params to SaveSearchParams
  const convertUrlParamsToSaveParams = (): SaveSearchParams => {
    const params: SaveSearchParams = {};

    // Basic search parameters
    const searchTerm = searchParams.get('searchTerm');
    if (searchTerm) params.searchTerm = searchTerm;

    const isDescending = searchParams.get('isDescending');
    if (isDescending) params.isDescending = isDescending === 'true';

    const sortBy = searchParams.get('sortBy');
    if (sortBy) params.sortBy = sortBy;

    // Location parameters
    const swLatitude = searchParams.get('swLatitude');
    if (swLatitude) params.swLatitude = parseFloat(swLatitude);

    const swLongitude = searchParams.get('swLongitude');
    if (swLongitude) params.swLongitude = parseFloat(swLongitude);

    const neLatitude = searchParams.get('neLatitude');
    if (neLatitude) params.neLatitude = parseFloat(neLatitude);

    const neLongitude = searchParams.get('neLongitude');
    if (neLongitude) params.neLongitude = parseFloat(neLongitude);

    // Array parameters - handle both comma-separated and multiple values
    const status = searchParams.getAll('status');
    if (status.length) params.status = status;

    // Transaction type - single value from buildFilterQuery
    const transactionType = searchParams.get('transactionType');
    if (transactionType) params.transactionType = [transactionType];

    // Property type - comma-separated from buildFilterQuery
    const propertyType = searchParams.get('type') || searchParams.get('propertyType');
    if (propertyType) params.type = propertyType.split(',');

    // Property detail filters - comma-separated from buildFilterQuery
    const propertyDetailFilters = searchParams.get('propertyDetailFilters');
    if (propertyDetailFilters) params.propertyDetailFilters = propertyDetailFilters.split(',');

    // Amenity filters - comma-separated from buildFilterQuery
    const amenityFilters = searchParams.get('amenityFilters');
    if (amenityFilters) params.amenityFilters = amenityFilters.split(',');

    const apartmentOrientation = searchParams.getAll('apartmentOrientation');
    if (apartmentOrientation.length) params.apartmentOrientation = apartmentOrientation;

    // Location parameters are handled via map bounds (swLatitude, etc.)

    // Numeric parameters - map from buildFilterQuery format
    const bedCount = searchParams.get('bedCount');
    if (bedCount && bedCount !== 'any') {
      params.bedrooms = parseInt(bedCount);
    }

    const exactBedMatch = searchParams.get('exactBedMatch');
    if (exactBedMatch === 'true') {
      // This could be used for exact matching logic
    }

    const bathCount = searchParams.get('bathCount');
    if (bathCount && bathCount !== 'any') {
      params.bathrooms = parseInt(bathCount);
    }

    const livingRoomCount = searchParams.get('livingRoomCount');
    if (livingRoomCount && livingRoomCount !== 'any') {
      params.livingRooms = parseInt(livingRoomCount);
    }

    const kitchenCount = searchParams.get('kitchenCount');
    if (kitchenCount && kitchenCount !== 'any') {
      params.kitchens = parseInt(kitchenCount);
    }

    // Legacy numeric parameters (keep for backward compatibility)
    const bedrooms = searchParams.get('bedrooms');
    if (bedrooms) params.bedrooms = parseInt(bedrooms);

    const minBedrooms = searchParams.get('minBedrooms');
    if (minBedrooms) params.minBedrooms = parseInt(minBedrooms);

    const maxBedrooms = searchParams.get('maxBedrooms');
    if (maxBedrooms) params.maxBedrooms = parseInt(maxBedrooms);

    const bathrooms = searchParams.get('bathrooms');
    if (bathrooms) params.bathrooms = parseInt(bathrooms);

    const minBathrooms = searchParams.get('minBathrooms');
    if (minBathrooms) params.minBathrooms = parseInt(minBathrooms);

    const maxBathrooms = searchParams.get('maxBathrooms');
    if (maxBathrooms) params.maxBathrooms = parseInt(maxBathrooms);

    const livingRooms = searchParams.get('livingRooms');
    if (livingRooms) params.livingRooms = parseInt(livingRooms);

    const minLivingRooms = searchParams.get('minLivingRooms');
    if (minLivingRooms) params.minLivingRooms = parseInt(minLivingRooms);

    const maxLivingRooms = searchParams.get('maxLivingRooms');
    if (maxLivingRooms) params.maxLivingRooms = parseInt(maxLivingRooms);

    const kitchens = searchParams.get('kitchens');
    if (kitchens) params.kitchens = parseInt(kitchens);

    const minKitchens = searchParams.get('minKitchens');
    if (minKitchens) params.minKitchens = parseInt(minKitchens);

    const maxKitchens = searchParams.get('maxKitchens');
    if (maxKitchens) params.maxKitchens = parseInt(maxKitchens);

    // Area parameters
    const landArea = searchParams.get('landArea');
    if (landArea) params.landArea = parseFloat(landArea);

    const landWidth = searchParams.get('landWidth');
    if (landWidth) params.landWidth = parseFloat(landWidth);

    const landLength = searchParams.get('landLength');
    if (landLength) params.landLength = parseFloat(landLength);

    const buildingArea = searchParams.get('buildingArea');
    if (buildingArea) params.buildingArea = parseFloat(buildingArea);

    // Floor parameters
    const numberOfFloors = searchParams.get('numberOfFloors');
    if (numberOfFloors) params.numberOfFloors = parseInt(numberOfFloors);

    const floorNumber = searchParams.get('floorNumber');
    if (floorNumber) params.floorNumber = parseInt(floorNumber);

    // Area parameters
    const minArea = searchParams.get('minArea');
    if (minArea) params.landArea = parseFloat(minArea);

    // Price parameters
    const minPrice = searchParams.get('minPrice');
    if (minPrice) params.minPrice = parseFloat(minPrice);

    const maxPrice = searchParams.get('maxPrice');
    if (maxPrice) params.maxPrice = parseFloat(maxPrice);

    return params;
  };

  const handleSaveSearch = async () => {
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để lưu tìm kiếm');
      return;
    }

    const searchParams = convertUrlParamsToSaveParams();

    // Add search term if provided by user
    if (searchName.trim()) {
      searchParams.searchTerm = searchName.trim();
    }

    try {
      // Apply filters first if onSave callback is provided
      if (onSave) {
        onSave();
        // Wait a bit for URL to update
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      await saveSearchMutation.mutateAsync(searchParams);
      setIsOpen(false);
      setSearchName('');
    } catch (error) {
      console.error('Error saving search:', error);
    }
  };

  const hasSearchParams = () => {
    return Array.from(searchParams.entries()).length > 0;
  };

  if (!isAuthenticated) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => toast.error('Vui lòng đăng nhập để lưu tìm kiếm')}
        disabled={isSearching}
      >
        {isSearching ? (
          <>
            <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Đang tìm...
          </>
        ) : (
          <>
            <Bookmark className="w-4 h-4 mr-2" />
            Lưu
          </>
        )}
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={!hasSearchParams() || isSearching}
        >
          {isSearching ? (
            <>
              <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Đang tìm...
            </>
          ) : (
            <>
              <Bookmark className="w-4 h-4 mr-2" />
              Lưu
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookmarkCheck className="w-5 h-5" />
            Lưu tìm kiếm
          </DialogTitle>
          <DialogDescription>
            Lưu bộ lọc tìm kiếm hiện tại để sử dụng lại sau này.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="search-name">Tên tìm kiếm (tùy chọn)</Label>
            <Input
              id="search-name"
              placeholder="VD: Căn hộ 2PN quận 1..."
              value={searchName}
              onChange={e => setSearchName(e.target.value)}
              maxLength={100}
            />
            <p className="text-xs text-muted-foreground">
              Nếu không nhập tên, hệ thống sẽ tự động tạo tên dựa trên bộ lọc
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
            Hủy
          </Button>
          <Button type="button" onClick={handleSaveSearch} disabled={saveSearchMutation.isPending}>
            {saveSearchMutation.isPending ? 'Đang lưu...' : 'Lưu tìm kiếm'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
