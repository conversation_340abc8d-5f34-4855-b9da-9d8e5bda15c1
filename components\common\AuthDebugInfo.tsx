'use client';

import { useEffect, useState } from 'react';
import { getCookie } from 'cookies-next';
import { useAuthStore } from '@/lib/store/authStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';

interface AuthDebugData {
  cookieToken: string | null;
  storeToken: string | null;
  storeIsAuthenticated: boolean;
  environment: string;
  protocol: string;
  domain: string;
  userAgent: string;
}

export function AuthDebugInfo() {
  const [debugData, setDebugData] = useState<AuthDebugData | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const { token, isAuthenticated } = useAuthStore();
  const { logout, isLoading } = useAuth();

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setDebugData({
        cookieToken: getCookie('auth-token') as string | null,
        storeToken: token,
        storeIsAuthenticated: isAuthenticated,
        environment: process.env.NODE_ENV || 'unknown',
        protocol: window.location.protocol,
        domain: window.location.hostname,
        userAgent: navigator.userAgent.substring(0, 100),
      });
    }
  }, [token, isAuthenticated]);

  // Only show in development or when manually enabled
  useEffect(() => {
    const showDebug =
      process.env.NODE_ENV === 'development' || localStorage.getItem('auth-debug') === 'true';
    setIsVisible(showDebug);
  }, []);

  if (!isVisible || !debugData) return null;

  const tokenMismatch = !!debugData.cookieToken !== !!debugData.storeToken;
  const authMismatch = !!debugData.cookieToken !== debugData.storeIsAuthenticated;

  const handleTestLogout = () => {
    console.log('[AuthDebug] Testing logout functionality...');
    logout();
  };

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 border-2 border-blue-500">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          Auth Debug Info
          <button
            onClick={() => {
              localStorage.removeItem('auth-debug');
              setIsVisible(false);
            }}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 text-xs">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <strong>Cookie Token:</strong>
            <Badge variant={debugData.cookieToken ? 'default' : 'destructive'} className="ml-1">
              {debugData.cookieToken ? 'Present' : 'Missing'}
            </Badge>
          </div>
          <div>
            <strong>Store Token:</strong>
            <Badge variant={debugData.storeToken ? 'default' : 'destructive'} className="ml-1">
              {debugData.storeToken ? 'Present' : 'Missing'}
            </Badge>
          </div>
          <div>
            <strong>Is Authenticated:</strong>
            <Badge
              variant={debugData.storeIsAuthenticated ? 'default' : 'destructive'}
              className="ml-1"
            >
              {debugData.storeIsAuthenticated ? 'Yes' : 'No'}
            </Badge>
          </div>
          <div>
            <strong>Environment:</strong>
            <Badge variant="outline" className="ml-1">
              {debugData.environment}
            </Badge>
          </div>
        </div>

        <div className="pt-2 border-t">
          <div>
            <strong>Protocol:</strong> {debugData.protocol}
          </div>
          <div>
            <strong>Domain:</strong> {debugData.domain}
          </div>
        </div>

        {tokenMismatch && (
          <div className="p-2 bg-red-100 border border-red-300 rounded text-red-700">
            ⚠️ Token mismatch detected!
          </div>
        )}

        {authMismatch && (
          <div className="p-2 bg-yellow-100 border border-yellow-300 rounded text-yellow-700">
            ⚠️ Auth state mismatch detected!
          </div>
        )}

        {debugData.storeIsAuthenticated && (
          <div className="pt-2 border-t">
            <Button
              size="sm"
              variant="destructive"
              onClick={handleTestLogout}
              disabled={isLoading}
              className="w-full text-xs"
            >
              {isLoading ? 'Logging out...' : 'Test Logout'}
            </Button>
          </div>
        )}

        <div className="pt-2 text-[10px] text-gray-500">
          To enable in production: localStorage.setItem(&apos;auth-debug&apos;, &apos;true&apos;)
        </div>
      </CardContent>
    </Card>
  );
}
