import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import feedbackService, {
  AgentFeedbackRequest,
  FeedbackActionResponse,
  FeedbackStatsResponse,
  FeedbackListParams,
  AgentFeedbackResponse,
} from '@/lib/api/services/fetchFeedback';
import { toast } from 'sonner';

/**
 * <PERSON><PERSON><PERSON> danh sách feedback theo agentId
 */
export const useFeedbackList = (agentId: string, params: FeedbackListParams) => {
  return useQuery({
    queryKey: ['feedback', 'list', agentId, params],
    queryFn: async () => {
      const res = await feedbackService.getFeedbackByAgentId(agentId, params);
      return res.data;
    },
    enabled: !!agentId,
  });
};

/**
 * L<PERSON>y thống kê số lượng và điểm trung bình
 */
export function useFeedbackStats(agentId?: string) {
  return useQuery<FeedbackStatsResponse, Error, FeedbackStatsResponse['data']>({
    queryKey: ['feedback', 'stats', agentId],
    queryFn: () => feedbackService.getFeedbackStats(agentId!),
    enabled: !!agentId,
    select: (res): FeedbackStatsResponse['data'] =>
      res.data ?? { totalFeedback: 0, averageRating: 0 },
  });
}

/**
 * Thêm mới feedback
 */
export function useCreateFeedback() {
  const qc = useQueryClient();
  return useMutation<FeedbackActionResponse, Error, AgentFeedbackRequest>({
    mutationFn: payload => feedbackService.addFeedback(payload),
    onSuccess: (_, payload) => {
      //toast.success('Gửi đánh giá thành công');
      qc.invalidateQueries({ queryKey: ['feedback', 'list', payload.agentId] });
      qc.invalidateQueries({ queryKey: ['feedback', 'stats', payload.agentId] });
    },
    onError: err => toast.error(err.message || 'Gửi đánh giá thất bại'),
  });
}

/**
 * Xoá feedback
 */
export function useDeleteFeedback() {
  const qc = useQueryClient();

  return useMutation<FeedbackActionResponse, Error, { agentId: string; id?: string }>({
    mutationFn: ({ agentId }) => feedbackService.deleteFeedback(agentId),

    onSuccess: (_, { agentId }) => {
      toast.success('Xoá đánh giá thành công');
      qc.invalidateQueries({ queryKey: ['feedback', 'list', agentId] });
      qc.invalidateQueries({ queryKey: ['feedback', 'stats', agentId] });
    },

    onError: err => toast.error(err.message || 'Xoá đánh giá thất bại'),
  });
}

/**
 * Cập nhật feedback
 */
export function useUpdateFeedback(agentId: string) {
  const qc = useQueryClient();
  return useMutation<
    FeedbackActionResponse,
    Error,
    { id: string; rating: number; content: string }
  >({
    mutationFn: ({ id, rating, content }) =>
      feedbackService.updateFeedback(id, { rating, content }),
    onSuccess: () => {
      toast.success('Cập nhật đánh giá thành công');
      qc.invalidateQueries({ queryKey: ['feedback', 'list', agentId] });
      qc.invalidateQueries({ queryKey: ['feedback', 'stats', agentId] });
    },
    onError: err => toast.error(err.message || 'Cập nhật đánh giá thất bại'),
  });
}

/**
 * Lấy chi tiết feedback theo agentFeedbackId và agentId
 */
export function useFeedbackDetail(agentFeedbackId?: string, agentId?: string) {
  return useQuery<AgentFeedbackResponse, Error>({
    queryKey: ['feedback', 'detail', agentFeedbackId, agentId],
    queryFn: () => feedbackService.getFeedbackById(agentFeedbackId!, agentId!),
    enabled: !!agentFeedbackId && !!agentId,
  });
}
