import {
  PersonStanding,
  Tag,
  Smile,
  SendHorizontal,
  ThumbsUp,
  ArrowLeft,
  MoreHorizontal,
  Pin,
  Reply,
  Paperclip,
  Images,
  FileText,
  Play,
  X,
  Loader2,
} from 'lucide-react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { useEffect, useRef, useState, useCallback } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import OtherMessage from './OtherMessage';
import MyMessage from './MyMessage';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useChat } from '@/hooks/useChat';
import { signalRService } from '@/lib/realtime/signalR';
import { chatService, MessageStructure, SendMessageRequest } from '@/lib/api/services/fetchChat';
import {
  useUploadAttachment,
  useUploadPropertyImage,
  useUploadPropertyVideo,
} from '@/hooks/useAttachment';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import type { MessageResponse, ChatMessage, RawAttachment } from '@/lib/api/services/fetchChat';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

type Props = {
  isMobile: boolean;
  setIsMobileChatOpen: React.Dispatch<React.SetStateAction<boolean>>;
  chatUser: {
    id: string;
    name: string;
    avatarUrl: string;
  };
  selectedConversation: string;
};

export default function ChannelChatContent(props: Props) {
  const { isMobile, setIsMobileChatOpen, chatUser, selectedConversation } = props;
  const [message, setMessage] = useState('');
  const [historyMessages, setHistoryMessages] = useState<MessageStructure[]>([]);
  const [pinnedMessages, setPinnedMessages] = useState<MessageStructure[]>([]);
  const [replyingMessage, setReplyingMessage] = useState<MessageStructure | null>(null);
  const [openMenuMessageId, setOpenMenuMessageId] = useState<string | null>(null);
  const [showPinnedList, setShowPinnedList] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedAttachmentIds, setUploadedAttachmentIds] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<{ url: string; type: string } | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [isJoiningConversation] = useState(false);
  const [isSending, setIsSending] = useState(false);

  const pageSize = 1000;
  const scrollPositionRef = useRef<number>(0);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const messageRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const { pinMessage, unpinMessage, deleteMessage } = useChat();
  const uploadAttachment = useUploadAttachment();
  const uploadImage = useUploadPropertyImage();
  const uploadVideo = useUploadPropertyVideo();

  const scrollToBottom = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        viewport.scrollTo({
          top: viewport.scrollHeight,
          behavior: 'smooth',
        });
      }
    }
  }, []);

  const handleScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    if (
      e.currentTarget.scrollTop === 0 &&
      !isLoadingMore &&
      selectedConversation &&
      hasMoreMessages
    ) {
      try {
        setIsLoadingMore(true);
        scrollPositionRef.current = e.currentTarget.scrollHeight;
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);
        await signalRService.loadMoreMessages(selectedConversation, nextPage, pageSize);
      } catch (error) {
        setCurrentPage(prev => prev - 1);
      } finally {
        setIsLoadingMore(false);
      }
    }
  };

  // File upload handlers
  const handleFileSelect = async (file: File, type: 'image' | 'video' | 'file') => {
    try {
      setIsUploading(true);
      let response;
      switch (type) {
        case 'image':
          response = await uploadImage.mutateAsync({ files: [file] });
          break;
        case 'video':
          response = await uploadVideo.mutateAsync({ file });
          break;
        default:
          response = await uploadAttachment.mutateAsync({ files: [file] });
      }
      let attachment;
      if (Array.isArray(response?.data)) {
        attachment = response.data[0];
      } else if (response?.data && typeof response.data === 'object') {
        attachment = response.data;
      }
      if (attachment && attachment.id) {
        setUploadedAttachmentIds(prev => [...prev, attachment.id]);
        setSelectedFiles(prev => [...prev, file]);
      }
    } catch (error) {
      <Alert variant="destructive">
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>Không thể tải lên file. Vui lòng thử lại.</AlertDescription>
      </Alert>;
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.type.startsWith('image/')) {
      handleFileSelect(file, 'image');
    } else if (file.type.startsWith('video/')) {
      handleFileSelect(file, 'video');
    } else {
      handleFileSelect(file, 'file');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    handleFileSelect(file, 'file');
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setUploadedAttachmentIds(prev => prev.filter((_, i) => i !== index));
  };

  // Message action handlers
  const handlePinMessage = async (messageId: string) => {
    const msg = historyMessages.find(m => m.id === messageId);
    if (!msg || pinnedMessages.some(m => m.id === messageId) || !selectedConversation) return;
    try {
      await pinMessage({ messageId, conversationId: selectedConversation });
      const newPinnedMessages = [...pinnedMessages, msg];
      setPinnedMessages(newPinnedMessages);
      savePinnedMessages(selectedConversation, newPinnedMessages);
      setHistoryMessages(prev =>
        prev.map(m => (m.id === messageId ? { ...m, isPinned: true } : m))
      );
      <Alert variant="default">
        <AlertTitle>Đã ghim</AlertTitle>
        <AlertDescription>Tin nhắn đã được ghim.</AlertDescription>
      </Alert>;
    } catch (err) {
      <Alert variant="destructive">
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>Không thể ghim tin nhắn. Vui lòng thử lại.</AlertDescription>
      </Alert>;
    }
  };

  const handleUnpinMessage = async (messageId: string) => {
    try {
      await unpinMessage({ messageId, conversationId: selectedConversation });
      const newPinnedMessages = pinnedMessages.filter(m => m.id !== messageId);
      setPinnedMessages(newPinnedMessages);
      savePinnedMessages(selectedConversation, newPinnedMessages);
      setHistoryMessages(prev =>
        prev.map(m => (m.id === messageId ? { ...m, isPinned: false } : m))
      );
      <Alert variant="default">
        <AlertTitle>Đã bỏ ghim</AlertTitle>
        <AlertDescription>Tin nhắn đã được bỏ ghim.</AlertDescription>
      </Alert>;
    } catch (err) {
      <Alert variant="destructive">
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>Không thể bỏ ghim. Vui lòng thử lại.</AlertDescription>
      </Alert>;
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    try {
      await deleteMessage(messageId);
      setHistoryMessages(prev => prev.filter(m => m.id !== messageId));
      setPinnedMessages(prev => prev.filter(m => m.id !== messageId));
      <Alert variant="default">
        <AlertTitle>Đã xóa tin nhắn</AlertTitle>
        <AlertDescription>Tin nhắn đã được xóa.</AlertDescription>
      </Alert>;
    } catch (err) {
      <Alert variant="destructive">
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>Không thể xóa tin nhắn. Vui lòng thử lại.</AlertDescription>
      </Alert>;
    }
  };

  const handleReply = (message: MessageStructure) => {
    setReplyingMessage(message);
  };

  // Render preview files
  const renderPreviewFiles = () => {
    if (selectedFiles.length === 0) return null;
    return (
      <div className="flex flex-wrap gap-2 p-2 bg-gray-50 rounded-lg mx-3 mb-2">
        {selectedFiles.map((file, index) => (
          <div key={index} className="relative group">
            {file.type.startsWith('image/') ? (
              <div className="relative">
                <Image
                  src={URL.createObjectURL(file)}
                  alt="Preview"
                  width={50}
                  height={50}
                  style={{ width: 'auto' }}
                  className="rounded-lg object-cover"
                />
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : file.type.startsWith('video/') ? (
              <div className="relative">
                <video
                  src={URL.createObjectURL(file)}
                  className="w-[50px] h-[50px] rounded-lg object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                  <Play size={24} className="text-white" />
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <div className="relative bg-white p-2 rounded-lg border">
                <div className="flex items-center gap-2">
                  <FileText size={20} className="text-gray-500" />
                  <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render replying message
  const renderReplyingMessage = () => {
    if (!replyingMessage) return null;
    return (
      <div className="flex items-start bg-gray-50 border-l-4 border-blue-500 rounded p-2 mb-2 mx-3 relative">
        <div className="flex-1">
          <div className="font-semibold text-xs text-gray-700 mb-0.5 flex items-center gap-1">
            <Reply size={14} className="text-blue-500" />
            Trả lời <span className="font-bold">Tin nhắn</span>
          </div>
          <div className="text-xs text-gray-600 truncate max-w-[320px]">
            {replyingMessage.content}
          </div>
        </div>
        <button
          className="ml-2 text-gray-400 hover:text-gray-600"
          onClick={() => setReplyingMessage(null)}
          aria-label="Hủy trả lời"
        >
          <X size={16} />
        </button>
      </div>
    );
  };

  useEffect(() => {
    if (!selectedConversation) {
      setHistoryMessages([]);
      return;
    }

    let isMounted = true;
    let localHistory: MessageStructure[] = [];

    function normalizeAttachment(
      att: RawAttachment | string
    ): MessageStructure['attachments'][number] {
      if (typeof att === 'string') {
        // Trường hợp ChatMessage.attachments là string[] (id file)
        return { id: att, type: '', url: '', name: '' };
      }
      return {
        id: att.id || '',
        type: att.type || att.fileType || '',
        url: att.url || att.fileUrl || '',
        name: att.name || att.fileName || '',
      };
    }

    const messageHistoryHandler = (history: MessageResponse[]) => {
      if (!isMounted) return;
      const extendedMessages: MessageStructure[] = history
        .filter(msg => !msg.isDeleted)
        .map(msg => ({
          id: msg.id || msg.messageId || Date.now().toString(),
          senderId: msg.senderId || '',
          direction: msg.direction || 'inbound',
          createdAt:
            typeof msg.createdAt === 'string'
              ? msg.createdAt
              : msg.createdAt instanceof Date
                ? msg.createdAt.toISOString()
                : msg.timestamp?.toString() || new Date().toISOString(),
          content: msg.content || '',
          replyToMessageId: msg.replyToMessageId || '',
          attachments: Array.isArray(msg.attachments)
            ? msg.attachments.map(normalizeAttachment)
            : [],
          isDeleted: msg.isDeleted || false,
          isPinned: false, // Pinned status will be merged later
        }));

      const messagesWithReplies = extendedMessages.map(msg => {
        if (msg.replyToMessageId) {
          const replyToMessage = extendedMessages.find(m => m.id === msg.replyToMessageId);
          if (replyToMessage) {
            return { ...msg, replyToMessage };
          }
        }
        return msg;
      });

      const sortedMessages = messagesWithReplies.sort(
        (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      setHistoryMessages(prev => {
        const newMessages = sortedMessages.filter(
          newMsg => !prev.some(existingMsg => existingMsg.id === newMsg.id)
        );
        if (newMessages.length === 0 && prev.length > 0) {
          setHasMoreMessages(false);
        }
        const updatedMessages = [...newMessages, ...prev];

        requestAnimationFrame(() => {
          if (scrollAreaRef.current) {
            const viewport = scrollAreaRef.current.querySelector(
              '[data-radix-scroll-area-viewport]'
            );
            if (viewport && newMessages.length > 0 && isLoadingMore) {
              const newScrollHeight = viewport.scrollHeight;
              const scrollOffset = newScrollHeight - scrollPositionRef.current;
              viewport.scrollTop = Math.max(0, scrollOffset);
            }
          }
        });

        localHistory = updatedMessages;
        return updatedMessages;
      });

      if (!isLoadingMore) {
        requestAnimationFrame(() => {
          scrollToBottom();
        });
      }
    };

    const messageHandler = (message: ChatMessage) => {
      if (!isMounted) return;
      const newMessage: MessageStructure = {
        id: message.id || message.messageId || Date.now().toString(),
        senderId: message.senderId || '',
        direction: message.direction || (message.senderId === 'admin' ? 'outbound' : 'inbound'),
        createdAt:
          typeof message.createdAt === 'string'
            ? message.createdAt
            : message.timestamp?.toString() || new Date().toISOString(),
        content: message.content || '',
        replyToMessageId: message.replyToMessageId || '',
        attachments: Array.isArray(message.attachments)
          ? message.attachments.map(normalizeAttachment)
          : [],
        isDeleted: message.isDeleted || false,
        isPinned: false,
      };

      if (newMessage.replyToMessageId) {
        const replyToMessage = localHistory.find(m => m.id === newMessage.replyToMessageId);
        if (replyToMessage) {
          newMessage.replyToMessage = replyToMessage;
        }
      }

      setHistoryMessages(prev => {
        // Check if message already exists (including temp messages)
        const existingMessage = prev.find(
          m =>
            m.id === newMessage.id ||
            (m.id.includes('temp') &&
              m.content === newMessage.content &&
              m.direction === newMessage.direction)
        );

        if (existingMessage) {
          // Replace temp message with real message
          if (existingMessage.id.includes('temp')) {
            const updated = prev.map(m => (m.id === existingMessage.id ? newMessage : m));
            localHistory = updated;
            return updated;
          }
          // Update existing message
          return prev.map(m => (m.id === newMessage.id ? newMessage : m));
        }

        // Add new message
        const updated = [...prev.filter(m => !m.id.includes('temp')), newMessage].sort(
          (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
        localHistory = updated;
        return updated;
      });

      requestAnimationFrame(() => {
        scrollToBottom();
      });
    };

    const setupConversation = async () => {
      // Reset state for new conversation
      setHistoryMessages([]);
      setPinnedMessages([]);
      setReplyingMessage(null);
      setCurrentPage(1);
      setHasMoreMessages(true);
      loadPinnedMessages(selectedConversation);

      signalRService.setMessageHistoryHandler(messageHistoryHandler);
      signalRService.setMessageHandler(messageHandler);

      try {
        await signalRService.connect();
        await signalRService.joinConversation(selectedConversation, chatUser.id);
        await signalRService.loadMoreMessages(selectedConversation, 1, pageSize);
      } catch (error) {
        console.error('Failed to setup conversation:', error);
      }
    };

    setupConversation();

    return () => {
      isMounted = false;
      if (selectedConversation) {
        signalRService.leaveConversation(selectedConversation);
      }
      signalRService.setMessageHistoryHandler(() => {});
      signalRService.setMessageHandler(() => {});
    };
  }, [selectedConversation, chatUser.id, scrollToBottom]);

  const handleSendOrLike = async () => {
    if (isSending) return; // Prevent double sends

    if (message.trim() || uploadedAttachmentIds.length > 0) {
      try {
        setIsSending(true);
        const tempMessageId = `temp-${Date.now()}-${Math.random()}`;
        const optimisticMessage: MessageStructure = {
          id: tempMessageId,
          senderId: 'admin',
          direction: 'outbound',
          createdAt: new Date().toISOString(),
          content: message,
          replyToMessageId: replyingMessage?.id || '',
          attachments: selectedFiles.map((file, index) => ({
            id: uploadedAttachmentIds[index] || '',
            type: file.type,
            url: URL.createObjectURL(file),
            name: file.name,
          })),
          isDeleted: false,
          isPinned: false,
          replyToMessage: replyingMessage || undefined,
        };

        // Add optimistic message
        setHistoryMessages(prev => [...prev, optimisticMessage]);
        scrollToBottom();

        // Clear form immediately
        const currentMessage = message;
        const currentAttachmentIds = [...uploadedAttachmentIds];
        const currentReplyingMessage = replyingMessage;

        setMessage('');
        setSelectedFiles([]);
        setUploadedAttachmentIds([]);
        setReplyingMessage(null);

        const messageData: SendMessageRequest = {
          content: currentMessage,
          conversationId: selectedConversation,
          attachmentIds: currentAttachmentIds.length > 0 ? currentAttachmentIds : undefined,
          replyToMessageId: currentReplyingMessage?.id,
          direction: 'outbound',
          platform: 'system',
        };

        await chatService.sendMessage(messageData);

        // Remove temp message after successful send (real message will come via SignalR)
        setHistoryMessages(prev => prev.filter(msg => msg.id !== tempMessageId));
      } catch (error) {
        console.error('Error sending message:', error);
        // Remove temp message on error
        setHistoryMessages(prev => prev.filter(msg => !msg.id.includes('temp')));
        <Alert variant="destructive">
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>Không thể gửi tin nhắn. Vui lòng thử lại.</AlertDescription>
        </Alert>;
      } finally {
        setIsSending(false);
      }
    } else {
      try {
        setIsSending(true);
        const tempLikeId = `temp-like-${Date.now()}-${Math.random()}`;
        const optimisticLikeMessage: MessageStructure = {
          id: tempLikeId,
          senderId: 'admin',
          direction: 'outbound',
          createdAt: new Date().toISOString(),
          content: '👍',
          replyToMessageId: '',
          attachments: [],
          isDeleted: false,
          isPinned: false,
        };

        // Add optimistic like
        setHistoryMessages(prev => [...prev, optimisticLikeMessage]);
        scrollToBottom();

        await chatService.sendMessage({
          content: '👍',
          conversationId: selectedConversation,
          direction: 'outbound',
          platform: 'system',
        });

        // Remove temp like after successful send
        setHistoryMessages(prev => prev.filter(msg => msg.id !== tempLikeId));
      } catch (error) {
        console.error('Error sending like:', error);
        // Remove temp like on error
        setHistoryMessages(prev => prev.filter(msg => !msg.id.includes('temp-like')));
        <Alert variant="destructive">
          <AlertTitle>Lỗi</AlertTitle>
          <AlertDescription>Không thể gửi like. Vui lòng thử lại.</AlertDescription>
        </Alert>;
      } finally {
        setIsSending(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && !isSending) {
      e.preventDefault();
      handleSendOrLike();
    }
  };

  const loadPinnedMessages = async (conversationId: string) => {
    try {
      const storedPinnedMessages = localStorage.getItem(`pinned_messages_${conversationId}`);
      if (storedPinnedMessages) {
        const parsedMessages = JSON.parse(storedPinnedMessages);
        setPinnedMessages(parsedMessages);
      } else {
        setPinnedMessages([]);
      }
    } catch (error) {
      console.error('Error saving pinned messages:', error);
    }
  };

  const savePinnedMessages = (conversationId: string, messages: MessageStructure[]) => {
    try {
      localStorage.setItem(`pinned_messages_${conversationId}`, JSON.stringify(messages));
    } catch (error) {
      console.error('Error saving pinned messages:', error);
    }
  };

  return (
    <div className="flex flex-col justify-between w-full h-full border rounded-lg shadow-md bg-white hover:shadow-lg transition-all duration-300 py-3">
      <div className="w-full flex justify-between items-center border-b pb-3 px-4 shadow-sm mb-4">
        <div className="flex gap-2 items-center">
          {isMobile && (
            <div
              className="flex gap-2 items-center mr-2"
              onClick={() => setIsMobileChatOpen(false)}
            >
              <ArrowLeft width={22} height={22} className="text-blue-800 cursor-pointer" />
            </div>
          )}
          <Image
            src={
              chatUser.avatarUrl ||
              `https://ui-avatars.com/api/?name=${encodeURIComponent(chatUser.name || 'Anonymous')}&background=F3F4F6&color=000000`
            }
            alt="logo"
            width={38}
            height={38}
            unoptimized
            className="rounded-full"
          />
          <p className="font-semibold text-[15px]">{chatUser.name || 'Anonymous'}</p>
        </div>
        <div className="flex gap-3 items-center">
          <Tag width={22} height={22} className="text-blue-800" />
          <PersonStanding width={22} height={22} className="text-blue-800" />
        </div>
      </div>

      {pinnedMessages.length > 0 && (
        <div className="px-4 py-2 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center justify-between">
            <span className="font-semibold text-sm text-blue-800">
              Tin nhắn ghim ({pinnedMessages.length})
            </span>
            <button
              onClick={() => setShowPinnedList(!showPinnedList)}
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft
                size={20}
                className={`transform transition-transform ${showPinnedList ? 'rotate-90' : '-rotate-90'}`}
              />
            </button>
          </div>
          {showPinnedList && (
            <div className="mt-2 max-h-[150px] overflow-y-auto">
              {pinnedMessages.map(msg => (
                <div
                  key={msg.id}
                  className="flex items-center justify-between py-1 border-b last:border-b-0 bg-white rounded-lg p-2 mb-2"
                >
                  <div className="flex items-center gap-2">
                    <Pin className="text-blue-600" size={16} />
                    <div className="text-xs text-gray-700 truncate max-w-[200px]">
                      {msg.content}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button className="w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100">
                        <MoreHorizontal size={16} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="z-[9999]">
                      <DropdownMenuItem onClick={() => handleUnpinMessage(msg.id)}>
                        Bỏ ghim
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          const el = messageRefs.current[msg.id];
                          if (el) {
                            el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            el.classList.add('ring-2', 'ring-blue-400');
                            setTimeout(() => el.classList.remove('ring-2', 'ring-blue-400'), 1500);
                          }
                          setShowPinnedList(false);
                        }}
                      >
                        Xem
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <ScrollArea ref={scrollAreaRef} className="h-full" onScroll={handleScroll}>
        <div className="flex justify-end flex-col pl-3 pr-3 h-full gap-8 mb-8">
          {isLoadingMore && (
            <div className="flex justify-center items-center py-2">
              <Loader2 className="animate-spin text-gray-400" size={24} />
            </div>
          )}
          {historyMessages.length === 0 && !isJoiningConversation ? (
            <div className="flex items-center justify-center h-[calc(100vh-200px)]">
              <p className="text-gray-500 text-lg font-medium">Please choose a conversation</p>
            </div>
          ) : (
            <div className="flex flex-col gap-4">
              {(() => {
                const messageGroups: MessageStructure[][] = [];
                let currentGroup: MessageStructure[] = [];
                let currentDirection = historyMessages[0]?.direction;

                historyMessages.forEach((message, index) => {
                  if (message.direction !== currentDirection) {
                    if (currentGroup.length > 0) {
                      messageGroups.push([...currentGroup]);
                      currentGroup = [];
                    }
                    currentDirection = message.direction;
                  }
                  currentGroup.push(message);

                  if (index === historyMessages.length - 1 && currentGroup.length > 0) {
                    messageGroups.push([...currentGroup]);
                  }
                });

                return messageGroups.map((group, index) => (
                  <div
                    key={index}
                    ref={el => {
                      messageRefs.current[group[0].id] = el;
                    }}
                    className={`flex w-full ${group[0].direction === 'outbound' ? 'justify-end' : 'justify-start'}`}
                  >
                    {group[0].direction === 'outbound' ? (
                      <MyMessage
                        messages={group}
                        onPin={handlePinMessage}
                        onDelete={handleDeleteMessage}
                        onReply={handleReply}
                        openMenuMessageId={openMenuMessageId}
                        setOpenMenuMessageId={setOpenMenuMessageId}
                        selectedMedia={selectedMedia}
                        setSelectedMedia={setSelectedMedia}
                      />
                    ) : (
                      <OtherMessage
                        messages={group}
                        chatUser={chatUser}
                        onPin={handlePinMessage}
                        onDelete={handleDeleteMessage}
                        onReply={handleReply}
                        openMenuMessageId={openMenuMessageId}
                        setOpenMenuMessageId={setOpenMenuMessageId}
                        selectedMedia={selectedMedia}
                        setSelectedMedia={setSelectedMedia}
                      />
                    )}
                  </div>
                ));
              })()}
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="px-3">
        {renderReplyingMessage()}
        {renderPreviewFiles()}

        <div className="flex items-center gap-3">
          <div className="flex gap-2">
            <Paperclip
              className="text-blue-800 cursor-pointer hover:text-blue-600"
              size={22}
              onClick={() => fileInputRef.current?.click()}
            />
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.txt,.csv,application/*"
            />
            <Images
              className="text-blue-800 cursor-pointer hover:text-blue-600"
              size={22}
              onClick={() => imageInputRef.current?.click()}
            />
            <Input
              type="file"
              ref={imageInputRef}
              onChange={handleImageChange}
              className="hidden"
              accept="image/*,video/*"
            />
          </div>

          {isUploading && (
            <div className="flex items-center gap-2">
              <Loader2 className="animate-spin text-gray-400" size={20} />
              <span className="text-sm text-gray-500">Đang tải lên...</span>
            </div>
          )}

          <div className="flex-1 relative">
            <Input
              value={message}
              onChange={e => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pr-10 rounded-full h-9 focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 hover:border-input"
              placeholder="Aa"
            />
            <div className="absolute right-0 inset-y-0 flex items-center justify-center pr-3">
              <Popover>
                <PopoverTrigger asChild>
                  <button className="focus:outline-none">
                    <Smile className="h-5 w-5 text-blue-800 cursor-pointer" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" side="top" align="end">
                  <Picker
                    data={data}
                    onEmojiSelect={(emoji: { native: string }) =>
                      setMessage(prev => prev + emoji.native)
                    }
                    theme="light"
                    set="native"
                    previewPosition="none"
                    skinTonePosition="none"
                    autoFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <button
            onClick={handleSendOrLike}
            disabled={isSending}
            className="focus:outline-none transition-transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSending ? (
              <Loader2 className="animate-spin text-blue-800" size={22} />
            ) : message.trim() || uploadedAttachmentIds.length > 0 ? (
              <SendHorizontal width={22} height={22} className="text-blue-800 cursor-pointer" />
            ) : (
              <ThumbsUp width={22} height={22} className="text-blue-800 cursor-pointer" />
            )}
          </button>
        </div>
      </div>

      {selectedMedia && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100]"
          onClick={() => setSelectedMedia(null)}
        >
          <div className="relative max-w-[90vw] max-h-[90vh]">
            {selectedMedia.type === 'image' ? (
              <Image
                src={selectedMedia.url}
                alt="Full size preview"
                width={1200}
                height={800}
                style={{ width: 'auto' }}
                className="max-w-full max-h-[90vh] object-contain"
              />
            ) : (
              <video controls className="max-w-full max-h-[90vh]" src={selectedMedia.url} autoPlay>
                Your browser does not support the video tag.
              </video>
            )}
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
              onClick={() => setSelectedMedia(null)}
            >
              <X size={24} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
