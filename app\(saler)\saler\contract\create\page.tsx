'use client';
import { Suspense } from 'react';
import { useProperty } from '@/hooks/useProperty';
import { useSearchParams } from 'next/navigation';
import RentalContractPage from './RenatlContract/RentalContract';
import HouseSaleContract from './SaleContract/HouseSaleContract';
import { SiteHeader } from '@/components/common/siteHeader';

const CreateContractByPropertyId = () => {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const { data, isLoading, isError, error } = useProperty(id || undefined);

  if (!id) return <div>Không tìm thấy property id trên URL</div>;
  if (isLoading)
    return (
      <div className="flex items-center justify-center w-full min-h-[100px]">
        <div
          className="
          animate-spin
          w-12 h-12
          border-4 border-solid border-gray-300 border-t-black border-l-white
          rounded-full
        "
          role="status"
          aria-label="Đang tải dữ liệu"
        >
          <span className="sr-only">Đang tải...</span>
        </div>
      </div>
    );
  if (isError) return <div>Error: {error?.message || 'Không lấy được dữ liệu'}</div>;
  if (!data?.property) return <div>Không tìm thấy property</div>;

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
        <SiteHeader title="Tạo hợp đồng" />
      </header>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        {data.property.transactionType === 'ForRent' ? (
          <RentalContractPage
            propertyLocation={data.property.location}
            propertyDetails={data.property.propertyDetails}
            priceDetails={data.property.priceDetails}
            propertyId={data.property.id}
            legalDocumentUrls={data.property.legalDocumentUrls || []}
          />
        ) : (
          <HouseSaleContract
            propertyLocation={data.property.location}
            propertyDetails={data.property.propertyDetails}
            propertyType={data.property.type}
            priceDetails={data.property.priceDetails}
            propertyId={data.property.id}
            legalDocumentUrls={data.property.legalDocumentUrls || []}
          />
        )}
      </div>
    </div>
  );
};

export default function Page() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center w-full min-h-[100px]">
          <div
            className="
          animate-spin
          w-12 h-12
          border-4 border-solid border-gray-300 border-t-black border-l-white
          rounded-full
        "
            role="status"
            aria-label="Đang tải dữ liệu"
          >
            <span className="sr-only">Đang tải...</span>
          </div>
        </div>
      }
    >
      <CreateContractByPropertyId />
    </Suspense>
  );
}
