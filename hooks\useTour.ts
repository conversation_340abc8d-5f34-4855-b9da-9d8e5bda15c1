'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import tourService, {
  AddPropertyToTourRequest,
  CreateTourRequest,
  SalerTourSearchParams,
  TourListResponse,
  TourPropertiesBySalerResponse,
  TourResponse,
  TourSearchParams,
  UpdateTourPropertyRequest,
  UpdateTourRequest,
} from '@/lib/api/services/fetchTour'; // Import the new tourService
import { toast } from 'sonner';

/**
 * Hook to fetch all tours for the current user
 */
export function useUserTours(filters?: TourSearchParams) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['tours', 'user', filters ? JSON.stringify(filters) : 'all'],
    queryFn: () => tourService.getUserTours(filters),
    select: (response: TourListResponse) => ({
      tours: response.data?.data || [],
      totalCount: response.data?.totalCount || 0,
      pageSize: response.data?.pageSize || 10,
      currentPage: response.data?.currentPage || 1,
      totalPages: response.data?.totalPages || 1,
      status: response.status,
      message: response.message,
      code: response.code,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    tours: data?.tours || [],
    totalCount: data?.totalCount || 0,
    currentPage: data?.currentPage || 1,
    pageSize: data?.pageSize || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
    code: data?.code,
  };
}

export function useSalerTours(filters?: SalerTourSearchParams) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['tourProperties', 'saler', filters ? JSON.stringify(filters) : 'allSaler'],
    queryFn: () => tourService.getSalerTours(filters),
    select: (response: TourPropertiesBySalerResponse) => ({
      tourProperties: response.data?.data || [],
      totalCount: response.data?.totalCount || 0,
      pageSize: response.data?.pageSize || 10,
      currentPage: response.data?.currentPage || 1,
      totalPages: response.data?.totalPages || 1,
      status: response.status,
      message: response.message,
      code: response.code,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    tourProperties: data?.tourProperties || [],
    totalCount: data?.totalCount || 0,
    currentPage: data?.currentPage || 1,
    pageSize: data?.pageSize || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
    code: data?.code,
  };
}

/**
 * Hook to create a new tour
 */
export function useCreateTour() {
  const queryClient = useQueryClient();
  return useMutation<TourResponse<{ tourId: string }>, Error, CreateTourRequest>({
    mutationFn: (newTour: CreateTourRequest) => tourService.createTour(newTour),
    onSuccess: data => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
        toast.success(data.message || 'Tour đã được tạo thành công!');
      } else {
        toast.error(data.message || 'Không thể tạo tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi tạo tour.');
    },
  });
}

/**
 * Hook to update an existing tour
 */
export function useUpdateTour() {
  const queryClient = useQueryClient();
  return useMutation<TourResponse<string>, Error, { id: string; tour: Partial<UpdateTourRequest> }>(
    {
      mutationFn: payload => tourService.updateTour(payload.id, payload.tour),
      onSuccess: (data, variables) => {
        if (data.status) {
          queryClient.invalidateQueries({ queryKey: ['tours', 'detail', variables.id] }); // Invalidate specific tour detail
          queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
          toast.success(data.message || 'Tour đã được cập nhật thành công!');
        } else {
          toast.error(data.message || 'Không thể cập nhật tour.');
        }
      },
      onError: (error: Error) => {
        toast.error(error.message || 'Đã xảy ra lỗi khi cập nhật tour.');
      },
    }
  );
}

export function useUpdateTourProperty() {
  const queryClient = useQueryClient();
  return useMutation<
    TourResponse<string>,
    Error,
    { id: string; tour: Partial<UpdateTourPropertyRequest> }
  >({
    mutationFn: payload => tourService.updateTourProperty(payload.id, payload.tour),
    onSuccess: (data, variables) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['tours', 'detail', variables.id] }); // Invalidate specific tour detail
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
        toast.success(data.message || 'Tour đã được cập nhật thành công!');
      } else {
        toast.error(data.message || 'Không thể cập nhật tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi cập nhật tour.');
    },
  });
}

/**
 * Hook to delete a tour
 */
export function useDeleteTour() {
  const queryClient = useQueryClient();
  return useMutation<TourResponse<string>, Error, string>({
    mutationFn: (id: string) => tourService.deleteTour(id),
    onSuccess: (data, id) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
        queryClient.removeQueries({ queryKey: ['tours', 'detail', id] }); // Remove deleted tour from cache
        toast.success(data.message || 'Tour đã được xóa thành công!');
      } else {
        toast.error(data.message || 'Không thể xóa tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi xóa tour.');
    },
  });
}

/**
 * Hook to add a property to a tour
 */
export function useAddPropertyToTour() {
  const queryClient = useQueryClient();
  return useMutation<
    TourResponse<string>,
    Error,
    { tourId: string; property: AddPropertyToTourRequest }
  >({
    mutationFn: payload => tourService.addPropertyToTour(payload.tourId, payload.property),
    onSuccess: (data, variables) => {
      if (data.status) {
        // Invalidate multiple related queries
        queryClient.invalidateQueries({ queryKey: ['tours', 'detail', variables.tourId] });
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
        queryClient.invalidateQueries({ queryKey: ['tours'] }); // Invalidate all tour queries

        toast.success(data.message || 'Bất động sản đã được thêm vào tour!');
      } else {
        toast.error(data.message || 'Không thể thêm bất động sản vào tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi thêm bất động sản vào tour.');
    },
  });
}

/**
 * Hook to remove a property from a tour
 */

export function useRemovePropertyFromTour() {
  const queryClient = useQueryClient();
  return useMutation<TourResponse<string>, Error, { tourId: string; propertyId: string }>({
    mutationFn: payload => tourService.removePropertyFromTour(payload.tourId, payload.propertyId),
    onSuccess: (data, variables) => {
      if (data.status) {
        // Invalidate multiple related queries
        queryClient.invalidateQueries({ queryKey: ['tours', 'detail', variables.tourId] });
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] }); // Invalidate user tours list
        queryClient.invalidateQueries({ queryKey: ['tours'] }); // Invalidate all tour queries

        toast.success(data.message || 'Bất động sản đã được xóa khỏi tour!');
      } else {
        toast.error(data.message || 'Không thể xóa bất động sản khỏi tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi xóa bất động sản khỏi tour.');
    },
  });
}

export function useUpdateTourStatus() {
  const queryClient = useQueryClient();

  return useMutation<
    TourResponse<string>,
    Error,
    { id: string; status: 'Pending' | 'Approved' | 'Completed' | 'Rejected' }
  >({
    mutationFn: payload => tourService.updateTourStatus(payload.id, payload.status),
    onSuccess: (data, variables) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['tours', 'detail', variables.id] });
        queryClient.invalidateQueries({ queryKey: ['tours', 'user'] });
        queryClient.invalidateQueries({ queryKey: ['tourProperties', 'saler'] });
        toast.success(data.message || 'Trạng thái tour đã được cập nhật thành công!');
      } else {
        toast.error(data.message || 'Không thể cập nhật trạng thái tour.');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Đã xảy ra lỗi khi cập nhật trạng thái tour.');
    },
  });
}
