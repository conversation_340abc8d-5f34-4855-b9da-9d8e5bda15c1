import React from 'react';
import { Mail, Phone, MapPin, Send, User, MessageSquare, Building } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useChat } from '@/hooks/useChat';
import { PropertyStatus } from '@/lib/api/services/fetchProperty';

interface ContactFormComponentProps {
  propertyInfo?: {
    name: string;
    code: string;
    address: string;
    contactName: string;
    contactPhone: string;
    contactEmail: string;
    imageUrls: string[];
    yearBuilt: number;
    status: PropertyStatus;
    propertyDetails: {
      bedrooms?: number;
      bathrooms?: number;
      livingRooms?: number;
      kitchens?: number;
      landArea?: number;
      landWidth?: number;
      landLength?: number;
      buildingArea?: number;
      numberOfFloors?: number;
      hasBasement?: boolean;
      floorNumber?: number;
      apartmentOrientation?: string;
      furnished?: boolean;
    };
    type: string;
    bedrooms?: number;
    bathrooms?: number;
    landArea?: number;
    id: string;
  };
  isPropertyDetail?: boolean;
}

export default function ContactFormComponent({
  propertyInfo,
  isPropertyDetail = false,
}: ContactFormComponentProps) {
  const { sendMessage } = useChat();

  const form = useForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      propertyPreferences: {
        preferredLocation: '',
        preferredPropertyType: '',
        bedrooms: '',
        bathrooms: '',
        budgetRange: '',
        preferredContact: 'email',
      },
      message: '',
      terms: false,
    },
  });

  const onSubmit = async () => {
    if (isPropertyDetail && propertyInfo) {
      if (!propertyInfo.id) {
        alert('Thiếu ID property, không thể gửi vào chat!');
        return;
      }
      await sendMessage({
        content: JSON.stringify({
          id: propertyInfo.id,
          name: propertyInfo.name,
          address: propertyInfo.address,
          imageUrls: propertyInfo.imageUrls,
          propertyDetails: propertyInfo.propertyDetails,
          type: propertyInfo.type,
          bedrooms: propertyInfo.bedrooms,
          bathrooms: propertyInfo.bathrooms,
          landArea: propertyInfo.landArea,
          yearBuilt: propertyInfo.yearBuilt,
          livingRooms: propertyInfo.propertyDetails.livingRooms,
          kitchens: propertyInfo.propertyDetails.kitchens,
          landWidth: propertyInfo.propertyDetails.landWidth,
          landLength: propertyInfo.propertyDetails.landLength,
          floorNumber: propertyInfo.propertyDetails.floorNumber,
          status: propertyInfo.status,
        }),
        messageType: 'property',
      });
    }
  };

  if (isPropertyDetail) {
    return (
      <div className="relative">
        {/* Content */}
        <div className="flex flex-col md:flex-row gap-10">
          {/* Left side - Information */}
          <div className="w-full md:w-2/5 flex flex-col justify-start items-start gap-4">
            <div className="inline-flex items-center px-4 py-1.5 bg-red-50 rounded-full">
              <MessageSquare className="h-4 w-4 text-red-600 mr-1.5" />
              <span className="text-sm font-medium text-red-600">Contact Form</span>
            </div>

            <h1 className="text-2xl md:text-3xl font-bold">Contact Form {propertyInfo?.name}</h1>

            <p className="text-muted-foreground font-light">Contact Form Description</p>

            {/* Agent/Contact Information */}
            <div className="bg-background border border-muted-foreground/30 rounded-xl p-5">
              <div className="flex items-center gap-4 mb-4">
                <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                  <User className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-medium">{propertyInfo?.contactName}</h3>
                  <p className="text-sm text-muted-foreground">Contact Form Agent</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-red-600" />
                  <span>{propertyInfo?.contactPhone}</span>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-red-600" />
                  <span>{propertyInfo?.contactEmail}</span>
                </div>
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-red-600" />
                  <span>Property ID {propertyInfo?.code}</span>
                </div>
              </div>
            </div>

            <div className="bg-red-50 rounded-xl p-4 border border-red-200">
              <p className="text-sm">Response Time</p>
            </div>
          </div>

          {/* Right side - Form */}
          <div className="w-full md:w-3/5 bg-background border border-muted-foreground/30 rounded-xl p-6 md:p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(() => onSubmit())} className="space-y-5">
                {/* Name Inputs */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      placeholder="First Name"
                      {...form.register('firstName')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input id="lastName" placeholder="Last Name" {...form.register('lastName')} />
                  </div>
                </div>

                {/* Contact Inputs */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Email"
                      {...form.register('email')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input id="phone" type="tel" placeholder="Phone" {...form.register('phone')} />
                  </div>
                </div>

                {/* Property Info */}
                {propertyInfo && (
                  <div className="space-y-2">
                    <Label htmlFor="property">Property</Label>
                    <Input
                      id="property"
                      value={`${propertyInfo.name} - ${propertyInfo.address}`}
                      readOnly
                      className="bg-muted/20"
                    />
                  </div>
                )}

                {/* Message */}
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <div className="relative">
                    <Textarea
                      id="message"
                      placeholder="Message"
                      rows={4}
                      className="resize-none"
                      {...form.register('message')}
                    />
                  </div>
                </div>

                {/* Terms and Submit */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 justify-between mt-2">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" {...form.register('terms')} />
                    <Label htmlFor="terms" className="text-sm text-muted-foreground">
                      Terms
                    </Label>
                  </div>
                  <Button
                    type="submit"
                    className="w-full sm:w-auto px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 transition-colors flex items-center justify-center gap-2"
                  >
                    <Send className="h-4 w-4" />
                    Send Inquiry
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    );
  }

  // For the properties page variant
  return (
    <section className="w-full max-w-screen px-8 md:px-8 xl:px-32 mx-auto py-16 bg-background text-foreground ">
      <div className="max-w-screen mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-start">
          <div className="lg:col-span-4">
            <div className="sticky top-24">
              <h2 className="text-3xl font-bold mb-4">Contact Form</h2>
              <p className="text-muted-foreground mb-8">Contact Form Description</p>

              <Card className="border-muted/30 bg-background/50 mb-6">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Contact Form</CardTitle>
                  <CardDescription>Contact Form Description</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <Phone className="h-5 w-5 mr-3 text-red-600 mt-0.5" />
                      <div>
                        <p className="font-medium">Phone</p>
                        <p className="text-muted-foreground">+84 379 750 637</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Mail className="h-5 w-5 mr-3 text-red-600 mt-0.5" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <MapPin className="h-5 w-5 mr-3 text-red-600 mt-0.5" />
                      <div>
                        <p className="font-medium">Office</p>
                        <p className="text-muted-foreground">D2A40, Quan 9, Ho Chi Minh, Vietnam</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Form */}
          <Card className="lg:col-span-8 border-muted/20 shadow-sm">
            <CardContent className="p-6 md:p-8">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(() => onSubmit())} className="space-y-8">
                  {/* Personal Information Section */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Personal Info</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>First Name</Label>
                        <Input
                          placeholder="First Name"
                          className="h-11"
                          {...form.register('firstName')}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Last Name</Label>
                        <Input
                          placeholder="Last Name"
                          className="h-11"
                          {...form.register('lastName')}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Email</Label>
                        <Input
                          type="email"
                          placeholder="Email"
                          className="h-11"
                          {...form.register('email')}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Phone</Label>
                        <Input placeholder="Phone" className="h-11" {...form.register('phone')} />
                      </div>
                    </div>
                  </div>

                  {/* Property Preferences Section */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Property Preferences</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Preferred Location</Label>
                        <Select
                          onValueChange={value =>
                            form.setValue('propertyPreferences.preferredLocation', value)
                          }
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Preferred Location" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="downtown">Downtown</SelectItem>
                            <SelectItem value="suburban">Suburban Area</SelectItem>
                            <SelectItem value="beachfront">Beachfront</SelectItem>
                            <SelectItem value="countryside">Countryside</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Preferred Property Type</Label>
                        <Select
                          onValueChange={value =>
                            form.setValue('propertyPreferences.preferredPropertyType', value)
                          }
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Preferred Property Type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="house">House</SelectItem>
                            <SelectItem value="apartment">Apartment</SelectItem>
                            <SelectItem value="condo">Condominium</SelectItem>
                            <SelectItem value="townhouse">Townhouse</SelectItem>
                            <SelectItem value="land">Land</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Bedrooms</Label>
                        <Select
                          onValueChange={value =>
                            form.setValue('propertyPreferences.bedrooms', value)
                          }
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Bedrooms" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 Bedroom</SelectItem>
                            <SelectItem value="2">2 Bedrooms</SelectItem>
                            <SelectItem value="3">3 Bedrooms</SelectItem>
                            <SelectItem value="4">4 Bedrooms</SelectItem>
                            <SelectItem value="5+">5+ Bedrooms</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Bathrooms</Label>
                        <Select
                          onValueChange={value =>
                            form.setValue('propertyPreferences.bathrooms', value)
                          }
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Bathrooms" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 Bathroom</SelectItem>
                            <SelectItem value="2">2 Bathrooms</SelectItem>
                            <SelectItem value="3">3 Bathrooms</SelectItem>
                            <SelectItem value="4+">4+ Bathrooms</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Budget Range</Label>
                        <Select
                          onValueChange={value =>
                            form.setValue('propertyPreferences.budgetRange', value)
                          }
                        >
                          <SelectTrigger className="h-11">
                            <SelectValue placeholder="Budget Range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="100k-300k">$100,000 - $300,000</SelectItem>
                            <SelectItem value="300k-500k">$300,000 - $500,000</SelectItem>
                            <SelectItem value="500k-750k">$500,000 - $750,000</SelectItem>
                            <SelectItem value="750k-1m">$750,000 - $1,000,000</SelectItem>
                            <SelectItem value="1m+">$1,000,000+</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="sm:col-span-2 space-y-2">
                        <Label>Preferred Contact</Label>
                        <RadioGroup
                          className="flex flex-col space-y-1 sm:flex-row sm:space-y-0 sm:space-x-4"
                          onValueChange={value =>
                            form.setValue('propertyPreferences.preferredContact', value)
                          }
                          defaultValue="email"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="email" id="contact-email" />
                            <Label htmlFor="contact-email">Email</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="phone" id="contact-phone" />
                            <Label htmlFor="contact-phone">Phone</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </div>

                  {/* Message Section */}
                  <div className="space-y-2">
                    <Label>Additional Info</Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      Additional Info Description
                    </p>
                    <Textarea
                      placeholder="Message"
                      className="min-h-[120px] resize-none"
                      {...form.register('message')}
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                    <div className="flex items-start space-x-2">
                      <Checkbox id="terms" className="mt-1" {...form.register('terms')} />
                      <div>
                        <Label htmlFor="terms" className="text-sm font-medium block mb-1">
                          Terms
                        </Label>
                        <p className="text-xs text-muted-foreground">Terms Description</p>
                      </div>
                    </div>
                    <Button type="submit" size="lg" className="px-8">
                      Submit
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
