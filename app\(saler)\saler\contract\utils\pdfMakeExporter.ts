import { TDocumentDefinitions, Content } from 'pdfmake/interfaces';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

// Set up fonts
pdfMake.vfs = pdfFonts.vfs;

// Type definitions for pdfMake
interface PdfMakeStatic {
  vfs: Record<string, string>;
  createPdf(documentDefinition: TDocumentDefinitions): {
    download(filename?: string): void;
    getBlob(callback: (blob: Blob) => void): void;
  };
}

// Type for VFS (Virtual File System)
interface VfsType {
  vfs: Record<string, string>;
}

// Parser for Quill HTML content
const parseQuillHtmlToContent = (html: string): Content[] => {
  if (!html || html.trim() === '') {
    return [{ text: '' }];
  }

  const content: Content[] = [];

  // Define types for better type safety
  interface StyleProperties {
    alignment?: 'left' | 'center' | 'right' | 'justify';
    bold?: boolean;
    italics?: boolean;
    decoration?: 'underline';
    fontSize?: number;
    margin?: number[];
    lineHeight?: number;
    color?: string;
  }

  interface TextPart {
    text: string;
    bold?: boolean;
    italics?: boolean;
    decoration?: string;
    fontSize?: number;
    color?: string;
  }

  // Parse inline styles
  const parseInlineStyle = (styleStr: string): StyleProperties => {
    const styles: StyleProperties = {};
    if (!styleStr) return styles;

    const rules = styleStr.split(';').filter(rule => rule.trim());
    rules.forEach(rule => {
      const [property, value] = rule.split(':').map(s => s.trim());
      switch (property?.toLowerCase()) {
        case 'text-align': {
          if (['left', 'center', 'right', 'justify'].includes(value)) {
            styles.alignment = value as 'left' | 'center' | 'right' | 'justify';
          }
          break;
        }
        case 'font-weight': {
          if (value === 'bold' || value === 'bolder' || parseInt(value) >= 600) {
            styles.bold = true;
          }
          break;
        }
        case 'font-style': {
          if (value === 'italic') {
            styles.italics = true;
          }
          break;
        }
        case 'text-decoration': {
          if (value.includes('underline')) {
            styles.decoration = 'underline';
          }
          break;
        }
        case 'font-size': {
          const fontSizeMatch = value.match(/(\d+)px/);
          if (fontSizeMatch) {
            styles.fontSize = parseInt(fontSizeMatch[1]);
          }
          break;
        }
        case 'margin-left': {
          const marginMatch = value.match(/(\d+)px/);
          if (marginMatch) {
            const leftMargin = parseInt(marginMatch[1]);
            styles.margin = [leftMargin, 0, 0, 0];
          }
          break;
        }
        case 'line-height': {
          const lineHeightMatch = value.match(/(\d+\.?\d*)/);
          if (lineHeightMatch) {
            styles.lineHeight = parseFloat(lineHeightMatch[1]);
          }
          break;
        }
        case 'color': {
          styles.color = value;
          break;
        }
      }
    });
    return styles;
  };

  // Parse text content with basic inline formatting
  const parseTextContent = (htmlStr: string): string | Content[] => {
    if (!htmlStr || htmlStr.trim() === '') return '';

    let text = htmlStr;
    const textParts: TextPart[] = [];

    // Handle bold
    text = text.replace(/<(strong|b)([^>]*)>(.*?)<\/\1>/gi, (match, tag, attrs, innerText) => {
      const styles: TextPart = { text: innerText.replace(/<[^>]*>/g, ''), bold: true };
      const styleMatch = attrs?.match(/style="([^"]*)"/);
      if (styleMatch) {
        const inlineStyles = parseInlineStyle(styleMatch[1]);
        if (inlineStyles.fontSize) styles.fontSize = inlineStyles.fontSize;
        if (inlineStyles.color) styles.color = inlineStyles.color;
      }
      textParts.push(styles);
      return `__PART_${textParts.length - 1}__`;
    });

    // Handle italic
    text = text.replace(/<(em|i)([^>]*)>(.*?)<\/\1>/gi, (match, tag, attrs, innerText) => {
      const styles: TextPart = { text: innerText.replace(/<[^>]*>/g, ''), italics: true };
      const styleMatch = attrs?.match(/style="([^"]*)"/);
      if (styleMatch) {
        const inlineStyles = parseInlineStyle(styleMatch[1]);
        if (inlineStyles.fontSize) styles.fontSize = inlineStyles.fontSize;
        if (inlineStyles.color) styles.color = inlineStyles.color;
      }
      textParts.push(styles);
      return `__PART_${textParts.length - 1}__`;
    });

    // Handle underline
    text = text.replace(/<u([^>]*)>(.*?)<\/u>/gi, (match, attrs, innerText) => {
      const styles: TextPart = { text: innerText.replace(/<[^>]*>/g, ''), decoration: 'underline' };
      const styleMatch = attrs?.match(/style="([^"]*)"/);
      if (styleMatch) {
        const inlineStyles = parseInlineStyle(styleMatch[1]);
        if (inlineStyles.fontSize) styles.fontSize = inlineStyles.fontSize;
        if (inlineStyles.color) styles.color = inlineStyles.color;
      }
      textParts.push(styles);
      return `__PART_${textParts.length - 1}__`;
    });

    // Clean remaining HTML tags
    text = text.replace(/<[^>]*>/g, '');

    // Build final result
    if (textParts.length === 0) {
      return text;
    }

    const result: (string | TextPart)[] = [];
    let lastIndex = 0;

    text.replace(/__PART_(\d+)__/g, (match, index, offset) => {
      if (offset > lastIndex) {
        const plainText = text.substring(lastIndex, offset);
        if (plainText) {
          result.push(plainText);
        }
      }
      result.push(textParts[parseInt(index)]);
      lastIndex = offset + match.length;
      return match;
    });

    if (lastIndex < text.length) {
      const remainingText = text.substring(lastIndex);
      if (remainingText) {
        result.push(remainingText);
      }
    }

    return result.length === 1 ? (result[0] as string) : (result as Content[]);
  };

  // Parse HTML content in order of appearance
  const cleanHtml = html
    .replace(/&nbsp;/g, ' ')
    .replace(/<br\s*\/?>/gi, '\n')
    .trim();

  // Split HTML into tokens while preserving order
  const tokens: string[] = [];
  const currentHtml = cleanHtml;
  const tagRegex = /<(\/?[a-z0-9]+)([^>]*)>/gi;
  let lastIndex = 0;

  currentHtml.replace(tagRegex, (match, tagName, attributes, offset) => {
    if (offset > lastIndex) {
      tokens.push(currentHtml.substring(lastIndex, offset));
    }
    tokens.push(match);
    lastIndex = offset + match.length;
    return match;
  });

  if (lastIndex < currentHtml.length) {
    tokens.push(currentHtml.substring(lastIndex));
  }

  // Process tokens
  let currentList: Content[] | null = null;
  let listType: 'ul' | 'ol' | null = null;
  let listIndex = 1;

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i];

    // Handle paragraph
    if (token.match(/^<p([^>]*)>/i)) {
      const attributes = token.match(/<p([^>]*)>/i)?.[1] || '';
      let innerContent = '';
      let j = i + 1;
      while (j < tokens.length && !tokens[j].match(/^<\/p>/i)) {
        innerContent += tokens[j];
        j++;
      }
      if (tokens[j]?.match(/^<\/p>/i)) {
        i = j;
      }

      if (!innerContent || innerContent.trim() === '' || innerContent === '\n') {
        content.push({ text: '', margin: [0, 3, 0, 3] });
        continue;
      }

      let styles: StyleProperties & { margin: number[] } = { margin: [0, 3, 0, 6] };
      const styleMatch = attributes.match(/style="([^"]*)"/);
      if (styleMatch) {
        styles = { ...styles, ...parseInlineStyle(styleMatch[1]) };
      }

      const classMatch = attributes.match(/class="([^"]*)"/);
      if (classMatch) {
        const classes = classMatch[1].split(' ');
        classes.forEach(className => {
          switch (className.trim()) {
            case 'ql-align-center':
              styles.alignment = 'center';
              break;
            case 'ql-align-right':
              styles.alignment = 'right';
              break;
            case 'ql-align-justify':
              styles.alignment = 'justify';
              break;
            case 'ql-align-left':
              styles.alignment = 'left';
              break;
            case 'ql-indent-1':
              styles.margin = [20, styles.margin?.[1] || 3, 0, styles.margin?.[3] || 6];
              break;
            case 'ql-indent-2':
              styles.margin = [40, styles.margin?.[1] || 3, 0, styles.margin?.[3] || 6];
              break;
            case 'ql-indent-3':
              styles.margin = [60, styles.margin?.[1] || 3, 0, styles.margin?.[3] || 6];
              break;
          }
        });
      }

      const textContent = parseTextContent(innerContent);
      content.push({
        text: textContent,
        ...styles,
      } as Content);
    }
    // Handle headings (h1-h6)
    else if (token.match(/^<h([1-6])([^>]*)>/i)) {
      const level = parseInt(token.match(/^<h([1-6])/i)?.[1] || '1');
      const attributes = token.match(/<h[1-6]([^>]*)>/i)?.[1] || '';
      let innerContent = '';
      let j = i + 1;
      while (j < tokens.length && !tokens[j].match(/^<\/h[1-6]>/i)) {
        innerContent += tokens[j];
        j++;
      }
      if (tokens[j]?.match(/^<\/h[1-6]>/i)) {
        i = j;
      }

      const styleMatch = attributes.match(/style="([^"]*)"/);

      let styles: StyleProperties & {
        fontSize: number;
        bold: boolean;
        margin: number[];
        alignment: string;
      } = {
        fontSize: level === 1 ? 18 : level === 2 ? 16 : level === 3 ? 14 : 12,
        bold: true,
        margin: [0, 12, 0, 8],
        alignment: 'left',
      };

      if (styleMatch) {
        styles = { ...styles, ...parseInlineStyle(styleMatch[1]) };
      }

      const classMatch = attributes.match(/class="([^"]*)"/);
      if (classMatch) {
        const classes = classMatch[1].split(' ');
        classes.forEach(className => {
          switch (className.trim()) {
            case 'ql-align-center':
              styles.alignment = 'center';
              break;
            case 'ql-align-right':
              styles.alignment = 'right';
              break;
            case 'ql-align-justify':
              styles.alignment = 'justify';
              break;
            case 'ql-align-left':
              styles.alignment = 'left';
              break;
          }
        });
      }

      const textContent = parseTextContent(innerContent);
      content.push({
        text: textContent,
        ...styles,
      } as Content);
    }
    // Handle unordered list start
    else if (token.match(/^<ul([^>]*)>/i)) {
      currentList = [];
      listType = 'ul';
      listIndex = 1;
    }
    // Handle ordered list start
    else if (token.match(/^<ol([^>]*)>/i)) {
      currentList = [];
      listType = 'ol';
      listIndex = 1;
    }
    // Handle list item
    else if (token.match(/^<li([^>]*)>/i)) {
      const attributes = token.match(/<li([^>]*)>/i)?.[1] || '';
      let innerContent = '';
      let j = i + 1;
      while (j < tokens.length && !tokens[j].match(/^<\/li>/i)) {
        innerContent += tokens[j];
        j++;
      }
      if (tokens[j]?.match(/^<\/li>/i)) {
        i = j;
      }

      let styles: StyleProperties & { margin: number[] } = { margin: [20, 2, 0, 4] };
      const styleMatch = attributes.match(/style="([^"]*)"/);
      if (styleMatch) {
        styles = { ...styles, ...parseInlineStyle(styleMatch[1]) };
      }

      const textContent = parseTextContent(innerContent);
      const prefix = listType === 'ul' ? '• ' : `${listIndex}. `;
      currentList?.push({
        text: `${prefix}${typeof textContent === 'string' ? textContent : ''}`,
        ...styles,
      } as Content);
      if (listType === 'ol') listIndex++;
    }
    // Handle list end
    else if (token.match(/^<\/(ul|ol)>/i)) {
      if (currentList) {
        content.push(...currentList);
        currentList = null;
        listType = null;
        listIndex = 1;
      }
    }
    // Handle plain text
    else if (!token.match(/^<(\/)?[a-z0-9]+/i)) {
      const textContent = parseTextContent(token);
      if (typeof textContent === 'string' && textContent.trim()) {
        content.push({ text: textContent, margin: [0, 0, 0, 0] });
      }
    }
  }

  // If no structured HTML, treat as plain text
  if (content.length === 0) {
    const textContent = html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .trim();
    if (textContent) {
      return [{ text: textContent, margin: [0, 0, 0, 0] }];
    }
  }

  return content.length > 0 ? content : [{ text: '' }];
};

// Enhanced PDF export function for Quill content with fixed signatures
export const exportQuillContentToPDFWithSignatures = async (
  htmlContent: string,
  returnBlob: boolean = false
): Promise<void | Blob> => {
  try {
    // Dynamic import to avoid SSR issues
    const pdfMake = (await import('pdfmake/build/pdfmake')).default as PdfMakeStatic;
    const vfs = (await import('pdfmake/build/vfs_fonts')).default as VfsType;

    // Set up fonts
    pdfMake.vfs = vfs.vfs;

    // Parse Quill HTML content
    const content = parseQuillHtmlToContent(htmlContent);

    // Add signature section (fixed, two signatures in one row)
    const signatureSection: Content = {
      table: {
        widths: ['*', '*'],
        body: [
          [
            {
              stack: [
                {
                  text: 'BÊN CHO THUÊ',
                  bold: true,
                  alignment: 'center',
                  fontSize: 14,
                  margin: [0, 0, 0, 10],
                },
                {
                  text: '(Ký tên, đóng dấu)',
                  italics: true,
                  alignment: 'center',
                  fontSize: 10,
                  margin: [0, 0, 0, 5],
                },
                {
                  text: '\n',
                  alignment: 'center',
                },
                {
                  text: '________________________________',
                  alignment: 'center',
                  margin: [0, 0, 0, 5],
                },
              ],
              border: [false, false, false, false],
            },
            {
              stack: [
                {
                  text: 'BÊN THUÊ',
                  bold: true,
                  alignment: 'center',
                  fontSize: 14,
                  margin: [0, 0, 0, 10],
                },
                {
                  text: '(Ký tên)',
                  italics: true,
                  alignment: 'center',
                  fontSize: 10,
                  margin: [0, 0, 0, 5],
                },
                {
                  text: '\n',
                  alignment: 'center',
                },
                {
                  text: '________________________________',
                  alignment: 'center',
                  margin: [0, 0, 0, 5],
                },
              ],
              border: [false, false, false, false],
            },
          ],
        ],
      },
      layout: 'noBorders',
      margin: [0, 30, 0, 0],
    };

    // Enhanced document definition
    const documentDefinition: TDocumentDefinitions = {
      content: [
        ...(content.length > 0 ? content : [{ text: 'Nội dung trống' }]),
        signatureSection, // Add signature section at the end
      ],
      pageSize: 'A4',
      pageMargins: [60, 80, 60, 80],
      defaultStyle: {
        font: 'Roboto',
        fontSize: 12,
        lineHeight: 1.1,
        color: '#1f2937',
      },
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          alignment: 'center',
          margin: [0, 0, 0, 20],
          color: '#1f2937',
        },
        subheader: {
          fontSize: 14,
          bold: true,
          margin: [0, 10, 0, 5],
          color: '#374151',
        },
        articleHeader: {
          fontSize: 13,
          bold: true,
          margin: [0, 12, 0, 8],
          color: '#374151',
        },
        signatureTitle: {
          fontSize: 16,
          bold: true,
          color: '#1f2937',
        },
      },
      // header: (currentPage: number, pageCount: number) => {
      //   return {
      //     text: `HỢP ĐỒNG THUÊ CĂN HỘ - Trang ${currentPage} / ${pageCount}`,
      //     alignment: 'center',
      //     fontSize: 9,
      //     margin: [0, 25, 0, 0],
      //     color: '#9ca3af',
      //   };
      // },
      footer: (currentPage: number, pageCount: number) => {
        return {
          columns: [
            {
              text: `Ngày tạo: ${new Date().toLocaleDateString('vi-VN')}`,
              fontSize: 8,
              alignment: 'left',
              margin: [50, 0, 0, 0],
              color: '#9ca3af',
            },
            {
              text: `Trang ${currentPage} / ${pageCount}`,
              fontSize: 8,
              alignment: 'right',
              margin: [0, 0, 50, 0],
              color: '#9ca3af',
            },
          ],
          margin: [0, 15, 0, 0],
        };
      },
    };

    // Create PDF
    const pdf = pdfMake.createPdf(documentDefinition);
    const currentDate = new Date();
    const dateString = currentDate.toISOString().split('T')[0];
    const timeString = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');
    const fileName = `hop-dong-thue-can-ho-quill-${dateString}-${timeString}.pdf`;

    if (returnBlob) {
      // Return blob for upload purposes
      return new Promise<Blob>((resolve, _reject) => {
        pdf.getBlob((blob: Blob) => {
          resolve(blob);
        });
      });
    } else {
      // Download PDF for user
      pdf.download(fileName);
    }
  } catch (error) {
    console.error('Error creating PDF from Quill content:', error);
    throw new Error('Không thể tạo file PDF từ nội dung Quill editor. Vui lòng thử lại.');
  }
};
