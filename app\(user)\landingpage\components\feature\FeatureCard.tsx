import Link from 'next/link';
import React from 'react';

interface FeatureCardProps {
  image: string;
  title: string;
  description: string;
  buttonText: string;
}

export default function FeatureCard({ image, title, description, buttonText }: FeatureCardProps) {
  return (
    <div className="bg-white rounded-3xl shadow-lg p-8 flex flex-col items-center text-center transition hover:shadow-2xl">
      <img src={image} alt={title} className="h-32 w-32 mb-6 object-contain" />
      <h3 className="text-2xl font-bold mb-3 text-gray-900">{title}</h3>
      <p className="text-gray-600 mb-6 text-base">{description}</p>
      <Link
        href="/properties"
        className="px-6 py-2 border-2 border-blue-600 text-blue-600 font-semibold rounded-xl hover:bg-blue-50 transition text-lg"
      >
        {buttonText}
      </Link>
    </div>
  );
}
