import { SiteHeader } from '@/components/common/siteHeader';
import { SidebarInset } from '@/components/ui/sidebar';
import { DealGrid } from './components/dealGrid';
import { AddDealSheet } from './components/addDealSheet';

export default function SalesPage() {
  return (
    <SidebarInset>
      <SiteHeader title="Quản lý giao dịch" />
      <div className="flex min-h-screen flex-col bg-background">
        <main className="flex-1 w-full px-6 py-6 mx-auto">
          <div className="space-y-8">
            {/* Header with Add Deal Button */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Quản lí giao dịch của tôi</h1>
                <p className="text-muted-foreground">
                  Quản lý và theo dõi tất cả các giao dịch từ khách hàng tiềm năng
                </p>
              </div>
              <AddDealSheet />
            </div>

            <DealGrid />
          </div>
        </main>
      </div>
    </SidebarInset>
  );
}
