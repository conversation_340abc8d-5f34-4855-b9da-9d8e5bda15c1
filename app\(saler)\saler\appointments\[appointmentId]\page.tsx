// app/appointments/[id]/page.tsx
'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CalendarDays,
  MapPin,
  User,
  Phone,
  Mail,
  Clock,
  ArrowLeft,
  Edit3,
  Trash2,
  MessageSquare,
  Building,
  Calendar,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';

import { toast } from 'sonner';
import { useAppointmentDetail } from '@/hooks/useAppointment';
import { RescheduleDialog } from '@/app/(user)/appointments/components/appointment/reschedule-dialog';
import { CancelAppointmentDialog } from '@/app/(user)/appointments/components/appointment/cancel-appointment-dialog';
import { formatDate } from '@/utils/dates/formatDate';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

export default function AppointmentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const appointmentId = params.appointmentId as string;

  const [rescheduleOpen, setRescheduleOpen] = useState(false);
  const [cancelOpen, setCancelOpen] = useState(false);

  const {
    data: appointmentResponse,
    isLoading,
    error,
    refetch,
  } = useAppointmentDetail(appointmentId);

  const appointment = appointmentResponse?.data;

  // Add formatTime utility (copied from dialog)
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getInitials = (name?: string) => {
    if (!name) return '';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleRescheduleSuccess = () => {
    setRescheduleOpen(false);
    refetch();
    toast.success('Đã chuyển lịch hẹn thành công');
  };

  const handleCancelSuccess = () => {
    setCancelOpen(false);
    refetch();
    toast.success('Đã hủy cuộc hẹn thành công');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header Skeleton */}
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-10 rounded-md" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>

          {/* Cards Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Skeleton className="h-64 w-full rounded-lg" />
              <Skeleton className="h-48 w-full rounded-lg" />
            </div>
            <div className="space-y-6">
              <Skeleton className="h-32 w-full rounded-lg" />
              <Skeleton className="h-48 w-full rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Quay lại
            </Button>
          </div>

          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error?.message || 'Không thể tải thông tin cuộc hẹn'}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 space-y-6">
        {/* Header */}
        <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Chi tiết cuộc hẹn</h1>
                <p className="text-sm text-gray-600 font-mono">ID: #{appointment.id.slice(-8)}</p>
              </div>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Date & Time */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Thời gian cuộc hẹn
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Ngày hẹn</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(appointment.date)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Giờ hẹn</p>
                      <p className="text-sm text-muted-foreground">
                        {formatTime(appointment.date)}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground pt-2 border-t">
                  <span className="font-medium">Tạo lúc:</span> {formatDate(appointment.createdAt)}{' '}
                  - {formatTime(appointment.createdAt)}
                </div>
              </CardContent>
            </Card>

            {/* Location & Property */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Địa điểm & Bất động sản
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Địa chỉ</p>
                    <p className="text-sm text-muted-foreground mt-1">{appointment.location}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Mã bất động sản</p>
                    <p className="text-sm text-muted-foreground font-mono mt-1">
                      {appointment.propertyId}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Messages */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Tin nhắn
                  <Badge variant="secondary" className="ml-2">
                    {appointment.messages?.length || 0}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {appointment.messages && appointment.messages.length > 0 ? (
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {appointment.messages.map((message, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-800">{message}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="font-medium">Chưa có tin nhắn nào</p>
                    <p className="text-sm">Tin nhắn sẽ hiển thị tại đây</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Saler Information */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Nhân viên bán hàng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>{getInitials(appointment.saler?.fullName)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{appointment.saler?.fullName}</p>
                    <p className="text-xs text-muted-foreground font-mono">
                      ID: {appointment.saler?.id?.slice(-8)}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{appointment.saler?.email}</span>
                  </div>
                  {appointment.saler?.phoneNumber ? (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.saler.phoneNumber}</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground italic">Chưa có số điện thoại</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Khách hàng
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>{getInitials(appointment.customer?.fullName)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{appointment.customer?.fullName}</p>
                    <p className="text-xs text-muted-foreground font-mono">
                      ID: {appointment.customer?.id?.slice(-8)}
                    </p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{appointment.customer?.email}</span>
                  </div>
                  {appointment.customer?.phoneNumber ? (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.customer.phoneNumber}</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground italic">Chưa có số điện thoại</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="min-h-[220px]">
              <CardHeader>
                <CardTitle>Hành động</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Placeholder for complete action */}
                <Button className="w-full" disabled>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Hoàn thành cuộc hẹn
                </Button>
                <Button
                  className="w-full"
                  variant="secondary"
                  onClick={() => setRescheduleOpen(true)}
                  disabled={
                    appointment.status.toLowerCase() === 'completed' ||
                    appointment.status.toLowerCase() === 'cancelled'
                  }
                >
                  <CalendarDays className="h-4 w-4 mr-2" />
                  Chuyển lịch hẹn
                </Button>
                <Button className="w-full" variant="outline" disabled>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Chỉnh sửa thông tin
                </Button>
                <Button className="w-full" variant="outline" disabled>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Gửi tin nhắn
                </Button>
                <Separator />
                <Button
                  className="w-full"
                  variant="destructive"
                  onClick={() => setCancelOpen(true)}
                  disabled={
                    appointment.status.toLowerCase() === 'completed' ||
                    appointment.status.toLowerCase() === 'cancelled'
                  }
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Hủy cuộc hẹn
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Dialogs */}
        <RescheduleDialog
          open={rescheduleOpen}
          onOpenChange={setRescheduleOpen}
          appointment={appointment}
          onSuccess={handleRescheduleSuccess}
        />
        <CancelAppointmentDialog
          open={cancelOpen}
          onOpenChange={setCancelOpen}
          appointment={appointment}
          onSuccess={handleCancelSuccess}
        />
      </div>
    </div>
  );
}
