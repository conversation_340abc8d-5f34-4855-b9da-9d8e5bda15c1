'use client';

import { Button } from '@/components/ui/button';
import SelectedPropertyCard from '@/components/SelectedPropertyCard';
import type { Property } from '@/lib/api/services/fetchProperty';

interface BottomTourSelectionProps {
  selectedProperties: Property[];
  onRemove: (id: string) => void;
  onCreateTour: () => void;
}

export default function BottomTourSelection({
  selectedProperties,
  onRemove,
  onCreateTour,
}: BottomTourSelectionProps) {
  const maxSlots = 5;
  const emptySlots = maxSlots - selectedProperties.length;

  if (selectedProperties.length === 0) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-lg">
      <div className="container mx-auto px-4 py-3 flex items-center justify-center gap-3">
        {selectedProperties.map(property => (
          <SelectedPropertyCard key={property.id} property={property} onRemove={onRemove} />
        ))}

        {Array.from({ length: emptySlots }).map((_, index) => (
          <div
            key={`empty-${index}`}
            className="flex-shrink-0 w-32 h-24 border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-default"
          >
            <p className="text-xs text-gray-400 text-center px-2">Chọn thêm căn hộ</p>
          </div>
        ))}

        <Button
          onClick={onCreateTour}
          className="bg-green-600 hover:bg-green-700 text-white text-sm rounded-md px-3 py-2"
          disabled={selectedProperties.length === 0}
        >
          Tạo lịch
        </Button>
      </div>
    </div>
  );
}
