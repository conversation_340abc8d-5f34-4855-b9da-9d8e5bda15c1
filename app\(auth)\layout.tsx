import type { Metadata } from 'next';

import '../globals.css';

import Link from 'next/link';
import Image from 'next/image';
import AuthImage from './components/auth-image';

export const metadata: Metadata = {
  title: 'Revoland - Đăng nhập',
  description: 'Revoland - Đăng nhập',
};

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <main className=" ">
        <div className="grid min-h-svh lg:grid-cols-2">
          <div className="relative flex flex-col gap-4 p-6 md:p-10 overflow-hidden ">
            {/* <AuthDecoration /> */}
            <div className="relative flex flex-1 items-center justify-center">
              <div className="w-full max-w-md">
                <div className="flex justify-center mb-6">
                  <Link href="/" className="flex items-center">
                    <Image
                      src="/logo_revoland_red.png"
                      alt="Revoland icon"
                      width={60}
                      height={60}
                      priority
                      className="rounded-md"
                    />
                  </Link>
                </div>
                {children}
              </div>
            </div>
          </div>
          <AuthImage />
        </div>
      </main>
    </>
  );
}
