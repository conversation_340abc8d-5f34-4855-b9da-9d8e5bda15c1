import { Button } from '@/components/ui/button';
import SelectedPropertyCard from '@/components/SelectedPropertyCard';
import type { Property } from '@/lib/api/services/fetchProperty';
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface BottomComparisonProps {
  selectedProperties: Property[];
  onRemove: (id: string) => void;
  onCreateTour?: (properties: Property[]) => void;
}

export default function BottomComparison({
  selectedProperties,
  onRemove,
  onCreateTour,
}: BottomComparisonProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const maxSlots = 5;
  const emptySlots = maxSlots - selectedProperties.length;

  if (selectedProperties.length === 0) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-lg">
      <div className="container mx-auto px-4 py-3">
        {!isCollapsed ? (
          <div className="flex items-center justify-center p-4 gap-3 w-full overflow-x-auto">
            {selectedProperties.map(property => (
              <SelectedPropertyCard key={property.id} property={property} onRemove={onRemove} />
            ))}

            {Array.from({ length: emptySlots }).map((_, index) => (
              <div
                key={`empty-${index}`}
                className="flex-shrink-0 w-32 h-24 border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-default rounded-lg bg-white"
              >
                <p className="text-xs text-gray-400 text-center px-2">Chọn thêm căn hộ</p>
              </div>
            ))}

            {/* Action Buttons */}
            <div className="ml-10 flex flex-col items-center gap-2">
              <div className=" flex gap-2 items-center">
                <Button variant="outline" size="sm" onClick={() => setIsCollapsed(true)}>
                  Thu gọn
                </Button>
                {selectedProperties.length >= 2 ? (
                  <Link
                    href={`/comparison/${selectedProperties.map(p => p.id).join('-vs-')}`}
                    target="_blank"
                    className="bg-red-600 hover:bg-red-700 text-white text-sm rounded-md px-3 py-1.5"
                  >
                    So sánh
                  </Link>
                ) : (
                  <Button size="sm" className="bg-red-600 hover:bg-red-700" disabled>
                    So sánh
                  </Button>
                )}
                {onCreateTour && selectedProperties.length >= 1 && (
                  <Button
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-white"
                    onClick={() => onCreateTour(selectedProperties)}
                  >
                    Tạo tour
                  </Button>
                )}
              </div>
              <div className="text-sm text-gray-600">
                Đã chọn {selectedProperties.length} sản phẩm
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <div className="text-sm text-gray-600">
                Đã chọn {selectedProperties.length} sản phẩm
              </div>
              <div className="flex -space-x-2">
                {selectedProperties.slice(0, 3).map((property, index) => (
                  <div
                    key={property.id}
                    className="relative w-8 h-8 rounded-full overflow-hidden border-2 border-white"
                    style={{ zIndex: 3 - index }}
                  >
                    <Image
                      src={property.imageUrls[0] || '/placeholder.svg'}
                      alt={property.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                ))}

                {selectedProperties.length > 3 && (
                  <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium">
                    +{selectedProperties.length - 3}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsCollapsed(false)}>
                Mở rộng
              </Button>
              {selectedProperties.length >= 2 ? (
                <Link
                  href={`/comparison/${selectedProperties.map(p => p.id).join('-vs-')}`}
                  target="_blank"
                  className="bg-red-600 hover:bg-red-700 text-white text-sm rounded-md px-3 py-1.5"
                >
                  So sánh
                </Link>
              ) : (
                <Button size="sm" className="bg-red-600 hover:bg-red-700" disabled>
                  So sánh
                </Button>
              )}
              {onCreateTour && selectedProperties.length >= 1 && (
                <Button
                  size="sm"
                  className="bg-red-500 hover:bg-red-700 text-white"
                  onClick={() => onCreateTour(selectedProperties)}
                >
                  Tạo tour
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
