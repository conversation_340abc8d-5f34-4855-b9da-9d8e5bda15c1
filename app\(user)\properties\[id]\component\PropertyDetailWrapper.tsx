'use client';

import { APIProvider } from '@vis.gl/react-google-maps';
import { Property } from '@/lib/api/services/fetchProperty';
import PropertyDetail from './PropertyDetail';
import Footer from '@/components/Footer';
import { Providers } from '@/lib/providers';

interface PropertyDetailWrapperProps {
  property: Property;
}

export default function PropertyDetailWrapper({ property }: PropertyDetailWrapperProps) {
  return (
    <Providers property={property}>
      <div className=" ">
        <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}>
          <PropertyDetail property={property} />
          <Footer />
        </APIProvider>
      </div>
    </Providers>
  );
}
