'use client';

import * as React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { useUserProfile } from '@/hooks/useUsers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  UserCircleIcon,
  LogOutIcon,
  Menu,
  LayoutDashboardIcon,
  UsersIcon,
  TrendingUpIcon,
  HeartIcon,
  CalendarIcon,
  PlusIcon,
  BuildingIcon,
  ChevronRightIcon,
  ClockIcon,
  Search,
  Home,
  MapPinPlus,
} from 'lucide-react';
import { navigationData, DropdownContent } from './NavigationBar';
import { motion } from 'framer-motion';
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON>itle, SheetTrigger } from '@/components/ui/sheet';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from './ui/scroll-area';
import EnhancedSearchAutocomplete from './enhancedSearchAutocomplete';
import { useIsMobile } from '@/hooks/useMobile';
import { useScrollDirection } from '@/hooks/useScrollDirection';
import SearchFilter from '@/app/(user)/properties/components/SearchFilter';

// Bottom Navigation Component
function BottomNavigation() {
  const { isAuthenticated, logout } = useAuth();
  const { data: profileData } = useUserProfile();
  const [isAccountOpen, setIsAccountOpen] = React.useState(false);
  const [isNavVisible, setIsNavVisible] = React.useState(true);
  const { isScrollingDown, isScrollingUp, isAtTop } = useScrollDirection(10);
  const pathname = usePathname();

  // Update navigation visibility based on scroll direction
  React.useEffect(() => {
    if (isAtTop) {
      setIsNavVisible(true);
    } else if (isScrollingDown) {
      setIsNavVisible(false);
    } else if (isScrollingUp) {
      setIsNavVisible(true);
    }
  }, [isScrollingDown, isScrollingUp, isAtTop]);

  // Get user data from profile response, with fallback for unauthenticated users
  const user =
    profileData?.profile && isAuthenticated
      ? {
          name: profileData.profile.fullName,
          email: profileData.profile.email,
          avatar: profileData.profile.avatar || '',
        }
      : {
          name: '',
          email: '',
          avatar: '',
        };

  // Create initials from name for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(user.name);

  // Function to check if current path matches navigation item
  const isActiveNavItem = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }

    // For /myrevo route, check if pathname starts with /myrevo
    if (href.startsWith('/myrevo')) {
      return pathname?.startsWith('/myrevo');
    }

    // For other routes, check if pathname starts with the href
    return pathname?.startsWith(href);
  };

  const navItems = isAuthenticated
    ? [
        {
          icon: Home,
          label: 'Trang chủ',
          href: '/',
          active: isActiveNavItem('/'),
        },
        {
          icon: Search,
          label: 'Tìm kiếm',
          href: '/properties',
          active: isActiveNavItem('/properties'),
        },
        {
          icon: MapPinPlus,
          label: 'Đăng tin',
          href: '/saler/property/action',
          active: isActiveNavItem('/saler/property/action'),
        },
        {
          icon: HeartIcon,
          label: 'Yêu thích',
          href: '/myrevo?tab=saved',
          active: isActiveNavItem('/myrevo'),
        },
        {
          icon: UserCircleIcon,
          label: 'Tài khoản',
          href: '#',
          isAccount: true,
          active: false,
        },
      ]
    : [
        {
          icon: Home,
          label: 'Trang chủ',
          href: '/',
          active: isActiveNavItem('/'),
        },
        {
          icon: Search,
          label: 'Tìm kiếm',
          href: '/properties',
          active: isActiveNavItem('/properties'),
        },
        {
          icon: MapPinPlus,
          label: 'Đăng tin',
          href: '/login',
          active: isActiveNavItem('/login'),
        },
        {
          icon: UserCircleIcon,
          label: 'Đăng nhập',
          href: '/login',
          active: isActiveNavItem('/login'),
        },
      ];

  return (
    <>
      {/* Bottom Navigation */}
      <motion.div
        className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-t border-gray-200 md:hidden shadow-lg"
        initial={{ y: 0, opacity: 1 }}
        animate={{
          y: isNavVisible ? 0 : 120,
          opacity: isNavVisible ? 1 : 0.8,
        }}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 35,
          mass: 0.8,
        }}
        style={{
          willChange: 'transform, opacity',
        }}
      >
        <div className={`flex items-center justify-around py-1 ${isAuthenticated ? '' : 'px-8'}`}>
          {navItems.map((item, index) => (
            <div key={index} className="flex flex-col items-center">
              {item.isAccount && isAuthenticated ? (
                <DropdownMenu open={isAccountOpen} onOpenChange={setIsAccountOpen}>
                  <DropdownMenuTrigger asChild>
                    <div className="flex flex-col items-center gap-1 p-1 rounded-lg transition-colors hover:bg-gray-50 cursor-pointer">
                      <Avatar className="size-6">
                        <AvatarImage src={user.avatar} alt={user.name} className="object-cover" />
                        <AvatarFallback className="bg-red-500/10 text-red-500 text-[0.60rem]">
                          {initials}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-[0.60rem] text-muted-foreground">{item.label}</span>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-64 mb-2" align="center" side="top">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{user.name}</p>
                        <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />

                    {/* Quick Actions Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Tin đăng
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/property/action" className="cursor-pointer">
                          <PlusIcon className="mr-2 h-4 w-4" />
                          Đăng tin bất động sản
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/property" className="cursor-pointer">
                          <BuildingIcon className="mr-2 h-4 w-4" />
                          Quản lý bất động sản
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />

                    {/* Dashboard Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Quản lý
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/dashboard" className="cursor-pointer">
                          <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                          Dashboard
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/lead" className="cursor-pointer">
                          <UsersIcon className="mr-2 h-4 w-4" />
                          Quản lý khách hàng
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/saler/sales" className="cursor-pointer">
                          <TrendingUpIcon className="mr-2 h-4 w-4" />
                          Quản lý giao dịch
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />

                    {/* User Account Section */}
                    <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                      Tài khoản
                    </DropdownMenuLabel>
                    <DropdownMenuGroup>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=account-settings" className="cursor-pointer">
                          <UserCircleIcon className="mr-2 h-4 w-4" />
                          Thông tin cá nhân
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=saved-homes" className="cursor-pointer">
                          <HeartIcon className="mr-2 h-4 w-4" />
                          Bất động sản đã lưu
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=manage-tours" className="cursor-pointer">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          Lịch hẹn xem nhà
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/myrevo?tab=recently-viewed" className="cursor-pointer">
                          <ClockIcon className="mr-2 h-4 w-4" />
                          Đã xem gần đây
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuGroup>

                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => logout()} className="cursor-pointer">
                      <LogOutIcon className="mr-2 h-4 w-4" />
                      Đăng xuất
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link
                  href={item.href}
                  className={`flex flex-col items-center gap-1 p-1 rounded-lg transition-colors `}
                >
                  <item.icon
                    className={`size-6 stroke-[1.5] ${item.active ? 'text-red-500' : 'text-muted-foreground'}`}
                  />
                  <span
                    className={`text-[0.62rem] ${item.active ? 'text-red-500 font-medium' : 'text-muted-foreground'}`}
                  >
                    {item.label}
                  </span>
                </Link>
              )}
            </div>
          ))}
        </div>
      </motion.div>
    </>
  );
}

export default function Header() {
  const { isAuthenticated, logout } = useAuth();
  const { data: profileData, error: profileError } = useUserProfile();
  const isMobile = useIsMobile();
  const pathname = usePathname();

  // Check if we're on a property detail page
  const isPropertyDetailPage =
    pathname?.startsWith('/properties/') && pathname.split('/').length >= 3;

  // Check if we're on a property listing page
  const isPropertyListingPage = pathname === '/properties';

  // Debug logging
  console.log('Header Debug:', { pathname, isPropertyListingPage, isMobile });

  // Dropdown state management
  const [activeDropdown, setActiveDropdown] = React.useState<string | null>(null);

  const handleMouseLeave = () => {
    setActiveDropdown(null);
  };

  console.log(profileError);
  // Get user data from profile response, with fallback for unauthenticated users
  const user =
    profileData?.profile && isAuthenticated
      ? {
          name: profileData.profile.fullName,
          email: profileData.profile.email,
          avatar: profileData.profile.avatar || '',
        }
      : {
          name: '',
          email: '',
          avatar: '',
        };

  // Create initials from name for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(user.name);

  return (
    <>
      <header
        className={`w-full bg-background text-foreground sticky top-0 z-50 flex-shrink-0 shadow-sm`}
      >
        <div className="container mx-auto px-4 py-2 md:py-4">
          {/* Mobile Layout */}
          <div className="flex md:hidden items-center gap-2">
            {/* Logo - Flexible on mobile */}
            <div className="flex items-center flex-shrink-0">
              <Link href="/" className="flex items-center">
                <Image
                  src="/logo_revoland_red.png"
                  alt="Revoland icon"
                  width={40}
                  height={40}
                  priority
                  className="rounded-md"
                />
                <span className="lg:block hidden text-2xl font-medium text-red-500">Revoland</span>
              </Link>
            </div>

            {/* Mobile Search - Center */}
            {isMobile && (
              <div className="flex-1 flex items-center gap-2">
                <div className="flex-1">
                  <EnhancedSearchAutocomplete />
                </div>
                {isPropertyListingPage && (
                  <div className="flex-shrink-0">
                    <SearchFilter />
                  </div>
                )}
              </div>
            )}

            {/* Mobile Menu Sheet - Hide on Property Listing Page */}
            {!isPropertyListingPage && (
              <Sheet>
                <SheetTrigger asChild className="md:hidden">
                  <Button variant="outline" size="icon" className="rounded-full">
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                  <SheetHeader>
                    <SheetTitle>Menu</SheetTitle>
                  </SheetHeader>
                  <ScrollArea className="h-full">
                    <div className="flex flex-col gap-2 p-2 pt-0 pb-10">
                      {/* Mobile Navigation Menu */}
                      {Object.entries(navigationData).map(([key, data], index) => (
                        <motion.div
                          key={key}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                        >
                          <Collapsible defaultOpen={false} className="space-y-2">
                            <CollapsibleTrigger className="flex items-center justify-between w-full py-2 px-4 text-left rounded-md hover:bg-accent transition-colors">
                              <span className="font-normal text-base capitalize">{data.title}</span>
                              <ChevronRightIcon className='size-4 shrink-0 transition-transform [[data-state="open"]>&]:rotate-90' />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down overflow-hidden transition-all duration-300">
                              <div className="p-2 space-y-4">
                                {/* Column 1 */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.1 }}
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    {data.column1.title}
                                  </h4>
                                  <div className="space-y-1">
                                    {data.column1.items.map((item, itemIndex) => (
                                      <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                          duration: 0.3,
                                          delay: 0.2 + itemIndex * 0.05,
                                        }}
                                      >
                                        <Link
                                          href={item.href}
                                          className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors group"
                                        >
                                          <span className="font-medium text-sm text-foreground group-hover:text-red-600">
                                            {item.title}
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </motion.div>

                                {/* Column 2 */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.3 }}
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    {data.column2.title}
                                  </h4>
                                  <div className="space-y-1">
                                    {data.column2.items.map((item, itemIndex) => (
                                      <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{
                                          duration: 0.3,
                                          delay: 0.4 + itemIndex * 0.04,
                                        }}
                                      >
                                        <Link
                                          href={item.href}
                                          className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-50 transition-colors group"
                                        >
                                          <span className="font-medium text-sm text-foreground group-hover:text-red-600">
                                            {item.title}
                                          </span>
                                        </Link>
                                      </motion.div>
                                    ))}
                                  </div>
                                </motion.div>

                                {/* Spotlight Section */}
                                {/* <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: 0.5 }}
                                className="border-t border-gray-200 pt-4"
                              >
                                <h4 className="font-normal text-sm text-muted-foreground mb-3">
                                  Spotlight
                                </h4>
                                <Link href={data.spotlight.href} className="block group">
                                  <div className="space-y-3">
                                    <h5 className="font-medium text-sm text-gray-900 group-hover:text-red-600 transition-colors leading-tight">
                                      {data.spotlight.title}
                                    </h5>
                                    <p className="text-xs text-gray-600 leading-relaxed line-clamp-2">
                                      {data.spotlight.description}
                                    </p>
                                    <div className="relative aspect-video rounded-lg overflow-hidden">
                                      <Image
                                        src={data.spotlight.image}
                                        alt={data.spotlight.title}
                                        fill
                                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                                      />
                                    </div>
                                    <div className="flex items-center gap-2 text-xs text-gray-500">
                                      <ClockIcon className="h-3 w-3" />
                                      <span>{data.spotlight.readTime}</span>
                                    </div>
                                  </div>
                                </Link>
                              </motion.div> */}

                                {/* Description Section */}
                                <motion.div
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ duration: 0.3, delay: 0.6 }}
                                  className="border-t border-gray-200 pt-4"
                                >
                                  <h4 className="font-normal text-sm text-muted-foreground mb-2">
                                    Về {data.title}
                                  </h4>
                                  <p className="text-xs text-gray-600 leading-relaxed mb-3">
                                    {data.description}
                                  </p>
                                  <Link
                                    href="/properties"
                                    className="inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 transition-colors"
                                  >
                                    Xem tất cả →
                                  </Link>
                                </motion.div>
                              </div>
                            </CollapsibleContent>
                          </Collapsible>
                        </motion.div>
                      ))}

                      {/* Additional Menu Items */}
                      {/* <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 }}
                      className="space-y-2"
                    >
                      <Link
                        href="/myrevo"
                        className="flex items-center gap-2 py-2 px-4 rounded-md hover:bg-accent transition-colors"
                      >
                        <span className="font-normal text-base">Tài khoản</span>
                      </Link>
                      <Link
                        href="/comparison"
                        className="flex items-center gap-2 py-2 px-4 rounded-md hover:bg-accent transition-colors"
                      >
                        <span className="font-normal text-base">So sánh</span>
                      </Link>
                    </motion.div> */}
                    </div>
                  </ScrollArea>
                </SheetContent>
              </Sheet>
            )}
          </div>

          {/* Desktop Layout using Grid */}
          <div className="hidden md:grid grid-cols-3 items-center gap-4">
            {/* Logo Section */}
            <div className="flex items-center justify-start">
              <Link href="/" className="flex items-center">
                <Image
                  src="/logo_revoland_red.png"
                  alt="Revoland icon"
                  width={40}
                  height={40}
                  priority
                  className="rounded-md"
                />
                <span className="lg:block hidden text-2xl font-medium text-red-500">Revoland</span>
              </Link>
            </div>

            {/* Search Section - Perfectly Centered */}
            <div className="flex justify-center">
              <div className="w-80 lg:w-96 xl:w-[400px]">
                <EnhancedSearchAutocomplete />
              </div>
            </div>

            {/* Auth Buttons Section */}
            <div className="flex items-center justify-end gap-2">
              {isAuthenticated ? (
                <>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="relative size-10 rounded-full">
                        <Avatar className="size-12">
                          <AvatarImage src={user.avatar} alt={user.name} className="object-cover" />
                          <AvatarFallback className="bg-red-500/10 text-red-500">
                            {initials}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-64" align="end" forceMount>
                      <DropdownMenuLabel className="font-normal">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none">{user.name}</p>
                          <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {/* Quick Actions Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Tin đăng
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/property/action" className="cursor-pointer">
                            <PlusIcon className="mr-2 h-4 w-4" />
                            Đăng tin bất động sản
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/property" className="cursor-pointer">
                            <BuildingIcon className="mr-2 h-4 w-4" />
                            Quản lý bất động sản
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />

                      {/* Dashboard Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Quản lý
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/dashboard" className="cursor-pointer">
                            <LayoutDashboardIcon className="mr-2 h-4 w-4" />
                            Dashboard
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/lead" className="cursor-pointer">
                            <UsersIcon className="mr-2 h-4 w-4" />
                            Quản lý khách hàng
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/saler/sales" className="cursor-pointer">
                            <TrendingUpIcon className="mr-2 h-4 w-4" />
                            Quản lý giao dịch
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />

                      {/* User Account Section */}
                      <DropdownMenuLabel className="text-xs font-semibold text-muted-foreground px-2 py-1.5">
                        Tài khoản
                      </DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=account-settings" className="cursor-pointer">
                            <UserCircleIcon className="mr-2 h-4 w-4" />
                            Thông tin cá nhân
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=saved-homes" className="cursor-pointer">
                            <HeartIcon className="mr-2 h-4 w-4" />
                            Bất động sản đã lưu
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=manage-tours" className="cursor-pointer">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            Lịch hẹn xem nhà
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href="/myrevo?tab=recently-viewed" className="cursor-pointer">
                            <ClockIcon className="mr-2 h-4 w-4" />
                            Đã xem gần đây
                          </Link>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>

                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => logout()} className="cursor-pointer">
                        <LogOutIcon className="mr-2 h-4 w-4" />
                        Đăng xuất
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <Button asChild variant="ghost" size="sm" className="max-md:hidden">
                    <Link href="/login?redirect=/">Đăng nhập</Link>
                  </Button>
                  <Button
                    asChild
                    variant="default"
                    size="sm"
                    className="bg-red-500 hover:bg-red-600"
                  >
                    <Link href="/login?redirect=/saler/property">Đăng tin</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Dropdown - Auto height content */}
      {activeDropdown && (
        <div
          className="fixed left-0 right-0 z-[60] bg-white/85 backdrop-blur-lg pointer-events-none md:!top-28"
          style={{
            top: '80px', // Height of header on mobile (h-20 = 80px)
            width: '100vw',
            margin: '0',
            padding: '0',
          }}
        >
          {/* Content area that detects mouse leave */}
          <div className="w-full pointer-events-auto" onMouseLeave={handleMouseLeave}>
            <DropdownContent data={navigationData[activeDropdown as keyof typeof navigationData]} />
          </div>
        </div>
      )}

      {/* Bottom Navigation for Mobile - Hidden on property detail pages */}
      {!isPropertyDetailPage && <BottomNavigation />}
    </>
  );
}
