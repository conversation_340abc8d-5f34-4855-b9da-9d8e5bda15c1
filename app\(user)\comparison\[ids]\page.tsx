'use client';

import { useParams, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import propertyService, { Property } from '@/lib/api/services/fetchProperty';
import { useState, useEffect } from 'react';
import ComparisonTable from '../components/ComparisonTable';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import Footer from '@/components/Footer';
import { ErrorState, EmptyState, LoadingSkeleton } from '@/components/ui/state';

const fetchPropertyById = async (id: string) => {
  const response = await propertyService.getProperty(id);
  return response;
};

export default function ComparisonPage() {
  const params = useParams();
  const router = useRouter();
  const ids = params.ids as string;

  const [propertyIds, setPropertyIds] = useState<string[]>([]);

  // Parse ids on first load
  useEffect(() => {
    if (ids) {
      const parsedIds = ids.split('-vs-').filter(id => id.length > 0);
      setPropertyIds(parsedIds);
    }
  }, [ids]);

  // Fetch selected properties
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['properties-comparison', propertyIds],
    queryFn: () =>
      Promise.all(
        propertyIds.map(async id => {
          const res = await fetchPropertyById(id);
          return res.data;
        })
      ),
    enabled: propertyIds.length > 0,
  });

  const selectedProperties = data || [];

  // Fetch all properties for adding
  const { data: allPropertiesData, isLoading: isLoadingAll } = useQuery({
    queryKey: ['all-properties'],
    queryFn: async () => {
      const res = await propertyService.getProperties({
        pageNumber: 1,
        pageSize: 9999,
      });
      return res.data.properties;
    },
  });

  const allProperties = allPropertiesData || [];

  // Handle add property
  const handleAddProperty = (property: Property) => {
    if (!propertyIds.includes(property.id)) {
      const newIds = [...propertyIds, property.id];
      setPropertyIds(newIds); // update state
      router.push(`/comparison/${newIds.join('-vs-')}`); // update URL without reload
    }
  };
  // Handle remove property
  const handleRemoveProperty = (propertyId: string) => {
    const newIds = propertyIds.filter(id => id !== propertyId);
    setPropertyIds(newIds);
    if (newIds.length > 0) {
      router.push(`/comparison/${newIds.join('-vs-')}`);
    }
  };

  // Loading state
  if (isLoading || isLoadingAll) return <LoadingSkeleton propertyCount={propertyIds.length || 3} />;

  // Error state
  if (error)
    return (
      <ErrorState
        message="Không thể tải dữ liệu bất động sản"
        detail={(error as Error).message}
        onRetry={() => refetch()}
      />
    );

  // Empty state
  if (selectedProperties.length === 0) return <EmptyState />;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto w-full max-w-[1800px] px-4 py-8 space-y-4">
        {/* Header */}
        <div>
          <div className="flex flex-col items-start gap-4">
            <Button variant="outline" size="sm" asChild className="bg-white">
              <Link href="/myrevo">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Quay lại
              </Link>
            </Button>
            <h1 className="text-3xl font-bold text-gray-900">So sánh bất động sản</h1>
          </div>
          <p className="text-gray-600 mt-1 py-2">
            Đang so sánh {selectedProperties.length} bất động sản
          </p>
        </div>

        {/* Comparison Table */}
        <ComparisonTable
          selectedProperties={selectedProperties}
          onAddProperty={handleAddProperty}
          availableProperties={allProperties}
          onRemoveProperty={handleRemoveProperty}
        />
      </div>
      <Footer />
    </div>
  );
}
