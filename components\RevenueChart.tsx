'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import {
  Respons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON>ar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
} from 'recharts';

interface RevenuePoint {
  month: string;
  value: number;
}

const MOCK_REVENUE: RevenuePoint[] = [
  { month: 'Jan', value: 12000 },
  { month: 'Feb', value: 18500 },
  { month: 'Mar', value: 25000 },
  { month: 'Apr', value: 21000 },
  { month: 'May', value: 27500 },
  { month: 'Jun', value: 24000 },
];

export function RevenueChart() {
  const totalRevenue = MOCK_REVENUE.reduce((sum, p) => sum + p.value, 0);
  const bestMonth = MOCK_REVENUE.reduce(
    (best, p) => (p.value > best.value ? p : best),
    MOCK_REVENUE[0]
  );

  return (
    <Card className="border-2 shadow-sm bg-white/50 backdrop-blur-lg w-full">
      <CardHeader>
        <CardTitle className="text-xl font-semibold">Dòng doanh thu</CardTitle>
      </CardHeader>
      <div className="absolute inset-0 z-20 flex flex-col items-center justify-center text-center pointer-events-none">
        <div className="text-lg font-semibold text-foreground">Sắp ra mắt</div>
        <div className="text-xs text-muted-foreground">
          Tính năng này đang được phát triển để giúp bạn theo dõi và tổ chức công việc hiệu quả hơn.
        </div>
      </div>
      <CardContent className="blur">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
          <div className="order-1 md:order-1 space-y-2">
            <div className="text-2xl font-bold tracking-tight">
              ${totalRevenue.toLocaleString('en-US')}
            </div>
            <div className="text-sm text-muted-foreground">Tổng doanh thu (6 tháng gần đây)</div>
            <div className="rounded-md border border-border/50 bg-muted/30 p-3">
              <div className="text-sm font-semibold">Tháng có doanh thu tốt nhất</div>
              <div className="text-xs text-muted-foreground mt-1">
                {bestMonth.month} đạt doanh thu cao nhất với $
                {bestMonth.value.toLocaleString('en-US')}
              </div>
            </div>
          </div>

          <div className="relative h-56 sm:h-64 order-2 md:order-2">
            <div className="h-full w-full opacity-40 blur-[1.5px]">
              <ResponsiveContainer width="100%" height="100%">
                <RBarChart data={MOCK_REVENUE} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.15} />
                  <XAxis dataKey="month" tickLine={false} axisLine={false} />
                  <YAxis tickLine={false} axisLine={false} />
                  <Tooltip cursor={{ fill: 'rgba(0,0,0,0.04)' }} />
                  <Bar dataKey="value" fill="#ef4444" radius={[6, 6, 0, 0]} />
                </RBarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
