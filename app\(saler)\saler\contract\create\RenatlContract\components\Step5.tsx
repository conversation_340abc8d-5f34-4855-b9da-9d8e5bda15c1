'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { contractClausesarticle7to10Schema } from '../schemas';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface Step5Props {
  data: { article7to10?: string };
  onNext: (data: { article7to10: string }) => void;
  onBack: () => void;
}

const defaultarticle7to10 = `ĐIỀU 7: CHẤM DỨT HỢP ĐỒNG

7.1. Hợp đồng thuê căn hộ chấm dứt trong các trường hợp sau:
a) Hết thời hạn thuê căn hộ mà hai bên không có thoả thuận gia hạn;
b) <PERSON> bên tho<PERSON> thuận chấm dứt hợp đồng trước thời hạn;
c) <PERSON><PERSON><PERSON> hộ cho thuê bị phá dỡ theo quyết định của cơ quan nhà nước có thẩm quyền;
d) Bên A đơn phương chấm dứt hợp đồng trong các trường hợp quy định tại khoản 5 Điều 4 của hợp đồng này;
e) Bên B đơn phương chấm dứt hợp đồng trong trường hợp bên A có những hành vi vi phạm nghiêm trọng nghĩa vụ quy định tại Điều 4 của hợp đồng này.

7.2. Khi hợp đồng thuê căn hộ chấm dứt:
a) Bên B phải trả lại căn hộ và trang thiết bị gắn liền với căn hộ cho bên A trong tình trạng ban đầu như khi nhận, trừ hao mòn tự nhiên;
b) Hai bên thanh toán công nợ với nhau (nếu có);
c) Bên A có trách nhiệm hoàn trả tiền đặt cọc cho bên B (nếu có) sau khi đã trừ đi các khoản bên B nợ bên A và chi phí sửa chữa do bên B gây ra.

7.3. Trường hợp một trong hai bên đơn phương chấm dứt hợp đồng trái với thoả thuận thì bên vi phạm phải bồi thường cho bên kia một khoản tiền tương đương với một tháng tiền thuê căn hộ.

ĐIỀU 8: CAM KẾT CỦA CÁC BÊN

8.1. Bên A cam kết:
a) Tất cả những thông tin về căn hộ và pháp lý căn hộ cung cấp cho bên B đều chính xác và chịu trách nhiệm trước pháp luật về tính chính xác đó;
b) Giao căn hộ cho bên B đúng ngày đã ký trong hợp đồng;
c) Trong thời gian cho thuê, bảo đảm quyền sử dụng ổn định và lâu dài căn hộ của bên B;
d) Chịu hoàn toàn trách nhiệm về mọi tranh chấp phát sinh liên quan đến quyền sở hữu căn hộ.

8.2. Bên B cam kết:
a) Sử dụng căn hộ đúng mục đích đã thoả thuận;
b) Trả đủ và đúng hạn tiền thuê căn hộ;
c) Không làm ảnh hưởng đến môi trường xung quanh;
d) Chấp hành mọi quy định của pháp luật và quy định của nơi cư trú.

ĐIỀU 9: GIẢI QUYẾT TRANH CHẤP

9.1. Trong quá trình thực hiện hợp đồng nếu có tranh chấp phát sinh, hai bên sẽ cùng nhau thương lượng giải quyết trên nguyên tắc tôn trọng quyền lợi của nhau.

9.2. Trong trường hợp không thể thoả thuận được thì hai bên có quyền khởi kiện tại tòa án có thẩm quyền để giải quyết theo quy định của pháp luật.

ĐIỀU 10: HIỆU LỰC CỦA HỢP ĐỒNG
10.1. Hợp đồng này có hiệu lực kể từ ngày ……… tháng …….. năm …………
10.2. Hợp đồng này được lập thành .....bản và có giá trị như nhau. Mỗi bên giữ .... bản, .... bản lưu tại cơ quan công chứng hoặc chứng thực (nếu có) và .... bản lưu tại cơ quan thuế (các bên có thể thỏa thuận lập thêm hợp đồng bằng tiếng Anh)./.`;

const Step5: React.FC<Step5Props> = ({ data, onNext, onBack }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<{ article7to10: string }>({
    resolver: zodResolver(contractClausesarticle7to10Schema),
    defaultValues: {
      article7to10: data.article7to10 || defaultarticle7to10,
    },
    mode: 'onChange',
  });

  const onSubmit = (formData: { article7to10: string }) => {
    onNext(formData);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Bước 5: Điều 7-9: Chấm dứt hợp đồng, Cam kết và Giải quyết tranh chấp
        </h2>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="article7to10" className="text-sm font-medium">
                Điều khoản 7-9: Chấm dứt hợp đồng, Cam kết và Giải quyết tranh chấp *
              </Label>
              <Textarea
                id="article7to10"
                {...register('article7to10')}
                rows={20}
                className="mt-2"
                placeholder="Nhập nội dung điều khoản 7-9..."
              />
              {errors.article7to10 && (
                <p className="mt-1 text-sm text-red-600">{errors.article7to10.message}</p>
              )}
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">💡 Hướng dẫn:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Bạn có thể chỉnh sửa nội dung điều khoản theo nhu cầu</li>
                <li>• ĐIỀU 7: CHẤM DỨT HỢP ĐỒNG - các trường hợp và quy trình</li>
                <li>• ĐIỀU 8: CAM KẾT CỦA CÁC BÊN - trách nhiệm và nghĩa vụ</li>
                <li>• ĐIỀU 9: GIẢI QUYẾT TRANH CHẤP - phương thức giải quyết</li>
                <li>• ĐIỀU 10: HIỆU LỰC CỦA HỢP ĐỒNG - thời điểm có hiệu lực và bản sao</li>
              </ul>
            </div>
          </div>
        </form>
      </Card>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onBack} className="px-6 py-2">
          ← Quay lại
        </Button>
        <Button
          type="button"
          onClick={handleSubmit(onSubmit)}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700"
        >
          Xem trước hợp đồng →
        </Button>
      </div>
    </div>
  );
};

export default Step5;
