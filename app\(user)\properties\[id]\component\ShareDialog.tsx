'use client';

import { useState, useEffect } from 'react';
import { Property, TransactionType } from '@/lib/api/services/fetchProperty';
import { CollectionWithItems } from '@/lib/api/services/fetchCollection';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Copy,
  Share2,
  HeartIcon,
  MessageCircleIcon,
  RepeatIcon,
  SendIcon,
  BadgeCheckIcon,
  EllipsisIcon,
  UserPlusIcon,
  Folder,
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { useIsMobile } from '@/hooks/useMobile';
import Image from 'next/image';

interface CollectionThumbnailProps {
  images: string[];
  collectionName: string;
  className?: string;
}

function CollectionThumbnail({ images, collectionName, className = '' }: CollectionThumbnailProps) {
  const placeholderImages = Array(4).fill(null);

  return (
    <div className={`w-full h-full rounded-md overflow-hidden bg-gray-100 ${className}`}>
      {images.length === 0 ? (
        // No images - show folder icon
        <div className="w-full h-full flex items-center justify-center">
          <Folder className="w-6 h-6 text-gray-400" />
        </div>
      ) : images.length === 1 ? (
        // 1 image - show single image
        <div className="w-full h-full relative">
          <Image src={images[0]} alt={collectionName} fill className="object-cover" sizes="100%" />
        </div>
      ) : (
        // Multiple images - show 4-image grid layout
        <div className="w-full h-full grid grid-cols-2 gap-0.5">
          {placeholderImages.map((_, index) => (
            <div key={index} className="relative bg-gray-200">
              {images[index] ? (
                <Image
                  src={images[index]}
                  alt={`${collectionName} - ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="50%"
                />
              ) : (
                <div className="w-full h-full bg-gray-100" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  property?: Property;
  collection?: CollectionWithItems;
  shareUrl?: string;
}

export default function ShareDialog({
  isOpen,
  onClose,
  property,
  collection,
  shareUrl,
}: ShareDialogProps) {
  const [copied, setCopied] = useState(false);
  const [liked, setLiked] = useState(false);
  const isMobile = useIsMobile();

  const currentUrl = shareUrl || (typeof window !== 'undefined' ? window.location.href : '');
  const encodedUrl = encodeURIComponent(currentUrl);

  // Generate appropriate share text based on type
  const shareText = property
    ? `Tìm thấy bất động sản tuyệt vời này trên Revoland! ${property.title} - ${property.location.district}, ${property.location.city}`
    : collection
      ? `Khám phá bộ sưu tập bất động sản "${collection.name}" với ${collection.itemCount} bất động sản trên Revoland!`
      : '';

  const encodedText = encodeURIComponent(shareText);

  const handleShare = (platform: string) => {
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank');
        break;
      case 'twitter':
        window.open(
          `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedText}`,
          '_blank'
        );
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`, '_blank');
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodedText}%20${encodedUrl}`, '_blank');
        break;
      case 'telegram':
        window.open(`https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`, '_blank');
        break;
      case 'native':
        if (typeof navigator !== 'undefined' && 'share' in navigator) {
          navigator
            .share({
              title: property?.title || collection?.name || 'Revoland',
              text: shareText,
              url: currentUrl,
            })
            .catch(console.error);
        }
        break;
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      setCopied(true);
      toast.success('Đã sao chép link vào clipboard!', {
        duration: 2000,
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error('Không thể sao chép link');
    }
  };

  const getPrice = () => {
    if (!property) return '';

    if (property.transactionType === TransactionType.FOR_SALE && property.priceDetails.salePrice) {
      return formatCurrency(property.priceDetails.salePrice);
    }
    if (
      property.transactionType === TransactionType.FOR_RENT &&
      property.priceDetails.rentalPrice
    ) {
      return `${formatCurrency(property.priceDetails.rentalPrice)}/tháng`;
    }
    return 'Liên hệ';
  };

  // Shared content component
  const ShareContent = () => (
    <div className="space-y-6">
      {/* Share Options */}
      <div className="space-y-3">
        <h3 className="text-xs md:text-sm font-medium text-muted-foreground">
          Chọn nền tảng chia sẻ
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={() => handleShare('facebook')}
          >
            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
              <svg className="h-4 w-4 text-white fill-white" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
            </div>
            <span className="text-xs md:text-sm">Facebook</span>
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={() => handleShare('twitter')}
          >
            <div className="w-6 h-6 bg-black rounded flex items-center justify-center">
              <svg className="h-4 w-4 text-white fill-white" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
              </svg>
            </div>
            <span className="text-xs md:text-sm">X</span>
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={() => handleShare('linkedin')}
          >
            <div className="w-6 h-6 bg-blue-700 rounded flex items-center justify-center">
              <svg className="h-4 w-4 text-white fill-white" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
              </svg>
            </div>
            <span className="text-xs md:text-sm">LinkedIn</span>
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={() => handleShare('whatsapp')}
          >
            <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
              <svg className="h-4 w-4 text-white fill-white" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.89 3.488" />
              </svg>
            </div>
            <span className="text-xs md:text-sm">WhatsApp</span>
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={() => handleShare('telegram')}
          >
            <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
              <svg className="h-4 w-4 text-white fill-white" viewBox="0 0 24 24">
                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
              </svg>
            </div>
            <span className="text-xs md:text-sm">Telegram</span>
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-2 justify-start h-12"
            onClick={handleCopy}
          >
            <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
              <Copy className="h-4 w-4 text-white" />
            </div>
            <span className="text-xs md:text-sm">{copied ? 'Đã sao chép!' : 'Sao chép link'}</span>
          </Button>
        </div>
      </div>

      {/* Preview */}
      <div className="space-y-3">
        <h3 className="text-xs md:text-sm font-medium text-muted-foreground">
          Xem trước nội dung được chia sẻ
        </h3>
        <Card className="max-w-xl mx-auto p-0">
          <CardHeader className="flex flex-row items-center justify-between gap-3 max-md:p-4">
            <div className="flex items-center gap-3">
              <Avatar className="ring-ring ring-1 ring-red-400 max-md:size-8">
                <AvatarImage src="/logo_revoland_red.png" alt="Revoland" className="scale-75" />
                <AvatarFallback className="text-xs bg-red-100">RL</AvatarFallback>
              </Avatar>
              <div className="flex flex-col gap-0.5">
                <CardTitle className="flex items-center gap-1 text-xs md:text-sm">
                  Revoland <BadgeCheckIcon className="size-4 fill-red-600 stroke-white" />
                </CardTitle>
                <CardDescription className="text-xs md:text-sm">@revoland_official</CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="text-xs md:text-sm max-md:h-7 max-md:px-2 max-md:py-1"
              >
                <UserPlusIcon className="" />
                Theo dõi
              </Button>
              <Button
                variant="ghost"
                size="icon"
                aria-label="Toggle menu"
                className="max-md:h-7 max-md:px-2 max-md:py-1"
              >
                <EllipsisIcon />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6 text-sm max-md:px-4 max-md:pb-1 max-md:pt-4">
            <div className="relative aspect-video w-full rounded-md overflow-hidden">
              {property ? (
                <Image
                  src={property.imageUrls[0] || '/placeholder-property.jpg'}
                  alt={property.title}
                  fill
                  className="object-cover"
                />
              ) : collection ? (
                <CollectionThumbnail
                  images={collection.collectionImage || []}
                  collectionName={collection.name}
                />
              ) : (
                <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                  <Folder className="w-12 h-12 text-gray-400" />
                </div>
              )}
            </div>
            <p className="text-xs md:text-sm">
              {property ? (
                <>
                  🏠 Khám phá bất động sản tuyệt vời này! {property.title} tại{' '}
                  {property.location.district}, {property.location.city} 🌟
                  {property.propertyDetails.bedrooms} phòng ngủ • {getPrice()} 💎{' '}
                  <a href="#" className="text-red-600">
                    #BấtĐộngSản
                  </a>{' '}
                  <a href="#" className="text-red-600">
                    #Revoland
                  </a>{' '}
                  <a href="#" className="text-red-600">
                    #{property.location.city.replace(/\s+/g, '')}
                  </a>
                </>
              ) : collection ? (
                <>
                  📂 Khám phá bộ sưu tập &quot;{collection.name}&quot; với {collection.itemCount}{' '}
                  bất động sản tuyệt vời!
                  {collection.description && ` ${collection.description}`} 🌟
                  <a href="#" className="text-red-600">
                    #BộSưuTập
                  </a>{' '}
                  <a href="#" className="text-red-600">
                    #BấtĐộngSản
                  </a>{' '}
                  <a href="#" className="text-red-600">
                    #Revoland
                  </a>
                </>
              ) : null}
            </p>
          </CardContent>
          <CardFooter className="flex items-center gap-1 max-md:px-4 max-md:py-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLiked(!liked)}
              className="text-xs md:text-sm"
            >
              <HeartIcon className={cn('size-4', liked && 'fill-destructive stroke-destructive')} />
              {liked ? '2.1K' : '2.0K'}
            </Button>
            <Button variant="ghost" size="sm" className="text-xs md:text-sm">
              <MessageCircleIcon className="size-4" />
              1.4K
            </Button>
            <Button variant="ghost" size="sm" className="text-xs md:text-sm">
              <RepeatIcon className="size-4" />
              669
            </Button>
            <Button variant="ghost" size="sm" className="text-xs md:text-sm">
              <SendIcon className="size-4" />
              1.1K
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );

  const getDialogTitle = () => {
    if (property) return 'Chia sẻ bất động sản';
    if (collection) return 'Chia sẻ bộ sưu tập';
    return 'Chia sẻ';
  };

  const getDialogDescription = () => {
    if (property) return 'Chia sẻ bất động sản này với bạn bè và gia đình để họ cùng xem!';
    if (collection)
      return 'Chia sẻ bộ sưu tập này với bạn bè để họ khám phá những bất động sản tuyệt vời!';
    return 'Chia sẻ nội dung này với bạn bè';
  };

  // Reload Zalo SDK when dialog opens to detect new buttons
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        if (typeof window !== 'undefined' && (window as any).ZaloSocialSDK) {
          try {
            console.log('Reloading Zalo SDK for dialog');
            (window as any).ZaloSocialSDK.reload();
          } catch (error) {
            console.log('Zalo SDK reload error:', error);
          }
        }
      }, 300); // Give time for dialog to render

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="h-[90vh]">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center gap-2 text-base">
              <Share2 className="h-5 w-5" />
              {getDialogTitle()}
            </DrawerTitle>
            <DrawerDescription className="text-xs">{getDialogDescription()}</DrawerDescription>
          </DrawerHeader>
          <div className="flex-1 overflow-y-auto px-4">
            <ShareContent />
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>
        <ShareContent />
      </DialogContent>
    </Dialog>
  );
}
