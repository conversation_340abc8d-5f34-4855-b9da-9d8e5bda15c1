import apiService from '../core';

export interface User {
  userName: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  avatar?: string;
  status?: string;
  role: string;
  about?: string;
  birthdate: string;
  joinedAt: string;
  yearsOfExperience?: number;
  companyName?: string;
  fieldOfWorks?: string[];
  serviceLocations?: {
    city: string;
    ward: string;
  }[];
  coverPhoto?: string;
}

export interface Seller {
  id: string;
  userName: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  avatar: string;
  status: string;
  role: string;
  about: string;
  gender: string;
  birthdate: string;
  joinedAt: string;
  isVerified: boolean;
}

export interface UserResponse {
  code: string;
  status: boolean;
  message?: string;
  data: User;
}

export interface UserUpdateResponse {
  code: string;
  status: boolean;
  message?: string;
  data?: string;
}

export interface GetSellerResponse {
  code: number;
  status: boolean;
  message?: string;
  data: {
    data: Seller[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}

export interface AgentFeedback {
  id: string;
  clientName: string;
  rating: number;
  date: string;
  comment: string;
}

export interface SellerProperty {
  propertyId: string;
  title: string;
  images: string;
  transactionType: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  landArea: number;
  buildingArea: number;
  address: string;
}

export interface SellerProfile {
  sellerId: string;
  name: string;
  phone: string | null;
  avatar: string | null;
  coverPhoto: string | null;
  introduction: string | null;
  joinedAt: string;
  yearsOfExperience: number | null;
  companyName: string | null;
  agentFeedbacks: AgentFeedback[];
  serviceLocations: string | null;
  fieldOfWorks: string | null;
  properties: SellerProperty[];
}

export interface SellerProfileResponse {
  code: number;
  status: boolean;
  message: string;
  data: SellerProfile;
}

// User service with profile-related API methods
export const userService = {
  // Get current user profile
  getUserProfile: async (): Promise<UserResponse> => {
    const response = await apiService.get<UserResponse>('/api/users/profile');
    return response.data;
  },

  // Update current user profile
  updateUserProfile: async (profileData: Partial<User>): Promise<UserUpdateResponse> => {
    const response = await apiService.put<UserUpdateResponse, Partial<User>>(
      '/api/users/update-profile',
      profileData
    );
    return response.data;
  },

  // Update user avatar
  updateUserAvatar: async (avatarFile: File): Promise<UserUpdateResponse> => {
    const formData = new FormData();
    formData.append('avatarFile', avatarFile);

    const response = await apiService.put<UserUpdateResponse, FormData>(
      '/api/users/update-avatar',
      formData
    );
    return response.data;
  },

  // Update user cover photo
  updateUserCoverPhoto: async (coverPhotoFile: File): Promise<UserUpdateResponse> => {
    const formData = new FormData();
    formData.append('coverPhotoFile', coverPhotoFile);

    const response = await apiService.put<UserUpdateResponse, FormData>(
      '/api/users/update-cover-photo',
      formData
    );
    return response.data;
  },

  // Change password
  changePassword: async (passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<UserUpdateResponse> => {
    const response = await apiService.put<UserUpdateResponse, typeof passwordData>(
      '/api/users/change-password',
      passwordData
    );
    return response.data;
  },

  // Export user data
  exportUserData: async (): Promise<{ status: boolean; message: string; downloadUrl?: string }> => {
    const response = await apiService.post<{
      status: boolean;
      message: string;
      downloadUrl?: string;
    }>('/api/users/export-data');
    return response.data;
  },

  // Delete user account
  deleteAccount: async (confirmationText: string): Promise<UserUpdateResponse> => {
    const response = await apiService.delete<UserUpdateResponse>('/api/users/delete-account', {
      confirmationText,
    });
    return response.data;
  },

  // Get seller profile
  getSellerProfile: async (): Promise<GetSellerResponse> => {
    const response = await apiService.get<GetSellerResponse>('/api/users/seller');
    return response.data;
  },

  // Get seller profile by ID
  getSellerProfileById: async (sellerId: string): Promise<SellerProfileResponse> => {
    const response = await apiService.get<SellerProfileResponse>(
      `/api/users/seller-profile/${sellerId}`
    );
    return response.data;
  },
};

export default userService;
