'use client';

import React, { useState } from 'react';
import { Property, PropertyType, Currency } from '@/lib/api/services/fetchProperty';
import { formatCurrency } from '@/utils/numbers/formatCurrency';
import { numberToVietnameseMoney } from '@/utils/numbers/numberToVietnameseMoney';
import { formatArea } from '@/utils/areas/formatArea';
import { PropertyCard } from '@/components/PropertyCard';
import ModalProperty from '../components/ModalProperty';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import {
  Home,
  Bed,
  Bath,
  Calendar,
  DollarSign,
  Ruler,
  MapPin,
  Building,
  Shield,
  X,
  Plus,
  ArrowUp,
} from 'lucide-react';

interface ComparisonTableProps {
  selectedProperties: Property[];
  availableProperties?: Property[];
  onRemoveProperty?: (propertyId: string) => void;
  onAddProperty?: (property: Property) => void;
}

export default function ComparisonTable({
  selectedProperties,
  availableProperties = [],
  onRemoveProperty,
  onAddProperty,
}: ComparisonTableProps) {
  const [showModal, setShowModal] = useState(false);
  const canAdd = selectedProperties.length < 5;

  const propertiesToAdd = availableProperties.filter(
    p => !selectedProperties.some(sp => sp.id === p.id)
  );

  const getPropertyType = (type: PropertyType) => {
    const typeMap: Record<PropertyType, string> = {
      Apartment: 'Chung cư',
      LandPlot: 'Nhà đất',
      Villa: 'Biệt thự',
      ShopHouse: 'Shophouse',
      MiniServiceApartment: 'Chung cư mini',
      Motel: 'Nhà nghỉ',
      Airbnb: 'Nhà nghỉ',
      House: 'Nhà riêng',
      Townhouse: 'Nhà liền kề',
      ProjectLand: 'Đất dự án',
      Office: 'Văn phòng',
      Warehouse: 'Kho bãi',
      Factory: 'Nhà máy',
      Industrial: 'Công nghiệp',
      Hotel: 'Khách sạn',
      SocialHousing: 'Nhà ở xã hội',
      NewUrbanArea: 'Khu đô thị mới',
      EcoResort: 'Khu resort',
      CommercialTownhouse: 'Nhà phố thương mại',
      Other: 'Khác',
    };
    return typeMap[type] || 'Khác';
  };

  const fields = [
    {
      label: 'Loại BĐS',
      icon: <Home className="size-4" />,
      getValue: (p: Property) => getPropertyType(p.type),
    },
    {
      label: 'Giá bán',
      icon: <DollarSign className="size-4" />,
      getValue: (p: Property) => p.priceDetails.salePrice || 0,
      highlight: 'min',
      isMoney: true,
    },
    {
      label: 'Giá thuê',
      icon: <DollarSign className="size-4" />,
      getValue: (p: Property) => p.priceDetails.rentalPrice || 0,
      highlight: 'min',
      isMoney: true,
    },
    {
      label: 'Diện tích sàn',
      icon: <Ruler className="size-4" />,
      getValue: (p: Property) => p.propertyDetails.buildingArea || 0,
      highlight: 'max',
      isArea: true,
    },
    {
      label: 'Phòng ngủ',
      icon: <Bed className="size-4" />,
      getValue: (p: Property) => p.propertyDetails.bedrooms || 0,
      highlight: 'max',
    },
    {
      label: 'Phòng tắm',
      icon: <Bath className="size-4" />,
      getValue: (p: Property) => p.propertyDetails.bathrooms || 0,
      highlight: 'max',
    },
    {
      label: 'Số tầng',
      icon: <Building className="size-4" />,
      getValue: (p: Property) => p.propertyDetails.numberOfFloors || 0,
      highlight: 'max',
    },
    {
      label: 'Năm xây dựng',
      icon: <Calendar className="size-4" />,
      getValue: (p: Property) => p.yearBuilt || '—',
    },
    {
      label: 'Thành phố',
      icon: <MapPin className="size-4" />,
      getValue: (p: Property) => p.location?.city || '—',
    },
    {
      label: 'Trạng thái',
      icon: <Shield className="size-4" />,
      getValue: (p: Property) => p.status || '—',
      isStatus: true,
    },
  ];

  // Calculate min/max for highlights
  const minMaxValues: Record<string, number> = {};
  fields.forEach(field => {
    const numericValues = selectedProperties
      .map(p => field.getValue(p))
      .filter(v => typeof v === 'number') as number[];

    if (numericValues.length === 0) return;
    if (field.highlight === 'min') minMaxValues[field.label] = Math.min(...numericValues);
    else if (field.highlight === 'max') minMaxValues[field.label] = Math.max(...numericValues);
  });

  return (
    <Card className="border-0 shadow-sm">
      <CardContent className="p-0">
        <ScrollArea className="w-full">
          <div className="min-w-full">
            <Table className="table-fixed w-full">
              <TableHeader>
                <TableRow>
                  <TableHead className="sticky left-0 bg-white w-[160px] px-2" />
                  {selectedProperties.map(property => (
                    <TableHead
                      key={property.id}
                      className="min-w-[280px] max-w-[280px] overflow-hidden px-1 py-4"
                    >
                      <div className="relative w-full">
                        <div className="overflow-hidden rounded-md">
                          <PropertyCard property={property} size="md" />
                        </div>
                        {onRemoveProperty && (
                          <Button
                            variant="outline"
                            size="icon"
                            className={cn(
                              'z-10 relative bg-white/90 backdrop-blur-sm hover:bg-white/90 rounded-full',
                              'absolute top-3 right-3'
                            )}
                            onClick={() => onRemoveProperty(property.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableHead>
                  ))}
                  {canAdd && (
                    <TableHead className="min-w-[180px]">
                      <div className="flex justify-center items-center h-full">
                        <Button
                          variant="outline"
                          size="icon"
                          className="rounded-full border-2 border-dashed border-gray-300 text-gray-400 hover:text-red-600 hover:border-red-400"
                          onClick={() => setShowModal(true)}
                        >
                          <Plus className="h-6 w-6" />
                        </Button>
                      </div>
                    </TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {fields.map(field => (
                  <TableRow key={field.label}>
                    <TableCell className="sticky left-0 bg-white font-medium min-w-[80px] max-w-[80px]">
                      <div className="flex items-center gap-2 truncate text-muted-foreground">
                        {field.icon}
                        <span className="truncate text-sm">{field.label}</span>
                      </div>
                    </TableCell>
                    {selectedProperties.map(property => {
                      const rawValue = field.getValue(property);
                      const isBestValue =
                        typeof rawValue === 'number' && rawValue === minMaxValues[field.label];

                      let displayValue = rawValue;
                      if (field.isMoney && typeof rawValue === 'number')
                        displayValue = formatCurrency(rawValue, Currency.VND);
                      if (field.isArea && typeof rawValue === 'number')
                        displayValue = formatArea(rawValue);

                      return (
                        <TableCell key={property.id + field.label} className="text-center">
                          {field.isStatus && rawValue ? (
                            <Badge variant="outline">{rawValue}</Badge>
                          ) : (
                            <div
                              className={cn(
                                'text-sm flex flex-col items-center justify-center',
                                isBestValue && 'text-green-600 font-bold'
                              )}
                            >
                              <span className="flex items-center gap-1">
                                {displayValue || '—'}
                                {isBestValue && <ArrowUp className="w-3 h-3 text-green-600" />}
                              </span>
                              {field.isMoney && typeof rawValue === 'number' && (
                                <span className="text-xs text-gray-500">
                                  {numberToVietnameseMoney(rawValue)}
                                </span>
                              )}
                            </div>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        <ModalProperty
          open={showModal}
          onOpenChange={setShowModal}
          properties={propertiesToAdd}
          onSelectProperty={onAddProperty || (() => {})}
        />
      </CardContent>
    </Card>
  );
}
