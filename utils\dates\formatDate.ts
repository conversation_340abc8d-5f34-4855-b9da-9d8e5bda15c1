import { format, formatDistanceToNow, isValid, parseISO } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
/**
 * Formats a date string into a readable format
 * @param dateString The date string to format
 * @param formatStr The format string to use
 * @returns Formatted date string or "N/A" if invalid
 */
export function formatDate(dateString?: string, formatStr: string = 'PPP'): string {
  if (!dateString) return 'N/A';

  try {
    const date = parseISO(dateString);

    if (isValid(date)) {
      return format(date, formatStr);
    }

    return 'N/A';
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'N/A';
  }
}

/**
 * Formats a date string with locale support
 * @param dateString The date string to format
 * @param localeCode The locale code ('en' or 'vi')
 * @returns Formatted date string or "N/A" if invalid
 */

/**
 * localeCode: 'en' | 'vi', there can be more locales added in the future
 * Example usage:
  {formatDateWithLocale(date)} // → 24 tháng 7, 2025
  {formatDateWithLocale(date, 'en')} // → July 24th, 2025
 */
export function formatDateWithLocale(
  dateString?: string,
  localeCode: 'en' | 'vi' = 'vi' // default to Vietnamese
): string {
  if (!dateString) return 'N/A';

  try {
    const date = parseISO(dateString);

    if (isValid(date)) {
      const localeMap = {
        en: enUS,
        vi: vi,
      };

      const locale = localeMap[localeCode] || enUS;

      const formatStr = localeCode === 'vi' ? "d 'tháng' M, yyyy" : 'PPP';

      return format(date, formatStr, { locale });
    }

    return 'N/A';
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'N/A';
  }
}

export const formatTimeAgo = (date: string | Date) => {
  if (!date) return '';
  return formatDistanceToNow(new Date(date), { addSuffix: true });
};

export function formatTimeFromISOString(
  isoString: string,
  options?: { useUTC?: boolean; hour12?: boolean }
): string {
  const date = new Date(isoString);
  const useUTC = options?.useUTC ?? false;
  const hour12 = options?.hour12 ?? false;

  if (useUTC) {
    const utcHours = date.getUTCHours().toString().padStart(2, '0');
    const utcMinutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `${utcHours}:${utcMinutes}`;
  } else {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: hour12,
    });
  }
}
