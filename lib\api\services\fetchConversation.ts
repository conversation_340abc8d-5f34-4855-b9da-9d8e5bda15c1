import apiService from '@/lib/api/core';
import { PinMessage as ChatPinMessage } from './fetchChat';

export interface PagingConversation {
  pageSize: number;
  pageNumber: number;
}

export interface PlatformUser {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface Conversation {
  id: string;
  platformUser: PlatformUser | null;
  platform: string;
  lastMessage: string;
  pinMessages: ChatPinMessage[];
  lastUpdated: string;
}

export interface ConversationPinMessage {
  id: string;
  messageId?: string;
  senderId: string;
  content: string;
  createdAt?: string;
  timestamp?: string;
  [key: string]: string | undefined;
}

export interface ConversationResponse {
  code: number;
  status: boolean;
  message: string;
  data: [
    {
      platform: string;
      conversations: Conversation[];
    },
  ];
}

export interface ZaloMessageRequest {
  customerProfileId: string;
  text: string;
  conversationId: string;
}

export const conversationService = {
  getConversation: async (pagingRequest: PagingConversation): Promise<ConversationResponse> => {
    const response = await apiService.get<ConversationResponse>('/api/conversations', {
      pageSize: pagingRequest.pageSize,
      pageNumber: pagingRequest.pageNumber,
    });

    return response.data;
  },

  newSystemMessage: async (messageData: {
    content: string;
    conversationId: string;
    attachmentIds?: string[];
    replyToMessageId?: string;
  }): Promise<{ success: boolean }> => {
    const response = await apiService.post<{ success: boolean }>(
      '/api/chat/send/system',
      messageData
    );
    return response.data;
  },
};

export default conversationService;
