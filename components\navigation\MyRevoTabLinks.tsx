/**
 * Example component showing how to create links to MyRevo tabs
 * This can be used in navigation menus, buttons, or anywhere you need to link to specific tabs
 */

'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  getMyRevoTabUrl,
  navigateToMyRevoTab,
  type MyRevoTab,
} from '@/utils/navigation/myRevoNavigation';
import { Heart, Bookmark, Search, MessageSquare, Home, Settings } from 'lucide-react';

interface TabLinkProps {
  tab: MyRevoTab;
  children: React.ReactNode;
  className?: string;
}

/**
 * A Link component that navigates to a specific MyRevo tab
 */
export function MyRevoTabLink({ tab, children, className }: TabLinkProps) {
  return (
    <Link href={getMyRevoTabUrl(tab)} className={className}>
      {children}
    </Link>
  );
}

/**
 * A Button component that navigates to a specific MyRevo tab
 */
export function MyRevoTabButton({ tab, children, className }: TabLinkProps) {
  const router = useRouter();

  const handleClick = () => {
    navigateToMyRevoTab(tab, router);
  };

  return (
    <Button onClick={handleClick} className={className}>
      {children}
    </Button>
  );
}

/**
 * Example navigation menu with links to popular MyRevo tabs
 */
export function MyRevoQuickNavigation() {
  const quickLinks = [
    { tab: 'saved-homes' as MyRevoTab, label: 'Nhà đã lưu', icon: Heart },
    { tab: 'collections' as MyRevoTab, label: 'Bộ sưu tập', icon: Bookmark },
    { tab: 'saved-searches' as MyRevoTab, label: 'Tìm kiếm đã lưu', icon: Search },
    { tab: 'inbox' as MyRevoTab, label: 'Hộp thư', icon: MessageSquare },
    { tab: 'your-homes' as MyRevoTab, label: 'Nhà của bạn', icon: Home },
    { tab: 'account-settings' as MyRevoTab, label: 'Cài đặt', icon: Settings },
  ];

  return (
    <nav className="flex flex-wrap gap-2">
      {quickLinks.map(({ tab, label, icon: Icon }) => (
        <MyRevoTabLink
          key={tab}
          tab={tab}
          className="flex items-center gap-2 px-3 py-2 text-sm bg-secondary hover:bg-secondary/80 rounded-md transition-colors"
        >
          <Icon className="h-4 w-4" />
          {label}
        </MyRevoTabLink>
      ))}
    </nav>
  );
}

/**
 * Example usage in a dropdown menu or mobile menu
 */
export function MyRevoMobileMenu() {
  const router = useRouter();

  const menuItems = [
    { tab: 'saved-homes' as MyRevoTab, label: 'Nhà đã lưu' },
    { tab: 'collections' as MyRevoTab, label: 'Bộ sưu tập' },
    { tab: 'inbox' as MyRevoTab, label: 'Hộp thư' },
    { tab: 'account-settings' as MyRevoTab, label: 'Cài đặt tài khoản' },
  ];

  return (
    <div className="space-y-1">
      {menuItems.map(({ tab, label }) => (
        <button
          key={tab}
          onClick={() => navigateToMyRevoTab(tab, router)}
          className="block w-full text-left px-4 py-2 text-sm hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
        >
          {label}
        </button>
      ))}
    </div>
  );
}
