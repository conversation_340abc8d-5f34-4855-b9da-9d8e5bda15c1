import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/lib/store/authStore';
import userService, {
  User,
  UserResponse,
  UserUpdateResponse,
  GetSellerResponse,
  SellerProfileResponse,
} from '@/lib/api/services/fetchUser';
import { toast } from 'sonner';

/**
 * Hook to fetch current user's profile
 */
export function useUserProfile() {
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  return useQuery({
    queryKey: ['users', 'profile'],
    queryFn: () => userService.getUserProfile(),
    enabled: isAuthenticated,
    select: (data: UserResponse) => ({
      profile: data.data,
      status: data.status,
      message: data.message,
    }),
    retry: (failureCount, error: unknown) => {
      // Don't retry on 401 errors
      if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}

/**
 * Hook to update user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (profileData: Partial<User>) => userService.updateUserProfile(profileData),
    onSuccess: (data: UserUpdateResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
      }
      toast.success(data.message || 'Cập nhật thông tin thành công');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật thông tin');
    },
  });
}

/**
 * Hook to update user avatar
 */
export function useUpdateAvatar() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (avatarFile: File) => userService.updateUserAvatar(avatarFile),
    onSuccess: (data: UserUpdateResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
      }
      toast.success(data.message || 'Cập nhật ảnh đại diện thành công');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật ảnh đại diện');
    },
  });
}

/**
 * Hook to update user cover photo
 */
export function useUpdateCoverPhoto() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (coverPhotoFile: File) => userService.updateUserCoverPhoto(coverPhotoFile),
    onSuccess: (data: UserUpdateResponse) => {
      if (data.status) {
        queryClient.invalidateQueries({ queryKey: ['users', 'profile'] });
      }
      toast.success(data.message || 'Cập nhật ảnh bìa thành công');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi cập nhật ảnh bìa');
    },
  });
}

/**
 * Hook to change password
 */
export function useChangePassword() {
  return useMutation({
    mutationFn: (passwordData: {
      currentPassword: string;
      newPassword: string;
      confirmPassword: string;
    }) => userService.changePassword(passwordData),
    onSuccess: (data: UserUpdateResponse) => {
      toast.success(data.message || 'Đổi mật khẩu thành công');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi đổi mật khẩu');
    },
  });
}

/**
 * Hook to export user data
 */
export function useExportUserData() {
  return useMutation({
    mutationFn: () => userService.exportUserData(),
    onSuccess: data => {
      if (data.status) {
        toast.success(data.message || 'Yêu cầu xuất dữ liệu đã được gửi');
        if (data.downloadUrl) {
          // Trigger download if URL is provided
          window.open(data.downloadUrl, '_blank');
        }
      } else {
        toast.error(data.message || 'Có lỗi xảy ra khi xuất dữ liệu');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi xuất dữ liệu');
    },
  });
}

/**
 * Hook to delete user account
 */
export function useDeleteAccount() {
  return useMutation({
    mutationFn: (confirmationText: string) => userService.deleteAccount(confirmationText),
    onSuccess: (data: UserUpdateResponse) => {
      if (data.status) {
        toast.success(data.message || 'Tài khoản đã được xóa thành công');
        // Redirect to home or login page after successful deletion
        setTimeout(() => {
          window.location.href = '/';
        }, 2000);
      } else {
        toast.error(data.message || 'Có lỗi xảy ra khi xóa tài khoản');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Có lỗi xảy ra khi xóa tài khoản');
    },
  });
}

export function useSeller() {
  return useQuery({
    queryKey: ['users', 'sellers'],
    queryFn: () => userService.getSellerProfile(),
    select: (response: GetSellerResponse) => ({
      sellers: response.data.data,
      status: response.status,
      message: response.message,
      totalCount: response.data.totalCount,
      totalPages: response.data.totalPages,
      currentPage: response.data.currentPage,
      pageSize: response.data.pageSize,
    }),
  });
}

/**
 * Hook to fetch seller profile by ID
 */
export function useSellerProfile(sellerId: string) {
  return useQuery({
    queryKey: ['users', 'seller-profile', sellerId],
    queryFn: () => userService.getSellerProfileById(sellerId),
    enabled: !!sellerId,
    select: (response: SellerProfileResponse) => ({
      profile: response.data,
      status: response.status,
      message: response.message,
    }),
    retry: (failureCount, error: unknown) => {
      // Don't retry on 404 errors (seller not found)
      if (error && typeof error === 'object' && 'status' in error && error.status === 404) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
}
