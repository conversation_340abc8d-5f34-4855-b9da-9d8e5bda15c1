// components/ui/DeleteConfirmModal.tsx
'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button'; // Assuming you have a Shadcn Button component

interface DeleteConfirmModalProps {
  onConfirm: () => void; // Function to call when the user confirms deletion
  title?: string; // Optional: Custom title for the dialog
  description?: string; // Optional: Custom description for the dialog
  open: boolean; // Control modal visibility from parent
  onOpenChange: (open: boolean) => void; // Callback to update modal visibility in parent
  isLoading?: boolean; // Optional: To show loading state on confirm button
}

const DeleteConfirmModal = ({
  onConfirm,
  title,
  description,
  open,
  onOpenChange,
  isLoading = false,
}: DeleteConfirmModalProps) => {
  const handleConfirmClick = () => {
    onConfirm();
    // Optionally close the modal after confirmation, or handle in parent based on success/failure
    // onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-6 rounded-lg shadow-xl">
        <DialogHeader className="pb-4 border-b border-gray-200 mb-4">
          <DialogTitle className="text-2xl font-bold text-red-600">{title}</DialogTitle>
          <DialogDescription className="text-gray-700 text-base mt-2">
            {description}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-between gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto px-6 py-2"
            disabled={isLoading}
          >
            Hủy
          </Button>
          <Button
            variant="destructive" // Shadcn destructive variant for delete
            onClick={handleConfirmClick}
            className="w-full sm:w-auto px-6 py-2"
            disabled={isLoading}
          >
            {isLoading ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmModal;
