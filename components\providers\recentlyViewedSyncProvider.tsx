'use client';

import { useRecentlyViewed } from '@/hooks/useRecentlyViewed';

interface RecentlyViewedSyncProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component for syncing Recently Viewed data between localStorage and server
 *
 * This provider should be used ONLY ONCE at the root level of the application
 * to prevent duplicate sync operations. It uses the unified useRecentlyViewed hook
 * which handles merge logic when users log in, ensuring that eye icons display
 * for all viewed properties from both localStorage and server.
 *
 * @example
 * ```tsx
 * // In app/layout.tsx
 * <QueryProvider>
 *   <NavigationProgressProvider>
 *     <RecentlyViewedSyncProvider>
 *       <main>{children}</main>
 *     </RecentlyViewedSyncProvider>
 *   </NavigationProgressProvider>
 * </QueryProvider>
 * ```
 */
export function RecentlyViewedSyncProvider({ children }: RecentlyViewedSyncProviderProps) {
  // Use unified hook with sync enabled - this handles all the merge and sync operations
  useRecentlyViewed({ enableSync: true });

  return <>{children}</>;
}
