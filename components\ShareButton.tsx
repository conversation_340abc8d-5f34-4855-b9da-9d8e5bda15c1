'use client';

import { useState, useEffect } from 'react';
import { Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Property } from '@/lib/api/services/fetchProperty';
import ShareDialog from '@/app/(user)/properties/[id]/component/ShareDialog';

interface ShareButtonProps {
  property: Property;
  variant?: 'card' | 'showcase';
  size?: 'sm' | 'md';
  className?: string;
  showLabel?: boolean;
  showSocialButtons?: boolean;
}

export default function ShareButton({
  property,
  variant = 'card',
  size = 'md',
  className,
  showLabel = false,
  showSocialButtons = false,
}: ShareButtonProps) {
  const [showShareDialog, setShowShareDialog] = useState(false);

  // Share functionality
  const getShareUrl = () => {
    if (variant === 'card') {
      // For property cards, use the property detail page URL
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://revoland.vn';
      return `${baseUrl}/properties/${property.id}`;
    } else {
      // For showcase variant, use current URL
      return typeof window !== 'undefined' ? window.location.href : '';
    }
  };

  const currentUrl = getShareUrl();
  const encodedUrl = encodeURIComponent(currentUrl);
  const shareText = `Tìm thấy bất động sản tuyệt vời này trên Revoland! ${property.title} - ${property.location.district}, ${property.location.city}`;
  const encodedText = encodeURIComponent(shareText);

  const handleShare = (platform: string, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank');
        break;
      case 'twitter':
        window.open(
          `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedText}`,
          '_blank'
        );
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`, '_blank');
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodedText}%20${encodedUrl}`, '_blank');
        break;
      case 'telegram':
        window.open(`https://t.me/share/url?url=${encodedUrl}&text=${encodedText}`, '_blank');
        break;
    }
  };

  // Initialize Zalo SDK for share button
  useEffect(() => {
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined' && (window as any).ZaloSocialSDK) {
        try {
          console.log('Reloading Zalo SDK for share button');
          (window as any).ZaloSocialSDK.reload();
        } catch (error) {
          console.log('Zalo SDK reload error:', error);
        }
      }
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const getButtonStyles = () => {
    if (variant === 'card') {
      return {
        button: cn('transition-colors rounded-full', size === 'sm' ? 'size-9' : '', className),
        icon: 'h-5 w-5',
      };
    }

    // Showcase variant styling
    return {
      button: cn(
        'rounded-full transition-colors',
        size === 'sm' ? 'size-8 border-none' : '',
        className
      ),
      icon: size === 'sm' ? 'size-3' : 'size-4',
    };
  };

  const styles = getButtonStyles();

  const handleShareClick = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setShowShareDialog(true);
  };

  const ShareButtonComponent = () => (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant === 'card' ? 'ghost' : 'outline'}
          size="icon"
          className={styles.button}
          onClick={handleShareClick}
        >
          <Share2 className={styles.icon} />
          {showLabel && <span className="hidden sm:inline ml-2">Chia sẻ</span>}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Chia sẻ bất động sản</p>
      </TooltipContent>
    </Tooltip>
  );

  const SocialButtons = () => (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="transition-colors rounded-full"
            onClick={e => handleShare('facebook', e)}
          >
            <div className="size-5 md:size-6 bg-blue-600 rounded flex items-center justify-center">
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/b/b9/2023_Facebook_icon.svg"
                alt="Facebook Icon"
                className="size-4 md:size-5"
              />
            </div>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Chia sẻ trên Facebook</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <div className="relative">
            <div
              className="zalo-share-button flex items-center gap-2 text-xs md:text-sm h-9 px-2 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors rounded-full"
              data-href={currentUrl}
              data-oaid="1655735604103741820"
              data-customize="true"
              data-share-type="1"
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div
                className="size-5 md:size-6 rounded flex items-center justify-center"
                style={{ backgroundColor: '#0068FF' }}
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/9/91/Icon_of_Zalo.svg"
                  alt="Zalo Icon"
                  className="size-4 md:size-5"
                />
              </div>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Chia sẻ trên Zalo</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <div className={cn('flex items-center gap-0 md:gap-2', className)}>
        <ShareButtonComponent />
        {showSocialButtons && <SocialButtons />}
      </div>

      {/* Share Dialog */}
      <ShareDialog
        isOpen={showShareDialog}
        onClose={() => setShowShareDialog(false)}
        property={property}
        shareUrl={currentUrl}
      />
    </>
  );
}
