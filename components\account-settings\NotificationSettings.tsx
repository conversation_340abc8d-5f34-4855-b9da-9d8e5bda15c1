'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Loader2, Bell, Mail, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';

interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
  updates: boolean;
  security: boolean;
}

interface NotificationSettingsProps {
  onSuccess?: () => void;
}

export function NotificationSettings({ onSuccess }: NotificationSettingsProps) {
  const [settings, setSettings] = useState<NotificationSettings>({
    email: true,
    push: false,
    sms: false,
    marketing: false,
    updates: true,
    security: true,
  });
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load settings from localStorage or API
    const savedSettings = localStorage.getItem('userNotificationSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error parsing saved settings:', error);
      }
    }
  }, []);

  const handleSettingChange = (key: keyof NotificationSettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    localStorage.setItem('userNotificationSettings', JSON.stringify(newSettings));

    // Simulate API call
    setIsSaving(true);
    setTimeout(() => {
      toast.success('Cài đặt thông báo đã được cập nhật');
      setIsSaving(false);
      onSuccess?.();
    }, 1000);
  };

  const saveAllSettings = async () => {
    setIsSaving(true);

    try {
      // Simulate API call to save all settings
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Tất cả cài đặt thông báo đã được lưu');
      onSuccess?.();
    } catch (error) {
      toast.error('Có lỗi xảy ra khi lưu cài đặt');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Cài đặt thông báo</h3>
          <p className="text-sm text-muted-foreground">
            Quản lý cách bạn nhận thông báo từ hệ thống
          </p>
        </div>
        <Button onClick={saveAllSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Đang lưu
            </>
          ) : (
            'Lưu tất cả'
          )}
        </Button>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Thông báo qua Email
            </CardTitle>
            <CardDescription>Nhận thông báo quan trọng qua email</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Thông báo chung</Label>
                <p className="text-sm text-muted-foreground">
                  Nhận thông báo về hoạt động tài khoản
                </p>
              </div>
              <Switch
                checked={settings.email}
                onCheckedChange={checked => handleSettingChange('email', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cập nhật hệ thống</Label>
                <p className="text-sm text-muted-foreground">
                  Thông báo về cập nhật và tính năng mới
                </p>
              </div>
              <Switch
                checked={settings.updates}
                onCheckedChange={checked => handleSettingChange('updates', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Bảo mật</Label>
                <p className="text-sm text-muted-foreground">Thông báo về hoạt động bảo mật</p>
              </div>
              <Switch
                checked={settings.security}
                onCheckedChange={checked => handleSettingChange('security', checked)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Thông báo Push
            </CardTitle>
            <CardDescription>Nhận thông báo trực tiếp trên thiết bị</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Thông báo Push</Label>
                <p className="text-sm text-muted-foreground">
                  Nhận thông báo ngay lập tức trên trình duyệt
                </p>
              </div>
              <Switch
                checked={settings.push}
                onCheckedChange={checked => handleSettingChange('push', checked)}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Thông báo SMS
            </CardTitle>
            <CardDescription>Nhận thông báo qua tin nhắn SMS</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Thông báo SMS</Label>
                <p className="text-sm text-muted-foreground">Nhận thông báo quan trọng qua SMS</p>
              </div>
              <Switch
                checked={settings.sms}
                onCheckedChange={checked => handleSettingChange('sms', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Marketing</Label>
                <p className="text-sm text-muted-foreground">
                  Nhận thông tin về sản phẩm và dịch vụ
                </p>
              </div>
              <Switch
                checked={settings.marketing}
                onCheckedChange={checked => handleSettingChange('marketing', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
