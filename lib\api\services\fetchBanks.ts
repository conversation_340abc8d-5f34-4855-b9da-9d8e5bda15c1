import apiService from '../core';

export interface Bank {
  id: number;
  name: string;
  code: string;
  bin: string;
  shortName: string;
  logo: string;
  transferSupported: number;
  lookupSupported: number;
}

export interface BanksResponse {
  code: number;
  status: boolean;
  message: string;
  data: Bank[];
}

export const banksService = {
  // Get all banks from VietQR API
  getBanks: async (): Promise<BanksResponse> => {
    try {
      // Call VietQR API directly or through your backend
      const response = await fetch('https://api.vietqr.io/v2/banks');
      const vietQRData = await response.json();

      // Transform VietQR response to match your API structure
      const transformedResponse: BanksResponse = {
        code: 200,
        status: true,
        message: 'L<PERSON>y danh sách ngân hàng thành công',
        data: vietQRData.data || [],
      };

      return transformedResponse;
    } catch (error) {
      console.error('Error fetching banks:', error);
      return {
        code: 500,
        status: false,
        message: 'Lỗi khi lấy danh sách ngân hàng',
        data: [],
      };
    }
  },

  // Alternative: Get banks through your backend API
  getBanksFromBackend: async (): Promise<BanksResponse> => {
    const response = await apiService.get<BanksResponse>('/api/banks');
    return response.data;
  },
};

export default banksService;
