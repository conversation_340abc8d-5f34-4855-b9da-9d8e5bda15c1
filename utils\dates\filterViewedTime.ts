import { RecentlyViewedPropertyWithTime } from '@/hooks/useRecentlyViewed';

export type ViewedTimeFilter = 'all' | 'today' | 'yesterday' | 'custom';

export interface ViewedTimeFilterOption {
  value: ViewedTimeFilter;
  label: string;
}

export const VIEWED_TIME_FILTER_OPTIONS: ViewedTimeFilterOption[] = [
  { value: 'all', label: 'Tất cả' },
  { value: 'today', label: 'Hôm nay' },
  { value: 'yesterday', label: 'Hôm qua' },
  { value: 'custom', label: 'Chọn ngày' },
];

/**
 * Kiểm tra xem property có được xem hôm nay không
 */
export function isViewedToday(viewedAt: string): boolean {
  const viewedDate = new Date(viewedAt);
  const today = new Date();

  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const viewedStart = new Date(
    viewedDate.getFullYear(),
    viewedDate.getMonth(),
    viewedDate.getDate()
  );

  return viewedStart.getTime() === todayStart.getTime();
}

/**
 * Kiểm tra xem property có được xem hôm qua không
 */
export function isViewedYesterday(viewedAt: string): boolean {
  const viewedDate = new Date(viewedAt);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  const yesterdayStart = new Date(
    yesterday.getFullYear(),
    yesterday.getMonth(),
    yesterday.getDate()
  );
  const viewedStart = new Date(
    viewedDate.getFullYear(),
    viewedDate.getMonth(),
    viewedDate.getDate()
  );

  return viewedStart.getTime() === yesterdayStart.getTime();
}

/**
 * Kiểm tra xem property có được xem trong ngày cụ thể không
 */
export function isViewedOnDate(viewedAt: string, targetDate: Date): boolean {
  const viewedDate = new Date(viewedAt);
  const targetStart = new Date(
    targetDate.getFullYear(),
    targetDate.getMonth(),
    targetDate.getDate()
  );
  const viewedStart = new Date(
    viewedDate.getFullYear(),
    viewedDate.getMonth(),
    viewedDate.getDate()
  );

  return viewedStart.getTime() === targetStart.getTime();
}

/**
 * Filter properties theo thời gian xem
 */
export function filterPropertiesByViewedTime(
  properties: RecentlyViewedPropertyWithTime[],
  filter: ViewedTimeFilter,
  customDate?: Date
): RecentlyViewedPropertyWithTime[] {
  switch (filter) {
    case 'today':
      return properties.filter(item => isViewedToday(item.viewedAt));
    case 'yesterday':
      return properties.filter(item => isViewedYesterday(item.viewedAt));
    case 'custom':
      if (!customDate) return properties;
      return properties.filter(item => isViewedOnDate(item.viewedAt, customDate));
    case 'all':
    default:
      return properties;
  }
}

/**
 * Đếm số lượng properties theo từng filter
 */
export function getFilterCounts(
  properties: RecentlyViewedPropertyWithTime[]
): Record<ViewedTimeFilter, number> {
  return {
    all: properties.length,
    today: properties.filter(item => isViewedToday(item.viewedAt)).length,
    yesterday: properties.filter(item => isViewedYesterday(item.viewedAt)).length,
    custom: 0, // Custom count sẽ được tính riêng
  };
}

/**
 * Lấy danh sách các ngày đã xem từ recently viewed data
 */
export function getViewedDates(properties: RecentlyViewedPropertyWithTime[]): Date[] {
  const dateStrings = new Set<string>();

  properties.forEach(item => {
    const viewedDate = new Date(item.viewedAt);
    const dateString = `${viewedDate.getFullYear()}-${viewedDate.getMonth()}-${viewedDate.getDate()}`;
    dateStrings.add(dateString);
  });

  return Array.from(dateStrings).map(dateString => {
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year, month, day);
  });
}

/**
 * Kiểm tra xem ngày có trong danh sách ngày đã xem không
 */
export function isDateViewable(date: Date, viewedDates: Date[]): boolean {
  return viewedDates.some(
    viewedDate =>
      viewedDate.getFullYear() === date.getFullYear() &&
      viewedDate.getMonth() === date.getMonth() &&
      viewedDate.getDate() === date.getDate()
  );
}

/**
 * Grouped properties theo thời gian xem
 */
export interface GroupedRecentlyViewed {
  today: RecentlyViewedPropertyWithTime[];
  yesterday: RecentlyViewedPropertyWithTime[];
  older: RecentlyViewedPropertyWithTime[];
}

/**
 * Group properties theo thời gian xem (hôm nay, hôm qua, cũ hơn)
 */
export function groupPropertiesByViewedTime(
  properties: RecentlyViewedPropertyWithTime[]
): GroupedRecentlyViewed {
  const today: RecentlyViewedPropertyWithTime[] = [];
  const yesterday: RecentlyViewedPropertyWithTime[] = [];
  const older: RecentlyViewedPropertyWithTime[] = [];

  properties.forEach(item => {
    if (isViewedToday(item.viewedAt)) {
      today.push(item);
    } else if (isViewedYesterday(item.viewedAt)) {
      yesterday.push(item);
    } else {
      older.push(item);
    }
  });

  return { today, yesterday, older };
}

/**
 * Lấy label cho mỗi group
 */
export function getGroupLabel(group: keyof GroupedRecentlyViewed): string {
  switch (group) {
    case 'today':
      return 'Hôm nay';
    case 'yesterday':
      return 'Hôm qua';
    case 'older':
      return 'Cũ hơn';
    default:
      return '';
  }
}
