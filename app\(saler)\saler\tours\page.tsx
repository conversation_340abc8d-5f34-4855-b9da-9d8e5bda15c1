'use client';
import React from 'react';
import { SiteHeader } from '@/components/common/siteHeader';
import { useSalerTours } from '@/hooks/useTour';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TourPropertiesTable } from './components/TourPropertiesTable';

const ToursPage = () => {
  const { tourProperties, isLoading, isError, error, refetch } = useSalerTours();
  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <header className="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-12 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear">
        <SiteHeader title="Phê duyệt lịch trình xem nhà " />
      </header>

      <div className="p-20">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Đang tải tour của bạn...</span>
          </div>
        ) : isError ? (
          <div className="text-center text-red-500 py-8">
            <p>{error?.message || 'Không thể tải danh sách tour.'}</p>
            <Button onClick={() => refetch()} className="mt-4">
              Thử lại
            </Button>
          </div>
        ) : (
          <>
            {tourProperties && tourProperties.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-lg text-muted-foreground mb-4">Bạn chưa có lịch trình nào.</p>
              </div>
            ) : (
              <TourPropertiesTable disabled={isLoading} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ToursPage;
