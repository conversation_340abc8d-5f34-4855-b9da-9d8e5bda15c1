'use client';

import * as React from 'react';
import { ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { DayButton, DayPicker, getDefaultClassNames } from 'react-day-picker';

// Utility function to combine class names
function cn(...classes: (string | undefined | null | boolean)[]): string {
  return classes.filter(Boolean).join(' ');
}
// Button variants utility
const buttonVariants = ({ variant = 'default' }: { variant?: string } = {}) => {
  const variants = {
    default: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500',
    ghost: 'bg-transparent hover:bg-slate-100 text-slate-700 hover:text-slate-900',
    outline: 'border border-slate-300 bg-white hover:bg-slate-50 text-slate-700',
  };

  return `inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ${variants[variant as keyof typeof variants] || variants.default}`;
};
// Button component
const Button = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: string;
    size?: string;
  }
>(({ className, variant = 'default', size = 'default', ...props }, ref) => {
  const sizes = {
    default: 'h-10 px-4 py-2',
    sm: 'h-8 px-3 py-1.5 text-xs',
    lg: 'h-12 px-6 py-3',
    icon: 'h-10 w-10 p-0',
  };

  return (
    <button
      className={cn(
        buttonVariants({ variant }),
        sizes[size as keyof typeof sizes] || sizes.default,
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Button.displayName = 'Button';

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  captionLayout = 'label',
  formatters,
  components,
  ...props
}: React.ComponentProps<typeof DayPicker>) {
  const defaultClassNames = getDefaultClassNames();

  return (
    <div className=" flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl shadow-red-500/10 border border-slate-200/50 overflow-hidden max-w-md w-full">
        <DayPicker
          showOutsideDays={showOutsideDays}
          className={cn(
            'bg-white group/calendar p-6 [--cell-size:3rem] transition-all duration-300',
            String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,
            String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,
            className
          )}
          captionLayout={captionLayout}
          weekStartsOn={1}
          formatters={{
            formatMonthDropdown: date => {
              const months = [
                'Tháng 1',
                'Tháng 2',
                'Tháng 3',
                'Tháng 4',
                'Tháng 5',
                'Tháng 6',
                'Tháng 7',
                'Tháng 8',
                'Tháng 9',
                'Tháng 10',
                'Tháng 11',
                'Tháng 12',
              ];
              return months[date.getMonth()];
            },
            formatWeekdayName: date => {
              const weekdays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
              return weekdays[date.getDay()];
            },
            formatCaption: date => {
              const months = [
                'Tháng 1',
                'Tháng 2',
                'Tháng 3',
                'Tháng 4',
                'Tháng 5',
                'Tháng 6',
                'Tháng 7',
                'Tháng 8',
                'Tháng 9',
                'Tháng 10',
                'Tháng 11',
                'Tháng 12',
              ];
              return `${months[date.getMonth()]} ${date.getFullYear()}`;
            },
            ...formatters,
          }}
          classNames={{
            root: cn('w-full', defaultClassNames.root),
            months: cn('relative flex flex-col gap-6 md:flex-row', defaultClassNames.months),
            month: cn('flex w-full flex-col gap-4 text-center', defaultClassNames.month),
            nav: cn(
              'absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1 z-10',
              defaultClassNames.nav
            ),
            button_previous: cn(
              'h-10 w-10 rounded-full bg-gradient-to-r from-red-500 to-red-400 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 transition-all duration-200 disabled:opacity-50 disabled:hover:scale-100',
              defaultClassNames.button_previous
            ),
            button_next: cn(
              'h-10 w-10 rounded-full bg-gradient-to-r from-red-500 to-red-400 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95 transition-all duration-200 disabled:opacity-50 disabled:hover:scale-100',
              defaultClassNames.button_next
            ),
            month_caption: cn(
              'flex h-12 w-full items-center justify-center px-12 mb-2',
              defaultClassNames.month_caption
            ),
            dropdowns: cn(
              'flex h-12 w-full items-center justify-center gap-2 text-lg font-bold text-slate-800',
              defaultClassNames.dropdowns
            ),
            dropdown_root: cn(
              'relative rounded-lg border border-slate-200 shadow-sm hover:border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-400 transition-all duration-200',
              defaultClassNames.dropdown_root
            ),
            dropdown: cn(
              'bg-white absolute inset-0 opacity-0 rounded-lg',
              defaultClassNames.dropdown
            ),
            caption_label: cn(
              'select-none font-bold text-lg text-slate-800 tracking-wide',
              captionLayout === 'label'
                ? 'text-lg'
                : 'flex h-10 items-center gap-2 rounded-lg px-3 py-2 text-sm bg-slate-50 hover:bg-slate-100 transition-colors duration-200 [&>svg]:size-4 [&>svg]:text-slate-600',
              defaultClassNames.caption_label
            ),
            table: 'w-full border-collapse mt-4',
            weekdays: cn('flex mb-2', defaultClassNames.weekdays),
            weekday: cn(
              'text-slate-600 flex-1 select-none text-sm font-semibold uppercase tracking-wider py-2',
              defaultClassNames.weekday
            ),
            week: cn('flex w-full gap-1', defaultClassNames.week),
            week_number_header: cn(
              'w-[--cell-size] select-none',
              defaultClassNames.week_number_header
            ),
            week_number: cn(
              'text-slate-500 select-none text-sm font-medium',
              defaultClassNames.week_number
            ),
            day: cn(
              'group/day relative aspect-square h-full w-full select-none p-0.5 text-center',
              defaultClassNames.day
            ),
            range_start: cn('bg-red-100 rounded-l-lg', defaultClassNames.range_start),
            range_middle: cn('bg-red-50 rounded-none', defaultClassNames.range_middle),
            range_end: cn('bg-red-100 rounded-r-lg', defaultClassNames.range_end),
            today: cn(
              'bg-gray-200 text-black font-bold shadow-sm rounded-lg',
              defaultClassNames.today
            ),
            outside: cn(
              'text-slate-400 hover:text-slate-600 opacity-60',
              defaultClassNames.outside
            ),
            disabled: cn(
              'text-slate-300 opacity-40 cursor-not-allowed',
              defaultClassNames.disabled
            ),
            hidden: cn('invisible', defaultClassNames.hidden),
            ...classNames,
          }}
          components={{
            Root: ({ className, rootRef, ...props }) => {
              return (
                <div data-slot="calendar" ref={rootRef} className={cn(className)} {...props} />
              );
            },
            Chevron: ({ className, orientation, ...props }) => {
              if (orientation === 'left') {
                return <ChevronLeftIcon className={cn('size-5', className)} {...props} />;
              }

              if (orientation === 'right') {
                return <ChevronRightIcon className={cn('size-5', className)} {...props} />;
              }

              return <ChevronDownIcon className={cn('size-4', className)} {...props} />;
            },
            DayButton: CalendarDayButton,
            WeekNumber: ({ children, ...props }) => {
              return (
                <td {...props}>
                  <div className="flex size-[--cell-size] items-center justify-center text-center">
                    {children}
                  </div>
                </td>
              );
            },
            ...components,
          }}
          {...props}
        />
        {/* Footer with legend */}
        <div className="px-6 pb-6 pt-2 border-t border-slate-100">
          <div className="flex items-center justify-between text-sm text-slate-500">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-400 border-2 border-red-500 rounded-full"></div>
              <span className="font-medium">Hôm nay</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-gradient-to-r from-red-500 to-red-400 rounded-full"></div>
              <span className="font-medium">Đã chọn</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
function CalendarDayButton({
  className,
  day,
  modifiers,
  ...props
}: React.ComponentProps<typeof DayButton>) {
  const defaultClassNames = getDefaultClassNames();

  const ref = React.useRef<HTMLButtonElement>(null);
  React.useEffect(() => {
    if (modifiers.focused) ref.current?.focus();
  }, [modifiers.focused]);

  const isSelected =
    modifiers.selected && !modifiers.range_start && !modifiers.range_end && !modifiers.range_middle;
  const isToday = modifiers.today;
  const isOutside = modifiers.outside;

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="icon"
      data-day={day.date.toLocaleDateString()}
      data-selected-single={isSelected}
      data-range-start={modifiers.range_start}
      data-range-end={modifiers.range_end}
      data-range-middle={modifiers.range_middle}
      className={cn(
        'relative aspect-square h-full w-full min-w-[--cell-size] rounded-lg text-sm font-semibold transition-all duration-200 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-1',
        // Default state
        'text-slate-700 hover:bg-slate-100 hover:text-slate-900',
        // Selected state
        isSelected &&
          'bg-gradient-to-br from-red-500 to-red-400 text-white shadow-lg hover:shadow-xl hover:from-red-600 hover:to-red-600',
        // Today state (when not selected)
        isToday && !isSelected && 'bg-gray-200 text-emerald-800 rounded-lg  font-bold ',
        // Range states
        modifiers.range_start &&
          'bg-gradient-to-r from-red-500 to-red-400 text-white rounded-l-lg rounded-r-none shadow-md',
        modifiers.range_end &&
          'bg-gradient-to-r from-red-400 to-red-500 text-white rounded-r-lg rounded-l-none shadow-md',
        modifiers.range_middle && 'bg-red-400 text-red-800 rounded-none',
        // Outside month
        isOutside && 'text-slate-400 hover:text-slate-600 opacity-60',
        // Disabled
        modifiers.disabled &&
          'text-slate-300 opacity-40 cursor-not-allowed hover:scale-100 hover:bg-transparent',
        // Focus state
        'group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10',
        defaultClassNames.day,
        className
      )}
      {...props}
    >
      <span className="relative z-10">{day.date.getDate()}</span>
      {/* Hover effect overlay */}
      <div className="absolute inset-0  transition-opacity duration-200 rounded-lg" />
      {/* Today indicator dot */}
      {isToday && !isSelected && (
        <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-red-500 rounded-full" />
      )}
      {/* Selected state shine effect */}
      {isSelected && (
        <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-lg" />
      )}
    </Button>
  );
}

export { Calendar, CalendarDayButton };
