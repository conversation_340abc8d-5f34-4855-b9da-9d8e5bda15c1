import { useMutation } from '@tanstack/react-query';
import attachmentService, {
  RequestAttachment,
  RequestVideoAttachment,
  ResponseAttachment,
  ResponseSingleAttachment,
} from '@/lib/api/services/fetchAttachment';
import { toast } from 'sonner';

export function useUploadAttachment() {
  return useMutation({
    mutationFn: (request: RequestAttachment) => attachmentService.uploadAttachment(request),
    onSuccess: (data: ResponseAttachment) => {
      toast.success(data.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUploadPropertyFloorPlan() {
  return useMutation({
    mutationFn: (request: RequestAttachment) => attachmentService.uploadPropertyFloorPlan(request),
    onSuccess: (data: ResponseAttachment) => {
      toast.success(data.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUploadPropertyVideo() {
  return useMutation({
    mutationFn: (request: RequestVideoAttachment) => attachmentService.uploadPropertyVideo(request),
    onSuccess: (data: ResponseSingleAttachment) => {
      toast.success(data.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUploadPropertyLegalDocument() {
  return useMutation({
    mutationFn: (request: RequestAttachment) => attachmentService.uploadPropertyDocument(request),
    onSuccess: (data: ResponseAttachment) => {
      toast.success(data.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUploadPropertyImage() {
  return useMutation({
    mutationFn: (request: RequestAttachment) => attachmentService.uploadPropertyImage(request),
    onSuccess: (data: ResponseAttachment) => {
      toast.success(data.message);
      return data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
