import conversationService, {
  ConversationResponse,
  PagingConversation,
} from '@/lib/api/services/fetchConversation';
import { useMutation, useQuery } from '@tanstack/react-query';

export function useGetConversation(pagingRequest: PagingConversation) {
  return useQuery({
    queryKey: ['conversations', pagingRequest],
    queryFn: () => conversationService.getConversation(pagingRequest),
    placeholderData: previousData => previousData, //Keep old data
    select: (data: ConversationResponse) => ({
      ...data,
    }),
  });
}

export function useNewSystemMessage() {
  return useMutation({
    mutationFn: (messageData: {
      content: string;
      conversationId: string;
      attachmentIds?: string[];
      replyToMessageId?: string;
    }) => conversationService.newSystemMessage(messageData),
  });
}
