'use client';

import { useState, useRef, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle2, Circle, Info } from 'lucide-react';
import { useCreateProperty, useProperty, useUpdateProperty } from '@/hooks/useProperty';
import {
  useUploadPropertyImage,
  useUploadPropertyFloorPlan,
  useUploadPropertyVideo,
  useUploadPropertyLegalDocument,
} from '@/hooks/useAttachment';
import { toast } from 'sonner';
import {
  PropertyDetailRequest,
  PropertyType,
  PropertyStatus,
  ApartmentOrientation,
  Currency,
  TransactionType,
} from '@/lib/api/services/fetchProperty';

import BasicDetailsForm from './components/basicDetailsForm';
import PropertyDetailsForm from './components/propertyDetailsForm';
import LocationDetailsForm from './components/locationDetailsForm';
import PricingDetailsForm from './components/pricingDetailsForm';
import OwnerDocumentsForm from './components/ownerDocumentsForm';
import { SiteHeader } from '@/components/common/siteHeader';

// Define a type for the property update payload
interface PropertyUpdatePayload {
  id: string;
  property: Partial<PropertyDetailRequest>;
}

const STEPS = [
  { id: 'basic', label: 'Thông tin cơ bản', description: 'Tên và mô tả bất động sản' },
  { id: 'property', label: 'Thông tin bất động sản', description: 'Phòng và đặc điểm' },
  { id: 'location', label: 'Địa chỉ', description: 'Địa chỉ và tọa độ' },
  { id: 'pricing', label: 'Giá cả', description: 'Giá và phương thức thanh toán' },
  {
    id: 'owner',
    label: 'Hình ảnh và tài liệu pháp lý',
    description: 'Hình ảnh và tài liệu pháp lý',
  },
];

export default function Page() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-full">
          <div className="w-8 h-8 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
        </div>
      }
    >
      <PropertyActionPage />
    </Suspense>
  );
}

function PropertyActionPage() {
  const searchParams = useSearchParams();
  const propertyId = searchParams.get('id') || undefined;
  const isEditMode = !!propertyId;

  const { mutate: createProperty, isPending: isCreating } = useCreateProperty();
  const { mutate: updateProperty, isPending: isUpdating } = useUpdateProperty();
  const { data: existingProperty, isLoading } = useProperty(propertyId);

  // Upload mutation hooks
  const { mutate: uploadImage, isPending: isUploadingImages } = useUploadPropertyImage();
  const { mutate: uploadFloorPlan, isPending: isUploadingFloorPlans } =
    useUploadPropertyFloorPlan();
  const { mutate: uploadVideo, isPending: isUploadingVideos } = useUploadPropertyVideo();
  const { mutate: uploadDocument, isPending: isUploadingDocuments } =
    useUploadPropertyLegalDocument();

  // Define form data with typed structure
  type FormDataType = Omit<PropertyDetailRequest, 'adminNote'> & { adminNote: string };

  const [formData, setFormData] = useState<FormDataType>({
    title: '',
    name: '',
    description: '',
    transactionType: '' as TransactionType,
    type: '' as PropertyType,
    status: '' as PropertyStatus,
    adminNote: '',
    code: '',
    //ownerId: '682ae84c05357353bf122171', // TODO: change to ownerId
    location: {
      address: '',
      latitude: 10.842935416604869,
      longitude: 106.84182012230411,
      city: '',
      district: '',
      ward: '',
    },
    propertyDetails: {
      bedrooms: 0,
      bathrooms: 0,
      livingRooms: 0,
      kitchens: 0,
      landArea: 0,
      landWidth: 0,
      landLength: 0,
      buildingArea: 0,
      numberOfFloors: 0,
      hasBasement: false,
      floorNumber: 0,
      apartmentOrientation: '' as ApartmentOrientation,
      furnished: false,
    },
    priceDetails: {
      salePrice: 0,
      rentalPrice: 0,
      pricePerSquareMeter: 0,
      currency: '' as Currency,
      depositAmount: 0,
      maintenanceFee: 0,
      paymentMethods: [] as string[],
    },
    amenities: {
      parking: false,
      elevator: false,
      swimmingPool: false,
      gym: false,
      securitySystem: false,
      airConditioning: false,
      balcony: false,
      garden: false,
      playground: false,
      backupGenerator: false,
    },
    yearBuilt: 0,
    images: [],
    floorPlans: [],
    video: {
      videoUrl: '',
      title: '',
      description: '',
    },
    legalDocuments: [],
    transactionId: undefined, // TODO: change to transactionId
  });

  // File state management
  const imageInputRef = useRef<HTMLInputElement>(null);
  const documentInputRef = useRef<HTMLInputElement>(null);
  const floorPlanInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [documentFiles, setDocumentFiles] = useState<File[]>([]);
  const [floorPlanFiles, setFloorPlanFiles] = useState<File[]>([]);
  const [videoFiles, setVideoFiles] = useState<File[]>([]);

  // State to track uploaded URLs for new files
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [uploadedDocumentUrls, setUploadedDocumentUrls] = useState<string[]>([]);
  const [uploadedFloorPlanUrls, setUploadedFloorPlanUrls] = useState<string[]>([]);
  const [uploadedVideoUrls, setUploadedVideoUrls] = useState<string[]>([]);

  // Existing files from server (when editing)
  const [existingImages, setExistingImages] = useState<
    Array<{ url: string; name: string; isExisting: boolean }>
  >([]);
  const [existingDocuments, setExistingDocuments] = useState<
    Array<{ url: string; name: string; isExisting: boolean }>
  >([]);
  const [existingFloorPlans, setExistingFloorPlans] = useState<
    Array<{ url: string; name: string; isExisting: boolean }>
  >([]);
  const [existingVideos, setExistingVideos] = useState<
    Array<{ url: string; name: string; isExisting: boolean }>
  >([]);

  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  // Load existing property data when in edit mode
  useEffect(() => {
    if (isEditMode && existingProperty?.data) {
      const propertyData = existingProperty.data;
      setFormData({
        title: propertyData.title,
        name: propertyData.name,
        description: propertyData.description,
        transactionType: propertyData.transactionType,
        type: propertyData.type,
        status: propertyData.status,
        adminNote: propertyData.adminNote || '',
        code: propertyData.code,
        // ownerId: propertyData.owner.id || '682ae84c05357353bf122171', // TODO: change to ownerId
        location: propertyData.location,
        propertyDetails: propertyData.propertyDetails,
        priceDetails: {
          salePrice: propertyData.priceDetails.salePrice || 0,
          rentalPrice: propertyData.priceDetails.rentalPrice || 0,
          pricePerSquareMeter: propertyData.priceDetails.pricePerSquareMeter || 0,
          currency: propertyData.priceDetails.currency || Currency.VND,
          depositAmount: propertyData.priceDetails.depositAmount || 0,
          maintenanceFee: propertyData.priceDetails.maintenanceFee || 0,
          paymentMethods: propertyData.priceDetails.paymentMethods || [],
        },
        amenities: propertyData.amenities,
        yearBuilt: propertyData.yearBuilt || 0,
        images: propertyData.imageUrls || [],
        floorPlans: propertyData.floorPlanUrls || [],
        video: propertyData.video || {
          videoUrl: '',
          title: '',
          description: '',
        },
        legalDocuments: propertyData.legalDocumentUrls || [],
        transactionId: undefined, // TODO: change to transactionId
      });

      // Handle existing images
      if (propertyData.imageUrls && propertyData.imageUrls.length > 0) {
        const previewImages = propertyData.imageUrls.map(url => ({
          url,
          name: url.split('/').pop() || 'image',
          isExisting: true,
        }));
        setExistingImages(previewImages);
      }

      // Handle existing documents
      if (propertyData.legalDocumentUrls && propertyData.legalDocumentUrls.length > 0) {
        const previewDocuments = propertyData.legalDocumentUrls.map(url => ({
          url,
          name: url.split('/').pop() || 'document',
          isExisting: true,
        }));
        setExistingDocuments(previewDocuments);
      }

      // Handle existing floor plans
      if (propertyData.floorPlanUrls && propertyData.floorPlanUrls.length > 0) {
        const previewFloorPlans = propertyData.floorPlanUrls.map(url => ({
          url,
          name: url.split('/').pop() || 'floor plan',
          isExisting: true,
        }));
        setExistingFloorPlans(previewFloorPlans);
      }
    } else if (!isEditMode) {
      // Set default values for new property
      setFormData({
        title: '',
        name: '',
        description: '',
        type: PropertyType.APARTMENT,
        status: PropertyStatus.AVAILABLE,
        transactionType: TransactionType.FOR_SALE,
        code: '',
        adminNote: '',
        // ownerId: '682ae84c05357353bf122171', // TODO: change to ownerId
        propertyDetails: {
          bedrooms: undefined,
          bathrooms: undefined,
          livingRooms: undefined,
          kitchens: undefined,
          landArea: undefined,
          landWidth: undefined,
          landLength: undefined,
          buildingArea: undefined,
          numberOfFloors: undefined,
          hasBasement: false,
          floorNumber: undefined,
          apartmentOrientation: ApartmentOrientation.NORTH,
          furnished: false,
        },
        priceDetails: {
          salePrice: 0,
          rentalPrice: 0,
          pricePerSquareMeter: 0,
          currency: Currency.VND,
          depositAmount: 0,
          maintenanceFee: 0,
          paymentMethods: [],
        },
        location: {
          address: '',
          latitude: 10.842935416604869,
          longitude: 106.84182012230411,
          city: '',
          district: '',
          ward: '',
        },
        amenities: {
          parking: false,
          elevator: false,
          swimmingPool: false,
          gym: false,
          securitySystem: false,
          airConditioning: false,
          balcony: false,
          garden: false,
          playground: false,
          backupGenerator: false,
        },
        yearBuilt: 0,
        images: [],
        floorPlans: [],
        video: {
          videoUrl: '',
          title: '',
          description: '',
        },
        legalDocuments: [],
        transactionId: undefined, // TODO: change to transactionId
      });
    }
  }, [isEditMode, existingProperty]);

  // Type-safe handler for input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;

    if (id.includes('.')) {
      const [section, field] = id.split('.');
      setFormData(prev => {
        if (section === 'location') {
          return {
            ...prev,
            location: {
              ...prev.location,
              [field]: value,
            },
          };
        } else if (section === 'propertyDetails') {
          return {
            ...prev,
            propertyDetails: {
              ...prev.propertyDetails,
              [field]: value,
            },
          };
        } else if (section === 'priceDetails') {
          return {
            ...prev,
            priceDetails: {
              ...prev.priceDetails,
              [field]: value,
            },
          };
        } else if (section === 'owner') {
          return {
            ...prev,
            ownerId: value,
          };
        } else if (section === 'amenities') {
          return {
            ...prev,
            amenities: {
              ...prev.amenities,
              [field]: value,
            },
          };
        } else if (section === 'video') {
          return {
            ...prev,
            video: {
              ...prev.video!,
              [field]: value,
            },
          };
        }
        return prev;
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [id]: value,
      }));
    }
  };

  // Type-safe handler for checkbox changes
  const handleCheckboxChange = (id: string, checked: boolean) => {
    if (id.includes('.')) {
      const [section, field] = id.split('.');
      setFormData(prev => {
        if (section === 'amenities') {
          return {
            ...prev,
            amenities: {
              ...prev.amenities,
              [field]: checked,
            },
          };
        } else if (section === 'propertyDetails') {
          return {
            ...prev,
            propertyDetails: {
              ...prev.propertyDetails,
              [field]: checked,
            },
          };
        }
        return prev;
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [id]: checked,
      }));
    }
  };

  // Type-safe handler for select changes
  const handleSelectChange = (id: string, value: string) => {
    if (id.includes('.')) {
      const [section, field] = id.split('.');
      setFormData(prev => {
        if (section === 'location') {
          return {
            ...prev,
            location: {
              ...prev.location,
              [field]: field === 'latitude' || field === 'longitude' ? parseFloat(value) : value,
            },
          };
        } else if (section === 'propertyDetails') {
          return {
            ...prev,
            propertyDetails: {
              ...prev.propertyDetails,
              [field]: field === 'hasBasement' || field === 'furnished' ? value === 'true' : value,
            },
          };
        } else if (section === 'priceDetails') {
          return {
            ...prev,
            priceDetails: {
              ...prev.priceDetails,
              [field]: field === 'paymentMethods' ? JSON.parse(value) : parseFloat(value),
            },
          };
        } else if (section === 'owner') {
          return {
            ...prev,
            ownerId: value,
          };
        } else if (section === 'video') {
          return {
            ...prev,
            video: {
              ...prev.video!,
              [field]: value,
            },
          };
        }
        return prev;
      });
    } else {
      setFormData(prev => ({
        ...prev,
        [id]: id === 'yearBuilt' ? parseInt(value, 10) : value,
      }));
    }
  };

  // File handling functions
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      setImageFiles(prev => [...prev, ...filesArray]);
      uploadImage(
        { files: filesArray },
        {
          onSuccess: response => {
            // Only update the URLs
            setUploadedImageUrls(prev => [...prev, ...response.data.map(file => file.fileUrl)]);
          },
        }
      );
    }
  };

  const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      setDocumentFiles(prev => [...prev, ...filesArray]);
      uploadDocument(
        { files: filesArray },
        {
          onSuccess: response => {
            setUploadedDocumentUrls(prev => [...prev, ...response.data.map(file => file.fileUrl)]);
          },
        }
      );
    }
  };

  const handleFloorPlanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      setFloorPlanFiles(prev => [...prev, ...filesArray]);
      uploadFloorPlan(
        { files: filesArray },
        {
          onSuccess: response => {
            setUploadedFloorPlanUrls(prev => [...prev, ...response.data.map(file => file.fileUrl)]);
          },
        }
      );
    }
  };

  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      setVideoFiles(prev => [...prev, ...filesArray]);
      uploadVideo(
        { file: filesArray[0] },
        {
          onSuccess: response => {
            setUploadedVideoUrls(prev => [...prev, response.data.fileUrl]);
          },
        }
      );
    }
  };

  // Click handlers
  const handleImageClick = () => imageInputRef.current?.click();
  const handleDocumentClick = () => documentInputRef.current?.click();
  const handleFloorPlanClick = () => floorPlanInputRef.current?.click();
  const handleVideoClick = () => videoInputRef.current?.click();

  // Preview handlers
  const handleImagePreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewImage(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Remove handlers
  const handleRemoveImage = (file: File) => {
    const fileIndex = imageFiles.indexOf(file);
    setImageFiles(prev => prev.filter(f => f !== file));

    // Also remove the corresponding uploaded URL if it exists
    if (fileIndex !== -1 && uploadedImageUrls[fileIndex]) {
      setUploadedImageUrls(prev => prev.filter((_, index) => index !== fileIndex));
    }

    toast.success('Hình ảnh đã được xóa khỏi danh sách');
  };

  const handleRemoveDocument = (file: File) => {
    const fileIndex = documentFiles.indexOf(file);
    setDocumentFiles(prev => prev.filter(f => f !== file));

    // Also remove the corresponding uploaded URL if it exists
    if (fileIndex !== -1 && uploadedDocumentUrls[fileIndex]) {
      setUploadedDocumentUrls(prev => prev.filter((_, index) => index !== fileIndex));
    }

    toast.success('Tài liệu pháp lý đã được xóa khỏi danh sách');
  };

  const handleRemoveFloorPlan = (file: File) => {
    const fileIndex = floorPlanFiles.indexOf(file);
    setFloorPlanFiles(prev => prev.filter(f => f !== file));

    // Also remove the corresponding uploaded URL if it exists
    if (fileIndex !== -1 && uploadedFloorPlanUrls[fileIndex]) {
      setUploadedFloorPlanUrls(prev => prev.filter((_, index) => index !== fileIndex));
    }

    toast.success('Bản vẽ mặt bằng đã được xóa khỏi danh sách');
  };

  const handleRemoveVideo = (file: File) => {
    const fileIndex = videoFiles.indexOf(file);
    setVideoFiles(prev => prev.filter(f => f !== file));

    // Also remove the corresponding uploaded URL if it exists
    if (fileIndex !== -1 && uploadedVideoUrls[fileIndex]) {
      setUploadedVideoUrls(prev => prev.filter((_, index) => index !== fileIndex));
    }

    toast.success('Video đã được xóa khỏi danh sách');
  };

  // Remove existing handlers
  const handleRemoveExistingImage = (url: string) => {
    setExistingImages(prev => prev.filter(image => image.url !== url));
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(imgUrl => imgUrl !== url),
    }));
    toast.success('Hình ảnh đã được xóa khỏi danh sách');
  };

  const handleRemoveExistingDocument = (url: string) => {
    setExistingDocuments(prev => prev.filter(doc => doc.url !== url));
    setFormData(prev => ({
      ...prev,
      legalDocuments: prev.legalDocuments.filter(docUrl => docUrl !== url),
    }));
    toast.success('Tài liệu pháp lý đã được xóa khỏi danh sách');
  };

  const handleRemoveExistingFloorPlan = (url: string) => {
    setExistingFloorPlans(prev => prev.filter(plan => plan.url !== url));
    setFormData(prev => ({
      ...prev,
      floorPlans: prev.floorPlans.filter(planUrl => planUrl !== url),
    }));
    toast.success('Bản vẽ mặt bằng đã được xóa khỏi danh sách');
  };

  const handleRemoveExistingVideo = (url: string) => {
    setExistingVideos(prev => prev.filter(video => video.url !== url));
    if (formData.video?.videoUrl === url) {
      setFormData(prev => ({
        ...prev,
        video: {
          videoUrl: '',
          title: '',
          description: '',
        },
      }));
    }
    toast.success('Video đã được xóa khỏi danh sách');
  };

  // Reorder handlers for drag and drop - Updated to handle combined arrays
  const handleReorderAllImages = (startIndex: number, endIndex: number) => {
    // Create a combined array of all images (existing + uploaded)
    const allImages = [
      ...existingImages.map(img => ({ ...img, type: 'existing' as const })),
      ...uploadedImageUrls.map((url, idx) => ({
        url,
        name: imageFiles[idx]?.name || url.split('/').pop() || 'Unknown',
        isExisting: false,
        type: 'uploaded' as const,
        fileIndex: idx,
      })),
    ];

    // Reorder the combined array
    const reorderedImages = Array.from(allImages);
    const [removed] = reorderedImages.splice(startIndex, 1);
    reorderedImages.splice(endIndex, 0, removed);

    // Split back into existing and uploaded arrays
    const newExistingImages = reorderedImages
      .filter(img => img.type === 'existing')
      .map(img => ({ url: img.url, name: img.name, isExisting: true }));

    const newUploadedImages = reorderedImages.filter(img => img.type === 'uploaded');

    // Update states
    setExistingImages(newExistingImages);

    // Reorder uploaded URLs and files to match
    const newUploadedImageUrls: string[] = [];
    const newImageFiles: File[] = [];

    newUploadedImages.forEach(img => {
      newUploadedImageUrls.push(img.url);
      if ('fileIndex' in img && typeof img.fileIndex === 'number') {
        newImageFiles.push(imageFiles[img.fileIndex]);
      }
    });

    setUploadedImageUrls(newUploadedImageUrls);
    setImageFiles(newImageFiles);

    // Update formData to match new order
    const allImageUrls = reorderedImages.map(img => img.url);
    setFormData(prev => ({
      ...prev,
      images: allImageUrls,
    }));
  };

  const handleReorderAllFloorPlans = (startIndex: number, endIndex: number) => {
    // Create a combined array of all floor plans (existing + uploaded)
    const allFloorPlans = [
      ...existingFloorPlans.map(plan => ({ ...plan, type: 'existing' as const })),
      ...uploadedFloorPlanUrls.map((url, idx) => ({
        url,
        name: floorPlanFiles[idx]?.name || url.split('/').pop() || 'Unknown',
        isExisting: false,
        type: 'uploaded' as const,
        fileIndex: idx,
      })),
    ];

    // Reorder the combined array
    const reorderedFloorPlans = Array.from(allFloorPlans);
    const [removed] = reorderedFloorPlans.splice(startIndex, 1);
    reorderedFloorPlans.splice(endIndex, 0, removed);

    // Split back into existing and uploaded arrays
    const newExistingFloorPlans = reorderedFloorPlans
      .filter(plan => plan.type === 'existing')
      .map(plan => ({ url: plan.url, name: plan.name, isExisting: true }));

    const newUploadedFloorPlans = reorderedFloorPlans.filter(plan => plan.type === 'uploaded');

    // Update states
    setExistingFloorPlans(newExistingFloorPlans);

    // Reorder uploaded URLs and files to match
    const newUploadedFloorPlanUrls: string[] = [];
    const newFloorPlanFiles: File[] = [];

    newUploadedFloorPlans.forEach(plan => {
      newUploadedFloorPlanUrls.push(plan.url);
      if ('fileIndex' in plan && typeof plan.fileIndex === 'number') {
        newFloorPlanFiles.push(floorPlanFiles[plan.fileIndex]);
      }
    });

    setUploadedFloorPlanUrls(newUploadedFloorPlanUrls);
    setFloorPlanFiles(newFloorPlanFiles);

    // Update formData to match new order
    const allFloorPlanUrls = reorderedFloorPlans.map(plan => plan.url);
    setFormData(prev => ({
      ...prev,
      floorPlans: allFloorPlanUrls,
    }));
  };

  // Navigation handlers
  const handleNextTab = () => {
    const tabOrder = ['basic', 'property', 'location', 'pricing', 'owner'];
    const currentIndex = tabOrder.indexOf(activeTab);

    if (currentIndex < tabOrder.length - 1) {
      setActiveTab(tabOrder[currentIndex + 1]);
    }
  };

  // Validation functions
  const validateBasicTab = () => {
    if (!formData.title || !formData.type || !formData.description || !formData.transactionType) {
      toast.error('Vui lòng điền đầy đủ các trường trong Chi tiết cơ bản');
      return false;
    }
    return true;
  };

  // Utility function to determine if room-related fields should be validated
  const shouldValidateRoomFields = (propertyType: PropertyType): boolean => {
    const landAndCommercialTypes = [
      PropertyType.LAND_PLOT,
      PropertyType.PROJECT_LAND,
      PropertyType.OFFICE,
      PropertyType.WAREHOUSE,
      PropertyType.FACTORY,
      PropertyType.INDUSTRIAL,
      PropertyType.NEW_URBAN_AREA,
      PropertyType.ECO_RESORT,
      PropertyType.OTHER,
    ];

    return !landAndCommercialTypes.includes(propertyType);
  };

  // Utility function to determine if building structure fields should be validated
  const shouldValidateBuildingStructureFields = (propertyType: PropertyType): boolean => {
    const landTypes = [
      PropertyType.LAND_PLOT,
      PropertyType.PROJECT_LAND,
      PropertyType.NEW_URBAN_AREA,
      PropertyType.ECO_RESORT,
    ];

    return !landTypes.includes(propertyType);
  };

  // Utility function to determine area field types based on property type
  const getAreaFieldType = (
    propertyType: PropertyType
  ): 'residential' | 'land' | 'commercial' | 'house' => {
    const apartmentTypes = [
      PropertyType.APARTMENT,
      PropertyType.MINI_SERVICE_APARTMENT,
      PropertyType.SOCIAL_HOUSING,
      PropertyType.MOTEL,
      PropertyType.AIRBNB,
      PropertyType.HOTEL,
    ];

    const houseTypes = [
      PropertyType.HOUSE,
      PropertyType.TOWNHOUSE,
      PropertyType.VILLA,
      PropertyType.SHOP_HOUSE,
      PropertyType.COMMERCIAL_TOWNHOUSE,
    ];

    const landTypes = [
      PropertyType.LAND_PLOT,
      PropertyType.PROJECT_LAND,
      PropertyType.NEW_URBAN_AREA,
      PropertyType.ECO_RESORT,
    ];

    if (apartmentTypes.includes(propertyType)) {
      return 'residential';
    } else if (houseTypes.includes(propertyType)) {
      return 'house';
    } else if (landTypes.includes(propertyType)) {
      return 'land';
    } else {
      return 'commercial';
    }
  };

  const validatePropertyTab = () => {
    const areaFieldType = getAreaFieldType(formData.type);

    // Validate area fields based on property type
    if (areaFieldType === 'land') {
      // Land properties only need land area
      if (!formData.propertyDetails.landArea || formData.propertyDetails.landArea <= 0) {
        toast.error('Vui lòng nhập diện tích đất');
        return false;
      }
    } else if (areaFieldType === 'residential') {
      // Apartment properties need building area (thông thủy)
      if (!formData.propertyDetails.buildingArea || formData.propertyDetails.buildingArea <= 0) {
        toast.error('Vui lòng nhập diện tích thông thủy');
        return false;
      }
    } else if (areaFieldType === 'house') {
      // House properties need both land area and building area (thông thủy)
      if (!formData.propertyDetails.landArea || formData.propertyDetails.landArea <= 0) {
        toast.error('Vui lòng nhập diện tích đất');
        return false;
      }
      if (!formData.propertyDetails.buildingArea || formData.propertyDetails.buildingArea <= 0) {
        toast.error('Vui lòng nhập diện tích thông thủy');
        return false;
      }
    } else if (areaFieldType === 'commercial') {
      // Commercial properties need both land and building areas
      if (!formData.propertyDetails.landArea || formData.propertyDetails.landArea <= 0) {
        toast.error('Vui lòng nhập diện tích đất');
        return false;
      }
      if (!formData.propertyDetails.buildingArea || formData.propertyDetails.buildingArea <= 0) {
        toast.error('Vui lòng nhập diện tích nhà');
        return false;
      }
    }

    // Validate room fields only for residential properties
    if (shouldValidateRoomFields(formData.type)) {
      if (!formData.propertyDetails.bedrooms || formData.propertyDetails.bedrooms < 0) {
        toast.error('Vui lòng nhập số phòng ngủ');
        return false;
      }
      if (!formData.propertyDetails.bathrooms || formData.propertyDetails.bathrooms < 0) {
        toast.error('Vui lòng nhập số phòng tắm');
        return false;
      }
      if (!formData.propertyDetails.livingRooms || formData.propertyDetails.livingRooms < 0) {
        toast.error('Vui lòng nhập số phòng khách');
        return false;
      }
    }

    // Validate building structure fields only for properties with buildings
    if (shouldValidateBuildingStructureFields(formData.type)) {
      if (
        !formData.propertyDetails.numberOfFloors ||
        formData.propertyDetails.numberOfFloors <= 0
      ) {
        toast.error('Vui lòng nhập số tầng');
        return false;
      }
      if (!formData.propertyDetails.floorNumber || formData.propertyDetails.floorNumber < 0) {
        toast.error('Vui lòng nhập tầng của bất động sản');
        return false;
      }
      if (!formData.propertyDetails.apartmentOrientation) {
        toast.error('Vui lòng chọn hướng của bất động sản');
        return false;
      }
    }

    return true;
  };

  const validateLocationTab = () => {
    if (
      !formData.location.address ||
      !formData.location.city ||
      !formData.location.district ||
      !formData.location.ward
    ) {
      toast.error('Vui lòng điền đầy đủ các trường trong Vị trí');
      return false;
    }
    return true;
  };

  const validatePricingTab = () => {
    return true;
  };

  const handleNext = () => {
    let isValid = false;

    switch (activeTab) {
      case 'basic':
        isValid = validateBasicTab();
        break;
      case 'property':
        isValid = validatePropertyTab();
        break;
      case 'location':
        isValid = validateLocationTab();
        break;
      case 'pricing':
        isValid = validatePricingTab();
        break;
      default:
        isValid = true;
    }

    if (isValid) {
      handleNextTab();
    }
  };

  // Upload files helper function - now simplified since files are uploaded immediately
  const getAllUploadedUrls = () => {
    return {
      imageUrls: [...formData.images, ...uploadedImageUrls], // Combine existing + new
      documentUrls: [...formData.legalDocuments, ...uploadedDocumentUrls],
      floorPlanUrls: [...formData.floorPlans, ...uploadedFloorPlanUrls],
      videoUrls: formData.video?.videoUrl
        ? [formData.video.videoUrl, ...uploadedVideoUrls]
        : uploadedVideoUrls,
    };
  };

  // Main submit handler
  const handleSubmit = async () => {
    // Validation
    if (
      !formData.title ||
      // !formData.name ||
      !formData.type ||
      // !formData.status ||
      !formData.transactionType ||
      !formData.description
    ) {
      toast.error('Vui lòng điền đầy đủ các trường (tiêu đề, loại, mô tả và loại giao dịch)');
      return;
    }

    if (
      !formData.location.address ||
      !formData.location.city ||
      !formData.location.district ||
      !formData.location.ward
    ) {
      toast.error('Vui lòng điền đầy đủ các trường trong Vị trí');
      return;
    }

    // Get all uploaded URLs
    const allUrls = getAllUploadedUrls();

    // Validate files
    if (allUrls.imageUrls.length === 0) {
      toast.error('Vui lòng tải lên ít nhất một hình ảnh');
      return;
    }

    // if (allUrls.documentUrls.length === 0) {
    //   toast.error('Vui lòng tải lên ít nhất một tài liệu pháp lý');
    //   return;
    // }

    // Validate pricing
    if (formData.transactionType === TransactionType.FOR_RENT) {
      if (!formData.priceDetails.rentalPrice || formData.priceDetails.rentalPrice <= 0) {
        toast.error('Giá thuê phải lớn hơn 0');
        return;
      }
    } else if (formData.transactionType === TransactionType.FOR_SALE) {
      if (!formData.priceDetails.salePrice || formData.priceDetails.salePrice <= 0) {
        toast.error('Giá bán phải lớn hơn 0');
        return;
      }
    }

    if (
      !formData.priceDetails.paymentMethods ||
      formData.priceDetails.paymentMethods.length === 0
    ) {
      toast.error('Vui lòng chọn ít nhất một phương thức thanh toán');
      return;
    }

    try {
      // Show loading toast
      toast.info(
        `Vui lòng chờ trong khi chúng tôi ${isEditMode ? 'cập nhật' : 'tạo'} bất động sản của bạn...`
      );

      // Prepare property data with all URLs
      const propertyData: Partial<PropertyDetailRequest> = {
        ...formData,
        images: allUrls.imageUrls,
        legalDocuments: allUrls.documentUrls,
        floorPlans: allUrls.floorPlanUrls,
        video:
          allUrls.videoUrls.length > 0
            ? {
                videoUrl: allUrls.videoUrls[0], // Use first video URL
                title: formData.video?.title || '',
                description: formData.video?.description || '',
              }
            : formData.video,
      };

      if (isEditMode && propertyId) {
        const updatePayload: PropertyUpdatePayload = {
          id: propertyId,
          property: propertyData,
        };

        updateProperty(updatePayload);
      } else {
        createProperty(propertyData);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Lỗi khi xử lý yêu cầu');
    }
  };

  // Helper function to check if a step is completed
  const isStepCompleted = (stepId: string): boolean => {
    switch (stepId) {
      case 'basic':
        return !!(
          formData.title &&
          formData.description &&
          formData.transactionType &&
          formData.type
        );
      case 'property': {
        const areaFieldType = getAreaFieldType(formData.type);

        // Check area fields based on property type
        let hasAreaFields = false;
        if (areaFieldType === 'land') {
          // Land properties only need land area
          hasAreaFields = Boolean(
            formData.propertyDetails.landArea && formData.propertyDetails.landArea > 0
          );
        } else if (areaFieldType === 'residential') {
          // Apartment properties need building area (thông thủy)
          hasAreaFields = Boolean(
            formData.propertyDetails.buildingArea && formData.propertyDetails.buildingArea > 0
          );
        } else if (areaFieldType === 'house') {
          // House properties need both land area and building area (thông thủy)
          hasAreaFields = Boolean(
            formData.propertyDetails.landArea &&
              formData.propertyDetails.landArea > 0 &&
              formData.propertyDetails.buildingArea &&
              formData.propertyDetails.buildingArea > 0
          );
        } else if (areaFieldType === 'commercial') {
          // Commercial properties need both land and building areas
          hasAreaFields = Boolean(
            formData.propertyDetails.landArea &&
              formData.propertyDetails.landArea > 0 &&
              formData.propertyDetails.buildingArea &&
              formData.propertyDetails.buildingArea > 0
          );
        }

        // Check room fields only for residential properties
        let hasRoomFields = true;
        if (shouldValidateRoomFields(formData.type)) {
          hasRoomFields = Boolean(
            formData.propertyDetails.bedrooms &&
              formData.propertyDetails.bathrooms &&
              formData.propertyDetails.livingRooms
          );
        }

        // Check building structure fields only for properties with buildings
        let hasBuildingFields = true;
        if (shouldValidateBuildingStructureFields(formData.type)) {
          hasBuildingFields = Boolean(
            formData.propertyDetails.numberOfFloors &&
              formData.propertyDetails.floorNumber &&
              formData.propertyDetails.apartmentOrientation
          );
        }

        return hasAreaFields && hasRoomFields && hasBuildingFields;
      }
      case 'location':
        return !!(formData.location.address && formData.location.city);
      case 'pricing':
        if (formData.transactionType === TransactionType.FOR_SALE) {
          return !!formData.priceDetails.salePrice && !!formData.priceDetails.paymentMethods;
        } else {
          return !!formData.priceDetails.rentalPrice && !!formData.priceDetails.paymentMethods;
        }
      case 'owner':
        return imageFiles.length > 0 || existingImages.length > 0;
      default:
        return false;
    }
  };

  // Calculate overall progress
  const completedSteps = STEPS.filter(step => isStepCompleted(step.id)).length;
  const progressPercentage = (completedSteps / STEPS.length) * 100;

  const isUploading =
    isUploadingImages || isUploadingDocuments || isUploadingFloorPlans || isUploadingVideos;

  if (isEditMode && isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-8 h-8 border-2 border-primary/50 border-t-primary animate-spin rounded-full" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <SiteHeader title={isEditMode ? 'Chỉnh sửa bất động sản' : 'Tạo bất động sản mới'} />
      <div className="w-full">
        {/* Progress Header */}
        <div className="border-b bg-muted/30 px-4 md:px-6 py-4 md:py-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <div className="flex items-center gap-4">
                <span className="text-muted-foreground">Tổng tiến trình</span>
                <Badge variant="outline" className="px-3 py-1 w-fit">
                  {completedSteps} trên {STEPS.length} đã hoàn thành
                </Badge>
              </div>
              <span className="font-medium">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </div>

        {/* Mobile Step Navigation */}
        <div className="md:hidden border-b bg-background">
          <div className="px-4 py-4">
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
              {STEPS.map((step, index) => {
                const isActive = activeTab === step.id;
                const isCompleted = isStepCompleted(step.id);

                return (
                  <button
                    key={step.id}
                    onClick={() => setActiveTab(step.id)}
                    className={`flex-shrink-0 flex items-center gap-2 px-3 py-2 rounded-lg border text-sm transition-all whitespace-nowrap ${
                      isActive
                        ? 'border-primary bg-primary/5 text-primary'
                        : 'border-border hover:border-primary/50 hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {isCompleted ? (
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                      ) : (
                        <Circle className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="text-xs font-medium text-muted-foreground">{index + 1}</span>
                    </div>
                    <span className="font-medium">{step.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto scrollbar-hide">
        <div className="flex flex-col lg:flex-row flex-1 gap-4 md:gap-6 lg:gap-8 p-4 md:p-6">
          {/* Desktop Step Navigation Sidebar */}
          <div className="hidden lg:block w-80 shrink-0">
            <Card className="sticky top-6">
              <CardContent className="p-6">
                <h3 className="font-medium mb-4">Các bước</h3>
                <div className="space-y-4">
                  {STEPS.map((step, index) => {
                    const isActive = activeTab === step.id;
                    const isCompleted = isStepCompleted(step.id);

                    return (
                      <button
                        key={step.id}
                        onClick={() => setActiveTab(step.id)}
                        className={`w-full text-left p-3 rounded-lg border transition-all ${
                          isActive
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-primary/50 hover:bg-muted/50'
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className="mt-0.5">
                            {isCompleted ? (
                              <CheckCircle2 className="h-5 w-5 text-green-600" />
                            ) : (
                              <Circle className="h-5 w-5 text-muted-foreground" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium text-muted-foreground">
                                Bước {index + 1}
                              </span>
                              {isCompleted && (
                                <Badge variant="secondary" className="h-5 text-xs">
                                  Hoàn thành
                                </Badge>
                              )}
                            </div>
                            <p className={`font-medium text-sm ${isActive ? 'text-primary' : ''}`}>
                              {step.label}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">{step.description}</p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>

                {/* Help Section */}
                <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Cần trợ giúp?</p>
                      <p className="text-xs text-muted-foreground">
                        Điền đầy đủ các trường bắt buộc ở mỗi bước. Bạn có thể chuyển đổi giữa các
                        bước bất cứ lúc nào.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Form Content */}
          <div className="flex-1 min-w-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              {/* Hidden tab list for accessibility */}
              <TabsList className="sr-only">
                {STEPS.map(step => (
                  <TabsTrigger key={step.id} value={step.id}>
                    {step.label}
                  </TabsTrigger>
                ))}
              </TabsList>

              {/* Form Content */}
              <div className="space-y-6">
                {/* Basic Details */}
                <TabsContent value="basic" className="mt-0">
                  <BasicDetailsForm
                    formData={formData}
                    handleChange={handleChange}
                    handleSelectChange={handleSelectChange}
                    handleNext={handleNext}
                  />
                </TabsContent>

                {/* Property Details */}
                <TabsContent value="property" className="mt-0">
                  <PropertyDetailsForm
                    formData={formData}
                    handleSelectChange={handleSelectChange}
                    handleCheckboxChange={handleCheckboxChange}
                    handleNext={handleNext}
                  />
                </TabsContent>

                {/* Location */}
                <TabsContent value="location" className="mt-0">
                  <LocationDetailsForm
                    formData={formData}
                    handleChange={handleChange}
                    handleSelectChange={handleSelectChange}
                    handleNext={handleNext}
                  />
                </TabsContent>

                {/* Pricing */}
                <TabsContent value="pricing" className="mt-0">
                  <PricingDetailsForm
                    formData={formData}
                    handleSelectChange={handleSelectChange}
                    handleNext={handleNext}
                  />
                </TabsContent>

                {/* Owner & Documents */}
                <TabsContent value="owner" className="mt-0">
                  <OwnerDocumentsForm
                    formData={formData}
                    handleChange={handleChange}
                    imageInputRef={imageInputRef}
                    documentInputRef={documentInputRef}
                    floorPlanInputRef={floorPlanInputRef}
                    videoInputRef={videoInputRef}
                    imageFiles={imageFiles}
                    documentFiles={documentFiles}
                    floorPlanFiles={floorPlanFiles}
                    videoFiles={videoFiles}
                    uploadedImageUrls={uploadedImageUrls}
                    uploadedDocumentUrls={uploadedDocumentUrls}
                    uploadedFloorPlanUrls={uploadedFloorPlanUrls}
                    uploadedVideoUrls={uploadedVideoUrls}
                    previewImage={previewImage}
                    handleImageClick={handleImageClick}
                    handleDocumentClick={handleDocumentClick}
                    handleFloorPlanClick={handleFloorPlanClick}
                    handleVideoClick={handleVideoClick}
                    handleImageChange={handleImageChange}
                    handleDocumentChange={handleDocumentChange}
                    handleFloorPlanChange={handleFloorPlanChange}
                    handleVideoChange={handleVideoChange}
                    handleImagePreview={handleImagePreview}
                    setPreviewImage={setPreviewImage}
                    handleRemoveImage={handleRemoveImage}
                    handleRemoveDocument={handleRemoveDocument}
                    handleRemoveFloorPlan={handleRemoveFloorPlan}
                    handleRemoveVideo={handleRemoveVideo}
                    handleRemoveExistingImage={handleRemoveExistingImage}
                    handleRemoveExistingDocument={handleRemoveExistingDocument}
                    handleRemoveExistingFloorPlan={handleRemoveExistingFloorPlan}
                    handleRemoveExistingVideo={handleRemoveExistingVideo}
                    handleCheckboxChange={handleCheckboxChange}
                    handleReorderAllImages={handleReorderAllImages}
                    handleReorderAllFloorPlans={handleReorderAllFloorPlans}
                    existingImages={existingImages}
                    existingDocuments={existingDocuments}
                    existingFloorPlans={existingFloorPlans}
                    existingVideos={existingVideos}
                    isPending={isCreating || isUpdating || isUploading}
                    handleSubmit={handleSubmit}
                    isEditMode={isEditMode}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
