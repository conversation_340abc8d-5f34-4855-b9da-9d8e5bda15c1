'use client';

import * as React from 'react';
import {
  BarChartIcon,
  Calendar1,
  CameraIcon,
  FileCodeIcon,
  FileTextIcon,
  FolderIcon,
  HandCoins,
  House,
  LayoutDashboardIcon,
  ListTodo,
  Settings2,
  Users,
  UsersIcon,
  ClipboardList,
  TrendingUp,
  Target,
  Clock,
  Contact,
  UserPlus,
  PieChart,
  DollarSign,
  Activity,
  ShoppingCart,
  Home,
  Hammer,
  CheckSquare,
  PlusSquare,
  UsersRound,
  ClipboardCheck,
  User,
  Sliders,
  Bell,
  Shield,
  Files,
  MessageCircleMore,
  CalendarCheck,
} from 'lucide-react';
import { NavMain } from '@/components/common/navMain';
import { NavUser } from '@/components/common/navUser';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import Image from 'next/image';
import Link from 'next/link';
import { NavSecondary } from './navSecondary';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigationData = {
    navMain: [
      {
        title: 'Tổng quan',
        url: '/saler/dashboard',
        icon: LayoutDashboardIcon,
      },
      {
        title: 'Bất động sản',
        url: '/saler/property',
        icon: House,
      },
      {
        title: 'Hợp đồng cho thuê',
        url: '/saler/contract',
        icon: FileTextIcon,
      },
      {
        title: 'Hộp thoại ',
        url: '/saler/messenger',
        icon: MessageCircleMore,
      },
      {
        title: 'Giao dịch của tôi',
        url: '/saler/sales',
        icon: HandCoins,
      },
      {
        title: 'Khách hàng tiềm năng',
        url: '/saler/lead',
        icon: Users,
      },
      {
        title: 'Công việc',
        url: '/saler/tasks',
        icon: ListTodo,
      },
      // {
      //   title: 'Quy trình',
      //   url: '#',
      //   icon: ListIcon,
      // },
      // {
      //   title: 'Thống kê',
      //   url: '#',
      //   icon: BarChartIcon,
      // },
      // {
      //   title: 'Dự án',
      //   url: '#',
      //   icon: FolderIcon,
      // },
      // {
      //   title: 'Nhóm',
      //   url: '#',
      //   icon: UsersIcon,
      // },
      {
        title: 'Lịch hẹn',
        url: '/saler/appointments',
        icon: Calendar1,
      },
      {
        title: 'Lịch trình xem nhà',
        url: '/saler/tours',
        icon: CalendarCheck,
      },
    ],
    navClouds: [
      {
        title: 'Quản lý',
        icon: CameraIcon,
        isActive: true,
        url: '#',
        items: [
          {
            title: 'Hoạt động',
            url: '#',
          },
          {
            title: 'Đã lưu',
            url: '#',
          },
        ],
      },
      {
        title: 'Hợp đồng',
        icon: FileTextIcon,
        url: '#',
        items: [
          {
            title: 'Hoạt động',
            url: '#',
          },
          {
            title: 'Đã lưu',
            url: '#',
          },
        ],
      },
      {
        title: 'Prompts',
        icon: FileCodeIcon,
        url: '#',
        items: [
          {
            title: 'Active Proposals',
            url: '#',
          },
          {
            title: 'Archived',
            url: '#',
          },
        ],
      },
    ],
    navSecondary: [
      // {
      //   title: 'Bất động sản',
      //   url: '/saler/property',
      //   icon: Building2,
      //   isActive: true,
      //   items: [
      //     {
      //       title: 'Danh sách',
      //       url: '/saler/property',
      //       icon: ListIcon,
      //     },
      //     {
      //       title: 'Bản nháp',
      //       url: '/saler/property',
      //       icon: FileEdit,
      //     },
      //     {
      //       title: 'Đã bán/Cho thuê',
      //       url: '/saler/property',
      //       icon: TrendingUp,
      //     },
      //     {
      //       title: 'Lịch sử',
      //       url: '/saler/property',
      //       icon: Calendar,
      //     },
      //   ],
      // },
      // {
      //   title: 'Hợp đồng',
      //   url: '/saler/contract',
      //   icon: FileTextIcon,
      //   items: [
      //     {
      //       title: 'Hợp đồng cho thuê',
      //       url: '/saler/contract',
      //       icon: FileSignature,
      //     },
      //     {
      //       title: 'Hợp đồng mua bán',
      //       url: '/saler/contract',
      //       icon: FileType,
      //     },
      //     {
      //       title: 'Đang chờ ký',
      //       url: '/saler/contract',
      //       icon: Clock,
      //     },
      //     {
      //       title: 'Đã hoàn thành',
      //       url: '/saler/contract',
      //       icon: CheckCircle,
      //     },
      //   ],
      // },
      {
        title: 'Chủ nhà',
        url: '#',
        icon: Users,
        items: [
          {
            title: 'Danh sách chủ nhà',
            url: '/saler/coming-soon',
            icon: Contact,
          },
          {
            title: 'Thêm chủ nhà',
            url: '/saler/coming-soon',
            icon: UserPlus,
          },
          {
            title: 'Thống kê',
            url: '/saler/coming-soon',
            icon: BarChartIcon,
          },
        ],
      },
      {
        title: 'Khách hàng',
        url: '#',
        icon: UsersRound,
        items: [
          {
            title: 'Danh sách khách hàng',
            url: '/saler/coming-soon',
            icon: Users,
          },
          {
            title: 'Thêm khách hàng',
            url: '/saler/coming-soon',
            icon: UserPlus,
          },
          {
            title: 'Khách hàng tiềm năng',
            url: '/saler/coming-soon',
            icon: Target,
          },
          {
            title: 'Lịch sử tương tác',
            url: '/saler/coming-soon',
            icon: Clock,
          },
          {
            title: 'Thống kê khách hàng',
            url: '/saler/coming-soon',
            icon: PieChart,
          },
        ],
      },
      {
        title: 'Thống kê & Báo cáo',
        url: '#',
        icon: BarChartIcon,
        items: [
          {
            title: 'Tổng quan',
            url: '/saler/coming-soon',
            icon: PieChart,
          },
          {
            title: 'Doanh thu',
            url: '/saler/coming-soon',
            icon: DollarSign,
          },
          {
            title: 'Hiệu suất',
            url: '/saler/coming-soon',
            icon: Activity,
          },
          {
            title: 'Báo cáo',
            url: '/saler/coming-soon',
            icon: FileTextIcon,
          },
        ],
      },
      {
        title: 'Quy trình',
        url: '#',
        icon: ClipboardList,
        items: [
          {
            title: 'Quy trình bán',
            url: '/saler/coming-soon',
            icon: ShoppingCart,
          },
          {
            title: 'Quy trình cho thuê',
            url: '/saler/coming-soon',
            icon: Home,
          },
          {
            title: 'Mẫu tài liệu',
            url: '/saler/coming-soon',
            icon: Files,
          },
        ],
      },
      {
        title: 'Dự án',
        url: '#',
        icon: FolderIcon,
        items: [
          {
            title: 'Dự án đang thực hiện',
            url: '/saler/coming-soon',
            icon: Hammer,
          },
          {
            title: 'Dự án đã hoàn thành',
            url: '/saler/coming-soon',
            icon: CheckSquare,
          },
          {
            title: 'Tạo dự án mới',
            url: '/saler/coming-soon',
            icon: PlusSquare,
          },
        ],
      },
      {
        title: 'Nhóm',
        url: '#',
        icon: UsersIcon,
        items: [
          {
            title: 'Thành viên nhóm',
            url: '/saler/coming-soon',
            icon: UsersRound,
          },
          {
            title: 'Phân công công việc',
            url: '/saler/coming-soon',
            icon: ClipboardCheck,
          },
          {
            title: 'Hiệu suất nhóm',
            url: '/saler/coming-soon',
            icon: TrendingUp,
          },
        ],
      },
      {
        title: 'Cài đặt',
        url: '/saler/profile',
        icon: Settings2,
        items: [
          {
            title: 'Hồ sơ cá nhân',
            url: '/saler/profile',
            icon: User,
          },
          {
            title: 'Cài đặt chung',
            url: '/saler/profile',
            icon: Sliders,
          },
          {
            title: 'Thông báo',
            url: '/saler/profile',
            icon: Bell,
          },
          {
            title: 'Bảo mật',
            url: '/saler/profile',
            icon: Shield,
          },
        ],
      },
    ],
  };

  return (
    <Sidebar collapsible="icon" {...props} className="font-medium">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link href="/">
                <Image
                  src="/logo_revoland_red.png"
                  alt="Revoland icon"
                  width={20}
                  height={20}
                  priority
                  className="rounded-md size-7"
                />
                <span className="text-base font-semibold text-red-500">RevoLand</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigationData.navMain} />
        <SidebarSeparator />
        {/* <NavDocuments items={navigationData.documents} /> */}
        <NavSecondary items={navigationData.navSecondary} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
