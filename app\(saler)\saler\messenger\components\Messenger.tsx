import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useChat } from '@/hooks/useChat';
import {
  Info,
  MoreHorizontal,
  Paperclip,
  Plus,
  Search,
  Send,
  Smile,
  Play,
  FileText,
  ChevronUp,
  MessageCircleMore,
  MessageCircle,
  X,
  User,
  Loader2,
} from 'lucide-react';
import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { signalRService } from '@/lib/realtime/signalR';
import { useAuthStore } from '@/lib/store/authStore';
import { PropertyCard } from '@/components/PropertyCard';
import {
  useUploadAttachment,
  useUploadPropertyImage,
  useUploadPropertyVideo,
} from '@/hooks/useAttachment';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { ChatMessage, MessageResponse } from '@/lib/api/services/fetchChat';
import { Conversation } from '@/lib/api/services/fetchConversation';
import { Alert, AlertTitle } from '@/components/ui/alert';
import { useIsMobile } from '@/hooks/useMobile';

// Tablet detection
function useIsTablet() {
  const [isTablet, setIsTablet] = useState(false);
  useEffect(() => {
    const checkTablet = () => {
      if (typeof window !== 'undefined') {
        setIsTablet(window.innerWidth <= 1024 && window.innerWidth > 640);
      }
    };
    checkTablet();
    window.addEventListener('resize', checkTablet);
    return () => window.removeEventListener('resize', checkTablet);
  }, []);
  return isTablet;
}

// Helper to normalize ChatMessage to Message
function normalizeChatMessage(msg: ChatMessage): MessageResponse {
  const dateVal = msg.createdAt ? new Date(msg.createdAt) : new Date();
  return {
    messageId: msg.messageId || msg.id || '',
    id: msg.id || msg.messageId || '',
    senderId: msg.senderId,
    content: msg.content,
    createdAt: dateVal,
    timestamp: dateVal,
    direction: msg.direction,
    replyToMessageId: msg.replyToMessageId,
    attachments:
      Array.isArray(msg.attachments) && msg.attachments.length > 0
        ? msg.attachments
            .filter(att => typeof att === 'object' && att !== null)
            .map(att => {
              const a = att as any;
              return {
                id: a.id,
                url: a.url || a.fileUrl,
                type: a.type || a.fileType,
                fileName: a.fileName || a.name,
              };
            })
        : [],
  };
}

// Thêm component mini property card cho reply preview
function PropertyCardMiniInReply({ property }: { property: any }) {
  return (
    <div className="flex items-center gap-1 border border-gray-200 rounded bg-white px-1 py-0.5 max-w-[120px] overflow-hidden">
      <img
        src={property.imageUrls?.[0] || '/revoland_logo.png'}
        alt={property.name || 'Property'}
        width={24}
        height={24}
        className="rounded object-cover w-6 h-6 border"
      />
      <div className="flex-1 min-w-0">
        <div className="text-xs font-semibold truncate max-w-[70px]">{property.name || 'BĐS'}</div>
        <div className="text-[10px] text-gray-500 truncate max-w-[70px]">
          {property.location?.address || ''}
        </div>
      </div>
    </div>
  );
}

export default function Messenger() {
  const formatTimestamp = (timestamp: string | Date | number) => {
    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      return date.toLocaleString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return '';
    }
  };

  const getTimestampNumber = (timestamp: string | Date | number) => {
    try {
      return timestamp instanceof Date ? timestamp.getTime() : new Date(timestamp).getTime();
    } catch {
      return 0;
    }
  };

  const [selectedConversation, setSelectedConversation] = useState<string>('');
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [messages, setMessages] = useState<Record<string, MessageResponse[]>>({});
  const [, setLoading] = useState(false);
  const {
    conversationQuery,
    refetchConversation,
    sendMessage,
    pinMessage,
    unpinMessage,
    deleteMessage,
  } = useChat();
  const { user } = useAuthStore();
  const userId = user?.userName || '';
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedAttachmentIds, setUploadedAttachmentIds] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [replyingMessage, setReplyingMessage] = useState<MessageResponse | null>(null);
  const [pinnedMessages, setPinnedMessages] = useState<MessageResponse[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const [showPinnedList, setShowPinnedList] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<{ url: string; type: string } | null>(null);
  const [confirmingDeleteId, setConfirmingDeleteId] = useState<string | null>(null);
  const uploadAttachment = useUploadAttachment();
  const uploadImage = useUploadPropertyImage();
  const uploadVideo = useUploadPropertyVideo();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState<Record<string, boolean>>({});
  const [currentPage, setCurrentPage] = useState<Record<string, number>>({});
  const [lastLoadMoreTime, setLastLoadMoreTime] = useState(0);
  const isLoadingMoreRef = useRef(false);
  const isFirstLoad = useRef(true);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [hasInitialHistory, setHasInitialHistory] = useState<Record<string, boolean>>({});
  const pageSize = 40;
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const [showSidebar, setShowSidebar] = useState(true);
  const [pendingScrollMessageId, setPendingScrollMessageId] = useState<string | null>(null);
  // Thêm các ref để lưu vị trí scroll trước khi load more
  const prevScrollHeightRef = useRef<number | null>(null);
  const prevScrollTopRef = useRef<number | null>(null);
  // Thêm ref để kiểm soát auto scroll về cuối
  const shouldAutoScrollToBottomRef = useRef(true);

  // Lắng nghe tin nhắn mới và lịch sử
  useEffect(() => {
    signalRService.setMessageHandler((message: ChatMessage) => {
      const normalized = normalizeChatMessage(message);
      setMessages(prev => {
        // Type guard for conversationId
        let convId = '';
        if (
          typeof message === 'object' &&
          message !== null &&
          'conversationId' in message &&
          typeof (message as { conversationId?: string }).conversationId === 'string'
        ) {
          convId = (message as { conversationId: string }).conversationId;
        }
        const filtered = (prev[convId] || []).filter(
          m =>
            !(
              m.pending &&
              ((m.content && m.content === normalized.content) ||
                (m.messageId && m.messageId === normalized.messageId))
            )
        );
        if (filtered.some(m => m.messageId === normalized.messageId)) {
          return prev;
        }
        return {
          ...prev,
          [convId]: [...filtered, normalized],
        };
      });
      shouldAutoScrollToBottomRef.current = true; // Có tin nhắn mới, scroll về cuối
    });
    signalRService.setMessageHistoryHandler((msgs: MessageResponse[]) => {
      if (!selectedConversation) return;
      const normalizedMsgs = msgs.map(msg => normalizeChatMessage(msg as unknown as ChatMessage));
      setMessages(prev => {
        const oldMsgs = prev[selectedConversation] || [];
        if (!hasInitialHistory[selectedConversation]) {
          setHasInitialHistory(prev => ({ ...(prev || {}), [selectedConversation]: true }));
          setCurrentPage(prev => ({ ...(prev || {}), [selectedConversation]: 1 }));
          setHasMoreMessages(prev => ({ ...(prev || {}), [selectedConversation]: true }));
          return { ...prev, [selectedConversation]: normalizedMsgs };
        }
        // Prepend các tin nhắn mới vào đầu
        const merged = {
          ...prev,
          [selectedConversation]: [...normalizedMsgs, ...oldMsgs],
        };
        // Nếu không còn tin nhắn thì set hasMoreMessages = false
        if (normalizedMsgs.length === 0) {
          setHasMoreMessages(prev => ({ ...(prev || {}), [selectedConversation]: false }));
        }
        // Luôn tăng currentPage lên 1 sau mỗi lần load thành công
        setCurrentPage(prev => ({
          ...(prev || {}),
          [selectedConversation]: (prev?.[selectedConversation] || 1) + 1,
        }));
        return merged;
      });
      setIsLoadingMore(false);
    });
    return () => signalRService.setMessageHistoryHandler(null);
  }, [selectedConversation, pageSize, hasInitialHistory]);

  // Hàm chọn conversation và load messages
  const handleSelectConversation = useCallback(
    async (conversationId: string) => {
      setSelectedConversation(conversationId);
      setHasInitialHistory(prev => ({ ...(prev || {}), [conversationId]: false })); // reset lại khi chọn conversation mới
      setCurrentPage(prev => ({ ...(prev || {}), [conversationId]: 1 })); // reset page về 1
      setLoading(true);
      try {
        await signalRService.joinConversation(conversationId, userId);
        setHasMoreMessages(prev => ({ ...(prev || {}), [conversationId]: true }));
        if (isMobile) setShowSidebar(false);
      } catch (e) {
        // handle error
      }
      setLoading(false);
    },
    [userId, isMobile]
  );

  // const currentConversation = mockConversations.find(conv => conv.id === selectedConversation);
  const currentMessagesRaw = messages[selectedConversation as keyof typeof messages] || [];

  const currentMessages = useMemo(() => {
    return currentMessagesRaw.slice().sort((a, b) => {
      const tA = new Date(a.createdAt).getTime();
      const tB = new Date(b.createdAt).getTime();
      return tA - tB;
    });
  }, [currentMessagesRaw]);
  const filteredConversations = conversationQuery?.data;

  function removeAccents(str: string) {
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D');
  }

  const filteredConversationsSafe: Conversation[] = useMemo(() => {
    let list: Conversation[] = [];
    if (Array.isArray(filteredConversations)) {
      list = filteredConversations as Conversation[];
    } else if (
      filteredConversations &&
      typeof filteredConversations === 'object' &&
      'id' in filteredConversations
    ) {
      list = [filteredConversations as Conversation];
    }
    if (!searchQuery.trim()) return list;
    const q = removeAccents(searchQuery.trim().toLowerCase());
    return list.filter(c => removeAccents((c.platformUser?.name || '').toLowerCase()).includes(q));
  }, [filteredConversations, searchQuery]);

  const currentConversation = useMemo(() => {
    return filteredConversationsSafe.find(c => c.id === selectedConversation);
  }, [filteredConversationsSafe, selectedConversation]);

  const currentPlatformUserId = currentConversation?.platformUser?.id;

  // Ghim tin nhắn
  const handlePinMessage = async (messageId: string) => {
    const msg = currentMessages.find(m => m.id === messageId || m.messageId === messageId);
    if (
      !msg ||
      pinnedMessages.some(m => m.id === messageId || m.messageId === messageId) ||
      !selectedConversation
    )
      return;
    try {
      await pinMessage({ messageId, conversationId: selectedConversation });
      // Refresh conversation để lấy danh sách tin nhắn ghim mới
      await refetchConversation();
      <Alert>
        <AlertTitle>Tin nhắn đã được ghim thành công</AlertTitle>
      </Alert>;
    } catch (err) {
      <Alert>
        <AlertTitle>Ghim tin nhắn thất bại</AlertTitle>
      </Alert>;
    }
  };

  // Xóa tin nhắn
  const handleDeleteMessage = async (messageId: string) => {
    try {
      await deleteMessage(messageId);
      setMessages(prev => ({
        ...prev,
        [selectedConversation]: (prev[selectedConversation] || []).filter(
          m => m.id !== messageId && m.messageId !== messageId
        ),
      }));
      setPinnedMessages(prev => prev.filter(m => m.id !== messageId && m.messageId !== messageId));
      // TODO: Hiện toast thành công
      <Alert>
        <AlertTitle>Tin nhắn đã được xóa thành công</AlertTitle>
      </Alert>;
    } catch (err) {
      console.error('Failed to delete message:', err);
      <Alert>
        <AlertTitle>Xóa tin nhắn thất bại</AlertTitle>
      </Alert>;
    }
  };

  // Bỏ ghim tin nhắn
  const handleUnpinMessage = async (messageId: string) => {
    try {
      await unpinMessage({ messageId, conversationId: selectedConversation });
      // Refresh conversation để lấy danh sách tin nhắn ghim mới
      await refetchConversation();
      <Alert>
        <AlertTitle>Tin nhắn đã được bỏ ghim thành công</AlertTitle>
      </Alert>;
      // TODO: Hiện toast thành công
    } catch (err) {
      console.error('Failed to unpin message:', err);
      <Alert>
        <AlertTitle>Bỏ ghim tin nhắn thất bại</AlertTitle>
      </Alert>;
    }
  };

  // Xem tin nhắn đã ghim
  // const handleViewMessage = (messageId: string) => {
  //   // TODO: scroll to message and highlight it
  //   const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
  //   if (messageElement) {
  //     messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
  //     messageElement.classList.add('ring-2', 'ring-blue-400');
  //     setTimeout(() => messageElement.classList.remove('ring-2', 'ring-blue-400'), 1500);
  //   }
  //   setShowPinnedList(false);
  // };

  // Reply tin nhắn
  const handleReply = (message: MessageResponse) => {
    setReplyingMessage(message);
    localStorage.setItem('chat_replying_message', JSON.stringify(message));
  };

  // Gửi file/ảnh/video
  const handleFileSelect = async (file: File, type: 'image' | 'video' | 'file') => {
    try {
      setIsUploading(true);
      let response;
      switch (type) {
        case 'image':
          response = await uploadImage.mutateAsync({ files: [file] });
          break;
        case 'video':
          response = await uploadVideo.mutateAsync({ file });
          break;
        default:
          response = await uploadAttachment.mutateAsync({ files: [file] });
      }
      let attachment;
      if (Array.isArray(response?.data)) {
        attachment = response.data[0];
      } else if (response?.data && typeof response.data === 'object') {
        attachment = response.data;
      }
      if (attachment && attachment.id) {
        setUploadedAttachmentIds(prev => [...prev, attachment.id]);
        setSelectedFiles(prev => [...prev, file]);
      }
    } catch (error) {
      // Hiện toast lỗi nếu muốn
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.type.startsWith('image/')) {
      handleFileSelect(file, 'image');
    } else if (file.type.startsWith('video/')) {
      handleFileSelect(file, 'video');
    } else {
      handleFileSelect(file, 'file');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    handleFileSelect(file, 'file');
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setUploadedAttachmentIds(prev => prev.filter((_, i) => i !== index));
  };

  // Infinite scroll: load thêm tin nhắn khi cuộn lên đầu
  const handleScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    if (
      scrollTop === 0 &&
      !isLoadingMore &&
      selectedConversation &&
      hasMoreMessages[selectedConversation] !== false &&
      currentMessagesRaw.length > 0 &&
      Date.now() - lastLoadMoreTime > 1000
    ) {
      // Lưu vị trí scroll trước khi load more
      const chatEl = chatContainerRef.current;
      if (chatEl) {
        prevScrollHeightRef.current = chatEl.scrollHeight;
        prevScrollTopRef.current = chatEl.scrollTop;
      }
      setIsLoadingMore(true);
      setLastLoadMoreTime(Date.now());
      try {
        await signalRService.loadMoreMessages(
          selectedConversation,
          (currentPage[selectedConversation] || 1) + 1,
          pageSize
        );
      } catch (error) {
        setIsLoadingMore(false);
      }
    }
  };
  useEffect(() => {
    isLoadingMoreRef.current = isLoadingMore;
  }, [isLoadingMore]);

  // Khi chọn conversation mới, reset phân trang
  useEffect(() => {
    setCurrentPage(prev => ({ ...(prev || {}), [selectedConversation]: 1 }));
    setHasMoreMessages(prev => ({ ...(prev || {}), [selectedConversation]: true }));
    setIsLoadingMore(false);
    isFirstLoad.current = true;
  }, [selectedConversation]);

  // Gửi tin nhắn (có thể kèm file, reply)
  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const trimmedMessage = newMessage.trim();
    const hasContent = trimmedMessage.length > 0;
    const hasAttachment = uploadedAttachmentIds.length > 0 && selectedFiles.length > 0;
    if (!hasContent && !hasAttachment) return;
    try {
      await sendMessage({
        content: hasContent ? trimmedMessage : '',
        attachmentIds: hasAttachment ? uploadedAttachmentIds : [],
        conversationId: selectedConversation,
        direction: 'outbound',
        replyToMessageId: replyingMessage?.messageId || replyingMessage?.id,
        recipientId: currentConversation?.platformUser?.id,
      });
      setNewMessage('');
      setSelectedFiles([]);
      setUploadedAttachmentIds([]);
      setReplyingMessage(null);
      localStorage.removeItem('chat_replying_message');
      setShowEmojiPicker(false);
      shouldAutoScrollToBottomRef.current = true; // Gửi tin nhắn xong, scroll về cuối
    } catch (error) {
      console.error('Failed to send message:', error);
      <Alert>
        <AlertTitle>Gửi tin nhắn thất bại</AlertTitle>
      </Alert>;
      // TODO: Hiện toast lỗi
    }
  };

  // Update the UI to show replying message
  const renderReplyingMessage = () => {
    if (!replyingMessage) return null;

    return (
      <div className="flex items-start bg-gray-50 border-l-4 border-blue-500 rounded p-2 m-2 relative">
        <div className="flex-1">
          <div className="font-semibold text-xs text-gray-700 mb-0.5 flex items-center gap-1">
            <MessageCircle size={14} className="text-blue-500" />
            Trả lời{' '}
            <span className="font-bold">
              {replyingMessage.direction === 'outbound'
                ? 'Bạn'
                : replyingMessage.senderName ||
                  currentConversation?.platformUser?.name ||
                  'Người dùng'}
            </span>
          </div>
          <div className="text-xs text-gray-600 truncate max-w-[320px]">
            {(() => {
              if (
                replyingMessage.content &&
                typeof replyingMessage.content === 'string' &&
                replyingMessage.content.trim().startsWith('{')
              ) {
                try {
                  const parsed = JSON.parse(replyingMessage.content);
                  if (parsed && parsed.property) {
                    return <PropertyCardMiniInReply property={parsed.property} />;
                  }
                } catch {
                  // fallthrough
                }
              }
              // Nếu không phải propertyCard thì render như cũ
              if (replyingMessage.content && replyingMessage.content !== '[FILE]') {
                return <span>{replyingMessage.content}</span>;
              }
              if (replyingMessage.attachments && replyingMessage.attachments.length > 0) {
                return (
                  <div className="flex items-center gap-1 flex-wrap mt-1">
                    {replyingMessage.attachments.map((attachment, idx) => {
                      const urlA = attachment.url;
                      const typeA = attachment.type;
                      const nameA = attachment.fileName;
                      if (!urlA || !typeA) return null;
                      const isImage = typeA.startsWith('image/');
                      const isVideo = typeA.startsWith('video/');
                      const isPdf = typeA === 'application/pdf';
                      const isFile = !isImage && !isVideo && !isPdf;
                      if (isImage) {
                        return (
                          <div
                            key={idx}
                            className="w-7 h-7 cursor-pointer"
                            onClick={() => setSelectedMedia({ url: urlA, type: typeA })}
                          >
                            <img
                              src={urlA}
                              alt={nameA || 'attachment'}
                              className="rounded object-cover w-7 h-7 border"
                            />
                          </div>
                        );
                      }
                      if (isVideo) {
                        return (
                          <div
                            key={idx}
                            className="w-7 h-7 cursor-pointer flex items-center justify-center bg-gray-200 rounded"
                            onClick={() => setSelectedMedia({ url: urlA, type: typeA })}
                          >
                            <Play size={16} className="text-blue-500" />
                          </div>
                        );
                      }
                      if (isPdf) {
                        return (
                          <a
                            key={idx}
                            href={urlA}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-7 h-7 flex items-center justify-center bg-gray-200 rounded"
                            title={nameA}
                          >
                            <FileText size={16} className="text-red-500" />
                          </a>
                        );
                      }
                      if (isFile) {
                        return (
                          <a
                            key={idx}
                            href={urlA}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-7 h-7 flex items-center justify-center bg-gray-200 rounded"
                            title={nameA}
                          >
                            <FileText size={16} className="text-gray-500" />
                          </a>
                        );
                      }
                      return null;
                    })}
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </div>
        <button
          className="ml-2 text-gray-400 hover:text-gray-600"
          onClick={() => {
            setReplyingMessage(null);
            localStorage.removeItem('chat_replying_message');
          }}
          aria-label="Hủy trả lời"
        >
          <X size={16} />
        </button>
      </div>
    );
  };

  // Preview file UI
  const renderPreviewFiles = () => {
    if (selectedFiles.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 p-2 bg-gray-50 rounded-lg">
        {selectedFiles.map((file, index) => (
          <div key={index} className="relative group">
            {file.type.startsWith('image/') ? (
              <div className="relative">
                <img
                  src={URL.createObjectURL(file)}
                  alt="Preview"
                  className="rounded-lg object-cover w-[50px] h-[50px]"
                />
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : file.type.startsWith('video/') ? (
              <div className="relative">
                <video
                  src={URL.createObjectURL(file)}
                  className="w-[50px] h-[50px] rounded-lg object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                  <Play size={24} className="text-white" />
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <div className="relative bg-white p-2 rounded-lg border">
                <div className="flex items-center gap-2">
                  <FileText size={20} className="text-gray-500" />
                  <span className="text-sm truncate max-w-[150px]">{file.name}</span>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={16} />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const handleEmojiSelect = (emoji: { native: string }) => {
    setNewMessage(prev => prev + emoji.native);
    // Không đóng picker ở đây để chọn nhiều emoji liên tiếp
  };

  // Add useEffect to save replyingMessage to localStorage when it changes
  useEffect(() => {
    if (replyingMessage) {
      localStorage.setItem('chat_replying_message', JSON.stringify(replyingMessage));
    } else {
      localStorage.removeItem('chat_replying_message');
    }
  }, [replyingMessage]);

  // Add useEffect to restore replyingMessage from localStorage on mount
  useEffect(() => {
    const storedReplyingMessage = localStorage.getItem('chat_replying_message');
    if (storedReplyingMessage) {
      const parsedMessage = JSON.parse(storedReplyingMessage);
      // Convert timestamp string back to Date object
      parsedMessage.timestamp = new Date(parsedMessage.timestamp);
      setReplyingMessage(parsedMessage);
    }
  }, []);

  // Tự động scroll xuống cuối khi có tin nhắn mới hoặc vừa mở chat, giữ nguyên vị trí khi load more
  useEffect(() => {
    if (
      isLoadingMore &&
      prevScrollHeightRef.current !== null &&
      prevScrollTopRef.current !== null &&
      chatContainerRef.current
    ) {
      // Đang load more, giữ nguyên vị trí scroll
      const chatEl = chatContainerRef.current;
      const newScrollHeight = chatEl.scrollHeight;
      chatEl.scrollTop = newScrollHeight - prevScrollHeightRef.current + prevScrollTopRef.current;
      prevScrollHeightRef.current = null;
      prevScrollTopRef.current = null;
      setIsLoadingMore(false);
      return;
    }
    // Chỉ scroll về cuối nếu shouldAutoScrollToBottomRef.current === true
    if (
      shouldAutoScrollToBottomRef.current &&
      messagesEndRef.current &&
      currentMessagesRaw.length > 0
    ) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      shouldAutoScrollToBottomRef.current = false; // reset sau khi scroll
    }
  }, [currentMessagesRaw]);

  // Lấy tin nhắn ghim từ conversation data
  useEffect(() => {
    const pinMessages =
      typeof currentConversation === 'object' &&
      currentConversation !== null &&
      'pinMessages' in currentConversation
        ? (currentConversation as unknown as { pinMessages?: unknown }).pinMessages
        : undefined;
    if (pinMessages && Array.isArray(pinMessages)) {
      setPinnedMessages(
        pinMessages.map((msg: any, idx: number) => {
          const dateVal = msg.createdAt ? new Date(msg.createdAt) : new Date();
          return {
            ...msg,
            messageId: msg.messageId || msg.id || `pinned-${idx}`,
            id: msg.id || msg.messageId || `pinned-${idx}`,
            timestamp: dateVal,
            createdAt: dateVal,
            attachments:
              Array.isArray(msg.attachments) &&
              msg.attachments.length > 0 &&
              typeof msg.attachments[0] === 'object'
                ? (msg.attachments as { id: string; type: string; url: string; name: string }[])
                : [],
          } as MessageResponse;
        })
      );
    } else {
      setPinnedMessages([]);
    }
  }, [currentConversation]);

  // Đồng bộ lại attachments cho pinnedMessages mỗi khi messages thay đổi
  useEffect(() => {
    if (currentMessagesRaw.length === 0) return;

    setPinnedMessages(prev => {
      const updated = prev.map(pinMsg => {
        const fullMsg = currentMessagesRaw.find(
          m => m.messageId === pinMsg.messageId || m.id === pinMsg.messageId
        );
        if (fullMsg && fullMsg.attachments && fullMsg.attachments.length > 0) {
          return { ...pinMsg, attachments: fullMsg.attachments };
        }
        return pinMsg;
      });

      // Chỉ update nếu có thay đổi thực sự
      const hasChanges = updated.some((msg, index) => {
        const original = prev[index];
        return JSON.stringify(msg.attachments) !== JSON.stringify(original?.attachments);
      });

      return hasChanges ? updated : prev;
    });
  }, [currentMessagesRaw]);

  // Lọc trùng id trước khi render messages
  const uniqueMessages: MessageResponse[] = [];
  const seenIds = new Set();
  for (const msg of currentMessages) {
    const id = msg.messageId || msg.id;
    if (!seenIds.has(id)) {
      uniqueMessages.push(msg);
      seenIds.add(id);
    }
  }

  // Effect: Khi messages thay đổi, nếu có pendingScrollMessageId và ref đã xuất hiện thì scroll đến đó
  useEffect(() => {
    if (pendingScrollMessageId && messageRefs.current[pendingScrollMessageId]) {
      const el = messageRefs.current[pendingScrollMessageId];
      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        el.classList.add('ring-2', 'ring-blue-400');
        setTimeout(() => {
          el.classList.remove('ring-2', 'ring-blue-400');
        }, 2000);
        setPendingScrollMessageId(null);
      }
    }
  }, [messages, pendingScrollMessageId]);

  // Khi nhấn 'Xem tin nhắn' pinned mà ref chưa có, sẽ tự động loadMore cho đến khi messageId xuất hiện hoặc hết hasMoreMessages
  useEffect(() => {
    if (
      pendingScrollMessageId &&
      !messageRefs.current[pendingScrollMessageId] &&
      hasMoreMessages[selectedConversation] !== false &&
      !isLoadingMore &&
      selectedConversation
    ) {
      setIsLoadingMore(true);
      signalRService.loadMoreMessages(
        selectedConversation,
        currentPage[selectedConversation] || 1,
        pageSize
      );
    }
  }, [
    pendingScrollMessageId,
    hasMoreMessages,
    isLoadingMore,
    selectedConversation,
    currentPage,
    pageSize,
  ]);

  return (
    <div
      className={
        isMobile
          ? 'flex bg-gray-50 min-h-0 w-full'
          : isTablet
            ? 'flex bg-gray-50 min-h-0 w-full h-screen min-h-screen max-h-screen'
            : 'flex h-full bg-gray-50 min-h-0 max-h-[99vh]'
      }
      style={
        isMobile
          ? { height: '100vh', minHeight: '100vh', maxHeight: '100vh' }
          : isTablet
            ? { height: '99vh', minHeight: '100vh', maxHeight: '100vh' }
            : undefined
      }
    >
      {/* Sidebar - Danh sách conversation */}
      {(!isMobile || showSidebar) && (
        <div
          className={
            isMobile
              ? 'w-80 flex flex-col min-h-0 bg-white border-r border-gray-200 min-w-[240px]'
              : isTablet
                ? 'w-80 flex flex-col min-h-0 bg-white border-r border-gray-200 min-w-[240px] h-screen min-h-screen max-h-screen'
                : 'w-80 flex flex-col min-h-0 bg-white border-r border-gray-200 min-w-[240px]'
          }
          style={
            isMobile
              ? { height: '100vh', minHeight: '100vh', maxHeight: '100vh' }
              : isTablet
                ? { height: '99vh', minHeight: '99vh', maxHeight: '99vh' }
                : { height: '99vh' }
          }
        >
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-xl font-semibold text-gray-900">Tin nhắn</h1>
              <Button size="sm" variant="ghost">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm cuộc trò chuyện..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Conversation List */}
          <ScrollArea className="flex-1">
            <div className="p-2">
              {filteredConversationsSafe.map(conversation => (
                <Card
                  key={conversation.id}
                  className={`p-3 mb-2 cursor-pointer transition-colors hover:bg-gray-50 ${
                    selectedConversation === conversation.id
                      ? 'bg-blue-50 border-blue-200'
                      : 'border-transparent'
                  }`}
                  onClick={() => handleSelectConversation(conversation.id)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={conversation.platformUser?.avatarUrl || '/placeholder.svg'}
                        />
                        <AvatarFallback>
                          {currentConversation?.platformUser?.name
                            ? currentConversation.platformUser.name
                                .split(' ')
                                .map((n: string) => n[0])
                                .join('')
                            : 'U'}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900 truncate">
                          {conversation.platformUser?.name}
                        </h3>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(conversation.lastUpdated)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-600 truncate">{conversation.lastMessage}</p>
                        {/* If you want to show unreadCount, extend Conversation type locally with unreadCount?: number; and use it here. Otherwise, remove this block. */}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
      {/* Main Chat Area */}
      {(!isMobile || !showSidebar) && (
        <div
          className={
            isMobile
              ? 'flex-1 flex flex-col h-full min-h-0 relative min-w-[320px] min-h-[400px]'
              : isTablet
                ? 'flex-1 flex flex-col h-full min-h-0 relative min-w-[320px] min-h-[400px] h-screen min-h-screen max-h-screen'
                : 'flex-1 flex flex-col h-full min-h-0 relative min-w-[320px] min-h-[400px]'
          }
          style={
            isMobile
              ? { height: '100vh', minHeight: '100vh', maxHeight: '100vh' }
              : isTablet
                ? { height: '99vh', minHeight: '99vh', maxHeight: '99vh' }
                : undefined
          }
        >
          {isMobile && !showSidebar && (
            <button
              onClick={() => setShowSidebar(true)}
              className="mb-2 px-4 py-2 bg-gray-100 rounded text-gray-700 shadow"
            >
              ← Quay lại danh sách
            </button>
          )}
          {selectedConversation ? (
            <>
              {/* Chat Header */}
              <div className="bg-white border-b border-gray-200 p-4 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={currentConversation?.platformUser?.avatarUrl || '/placeholder.svg'}
                        />
                        <AvatarFallback>
                          {currentConversation?.platformUser?.name
                            ? currentConversation.platformUser.name
                                .split(' ')
                                .map((n: string) => n[0])
                                .join('')
                            : 'U'}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <div>
                      <h2 className="font-semibold text-gray-900">
                        {currentConversation?.platformUser?.name}
                      </h2>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* <Button size="sm" variant="ghost">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Video className="h-4 w-4" />
                    </Button> */}
                    <Button size="sm" variant="ghost">
                      <Info className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Pinned messages card UI giống hình mẫu */}
              {pinnedMessages.length > 0 && (
                <div
                  className={`bg-white rounded-lg shadow p-3 mb-2  max-w-lg mx-auto relative z-10 mt-1 w-[850px] ${isMobile ? 'max-w-[460px]' : isTablet ? 'max-w-[768px]' : 'max-w-[850px]'}`}
                >
                  <div
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setShowPinnedList(v => !v)}
                  >
                    <span className="font-semibold text-base text-gray-900">
                      Danh sách ghim ({pinnedMessages.length})
                    </span>
                    <ChevronUp
                      size={20}
                      className={`transform transition-transform ${showPinnedList ? 'rotate-0' : 'rotate-180'}`}
                    />
                  </div>
                  {showPinnedList && (
                    <div className=" absolute top-11 bg-white border-b border-x rounded-b-lg p-3 shadow-sm justify-center border-t rounded-t-lg border-gray-200 bg-white left-0 right-0 z-10 mt-2 max-h-[180px] overflow-y-auto space-y-2">
                      {pinnedMessages.map(msg => (
                        <div
                          key={
                            msg.messageId ||
                            msg.id ||
                            `${msg.content}-${msg.createdAt ? (msg.createdAt instanceof Date ? msg.createdAt.getTime() : String(msg.createdAt)) : 'no-timestamp'}`
                          }
                          className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center gap-2">
                            <MessageCircleMore className="inline-block text-gray-600" size={18} />
                            <div>
                              <span className="font-medium text-xs">Tin nhắn</span>
                              <div className="text-xs text-gray-700 pb-1 flex items-center gap-2 flex-wrap max-w-[200px]">
                                {/* Hiển thị text content hoặc card mini */}
                                {(() => {
                                  if (
                                    typeof msg.content === 'string' &&
                                    msg.content.trim().startsWith('{')
                                  ) {
                                    try {
                                      const parsed = JSON.parse(msg.content);
                                      if (parsed && parsed.property) {
                                        return (
                                          <PropertyCardMiniInReply property={parsed.property} />
                                        );
                                      }
                                    } catch {
                                      // fallthrough
                                    }
                                  }
                                  if (msg.content && msg.content !== '[FILE]') {
                                    return (
                                      <span className="truncate max-w-[150px]">
                                        Bạn: {msg.content}
                                      </span>
                                    );
                                  }
                                  // ... giữ nguyên logic preview file ...
                                  if (msg.attachments && msg.attachments.length > 0) {
                                    // ... giữ nguyên code ...
                                  }
                                  return null;
                                })()}
                              </div>
                            </div>
                          </div>
                          <Popover>
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white opacity-100 hover:bg-gray-100 ml-2"
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              align="center"
                              className="z-[9999] rounded-xl shadow-lg bg-white min-w-[100px] max-w-[120px] py-2 px-0 border"
                            >
                              <div className="flex flex-col">
                                <button
                                  onClick={() => {
                                    const messageId = msg.messageId || msg.id;
                                    if (!messageId) return;
                                    const messageElement = messageRefs.current[messageId];
                                    if (messageElement) {
                                      messageElement.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center',
                                      });
                                      messageElement.classList.add('ring-2', 'ring-blue-400');
                                      setTimeout(() => {
                                        messageElement.classList.remove('ring-2', 'ring-blue-400');
                                      }, 2000);
                                      setShowPinnedList(false);
                                    } else {
                                      setPendingScrollMessageId(messageId);
                                    }
                                  }}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors"
                                >
                                  Xem tin nhắn
                                </button>
                                <button
                                  onClick={() => {
                                    const messageId = msg.messageId || msg.id;
                                    if (messageId) {
                                      handleUnpinMessage(messageId);
                                    }
                                  }}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors text-orange-600"
                                >
                                  Bỏ ghim
                                </button>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Messages */}
              {/* <div className="flex-1 min-h-0 p-4"> */}
              <div
                ref={chatContainerRef}
                className={`space-y-2 flex-1  p-4 h-[99vh] overflow-y-auto  
                  ${
                    isMobile
                      ? 'w-full max-w-[480px]'
                      : isTablet
                        ? 'w-full max-w-[768px]'
                        : 'w-[900px] max-w-[900px]'
                  }
                `}
                onScroll={handleScroll}
              >
                {isLoadingMore && (
                  <div className="flex justify-center items-center py-2">
                    <span className="text-xs text-gray-400">Đang tải tin nhắn cũ...</span>
                  </div>
                )}
                {!hasMoreMessages[selectedConversation] && uniqueMessages.length > 0 && (
                  <div className="flex justify-center items-center py-2">
                    <span className="text-xs text-gray-400">Đã hiển thị tất cả tin nhắn</span>
                  </div>
                )}
                {uniqueMessages.map((message, idx) => {
                  // Xác định là tin nhắn gửi đi hay nhận được
                  const isMe = message.senderId !== currentPlatformUserId;
                  const nextMsg = uniqueMessages[idx + 1];
                  const showAvatar =
                    !nextMsg ||
                    nextMsg.senderId !== message.senderId ||
                    Math.abs(
                      getTimestampNumber(message.createdAt) - getTimestampNumber(nextMsg.createdAt)
                    ) > 60000;

                  // Lấy avatar đối phương
                  let otherAvatar = '';
                  let otherName = '';
                  if (!isMe && filteredConversations) {
                    // Tìm conversation hiện tại
                    const conv = Array.isArray(filteredConversations)
                      ? filteredConversations.find(c => c.id === selectedConversation)
                      : null;
                    otherAvatar = conv?.platformUser?.avatarUrl || '/placeholder.svg';
                    otherName = conv?.platformUser?.name || '';
                  }

                  // Kiểm tra nếu là message propertyCard
                  let propertyData = undefined;
                  if (typeof message.content === 'string') {
                    try {
                      const parsed = JSON.parse(message.content);
                      if (parsed && parsed.property) {
                        propertyData = parsed.property;
                      }
                    } catch (e) {
                      console.log(e);
                    }
                  }

                  if (
                    (!message.content || message.content.trim() === '') &&
                    (!message.attachments || message.attachments.length === 0)
                  ) {
                    return null; // KHÔNG render message trống hoàn toàn
                  }

                  const isMediaOnly =
                    (!message.content || message.content === '[FILE]') &&
                    Array.isArray(message.attachments) &&
                    message.attachments.length > 0 &&
                    message.attachments.every(
                      att =>
                        typeof att === 'object' &&
                        (att.type?.startsWith('image/') || att.type?.startsWith('video/'))
                    );

                  return (
                    <div
                      key={
                        message.id || message.messageId || `${message.content}-${message.createdAt}`
                      }
                      data-message-id={message.id || message.messageId}
                      ref={el => {
                        const messageId = message.messageId || message.id;
                        if (messageId) {
                          messageRefs.current[messageId] = el;
                        }
                      }}
                      className={`flex ${isMe ? 'justify-end' : 'justify-start'} items-end group`}
                    >
                      {/* Tin nhắn gửi đi: dấu 3 chấm bên trái */}
                      {isMe && (
                        <div className="relative mr-4 flex items-center">
                          <Popover>
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-100"
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              side="top"
                              align="center"
                              className="z-[9999] rounded-xl shadow-lg bg-white min-w-[100px] max-w-[100px] py-2 px-0 border"
                            >
                              <div className="flex flex-col">
                                <button
                                  onClick={() => {
                                    const messageId = message.messageId || message.id;
                                    if (messageId) {
                                      handlePinMessage(messageId);
                                    }
                                  }}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors"
                                >
                                  Ghim
                                </button>
                                <button
                                  onClick={() => handleReply(message)}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors"
                                >
                                  Trả lời
                                </button>
                                <div className="relative inline-block">
                                  <button
                                    onClick={() =>
                                      setConfirmingDeleteId(message.messageId || message.id)
                                    }
                                    className="cursor-pointer w-full px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors text-red-500"
                                  >
                                    Thu hồi
                                  </button>
                                  {confirmingDeleteId === (message.messageId || message.id) && (
                                    <div className="absolute left-1/2 top-full z-50 -translate-x-1/2 mt-2 bg-white border rounded shadow-lg px-4 py-3 w-64 animate-fade-in">
                                      <div className="text-sm mb-3">
                                        Bạn có chắc chắn muốn thu hồi tin nhắn này?
                                      </div>
                                      <div className="flex justify-end gap-2">
                                        <button
                                          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300"
                                          onClick={() => setConfirmingDeleteId(null)}
                                        >
                                          Hủy
                                        </button>
                                        <button
                                          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                                          onClick={() => {
                                            handleDeleteMessage(message.messageId || message.id);
                                            setConfirmingDeleteId(null);
                                          }}
                                        >
                                          Xác nhận
                                        </button>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      )}
                      {/* Avatar bên trái cho tin nhắn nhận */}
                      {!isMe ? (
                        showAvatar ? (
                          <Avatar className="h-8 w-8 mr-2 flex-shrink-0">
                            <AvatarImage src={otherAvatar} />
                            <AvatarFallback>{otherName?.[0] || 'U'}</AvatarFallback>
                          </Avatar>
                        ) : (
                          <div className="h-8 w-8 mr-2" />
                        )
                      ) : null}
                      {/* Nếu là propertyCard thì render PropertyCard */}
                      {propertyData ? (
                        <div className="max-w-xs lg:max-w-md px-2 py-2">
                          <div className="border border-gray-300 rounded-xl shadow-md bg-white p-2">
                            <PropertyCard property={propertyData} size="sm" />
                          </div>
                        </div>
                      ) : (
                        <div
                          className={`relative max-w-xs lg:max-w-md rounded-lg ${
                            isMediaOnly
                              ? 'p-0 m-0 bg-transparent items-start' // KHÔNG bg, margin, padding nếu chỉ là media, thẳng hàng avatar
                              : isMe
                                ? 'bg-red-500 text-white rounded-br-none px-3 py-1'
                                : 'bg-white text-gray-900 rounded-bl-none border border-gray-200 px-3 py-1'
                          }`}
                        >
                          {message.replyToMessageId &&
                            uniqueMessages.find(
                              m =>
                                m.messageId === message.replyToMessageId ||
                                m.id === message.replyToMessageId
                            ) && (
                              <div
                                className="rounded-lg bg-blue-50 border-l-4 border-blue-500 px-2 py-1 m-2 min-w-[80px] cursor-pointer"
                                onClick={() => {
                                  const el = messageRefs.current[message.replyToMessageId || ''];
                                  if (el) {
                                    el.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    el.classList.add('ring-2', 'ring-blue-400');
                                    setTimeout(
                                      () => el.classList.remove('ring-2', 'ring-blue-400'),
                                      1500
                                    );
                                  }
                                  setShowPinnedList(false);
                                }}
                              >
                                <div className="font-semibold text-xs text-blue-700">
                                  {(() => {
                                    const repliedMsg = uniqueMessages.find(
                                      m =>
                                        m.messageId === message.replyToMessageId ||
                                        m.id === message.replyToMessageId
                                    );
                                    if (!repliedMsg) return null;

                                    // Dựa vào direction để xác định tên người gửi
                                    if (repliedMsg.direction === 'outbound') {
                                      return 'Bạn'; // Tin nhắn gửi đi
                                    } else {
                                      return (
                                        repliedMsg.senderName ||
                                        currentConversation?.platformUser?.name ||
                                        'Người dùng'
                                      );
                                    }
                                  })()}
                                </div>
                                <div className="text-xs text-gray-700 max-w-[180px]">
                                  {(() => {
                                    const repliedMsg = uniqueMessages.find(
                                      m =>
                                        m.messageId === message.replyToMessageId ||
                                        m.id === message.replyToMessageId
                                    );
                                    if (!repliedMsg) return null;
                                    // Nếu là propertyCard (content là JSON có property)
                                    if (
                                      typeof repliedMsg.content === 'string' &&
                                      repliedMsg.content.trim().startsWith('{')
                                    ) {
                                      try {
                                        const parsed = JSON.parse(repliedMsg.content);
                                        if (parsed && parsed.property) {
                                          return (
                                            <PropertyCardMiniInReply property={parsed.property} />
                                          );
                                        }
                                      } catch {
                                        // fallthrough
                                      }
                                    }
                                    // Nếu là text thường
                                    if (repliedMsg.content && repliedMsg.content !== '[FILE]')
                                      return (
                                        <span className="truncate block">{repliedMsg.content}</span>
                                      );
                                    // ... giữ nguyên logic preview file/ảnh/video ...
                                    if (
                                      repliedMsg.attachments &&
                                      repliedMsg.attachments.length > 0
                                    ) {
                                      const att = repliedMsg.attachments[0];
                                      const urlA = att.url;
                                      const typeA = att.type;
                                      const nameA = att.fileName;
                                      if (!urlA || !typeA) return <span>[File]</span>;
                                      const isImage = typeA.startsWith('image/');
                                      const isVideo = typeA.startsWith('video/');
                                      const isPdf = typeA === 'application/pdf';
                                      const isFile = !isImage && !isVideo && !isPdf;
                                      if (isImage) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <img
                                              src={urlA}
                                              alt={nameA || 'attachment'}
                                              className="w-6 h-6 rounded object-cover"
                                            />
                                            <span className="truncate">Ảnh</span>
                                          </div>
                                        );
                                      }
                                      if (isVideo) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                                              <Play size={12} className="text-gray-600" />
                                            </div>
                                            <span className="truncate">Video</span>
                                          </div>
                                        );
                                      }
                                      if (isPdf) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 bg-red-100 rounded flex items-center justify-center">
                                              <FileText size={12} className="text-red-500" />
                                            </div>
                                            <span className="truncate">{nameA || 'PDF file'}</span>
                                          </div>
                                        );
                                      }
                                      if (isFile) {
                                        return (
                                          <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                                              <FileText size={12} className="text-gray-500" />
                                            </div>
                                            <span className="truncate">{nameA || 'File'}</span>
                                          </div>
                                        );
                                      }
                                    }
                                    return null;
                                  })()}
                                </div>
                              </div>
                            )}
                          {message.content && message.content !== '[FILE]' && (
                            <p className="text-sm">{message.content}</p>
                          )}
                          {message.attachments && message.attachments.length > 0 && (
                            <div className="flex flex-col gap-2 mt-2">
                              {message.attachments.map((att, index) => {
                                const urlA = att.url;
                                const typeA = att.type;
                                const nameA = att.fileName;
                                if (!urlA || !typeA) return null;
                                const isImage = typeA.startsWith('image/');
                                const isVideo = typeA.startsWith('video/');
                                const isPdf = typeA === 'application/pdf';
                                const isFile = !isImage && !isVideo && !isPdf;
                                if (isImage) {
                                  return (
                                    <img
                                      key={index}
                                      src={urlA}
                                      alt={nameA || 'attachment'}
                                      className="rounded-lg object-cover max-w-[200px] max-h-[200px] cursor-pointer hover:opacity-90 transition-opacity"
                                      onClick={() => setSelectedMedia({ url: urlA, type: typeA })}
                                    />
                                  );
                                }
                                if (isVideo) {
                                  return (
                                    <video
                                      key={index}
                                      controls
                                      className="rounded-lg max-w-[200px] max-h-[200px] cursor-pointer hover:opacity-90 transition-opacity"
                                      src={urlA}
                                      onClick={() => setSelectedMedia({ url: urlA, type: typeA })}
                                    >
                                      Your browser does not support the video tag.
                                    </video>
                                  );
                                }
                                if (isPdf) {
                                  return (
                                    <a
                                      key={index}
                                      href={urlA}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className={`flex items-center gap-2 text-sm hover:text-blue-400 ${isMe ? 'text-blue-300' : 'text-blue-500'}`}
                                    >
                                      <span>{nameA || 'PDF file'}</span>
                                    </a>
                                  );
                                }
                                if (isFile) {
                                  return (
                                    <a
                                      key={index}
                                      href={urlA}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className={`flex items-center gap-2 text-sm hover:text-blue-500 ${isMe ? 'text-blue-300' : 'text-blue-500'}`}
                                    >
                                      <span>{nameA || urlA}</span>
                                    </a>
                                  );
                                }
                                return null;
                              })}
                            </div>
                          )}
                          <p
                            className={`text-[10px] mt-1 ${
                              isMe
                                ? (() => {
                                    // Kiểm tra nếu tin nhắn có ảnh/video thì màu xám
                                    const hasMedia =
                                      message.attachments &&
                                      message.attachments.length > 0 &&
                                      message.attachments.some(att => {
                                        const type = att.type;
                                        return (
                                          type &&
                                          (type.startsWith('image/') || type.startsWith('video/'))
                                        );
                                      });

                                    if (hasMedia) {
                                      return 'text-gray-500'; // Màu xám cho ảnh/video
                                    }

                                    // Kiểm tra nếu tin nhắn có file thì màu xám
                                    // const hasFile =
                                    //   message.attachments &&
                                    //   message.attachments.length > 0 &&
                                    //   message.attachments.some(att => {
                                    //     const type = att.type;
                                    //     return (
                                    //       type &&
                                    //       !type.startsWith('image/') &&
                                    //       !type.startsWith('video/')
                                    //     );
                                    //   });

                                    // if (hasFile) {
                                    //   return 'text-gray-500'; // Màu xám cho file
                                    // }

                                    return 'text-blue-100'; // Màu xanh nhạt cho text thường
                                  })()
                                : 'text-gray-500'
                            }`}
                          >
                            {formatTimestamp(message.createdAt)}
                          </p>
                        </div>
                      )}
                      {/* Avatar bên phải cho tin nhắn gửi đi */}
                      {isMe ? (
                        showAvatar ? (
                          // <Avatar className="h-8 w-8 ml-2">
                          //   <AvatarImage src={userAvatarUrl} />
                          //   <AvatarFallback>{user?.fullName?.[0] || 'Me'}</AvatarFallback>
                          // </Avatar>
                          <div className="ml-2 mt-2 justify-center rounded-full bg-red-500 p-1 flex items-center justify-center">
                            <User size={20} className="text-white" />
                          </div>
                        ) : (
                          <div className="h-8 w-8 ml-1" />
                        )
                      ) : null}
                      {/* Tin nhắn nhận được: dấu 3 chấm bên phải */}
                      {!isMe && (
                        <div className="left-2 relative flex items-center">
                          <Popover>
                            <PopoverTrigger asChild>
                              <button
                                className="w-8 h-8 flex items-center justify-center rounded-full bg-white opacity-0 group-hover:opacity-100 transition-opacity hover:bg-gray-100 ml-2"
                                aria-label="Mở menu"
                                type="button"
                                tabIndex={0}
                              >
                                <MoreHorizontal size={20} className="text-gray-600" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent
                              align="center"
                              className="z-[9999] rounded-xl shadow-lg bg-white min-w-[100px] max-w-[100px] py-2 px-0 border"
                            >
                              <div className="flex flex-col">
                                <button
                                  onClick={() => {
                                    const messageId = message.messageId || message.id;
                                    if (messageId) {
                                      handlePinMessage(messageId);
                                    }
                                  }}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors"
                                >
                                  Ghim
                                </button>
                                <button
                                  onClick={() => handleReply(message)}
                                  className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors"
                                >
                                  Trả lời
                                </button>
                                <div className="relative inline-block">
                                  <button
                                    onClick={() =>
                                      setConfirmingDeleteId(message.messageId || message.id)
                                    }
                                    className="cursor-pointer w-full px-3 py-2 text-sm hover:bg-gray-100 rounded-md transition-colors text-red-500"
                                  >
                                    Thu hồi
                                  </button>
                                  {confirmingDeleteId === (message.messageId || message.id) && (
                                    <div className="absolute left-1/2 top-full z-50 -translate-x-1/2 mt-2 bg-white border rounded shadow-lg px-4 py-3 w-64 animate-fade-in">
                                      <div className="text-sm mb-3">
                                        Bạn có chắc chắn muốn thu hồi tin nhắn này?
                                      </div>
                                      <div className="flex justify-end gap-2">
                                        <button
                                          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300"
                                          onClick={() => setConfirmingDeleteId(null)}
                                        >
                                          Hủy
                                        </button>
                                        <button
                                          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                                          onClick={() => {
                                            handleDeleteMessage(message.messageId || message.id);
                                            setConfirmingDeleteId(null);
                                          }}
                                        >
                                          Xác nhận
                                        </button>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      )}
                    </div>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
              {/* </div> */}

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 p-4 flex-shrink-0">
                {isUploading && (
                  <div className="flex items-center gap-2 mb-2 text-blue-500 animate-pulse">
                    <Loader2 className="animate-spin" size={18} />
                    <span>Đang tải file...</span>
                  </div>
                )}
                {renderReplyingMessage()}
                {renderPreviewFiles()}
                <form onSubmit={handleSendMessage} className="flex flex-col gap-1">
                  <div className="flex items-center space-x-2">
                    {/* Icon gửi file */}
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      className="hidden"
                      multiple
                    />
                    {/* Icon gửi ảnh/video */}
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => imageInputRef.current?.click()}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                    <input
                      type="file"
                      ref={imageInputRef}
                      onChange={handleImageChange}
                      className="hidden"
                      accept="image/*,video/*"
                      multiple
                    />
                    <div className="flex-1 relative">
                      <Input
                        placeholder="Nhập tin nhắn..."
                        value={newMessage}
                        onChange={e => setNewMessage(e.target.value)}
                        onKeyDown={e => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage(e);
                          }
                        }}
                        className="pr-20"
                      />
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <div className="relative">
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => setShowEmojiPicker(v => !v)}
                          >
                            <Smile className="h-4 w-4" />
                          </Button>
                          {showEmojiPicker && (
                            <div className="absolute z-50 right-0 bottom-full mb-2">
                              <Picker
                                data={data}
                                onEmojiSelect={handleEmojiSelect}
                                theme="light"
                                set="native"
                                previewPosition="none"
                                skinTonePosition="none"
                                autoFocus
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      type="submit"
                      disabled={!newMessage.trim() && selectedFiles.length === 0}
                      size="sm"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center h-full flex flex-col items-center justify-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Send className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chọn một cuộc trò chuyện</h3>
                <p className="text-gray-500">
                  Chọn một cuộc trò chuyện từ danh sách để bắt đầu nhắn tin
                </p>
              </div>
            </div>
          )}

          {/* Media Preview Modal */}
          {selectedMedia && (
            <div
              className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-[100]"
              onClick={() => setSelectedMedia(null)}
            >
              <div className="relative max-w-[90vw] max-h-[90vh]">
                {selectedMedia.type.startsWith('image/') ? (
                  <img
                    src={selectedMedia.url}
                    alt="Full size preview"
                    className="max-w-full max-h-[90vh] object-contain"
                  />
                ) : selectedMedia.type.startsWith('video/') ? (
                  <video
                    controls
                    className="max-w-full max-h-[90vh]"
                    src={selectedMedia.url}
                    autoPlay
                  >
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <div className="bg-white p-4 rounded-lg max-w-md">
                    <p className="text-gray-700 mb-2">File không hỗ trợ xem trước</p>
                    <a
                      href={selectedMedia.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:text-blue-700 underline"
                    >
                      Tải xuống file
                    </a>
                  </div>
                )}
                <button
                  className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
                  onClick={() => setSelectedMedia(null)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
