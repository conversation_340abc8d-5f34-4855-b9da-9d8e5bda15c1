'use client';

import React, {
  createContext,
  useContext,
  ReactNode,
  useEffect,
  useState,
  useCallback,
} from 'react';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { toast } from 'sonner';

// Types
interface NotificationAction {
  text: string;
  url: string;
}

interface NotificationData {
  type: string;
  title: string;
  message: string;
  action?: NotificationAction;
  timestamp: string;
}

interface NotificationContextType {
  isConnected: boolean;
  connectionId: string | null | undefined;
  connect: (token: string) => Promise<void>;
  disconnect: () => Promise<void>;
  notifications: NotificationData[];
  clearNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotificationContext() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: ReactNode;
  enabled?: boolean;
  apiBaseUrl?: string;
}

export function NotificationProvider({
  children,
  enabled = true,
  apiBaseUrl = process.env.NEXT_PUBLIC_API_URL_BACKEND || 'https://localhost:7155',
}: NotificationProviderProps) {
  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionId, setConnectionId] = useState<string | null | undefined>(null);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Handle notification display
  const handleNotification = useCallback((notification: NotificationData, type: string) => {
    console.log(`[NotificationProvider] Received ${type}:`, notification);

    // Add to notifications list
    setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50 notifications

    // Show toast notification
    const toastId = toast(notification.title, {
      description: notification.message,
      duration: notification.action ? 10000 : 5000, // Longer duration if has action
      action: notification.action
        ? {
            label: notification.action.text,
            onClick: () => handleNotificationAction(notification.action!),
          }
        : undefined,
      onDismiss: () => {
        console.log(`[NotificationProvider] Toast dismissed for: ${notification.title}`);
      },
    });

    console.log(`[NotificationProvider] Toast shown with ID: ${toastId}`);
  }, []);

  // Handle notification action
  const handleNotificationAction = useCallback((action: NotificationAction) => {
    console.log(`[NotificationProvider] Handling action: ${action.text} -> ${action.url}`);

    try {
      const newWindow = window.open(
        action.url,
        '_blank',
        'width=800,height=600,scrollbars=yes,resizable=yes'
      );

      if (newWindow) {
        // Monitor window close
        const checkClosed = setInterval(() => {
          if (newWindow.closed) {
            clearInterval(checkClosed);
            toast.success('Action completed', {
              description: 'The authentication window has been closed. Please refresh if needed.',
              duration: 5000,
            });
          }
        }, 1000);
      } else {
        // Fallback if popup blocked
        window.location.href = action.url;
      }
    } catch (error) {
      console.error('[NotificationProvider] Error opening action URL:', error);
      toast.error('Unable to open action URL', {
        description: 'Please check your popup blocker settings.',
      });
    }
  }, []);

  // Setup SignalR connection
  const connect = useCallback(
    async (token: string) => {
      if (!enabled || connection) return;

      try {
        console.log('[NotificationProvider] Connecting to SignalR...');

        const hubUrl = `${apiBaseUrl}/notificationHub`;
        const newConnection = new HubConnectionBuilder()
          .withUrl(hubUrl, {
            accessTokenFactory: () => token,
          })
          .withAutomaticReconnect([0, 2000, 10000, 30000])
          .configureLogging(LogLevel.Information)
          .build();

        // Setup event handlers
        newConnection.on('GoogleReauthRequired', (notification: NotificationData) => {
          handleNotification(notification, 'GoogleReauthRequired');
        });

        newConnection.on('ReceiveNotification', (notification: NotificationData) => {
          handleNotification(notification, 'ReceiveNotification');
        });

        newConnection.on('NotifyUser', (notification: NotificationData) => {
          handleNotification(notification, 'NotifyUser');
        });

        newConnection.on('NotifyGroup', (notification: NotificationData) => {
          handleNotification(notification, 'NotifyGroup');
        });

        newConnection.on('BroadcastMessage', (notification: NotificationData) => {
          handleNotification(notification, 'BroadcastMessage');
        });

        // Connection state handlers
        newConnection.onreconnecting(error => {
          console.log('[NotificationProvider] Reconnecting...', error);
          setIsConnected(false);
          // toast.info('Reconnecting to notifications...', { duration: 2000 });
        });

        newConnection.onreconnected(connectionId => {
          console.log('[NotificationProvider] Reconnected:', connectionId);
          setIsConnected(true);
          setConnectionId(connectionId);
          // toast.success('Notifications reconnected', { duration: 2000 });
        });

        newConnection.onclose(error => {
          console.log('[NotificationProvider] Connection closed:', error);
          setIsConnected(false);
          setConnectionId(null);
          if (error) {
            // toast.error('Notification connection lost', {
            //   description: 'Attempting to reconnect...',
            //   duration: 3000,
            // });
          }
        });

        // Start connection
        await newConnection.start();

        setConnection(newConnection);
        setIsConnected(true);
        setConnectionId(newConnection.connectionId);

        console.log('[NotificationProvider] Connected successfully:', newConnection.connectionId);
        // toast.success('Notifications connected', { duration: 2000 });
      } catch (error) {
        console.error('[NotificationProvider] Connection failed:', error);
        // toast.error('Failed to connect notifications', {
        //   description: 'Please check your connection and try again.',
        //   duration: 5000,
        // });
      }
    },
    [enabled, connection, apiBaseUrl, handleNotification]
  );

  // Disconnect
  const disconnect = useCallback(async () => {
    if (connection) {
      try {
        console.log('[NotificationProvider] Disconnecting...');
        await connection.stop();
        setConnection(null);
        setIsConnected(false);
        setConnectionId(null);
        // toast.info('Notifications disconnected', { duration: 2000 });
      } catch (error) {
        console.error('[NotificationProvider] Disconnect error:', error);
      }
    }
  }, [connection]);

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (connection) {
        connection.stop().catch(console.error);
      }
    };
  }, [connection]);

  const contextValue: NotificationContextType = {
    isConnected,
    connectionId,
    connect,
    disconnect,
    notifications,
    clearNotifications,
  };

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <NotificationContext.Provider value={contextValue}>{children}</NotificationContext.Provider>
  );
}
