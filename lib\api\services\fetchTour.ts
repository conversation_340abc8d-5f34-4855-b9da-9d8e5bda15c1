import apiService, { RequestParams } from '../core';
import { Property } from './fetchProperty';
// types/tour.ts
export interface TourProperty {
  id: string;
  tourId: string;
  response: Property;
  visitTimeStart: string; // ISO string format
  visitTimeEnd: string; // ISO string format
  status?: 'Pending' | 'Approved' | 'Completed' | 'Rejected'; // Assuming possible statuses
}

export interface Tour {
  id: string;
  name: string;
  startDateTime: string; // ISO string format
  status: 'Pending' | 'Approved' | 'Completed'; // Assuming possible statuses
  note: string;
  tourProperties: TourProperty[];
}

// Request/Response types for Tour API
export interface CreateTourRequest {
  name?: string;
  startDateTime: string;
  status: 'Pending' | 'Approved' | 'Completed' | 'Rejected';
  note?: string;
  tourProperties: {
    propertyId: string;
    visitTimeStart: string;
    visitTimeEnd: string;
  }[];
}

export interface UpdateTourRequest {
  name?: string;
  startDateTime?: string;
  note?: string;
}

export interface UpdateTourPropertyRequest {
  propertyId?: string;
  visitTimeStart?: string;
  visitTimeEnd?: string;
}

export interface AddPropertyToTourRequest {
  propertyId: string;
  visitTimeStart: string;
  visitTimeEnd: string;
}

export interface TourResponse<T> {
  code: string;
  status: boolean;
  message: string;
  data?: T;
}

export interface TourListResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    totalCount: number;
    pageSize: number;
    currentPage: number;
    totalPages: number;
    data: Tour[];
  };
}

export interface TourPropertiesBySalerResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    totalCount: number;
    pageSize: number;
    currentPage: number;
    totalPages: number;
    data: TourProperty[];
  };
}

export interface TourDetailResponse {
  code: string;
  status: boolean;
  message: string;
  data?: Tour; // For GET /api/tour/{id}
}

export type TourSortBy = 'name' | 'StartDateTime';

export interface TourSearchParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isDescending?: boolean;
  startDateTime?: string;
  sortBy?: TourSortBy;
}

export type SalerTourSortBy = 'VisitTimeStart' | 'AssignTo';

export interface SalerTourSearchParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isDescending?: boolean;
  visitTimeStart?: string;
  assignTo?: string;
  sortBy?: SalerTourSortBy;
}

export const convertTourFilters = (filters?: TourSearchParams): RequestParams => {
  if (!filters) return {};

  const { searchTerm, pageNumber, pageSize, isDescending, startDateTime, sortBy } = filters;

  const params: RequestParams = {};

  if (searchTerm) params.searchTerm = searchTerm;
  if (pageNumber !== undefined) params.pageNumber = pageNumber;
  if (pageSize !== undefined) params.pageSize = pageSize;
  if (isDescending !== undefined) params.isDescending = isDescending;
  if (startDateTime) params.startDateTime = startDateTime;
  if (sortBy) params.sortBy = sortBy;

  return params;
};

export const convertSalerTourFilters = (filters?: SalerTourSearchParams): RequestParams => {
  if (!filters) return {};

  const { searchTerm, pageNumber, pageSize, isDescending, visitTimeStart, assignTo, sortBy } =
    filters;

  const params: RequestParams = {};

  if (searchTerm) params.searchTerm = searchTerm;
  if (pageNumber !== undefined) params.pageNumber = pageNumber;
  if (pageSize !== undefined) params.pageSize = pageSize;
  if (isDescending !== undefined) params.isDescending = isDescending;
  if (visitTimeStart) params.startDateTime = visitTimeStart;
  if (assignTo) params.assignTo = assignTo;
  if (sortBy) params.sortBy = sortBy;

  return params;
};

export const tourService = {
  // Create a new tour
  createTour: async (tourData: CreateTourRequest): Promise<TourResponse<{ tourId: string }>> => {
    const response = await apiService.post<TourResponse<{ tourId: string }>, CreateTourRequest>(
      '/api/tour/create',
      tourData
    );
    return response.data;
  },

  // Get all tours for the current user
  getUserTours: async (filters?: TourSearchParams): Promise<TourListResponse> => {
    const params = convertTourFilters(filters);
    const response = await apiService.get<TourListResponse>('/api/tour/user', params);
    return response.data;
  },

  getSalerTours: async (
    filters?: SalerTourSearchParams
  ): Promise<TourPropertiesBySalerResponse> => {
    const params = convertSalerTourFilters(filters);
    const response = await apiService.get<TourPropertiesBySalerResponse>('/api/tour', params);
    return response.data;
  },

  // Get a single tour by ID
  getTour: async (id: string): Promise<TourDetailResponse> => {
    const response = await apiService.get<TourDetailResponse>(`/api/tour/${id}`);
    return response.data;
  },

  // Update an existing tour
  updateTour: async (
    id: string,
    tourData: Partial<UpdateTourRequest>
  ): Promise<TourResponse<string>> => {
    const response = await apiService.put<TourResponse<string>, Partial<UpdateTourRequest>>(
      `/api/tour/update-tourItinerary/${id}`,
      tourData
    );
    return response.data;
  },
  updateTourProperty: async (
    id: string,
    tourData: Partial<UpdateTourPropertyRequest>
  ): Promise<TourResponse<string>> => {
    const response = await apiService.put<TourResponse<string>, Partial<UpdateTourPropertyRequest>>(
      `/api/tour/tour-properties/${id}`,
      tourData
    );
    return response.data;
  },

  updateTourStatus: async (
    id: string,
    status: 'Pending' | 'Approved' | 'Completed' | 'Rejected'
  ): Promise<TourResponse<string>> => {
    const response = await apiService.patch<TourResponse<string>>(
      `/api/tour/update-status/${id}?status=${status}`,
      {}
    );
    return response.data;
  },

  // Delete a tour
  deleteTour: async (id: string): Promise<TourResponse<string>> => {
    const response = await apiService.delete<TourResponse<string>>(`/api/tour/${id}`);
    return response.data;
  },

  // Add a property to a tour
  addPropertyToTour: async (
    tourId: string,
    propertyData: AddPropertyToTourRequest
  ): Promise<TourResponse<string>> => {
    const response = await apiService.post<TourResponse<string>, AddPropertyToTourRequest>(
      `/api/tour/${tourId}/add-property`,
      propertyData
    );
    return response.data;
  },

  // Remove a property from a tour
  removePropertyFromTour: async (
    tourId: string,
    propertyId: string
  ): Promise<TourResponse<string>> => {
    const response = await apiService.delete<TourResponse<string>>(
      `/api/tour/${tourId}/remove-property/${propertyId}`
    );
    return response.data;
  },
};

export default tourService;
