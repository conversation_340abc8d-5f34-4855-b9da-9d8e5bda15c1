import { memo, useMemo } from 'react';

import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { PropertyCard } from '@/components/PropertyCard';
import { AlertTriangle, Search } from 'lucide-react';
import { Property } from '@/lib/api/services/fetchProperty';
import Footer from '@/components/Footer';

interface PropertyListingsProps {
  properties: Property[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isFetching: boolean;
  count: number;
  totalPages: number;
  page: number;
  onPageChange: (page: number) => void;
  onPropertyHover?: (propertyId: string | null) => void;
  containerWidth?: number;
}

// Custom hook to measure container width
// function useContainerWidth() {
//   const containerRef = useRef<HTMLDivElement>(null);
//   const [width, setWidth] = useState<number>(0);
//   const [isInitialized, setIsInitialized] = useState(false);

//   useEffect(() => {
//     if (!containerRef.current) return;

//     const updateWidth = () => {
//       if (containerRef.current) {
//         const newWidth = containerRef.current.getBoundingClientRect().width;
//         if (newWidth > 0) {
//           setWidth(newWidth);
//           setIsInitialized(true);
//         }
//       }
//     };

//     // Initial measurement with a small delay to allow panel to settle
//     const initialTimer = setTimeout(updateWidth, 100);

//     // Use ResizeObserver for more reliable measurements
//     const resizeObserver = new ResizeObserver(() => {
//       updateWidth();
//     });

//     resizeObserver.observe(containerRef.current);

//     // Force an update after a short delay to ensure we get the correct width
//     const forceUpdateTimer = setTimeout(updateWidth, 500);

//     return () => {
//       clearTimeout(initialTimer);
//       clearTimeout(forceUpdateTimer);
//       resizeObserver.disconnect();
//     };
//   }, []);

//   // Force a re-render when the component is mounted
//   useEffect(() => {
//     if (!isInitialized && containerRef.current) {
//       const timer = setTimeout(() => {
//         const newWidth = containerRef.current?.getBoundingClientRect().width ?? 0;
//         if (newWidth > 0) {
//           setWidth(newWidth);
//           setIsInitialized(true);
//         }
//       }, 1000);
//       return () => clearTimeout(timer);
//     }
//   }, [isInitialized]);

//   return { containerRef, width, isInitialized };
// }

// Helper function to determine grid columns based on width
function getGridColumns(width: number): number {
  if (width >= 1600) return 5;
  if (width >= 1200) return 4;
  if (width >= 900) return 3;
  if (width >= 600) return 2;
  return 1;
}

const PropertyListings = memo(function PropertyListings({
  properties,
  isLoading,
  isError,
  error,
  isFetching,
  //count,
  totalPages,
  page,
  onPageChange,
  onPropertyHover,
  containerWidth = 0,
}: PropertyListingsProps) {
  const gridColumns = useMemo(() => getGridColumns(containerWidth), [containerWidth]);

  // Loading state
  if (isLoading) {
    return (
      <div
        className="grid gap-3 w-full"
        style={{
          gridTemplateColumns: `repeat(${gridColumns}, minmax(0, 1fr))`,
          minWidth: '100%',
          width: '100%',
        }}
      >
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="overflow-hidden transition-all duration-300 flex flex-col">
            {/* Image Section with Badges */}
            <div className="relative aspect-square size-full mb-2 group">
              <Skeleton className="w-full h-full rounded-2xl" />
              {/* Badge Skeletons */}
              <div className="absolute top-4 left-4 flex gap-2">
                <Skeleton className="h-6 w-12 rounded-full bg-white/20" />
                <Skeleton className="h-6 w-16 rounded-full bg-white/20" />
              </div>
            </div>

            <div className="px-1">
              {/* Price and Actions Section */}
              <div className="flex justify-between items-center">
                <div>
                  <Skeleton className="h-6 w-24" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8 rounded-md" />
                  <Skeleton className="h-8 w-8 rounded-md" />
                </div>
              </div>

              {/* Property Details Section */}
              <div className="flex justify-between items-center mb-2 mt-2">
                <div className="flex gap-4">
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 mr-1 rounded" />
                    <Skeleton className="h-4 w-3" />
                  </div>
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 mr-1 rounded" />
                    <Skeleton className="h-4 w-3" />
                  </div>
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 mr-1 rounded" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                </div>
                <Skeleton className="h-3 w-16" />
              </div>

              {/* Location Section */}
              <div className="mb-2">
                <div className="flex items-center">
                  <Skeleton className="h-4 w-4 mr-1 rounded" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex justify-center items-center min-h-[500px] bg-background text-foreground text-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Lỗi</h3>
          <p className="text-muted-foreground mb-2">Đã xảy ra lỗi khi tải bất động sản</p>
          <p className="text-sm text-destructive mb-4">
            {error instanceof Error ? error.message : 'Đã xảy ra lỗi khi tải bất động sản'}
          </p>
          <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
            Tải lại
          </Button>
        </div>
      </div>
    );
  }

  // Empty results state
  if (!properties || properties.length === 0) {
    return (
      <div className="flex justify-center items-center min-h-[500px] bg-background text-foreground text-center">
        <div className="text-center max-w-md mx-auto">
          <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Không tìm thấy bất động sản</h3>
          <p className="text-muted-foreground mb-6">Không tìm thấy bất động sản</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => (window.location.href = '/properties')}
          >
            Xóa bộ lọc
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6 w-full">
        {/* Properties grid with loading overlay when fetching */}
        <div className="relative w-full">
          {isFetching && !isLoading && (
            <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10 backdrop-blur-sm">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
                <p className="text-primary font-medium">Đang cập nhật</p>
              </div>
            </div>
          )}

          <div
            className="grid gap-3 w-full"
            style={{
              gridTemplateColumns: `repeat(${gridColumns}, minmax(0, 1fr))`,
              minWidth: '100%',
              width: '100%',
            }}
          >
            {properties.map((property, index) => (
              <PropertyCard
                key={property.id}
                property={property}
                priority={index < 6}
                onHover={onPropertyHover}
              />
            ))}
          </div>
        </div>

        {totalPages > 1 && (
          <Pagination className="mt-12">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => onPageChange(Math.max(page - 1, 1))}
                  className={page === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }).map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink isActive={page === i + 1} onClick={() => onPageChange(i + 1)}>
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => onPageChange(Math.min(page + 1, totalPages))}
                  className={
                    page === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
      <Footer containerWidth={containerWidth} />
    </>
  );
});

export default PropertyListings;
