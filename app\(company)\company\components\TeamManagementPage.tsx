import React, { useState } from 'react';
import {
  ArrowLeft,
  Users,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  Search,
  Crown,
  Shield,
  Eye,
  Move,
  Award,
  Zap,
  TrendingUp,
  Star,
  Mail,
  Phone,
} from 'lucide-react';

interface TeamManagementPageProps {
  onBack?: () => void;
}

interface Agent {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'busy';
  performance: {
    deals: number;
    revenue: number;
    rating: number;
  };
  specialties: string[];
  joinedDate: string;
  lastActive: string;
}

interface Team {
  id: string;
  name: string;
  description: string;
  color: string;
  leader: string;
  members: Agent[];
  stats: {
    totalDeals: number;
    totalRevenue: number;
    avgRating: number;
    activeProjects: number;
  };
  createdDate: string;
}

const TeamManagementPage: React.FC<TeamManagementPageProps> = ({ onBack }) => {
  const [teams, setTeams] = useState<Team[]>([
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON> về bán bất động sản cao cấp',
      color: 'from-blue-500 to-cyan-500',
      leader: '<PERSON><PERSON><PERSON><PERSON>',
      members: [
        {
          id: '1',
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phone: '0123456789',
          role: 'Team Leader',
          status: 'active',
          performance: { deals: 25, revenue: 2500000000, rating: 4.8 },
          specialties: ['Căn hộ cao cấp', 'Biệt thự'],
          joinedDate: '2024-01-15',
          lastActive: '2 giờ trước',
        },
        {
          id: '2',
          name: 'Trần Thị B',
          email: '<EMAIL>',
          phone: '0987654321',
          role: 'Senior Agent',
          status: 'active',
          performance: { deals: 18, revenue: 1800000000, rating: 4.6 },
          specialties: ['Chung cư', 'Nhà phố'],
          joinedDate: '2024-02-01',
          lastActive: '1 giờ trước',
        },
      ],
      stats: { totalDeals: 43, totalRevenue: 4300000000, avgRating: 4.7, activeProjects: 12 },
      createdDate: '2024-01-10',
    },
    {
      id: '2',
      name: 'Nhóm Cho Thuê',
      description: 'Chuyên về cho thuê và quản lý tài sản',
      color: 'from-green-500 to-emerald-500',
      leader: 'Lê Văn C',
      members: [
        {
          id: '3',
          name: 'Lê Văn C',
          email: '<EMAIL>',
          phone: '0369852147',
          role: 'Team Leader',
          status: 'active',
          performance: { deals: 32, revenue: 960000000, rating: 4.9 },
          specialties: ['Cho thuê văn phòng', 'Quản lý tòa nhà'],
          joinedDate: '2024-01-20',
          lastActive: '30 phút trước',
        },
      ],
      stats: { totalDeals: 32, totalRevenue: 960000000, avgRating: 4.9, activeProjects: 8 },
      createdDate: '2024-01-15',
    },
  ]);

  const [unassignedAgents, setUnassignedAgents] = useState<Agent[]>([
    {
      id: '4',
      name: 'Phạm Thị D',
      email: '<EMAIL>',
      phone: '0147258369',
      role: 'Junior Agent',
      status: 'active',
      performance: { deals: 8, revenue: 400000000, rating: 4.2 },
      specialties: ['Đất nền', 'Nhà trọ'],
      joinedDate: '2024-03-01',
      lastActive: '1 ngày trước',
    },
    {
      id: '5',
      name: 'Hoàng Văn E',
      email: '<EMAIL>',
      phone: '0258147963',
      role: 'Agent',
      status: 'inactive',
      performance: { deals: 12, revenue: 720000000, rating: 4.4 },
      specialties: ['Kho xưởng', 'Đất công nghiệp'],
      joinedDate: '2024-02-15',
      lastActive: '3 ngày trước',
    },
  ]);

  const [isCreatingTeam, setIsCreatingTeam] = useState(false);
  const [editingTeam, setEditingTeam] = useState<string | null>(null);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamDescription, setNewTeamDescription] = useState('');
  const [newTeamColor, setNewTeamColor] = useState('from-red-500 to-orange-500');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [draggedAgent, setDraggedAgent] = useState<Agent | null>(null);
  const [dragOverTeam, setDragOverTeam] = useState<string | null>(null);

  const teamColors = [
    'from-red-500 to-orange-500',
    'from-blue-500 to-cyan-500',
    'from-green-500 to-emerald-500',
    'from-purple-500 to-pink-500',
    'from-yellow-500 to-orange-500',
    'from-indigo-500 to-purple-500',
    'from-pink-500 to-rose-500',
    'from-teal-500 to-green-500',
  ];

  const roleConfig = {
    'Team Leader': { icon: Crown, color: 'text-purple-600 bg-purple-100' },
    'Senior Agent': { icon: Shield, color: 'text-blue-600 bg-blue-100' },
    Agent: { icon: Users, color: 'text-green-600 bg-green-100' },
    'Junior Agent': { icon: Eye, color: 'text-gray-600 bg-gray-100' },
  };

  const statusConfig = {
    active: { label: 'Hoạt động', color: 'bg-green-100 text-green-800' },
    inactive: { label: 'Không hoạt động', color: 'bg-gray-100 text-gray-800' },
    busy: { label: 'Bận', color: 'bg-yellow-100 text-yellow-800' },
  };

  const createTeam = () => {
    if (!newTeamName.trim()) return;

    const newTeam: Team = {
      id: Date.now().toString(),
      name: newTeamName,
      description: newTeamDescription,
      color: newTeamColor,
      leader: '',
      members: [],
      stats: { totalDeals: 0, totalRevenue: 0, avgRating: 0, activeProjects: 0 },
      createdDate: new Date().toISOString().split('T')[0],
    };

    setTeams(prev => [...prev, newTeam]);
    setIsCreatingTeam(false);
    setNewTeamName('');
    setNewTeamDescription('');
    setNewTeamColor('from-red-500 to-orange-500');
  };

  const deleteTeam = (teamId: string) => {
    const team = teams.find(t => t.id === teamId);
    if (team) {
      // Move all members back to unassigned
      setUnassignedAgents(prev => [...prev, ...team.members]);
      setTeams(prev => prev.filter(t => t.id !== teamId));
    }
  };

  // const updateTeamName = (teamId: string, newName: string) => {
  //   setTeams(prev => prev.map(team => (team.id === teamId ? { ...team, name: newName } : team)));
  //   setEditingTeam(null);
  // };

  const handleDragStart = (agent: Agent) => {
    setDraggedAgent(agent);
  };

  const handleDragOver = (e: React.DragEvent, teamId: string) => {
    e.preventDefault();
    setDragOverTeam(teamId);
  };

  const handleDragLeave = () => {
    setDragOverTeam(null);
  };

  const handleDrop = (e: React.DragEvent, targetTeamId: string) => {
    e.preventDefault();
    setDragOverTeam(null);

    if (!draggedAgent) return;

    // Remove agent from current location
    const sourceTeam = teams.find(team =>
      team.members.some(member => member.id === draggedAgent.id)
    );

    if (sourceTeam) {
      // Moving from team to team
      setTeams(prev =>
        prev.map(team => {
          if (team.id === sourceTeam.id) {
            return { ...team, members: team.members.filter(m => m.id !== draggedAgent.id) };
          }
          if (team.id === targetTeamId) {
            return { ...team, members: [...team.members, draggedAgent] };
          }
          return team;
        })
      );
    } else {
      // Moving from unassigned to team
      setUnassignedAgents(prev => prev.filter(agent => agent.id !== draggedAgent.id));
      setTeams(prev =>
        prev.map(team =>
          team.id === targetTeamId ? { ...team, members: [...team.members, draggedAgent] } : team
        )
      );
    }

    setDraggedAgent(null);
  };

  const handleDropToUnassigned = (e: React.DragEvent) => {
    e.preventDefault();

    if (!draggedAgent) return;

    // Remove from current team
    setTeams(prev =>
      prev.map(team => ({
        ...team,
        members: team.members.filter(m => m.id !== draggedAgent.id),
      }))
    );

    // Add to unassigned
    setUnassignedAgents(prev => [...prev, draggedAgent]);
    setDraggedAgent(null);
  };

  // const formatCurrency = (amount: number) => {
  //   return new Intl.NumberFormat('vi-VN', {
  //     style: 'currency',
  //     currency: 'VND',
  //   }).format(amount);
  // };

  const filteredUnassignedAgents = unassignedAgents.filter(agent => {
    const matchesSearch =
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || agent.role === filterRole;
    return matchesSearch && matchesRole;
  });

  return (
    <div className="flex-1 bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <button
              onClick={onBack}
              className="flex items-center gap-3 text-gray-600 hover:text-gray-900 transition-colors font-medium"
            >
              <ArrowLeft size={20} />
              <span>Quay lại</span>
            </button>
            <div className="h-6 w-px bg-gray-300"></div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Quản lý Nhóm</h1>
              <p className="text-gray-600">Tạo nhóm và phân công nhân viên</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsCreatingTeam(true)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white rounded-xl font-medium shadow-lg shadow-red-500/25 transition-all"
            >
              <Plus size={16} />
              <span>Tạo nhóm mới</span>
            </button>
          </div>
        </div>
      </div>
      <div className="p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users size={24} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{teams.length}</p>
                  <p className="text-gray-600 text-sm">Tổng số nhóm</p>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp size={24} className="text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {teams.reduce((sum, team) => sum + team.stats.totalDeals, 0)}
                  </p>
                  <p className="text-gray-600 text-sm">Tổng giao dịch</p>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <Award size={24} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {teams.reduce((sum, team) => sum + team.members.length, 0)}
                  </p>
                  <p className="text-gray-600 text-sm">Nhân viên đã phân công</p>
                </div>
              </div>
            </div>
            <div className="bg-white rounded-2xl border border-gray-200 p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                  <Zap size={24} className="text-yellow-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{unassignedAgents.length}</p>
                  <p className="text-gray-600 text-sm">Chưa phân công</p>
                </div>
              </div>
            </div>
          </div>
          {/* Create Team Modal */}
          {isCreatingTeam && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl border border-gray-200 shadow-2xl max-w-md w-full">
                <div className="p-6 border-b border-gray-100">
                  <h3 className="text-xl font-bold text-gray-900">Tạo nhóm mới</h3>
                </div>
                <div className="p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Tên nhóm
                    </label>
                    <input
                      type="text"
                      value={newTeamName}
                      onChange={e => setNewTeamName(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:border-red-500 focus:ring-2 focus:ring-red-100 transition-all"
                      placeholder="Nhập tên nhóm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Mô tả</label>
                    <textarea
                      value={newTeamDescription}
                      onChange={e => setNewTeamDescription(e.target.value)}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:border-red-500 focus:ring-2 focus:ring-red-100 transition-all resize-none"
                      placeholder="Mô tả về nhóm"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Màu sắc
                    </label>
                    <div className="grid grid-cols-4 gap-3">
                      {teamColors.map(color => (
                        <button
                          key={color}
                          onClick={() => setNewTeamColor(color)}
                          className={`w-12 h-12 bg-gradient-to-br ${color} rounded-xl border-2 transition-all ${
                            newTeamColor === color ? 'border-gray-900 scale-110' : 'border-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <div className="p-6 border-t border-gray-100 flex gap-3">
                  <button
                    onClick={() => setIsCreatingTeam(false)}
                    className="flex-1 px-4 py-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-xl transition-all font-medium"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={createTeam}
                    disabled={!newTeamName.trim()}
                    className={`flex-1 px-4 py-3 rounded-xl font-medium transition-all ${
                      newTeamName.trim()
                        ? 'bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white'
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    Tạo nhóm
                  </button>
                </div>
              </div>
            </div>
          )}
          {/* Teams Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {teams.map(team => (
              <div
                key={team.id}
                onDragOver={e => handleDragOver(e, team.id)}
                onDragLeave={handleDragLeave}
                onDrop={e => handleDrop(e, team.id)}
                className={`bg-white rounded-2xl border-2 transition-all duration-300 ${
                  dragOverTeam === team.id
                    ? 'border-red-500 bg-red-50 scale-105 shadow-xl shadow-red-500/20'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-lg'
                }`}
              >
                {/* Team Header */}
                <div className={`p-6 bg-gradient-to-br ${team.color} rounded-t-2xl text-white`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {editingTeam === team.id ? (
                        <input
                          type="text"
                          value={team.name}
                          onChange={e =>
                            setTeams(prev =>
                              prev.map(t => (t.id === team.id ? { ...t, name: e.target.value } : t))
                            )
                          }
                          onBlur={() => setEditingTeam(null)}
                          onKeyPress={e => e.key === 'Enter' && setEditingTeam(null)}
                          className="bg-white/20 text-white placeholder-white/70 border border-white/30 rounded-lg px-3 py-1 text-lg font-bold"
                          autoFocus
                        />
                      ) : (
                        <h3 className="text-xl font-bold">{team.name}</h3>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setEditingTeam(team.id)}
                        className="p-2 hover:bg-white/20 rounded-lg transition-all"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => deleteTeam(team.id)}
                        className="p-2 hover:bg-white/20 rounded-lg transition-all"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  <p className="text-white/90 text-sm mb-4">{team.description}</p>
                  {/* Team Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white/20 rounded-lg p-3">
                      <p className="text-white/70 text-xs">Giao dịch</p>
                      <p className="text-white font-bold text-lg">{team.stats.totalDeals}</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3">
                      <p className="text-white/70 text-xs">Đánh giá</p>
                      <p className="text-white font-bold text-lg">
                        {team.stats.avgRating.toFixed(1)}
                      </p>
                    </div>
                  </div>
                </div>
                {/* Team Members */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-900">
                      Thành viên ({team.members.length})
                    </h4>
                    <button className="text-gray-400 hover:text-red-500 transition-colors">
                      <UserPlus size={16} />
                    </button>
                  </div>
                  <div className="space-y-3 min-h-[120px]">
                    {team.members.length === 0 ? (
                      <div className="flex items-center justify-center h-24 border-2 border-dashed border-gray-200 rounded-xl">
                        <p className="text-gray-400 text-sm">Kéo thả nhân viên vào đây</p>
                      </div>
                    ) : (
                      team.members.map(member => {
                        const roleInfo = roleConfig[member.role as keyof typeof roleConfig];
                        const statusInfo = statusConfig[member.status];
                        return (
                          <div
                            key={member.id}
                            draggable
                            onDragStart={() => handleDragStart(member)}
                            className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all cursor-move group"
                          >
                            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold text-sm flex-shrink-0">
                              {member.name.charAt(0)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <p className="font-medium text-gray-900 truncate">{member.name}</p>
                                <span
                                  className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}
                                >
                                  {statusInfo.label}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div
                                  className={`flex items-center gap-1 px-2 py-0.5 rounded-lg ${roleInfo.color}`}
                                >
                                  <roleInfo.icon size={12} />
                                  <span className="text-xs font-medium">{member.role}</span>
                                </div>
                              </div>
                            </div>
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                              <Move size={16} className="text-gray-400" />
                            </div>
                          </div>
                        );
                      })
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Unassigned Agents */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-sm">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    Nhân viên chưa phân công ({filteredUnassignedAgents.length})
                  </h2>
                  <p className="text-gray-600">Kéo thả để phân công vào nhóm</p>
                </div>
              </div>
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                      size={18}
                    />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-400 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                      placeholder="Tìm kiếm nhân viên..."
                    />
                  </div>
                </div>
                <select
                  value={filterRole}
                  onChange={e => setFilterRole(e.target.value)}
                  className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 focus:border-red-500 focus:outline-none focus:ring-2 focus:ring-red-100 transition-all"
                >
                  <option value="all">Tất cả vai trò</option>
                  <option value="Team Leader">Team Leader</option>
                  <option value="Senior Agent">Senior Agent</option>
                  <option value="Agent">Agent</option>
                  <option value="Junior Agent">Junior Agent</option>
                </select>
              </div>
            </div>
            <div
              onDragOver={e => e.preventDefault()}
              onDrop={handleDropToUnassigned}
              className="p-6 min-h-[200px]"
            >
              {filteredUnassignedAgents.length === 0 ? (
                <div className="flex items-center justify-center h-32 border-2 border-dashed border-gray-200 rounded-xl">
                  <div className="text-center">
                    <Users size={32} className="text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">Không có nhân viên chưa phân công</p>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredUnassignedAgents.map(agent => {
                    const roleInfo = roleConfig[agent.role as keyof typeof roleConfig];
                    const statusInfo = statusConfig[agent.status];
                    return (
                      <div
                        key={agent.id}
                        draggable
                        onDragStart={() => handleDragStart(agent)}
                        className="p-4 bg-gray-50 hover:bg-gray-100 rounded-xl border border-gray-200 hover:border-gray-300 transition-all cursor-move group"
                      >
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center text-white font-semibold flex-shrink-0">
                            {agent.name.charAt(0)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-semibold text-gray-900 truncate">{agent.name}</h4>
                              <span
                                className={`px-2 py-0.5 rounded-full text-xs font-medium ${statusInfo.color}`}
                              >
                                {statusInfo.label}
                              </span>
                            </div>
                            <div
                              className={`flex items-center gap-1 px-2 py-0.5 rounded-lg ${roleInfo.color} w-fit`}
                            >
                              <roleInfo.icon size={12} />
                              <span className="text-xs font-medium">{agent.role}</span>
                            </div>
                          </div>
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <Move size={16} className="text-gray-400" />
                          </div>
                        </div>
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <Mail size={14} />
                            <span className="truncate">{agent.email}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Phone size={14} />
                            <span>{agent.phone}</span>
                          </div>
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Giao dịch:</span>
                            <span className="font-semibold text-gray-900">
                              {agent.performance.deals}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Đánh giá:</span>
                            <div className="flex items-center gap-1">
                              <Star size={12} className="text-yellow-500 fill-current" />
                              <span className="font-semibold text-gray-900">
                                {agent.performance.rating}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamManagementPage;
