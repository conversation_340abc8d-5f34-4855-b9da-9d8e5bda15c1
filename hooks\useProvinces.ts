import { useQuery } from '@tanstack/react-query';
import provinceService, { Province, Ward } from '@/lib/api/services/fetchProvinces';
import { toast } from 'sonner';

// Query keys
export const provinceKeys = {
  all: ['provinces'] as const,
  districts: (provinceCode: string) => [...provinceKeys.all, 'districts', provinceCode] as const,
  wards: (districtCode: string) => [...provinceKeys.all, 'wards', districtCode] as const,
};

/**
 * Hook to fetch all provinces
 */
export function useProvinces() {
  return useQuery({
    queryKey: provinceKeys.all,
    queryFn: async (): Promise<Province[]> => {
      try {
        return await provinceService.getProvinces();
      } catch (error) {
        toast.error('Không thể tải danh sách tỉnh/thành phố. Vui lòng thử lại.');
        throw error;
      }
    },
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
    retry: 3,
  });
}

/**
 * Hook to fetch wards by province code
 */
export function useWards(provinceCode: string | null) {
  return useQuery({
    queryKey: provinceKeys.wards(provinceCode || ''),
    queryFn: async (): Promise<Ward[]> => {
      if (!provinceCode) return [];
      try {
        return await provinceService.getWardsByProvince(provinceCode);
      } catch (error) {
        toast.error('Không thể tải danh sách phường/xã. Vui lòng thử lại.');
        throw error;
      }
    },
    enabled: !!provinceCode,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 days
    retry: 3,
  });
}

/**
 * Hook to prefetch provinces data
 * Useful for improving perceived performance
 */
export function usePrefetchProvinces() {
  return async () => {
    try {
      await provinceService.getProvinces();
    } catch (error) {
      toast.error('Không thể tải trước danh sách tỉnh/thành phố.');
    }
  };
}
