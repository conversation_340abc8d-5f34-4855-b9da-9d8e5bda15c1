'use client';

import React, { useRef, useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { partyBSchema, PartyBInput } from '../schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { useUploadPropertyImage } from '@/hooks/useAttachment';

interface Step2Props {
  data: PartyBInput;
  onNext: (data: PartyBInput) => void;
  onBack: () => void;
}

const Step2: React.FC<Step2Props> = ({ data, onNext, onBack }) => {
  const [uploadedFiles, setUploadedFiles] = useState<string[]>(data.idVerification || []);
  const updateApi = useUploadPropertyImage();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    trigger,
  } = useForm<PartyBInput>({
    resolver: zodResolver(partyBSchema),
    defaultValues: {
      ...data,
      idVerification: uploadedFiles,
    },
    mode: 'onChange',
  });

  // Sync uploadedFiles with React Hook Form
  useEffect(() => {
    setValue('idVerification', uploadedFiles);
    trigger('idVerification');
  }, [uploadedFiles, setValue, trigger]);

  const handleFileUpload = async (files: FileList | null) => {
    if (files && files.length > 0) {
      setIsUploading(true);
      try {
        const uploadResponse = await updateApi.mutateAsync({
          files: Array.from(files),
        });
        const newFileUrls = uploadResponse.data.map(file => file.fileUrl);
        const allFiles = [...uploadedFiles, ...newFileUrls];
        setUploadedFiles(allFiles);
        // Update React Hook Form value and trigger validation
        setValue('idVerification', allFiles);
        await trigger('idVerification');
      } catch (error) {
        toast.error('Lỗi khi tải lên tài liệu');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const onSubmit = (formData: PartyBInput) => {
    onNext({
      ...formData,
      idVerification: uploadedFiles,
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Bước 2: Thông tin Bên B (Bên thuê)
        </CardTitle>
        <CardDescription className="text-center">
          Vui lòng nhập đầy đủ thông tin của bên thuê
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium">
                Họ và tên *
              </Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Nhập họ và tên đầy đủ"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="idNumber" className="text-sm font-medium">
                Số CMND/CCCD *
              </Label>
              <Input
                id="idNumber"
                {...register('idNumber')}
                placeholder="9 hoặc 12 số"
                className={errors.idNumber ? 'border-red-500' : ''}
              />
              {errors.idNumber && <p className="text-sm text-red-500">{errors.idNumber.message}</p>}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium">
                Số điện thoại *
              </Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="Ví dụ: 0901234567"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-sm text-red-500">{errors.phone.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="birthDate" className="text-sm font-medium">
                Ngày sinh *
              </Label>
              <Input
                id="birthDate"
                type="date"
                {...register('birthDate')}
                className={errors.birthDate ? 'border-red-500' : ''}
              />
              {errors.birthDate && (
                <p className="text-sm text-red-500">{errors.birthDate.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address" className="text-sm font-medium">
              Địa chỉ thường trú *
            </Label>
            <Input
              id="address"
              {...register('address')}
              placeholder="Nhập địa chỉ thường trú đầy đủ"
              className={errors.address ? 'border-red-500' : ''}
            />
            {errors.address && <p className="text-sm text-red-500">{errors.address.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="Email liên hệ (không bắt buộc)"
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
          </div>

          {/* ID Verification Upload */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Ảnh CMND/CCCD *</Label>

            <div className="space-y-2">
              <Input
                type="file"
                accept="image/*,.pdf"
                multiple
                ref={fileInputRef}
                onChange={e => handleFileUpload(e.target.files)}
                disabled={isUploading}
              />
              {isUploading && (
                <p className="text-sm text-muted-foreground">Đang tải lên tài liệu...</p>
              )}
              {uploadedFiles.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    Đã tải lên {uploadedFiles.length} tài liệu
                  </p>
                </div>
              )}
            </div>
            {errors.idVerification && (
              <p className="text-sm text-red-500">{errors.idVerification.message}</p>
            )}
          </div>

          {/* Lease dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="leaseStartDate" className="text-sm font-medium">
                Ngày bắt đầu thuê *
              </Label>
              <Input
                id="leaseStartDate"
                type="date"
                {...register('leaseStartDate')}
                className={errors.leaseStartDate ? 'border-red-500' : ''}
              />
              {errors.leaseStartDate && (
                <p className="text-sm text-red-500">{errors.leaseStartDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="leaseEndDate" className="text-sm font-medium">
                Ngày kết thúc thuê *
              </Label>
              <Input
                id="leaseEndDate"
                type="date"
                {...register('leaseEndDate')}
                className={errors.leaseEndDate ? 'border-red-500' : ''}
              />
              {errors.leaseEndDate && (
                <p className="text-sm text-red-500">{errors.leaseEndDate.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-between">
            <Button type="button" onClick={onBack} variant="outline" className="px-8 py-2">
              Quay lại
            </Button>
            <Button
              type="submit"
              disabled={!isValid}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              Tiếp theo
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default Step2;
