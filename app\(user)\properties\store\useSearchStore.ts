import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  TransactionType,
  PropertyType,
  Amenity as AmenityType,
  PropertyDetailFilters,
} from '@/lib/api/services/fetchProperty';

// --- Interfaces & Types ---

export interface Province {
  code: number;
  name: string;
}
export interface District {
  code: number;
  name: string;
}
export interface Ward {
  code: number;
  name: string;
}

interface LocationState {
  provinces: Province[];
  districts: District[];
  wards: Ward[];
  selectedProvince?: string;
  selectedDistrict?: string;
  selectedWard?: string;
  loading: boolean;
}

interface PropertyFilterState {
  selectedPropertyTypes: PropertyType[];
  selectedTransactionType?: TransactionType | 'both';
  selectedBedCount: string;
  isExactBedMatch: boolean;
  selectedBathCount: string;
  selectedLivingRoomCount: string;
  selectedKitchenCount: string;
  selectedAmenities: (keyof AmenityType)[];
  selectedDetails: PropertyDetailFilters[];
}

interface RangeFilterState {
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  priceSliderValue: [number, number];
  areaSliderValue: [number, number];
}

interface SearchState {
  searchTerm: string;
  mapBounds?: google.maps.LatLngBoundsLiteral;
  isDropdownOpen: {
    location: boolean;
    propertyType: boolean;
    transactionType: boolean;
    price: boolean;
    area: boolean;
    rooms: boolean;
    moreFilters: boolean;
  };
  isSearching: boolean;
}

interface SearchStoreState {
  location: LocationState;
  property: PropertyFilterState;
  range: RangeFilterState;
  search: SearchState;
}

// --- Constants ---

export const CENTRAL_CITIES = [
  {
    code: '1',
    name: 'Hà Nội',
    image:
      'https://cdn-media.sforum.vn/storage/app/media/wp-content/uploads/2024/01/dia-diem-du-lich-o-ha-noi-thumb.jpg',
    icon: '🏛️',
  },
  {
    code: '79',
    name: 'TP. Hồ Chí Minh',
    image:
      'https://media.vneconomy.vn/images/upload/2024/05/20/tphcm-16726501373541473396704-16994302498261147920222.jpg',
    icon: '🌆',
  },
  {
    code: '48',
    name: 'Đà Nẵng',
    image:
      'https://vcdn1-dulich.vnecdn.net/2022/06/03/cauvang-1654247842-9403-1654247849.jpg?w=1200&h=0&q=100&dpr=1&fit=crop&s=Swd6JjpStebEzT6WARcoOA',
    icon: '🏖️',
  },
  {
    code: '92',
    name: 'Cần Thơ',
    image: 'https://ik.imagekit.io/tvlk/blog/2021/11/dia-diem-du-lich-can-tho-cover.jpg',
    icon: '🌾',
  },
  {
    code: '31',
    name: 'Hải Phòng',
    image: 'https://heza.gov.vn/wp-content/uploads/2023/09/hai_phong-scaled.jpg',
    icon: '⚓',
  },
];

const MAX_PRICE_SALE = 10_000_000_000;
const MAX_PRICE_RENT = 100_000_000;
const MAX_AREA = 1000;

// --- Store Definition ---

interface SearchStore extends SearchStoreState {
  actions: {
    // Initializer
    initializeFromUrl: (params: URLSearchParams) => Promise<void>;

    // UI Actions
    setDropdownOpen: (dropdown: keyof SearchState['isDropdownOpen'], isOpen: boolean) => void;

    // Search Actions
    setSearchTerm: (term: string) => void;
    setMapBounds: (bounds: google.maps.LatLngBoundsLiteral) => void;
    setIsSearching: (isSearching: boolean) => void;

    // Location Actions
    fetchProvinces: () => Promise<void>;
    selectProvince: (provinceCode: string) => void;
    selectDistrict: (districtCode: string) => void;
    selectWard: (wardCode: string) => void;

    // Property Filter Actions
    togglePropertyType: (type: PropertyType) => void;
    selectAllPropertyTypes: () => void;
    setTransactionType: (type: TransactionType | 'both') => void;
    setBedCount: (count: string) => void;
    setBathCount: (count: string) => void;
    setLivingRoomCount: (count: string) => void;
    setKitchenCount: (count: string) => void;
    toggleExactBedMatch: () => void;
    toggleAmenity: (amenity: keyof AmenityType) => void;
    toggleDetail: (detail: PropertyDetailFilters) => void;

    // Range Filter Actions
    setPriceSliderValue: (value: [number, number]) => void;
    setAreaSliderValue: (value: [number, number]) => void;
    setMinPrice: (price: string) => void;
    setMaxPrice: (price: string) => void;
    setMinArea: (area: string) => void;
    setMaxArea: (area: string) => void;
    resetPrice: () => void;
    resetArea: () => void;

    // Formatters & Derived State
    formatPriceLabel: (value: number, isMax?: boolean) => string;
    formatSizeLabel: (value: number, isMax?: boolean) => string;
    formatPriceDisplay: (min: number, max: number) => string;
    formatSizeDisplay: (min: number, max: number) => string;
    getCurrentPriceRanges: () => {
      maxValue: number;
      step: number;
    };

    // Main Actions
    buildFilterQuery: () => string;
    clearFilters: () => void;
  };
}

const initialState: SearchStoreState = {
  location: {
    provinces: [],
    districts: [],
    wards: [],
    selectedProvince: undefined,
    selectedDistrict: undefined,
    selectedWard: undefined,
    loading: false,
  },
  property: {
    selectedPropertyTypes: [],
    selectedTransactionType: 'both',
    selectedBedCount: 'any',
    isExactBedMatch: false,
    selectedBathCount: 'any',
    selectedLivingRoomCount: 'any',
    selectedKitchenCount: 'any',
    selectedAmenities: [],
    selectedDetails: [],
  },
  range: {
    minPrice: undefined,
    maxPrice: undefined,
    minArea: undefined,
    maxArea: undefined,
    priceSliderValue: [0, MAX_PRICE_SALE],
    areaSliderValue: [0, MAX_AREA],
  },
  search: {
    searchTerm: '',
    mapBounds: undefined,
    isDropdownOpen: {
      location: false,
      propertyType: false,
      transactionType: false,
      price: false,
      area: false,
      rooms: false,
      moreFilters: false,
    },
    isSearching: false,
  },
};

export const useSearchStore = create<SearchStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      actions: {
        initializeFromUrl: async params => {
          const { actions } = get();
          if (get().location.provinces.length === 0) {
            await actions.fetchProvinces();
          }

          const stateToSet = {
            search: { ...initialState.search },
            property: { ...initialState.property },
            range: { ...initialState.range },
          };

          stateToSet.search.searchTerm = params.get('searchTerm') || '';

          const transactionType = params.get('transactionType');
          if (
            transactionType &&
            Object.values(TransactionType).includes(transactionType as TransactionType)
          ) {
            stateToSet.property.selectedTransactionType = transactionType as TransactionType;
          } else {
            stateToSet.property.selectedTransactionType = 'both';
          }

          const isRent = stateToSet.property.selectedTransactionType === TransactionType.FOR_RENT;
          const maxPriceFromParams = isRent ? MAX_PRICE_RENT : MAX_PRICE_SALE;

          const minPrice = Number(params.get('minPrice')) || 0;
          const maxPrice = Number(params.get('maxPrice')) || maxPriceFromParams;
          stateToSet.range.priceSliderValue = [minPrice, maxPrice];
          stateToSet.range.minPrice = minPrice;
          stateToSet.range.maxPrice = maxPrice;

          const minArea = Number(params.get('minArea')) || 0;
          const maxArea = Number(params.get('maxArea')) || MAX_AREA;
          stateToSet.range.areaSliderValue = [minArea, maxArea];
          stateToSet.range.minArea = minArea;
          stateToSet.range.maxArea = maxArea;

          const propertyTypes = params.get('type') || params.get('propertyType');
          stateToSet.property.selectedPropertyTypes = propertyTypes
            ? (propertyTypes.split(',') as PropertyType[])
            : [];

          stateToSet.property.selectedBedCount = params.get('bedCount') || 'any';
          stateToSet.property.isExactBedMatch = params.get('exactBedMatch') === 'true';
          stateToSet.property.selectedBathCount = params.get('bathCount') || 'any';
          stateToSet.property.selectedLivingRoomCount = params.get('livingRoomCount') || 'any';
          stateToSet.property.selectedKitchenCount = params.get('kitchenCount') || 'any';

          const amenities = params.get('amenityFilters');
          stateToSet.property.selectedAmenities = amenities
            ? (amenities.split(',') as (keyof AmenityType)[])
            : [];

          const details = params.get('propertyDetailFilters');
          stateToSet.property.selectedDetails = details
            ? (details.split(',') as PropertyDetailFilters[])
            : [];

          set(stateToSet);

          const cityName = params.get('city');
          if (cityName) {
            const province = get().location.provinces.find(p => p.name === cityName);
            if (province) {
              await actions.selectProvince(province.code.toString());

              const districtName = params.get('district');
              if (districtName) {
                const district = get().location.districts.find(d => d.name === districtName);
                if (district) {
                  await actions.selectDistrict(district.code.toString());

                  const wardName = params.get('ward');
                  if (wardName) {
                    const ward = get().location.wards.find(w => w.name === wardName);
                    if (ward) {
                      actions.selectWard(ward.code.toString());
                    }
                  }
                }
              }
            }
          }
        },
        setDropdownOpen: (dropdown, isOpen) => {
          set(state => ({
            search: {
              ...state.search,
              isDropdownOpen: { ...state.search.isDropdownOpen, [dropdown]: isOpen },
            },
          }));
        },
        setSearchTerm: term => {
          set(state => ({ search: { ...state.search, searchTerm: term } }));
        },
        setMapBounds: bounds => {
          set(state => ({ search: { ...state.search, mapBounds: bounds } }));
        },
        setIsSearching: isSearching => {
          set(state => ({ search: { ...state.search, isSearching } }));
        },
        fetchProvinces: async () => {
          set(state => ({ location: { ...state.location, loading: true } }));
          try {
            const response = await fetch('https://provinces.open-api.vn/api/v2/p/');
            if (!response.ok) throw new Error('Failed to fetch provinces');
            const data = await response.json();
            set(state => ({ location: { ...state.location, provinces: data, loading: false } }));
          } catch (error) {
            console.error('Error fetching provinces:', error);
            set(state => ({ location: { ...state.location, loading: false } }));
          }
        },
        selectProvince: async provinceCode => {
          if (!provinceCode) {
            set(state => ({
              location: {
                ...state.location,
                selectedProvince: '',
                selectedDistrict: '',
                selectedWard: '',
                districts: [],
                wards: [],
              },
            }));
            return;
          }
          set(state => ({
            location: {
              ...state.location,
              selectedProvince: provinceCode,
              selectedDistrict: undefined,
              selectedWard: undefined,
              districts: [],
              wards: [],
              loading: true,
            },
          }));
          try {
            const response = await fetch(
              `https://provinces.open-api.vn/api/v2/p/${provinceCode}?depth=2`
            );
            if (!response.ok) throw new Error('Failed to fetch districts');
            const data = await response.json();
            set(state => ({
              location: { ...state.location, districts: data.districts || [], loading: false },
            }));
          } catch (error) {
            console.error('Error fetching districts:', error);
            set(state => ({ location: { ...state.location, loading: false } }));
          }
        },
        selectDistrict: async districtCode => {
          if (!districtCode) {
            set(state => ({
              location: { ...state.location, selectedDistrict: '', selectedWard: '', wards: [] },
            }));
            return;
          }
          set(state => ({
            location: {
              ...state.location,
              selectedDistrict: districtCode,
              selectedWard: undefined,
              wards: [],
              loading: true,
            },
          }));
          try {
            const response = await fetch(
              `https://provinces.open-api.vn/api/v2/d/${districtCode}?depth=2`
            );
            if (!response.ok) throw new Error('Failed to fetch wards');
            const data = await response.json();
            set(state => ({
              location: { ...state.location, wards: data.wards || [], loading: false },
            }));
          } catch (error) {
            console.error('Error fetching wards:', error);
            set(state => ({ location: { ...state.location, loading: false } }));
          }
        },
        selectWard: wardCode => {
          set(state => ({ location: { ...state.location, selectedWard: wardCode } }));
        },
        togglePropertyType: type => {
          set(state => {
            const currentTypes = state.property.selectedPropertyTypes;
            const newTypes = currentTypes.includes(type)
              ? currentTypes.filter(t => t !== type)
              : [...currentTypes, type];
            return { property: { ...state.property, selectedPropertyTypes: newTypes } };
          });
        },
        selectAllPropertyTypes: () => {
          set(state => {
            const allTypes = Object.values(PropertyType).filter(
              v => typeof v === 'string'
            ) as PropertyType[];
            const currentTypes = state.property.selectedPropertyTypes;
            const newTypes = currentTypes.length === allTypes.length ? [] : allTypes;
            return { property: { ...state.property, selectedPropertyTypes: newTypes } };
          });
        },
        setTransactionType: type => {
          set(state => {
            const isRent = type === TransactionType.FOR_RENT;
            const maxPrice = isRent ? MAX_PRICE_RENT : MAX_PRICE_SALE;
            return {
              property: { ...state.property, selectedTransactionType: type },
              range: { ...state.range, priceSliderValue: [0, maxPrice], minPrice: 0, maxPrice },
            };
          });
        },
        setBedCount: count => {
          set(state => ({ property: { ...state.property, selectedBedCount: count } }));
        },
        setBathCount: count => {
          set(state => ({ property: { ...state.property, selectedBathCount: count } }));
        },
        setLivingRoomCount: count => {
          set(state => ({ property: { ...state.property, selectedLivingRoomCount: count } }));
        },
        setKitchenCount: count => {
          set(state => ({ property: { ...state.property, selectedKitchenCount: count } }));
        },
        toggleExactBedMatch: () => {
          set(state => ({
            property: { ...state.property, isExactBedMatch: !state.property.isExactBedMatch },
          }));
        },
        toggleAmenity: amenity => {
          set(state => {
            const current = state.property.selectedAmenities;
            const newAmenities = current.includes(amenity)
              ? current.filter(a => a !== amenity)
              : [...current, amenity];
            return { property: { ...state.property, selectedAmenities: newAmenities } };
          });
        },
        toggleDetail: detail => {
          set(state => {
            const current = state.property.selectedDetails;
            const newDetails = current.includes(detail)
              ? current.filter(d => d !== detail)
              : [...current, detail];
            return { property: { ...state.property, selectedDetails: newDetails } };
          });
        },
        setPriceSliderValue: value => {
          set(state => ({
            range: {
              ...state.range,
              priceSliderValue: value,
              minPrice: value[0],
              maxPrice: value[1],
            },
          }));
        },
        setAreaSliderValue: value => {
          set(state => ({
            range: { ...state.range, areaSliderValue: value, minArea: value[0], maxArea: value[1] },
          }));
        },
        setMinPrice: price => {
          const value = Number(price);
          set(state => ({
            range: {
              ...state.range,
              minPrice: value,
              priceSliderValue: [value, state.range.priceSliderValue[1]],
            },
          }));
        },
        setMaxPrice: price => {
          const value = Number(price);
          set(state => ({
            range: {
              ...state.range,
              maxPrice: value,
              priceSliderValue: [state.range.priceSliderValue[0], value],
            },
          }));
        },
        setMinArea: area => {
          const value = Number(area);
          set(state => ({
            range: {
              ...state.range,
              minArea: value,
              areaSliderValue: [value, state.range.areaSliderValue[1]],
            },
          }));
        },
        setMaxArea: area => {
          const value = Number(area);
          set(state => ({
            range: {
              ...state.range,
              maxArea: value,
              areaSliderValue: [state.range.areaSliderValue[0], value],
            },
          }));
        },
        resetPrice: () => {
          const { property } = get();
          const maxPrice =
            property.selectedTransactionType === TransactionType.FOR_RENT
              ? MAX_PRICE_RENT
              : MAX_PRICE_SALE;
          set(state => ({
            range: { ...state.range, minPrice: 0, maxPrice, priceSliderValue: [0, maxPrice] },
          }));
        },
        resetArea: () => {
          set(state => ({
            range: {
              ...state.range,
              minArea: 0,
              maxArea: MAX_AREA,
              areaSliderValue: [0, MAX_AREA],
            },
          }));
        },
        formatPriceLabel: (value, isMax = false) => {
          const { property } = get();
          const isRent = property.selectedTransactionType === TransactionType.FOR_RENT;
          if (isRent) {
            if (value >= 1000000) return `${value / 1000000}tr${isMax ? '+' : ''}`;
            return `${value / 1000}k${isMax ? '+' : ''}`;
          }
          if (value >= 1000000000) return `${value / 1000000000} tỷ${isMax ? '+' : ''}`;
          return `${value / 1000000}tr${isMax ? '+' : ''}`;
        },
        formatSizeLabel: (value, isMax = false) => {
          return `${value}${isMax ? '+' : ''} m²`;
        },
        formatPriceDisplay: (min, max) => {
          const { actions, property } = get();
          const isRent = property.selectedTransactionType === TransactionType.FOR_RENT;
          const maxValue = isRent ? MAX_PRICE_RENT : MAX_PRICE_SALE;
          if (min === 0 && max === maxValue) return 'Mức giá';
          return `${actions.formatPriceLabel(min)} - ${actions.formatPriceLabel(max, max === maxValue)}`;
        },
        formatSizeDisplay: (min, max) => {
          const { actions } = get();
          if (min === 0 && max === MAX_AREA) return 'Diện tích';
          return `${actions.formatSizeLabel(min)} - ${actions.formatSizeLabel(max, max === MAX_AREA)}`;
        },
        getCurrentPriceRanges: () => {
          const { property } = get();
          const isRent = property.selectedTransactionType === TransactionType.FOR_RENT;
          return {
            maxValue: isRent ? MAX_PRICE_RENT : MAX_PRICE_SALE,
            step: isRent ? 100000 : 1000000,
          };
        },
        buildFilterQuery: () => {
          const { location, property, range, search } = get();
          const queryParams = new URLSearchParams();

          if (search.searchTerm.trim()) {
            queryParams.set('searchTerm', search.searchTerm.trim());
          }

          const province = location.provinces.find(
            p => p.code.toString() === location.selectedProvince
          );
          if (province) {
            queryParams.set('city', province.name);
            const district = location.districts.find(
              d => d.code.toString() === location.selectedDistrict
            );
            if (district) {
              queryParams.set('district', district.name);
              const ward = location.wards.find(w => w.code.toString() === location.selectedWard);
              if (ward) {
                queryParams.set('ward', ward.name);
              }
            }
          }

          if (property.selectedPropertyTypes.length > 0) {
            queryParams.set('type', property.selectedPropertyTypes.join(','));
          }

          if (property.selectedTransactionType && property.selectedTransactionType !== 'both') {
            queryParams.set('transactionType', property.selectedTransactionType);
          }

          const isRent = property.selectedTransactionType === TransactionType.FOR_RENT;
          const maxPriceDefault = isRent ? MAX_PRICE_RENT : MAX_PRICE_SALE;
          const [minPrice, maxPrice] = range.priceSliderValue;
          if (minPrice > 0 || maxPrice < maxPriceDefault) {
            queryParams.set('minPrice', minPrice.toString());
            queryParams.set('maxPrice', maxPrice.toString());
          }

          const [minArea, maxArea] = range.areaSliderValue;
          if (minArea > 0 || maxArea < MAX_AREA) {
            queryParams.set('minArea', minArea.toString());
            queryParams.set('maxArea', maxArea.toString());
          }

          if (property.selectedBedCount !== 'any') {
            queryParams.set('bedCount', property.selectedBedCount);
            if (property.isExactBedMatch) {
              queryParams.set('exactBedMatch', 'true');
            }
          }
          if (property.selectedBathCount !== 'any') {
            queryParams.set('bathCount', property.selectedBathCount);
          }
          if (property.selectedLivingRoomCount !== 'any') {
            queryParams.set('livingRoomCount', property.selectedLivingRoomCount);
          }
          if (property.selectedKitchenCount !== 'any') {
            queryParams.set('kitchenCount', property.selectedKitchenCount);
          }

          if (property.selectedAmenities.length > 0) {
            queryParams.set('amenityFilters', property.selectedAmenities.join(','));
          }
          if (property.selectedDetails.length > 0) {
            queryParams.set('propertyDetailFilters', property.selectedDetails.join(','));
          }

          if (search.mapBounds) {
            queryParams.set('swLatitude', search.mapBounds.south.toString());
            queryParams.set('neLatitude', search.mapBounds.north.toString());
            queryParams.set('swLongitude', search.mapBounds.west.toString());
            queryParams.set('neLongitude', search.mapBounds.east.toString());
          }

          return queryParams.toString();
        },
        clearFilters: () => {
          set(state => ({
            ...initialState,
            location: {
              ...initialState.location,
              provinces: state.location.provinces,
            },
          }));
          // Giữ lại state đã fetch của provinces
        },
      },
    }),
    {
      name: 'search-store',
    }
  )
);
