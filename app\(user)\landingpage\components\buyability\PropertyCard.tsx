import Image from 'next/image';

interface PropertyCardProps {
  image: string;
  address?: string;
  phoneNumber?: string;
  saleName?: string;
}

export default function PropertyCard({ image, address, phoneNumber, saleName }: PropertyCardProps) {
  return (
    <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden w-full max-w-xs">
      <div className="absolute top-4 left-4 z-10">
        <span className="bg-orange-600 text-white text-xs font-semibold px-3 py-1 rounded-full shadow">
          <PERSON><PERSON>
        </span>
      </div>
      <Image
        width={300}
        height={160}
        src={image}
        alt="Property"
        className="w-full h-40 object-cover rounded-t-2xl"
      />
      <div className="p-4 pt-2">
        <div className="flex flex-col space-y-1 p-2">
          <span className="font-normal text-sm">{address}</span>
          <span className="font-bold text-sm"><PERSON><PERSON><PERSON><PERSON> thoại: {phoneNumber}</span>
          <span className="font-bold text-sm"><PERSON><PERSON><PERSON><PERSON>: {saleName}</span>
        </div>
      </div>
    </div>
  );
}
