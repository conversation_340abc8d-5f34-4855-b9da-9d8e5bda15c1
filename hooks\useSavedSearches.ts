import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import savedSearchesService, {
  SaveSearchParams,
  SavedSearchesPagination,
  SavedSearchesResponse,
  SaveSearchResponse,
  QueryStringResponse,
  DeleteSearchResponse,
  SearchCreateResponse,
} from '@/lib/api/services/fetchSavedSearches';

/**
 * Hook to fetch saved searches with pagination
 */
export function useSavedSearches(pagination?: SavedSearchesPagination) {
  const { isLoading, isError, data, error, refetch, isFetching } = useQuery({
    queryKey: ['saved-searches', pagination ? JSON.stringify(pagination) : 'all'],
    queryFn: () => savedSearchesService.getSavedSearches(pagination),
    select: (response: SavedSearchesResponse) => ({
      searches: response.data?.searchCreateResponses || [],
      totalCount: response.data?.count || 0,
      pageSize: response.data?.limit || 10,
      currentPage: response.data?.page || 1,
      totalPages: response.data?.totalPages || 1,
      status: response.status,
      message: response.message,
      code: response.code,
    }),
  });

  return {
    isLoading,
    isError,
    data,
    error,
    refetch,
    isFetching,
    searches: data?.searches || [],
    totalCount: data?.totalCount || 0,
    currentPage: data?.currentPage || 1,
    pageSize: data?.pageSize || 10,
    totalPages: data?.totalPages || 1,
    status: data?.status,
    message: data?.message,
  };
}

/**
 * Hook to save a search filter
 */
export function useSaveSearch() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: SaveSearchParams) => savedSearchesService.saveSearch(params),
    onSuccess: (response: SaveSearchResponse) => {
      if (response.status) {
        toast.success(response.message || 'Lưu bộ lọc tìm kiếm thành công');
        // Invalidate saved searches queries to refresh the list
        queryClient.invalidateQueries({ queryKey: ['saved-searches'] });
      } else {
        toast.error(response.message || 'Có lỗi xảy ra khi lưu bộ lọc');
      }
    },
    onError: (error: any) => {
      console.error('Error saving search:', error);
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi lưu bộ lọc tìm kiếm';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to get query string for a saved search
 */
export function useGetQueryString() {
  return useMutation({
    mutationFn: (searchId: string) => savedSearchesService.getQueryString(searchId),
    onError: (error: any) => {
      console.error('Error getting query string:', error);
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi lấy thông tin tìm kiếm';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to delete a saved search
 */
export function useDeleteSearch() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (searchId: string) => savedSearchesService.deleteSearch(searchId),
    onSuccess: (response: DeleteSearchResponse, searchId: string) => {
      if (response.status) {
        toast.success(response.message || 'Xóa bộ lọc tìm kiếm thành công');
        // Invalidate saved searches queries to refresh the list
        queryClient.invalidateQueries({ queryKey: ['saved-searches'] });

        // Optionally, remove the item from cache immediately for better UX
        queryClient.setQueryData(['saved-searches'], (oldData: any) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            data: {
              ...oldData.data,
              searchCreateResponses: oldData.data.searchCreateResponses.filter(
                (search: SearchCreateResponse) => search.id !== searchId
              ),
              count: oldData.data.count - 1,
            },
          };
        });
      } else {
        toast.error(response.message || 'Có lỗi xảy ra khi xóa bộ lọc');
      }
    },
    onError: (error: any) => {
      console.error('Error deleting search:', error);
      const errorMessage =
        error?.response?.data?.message || 'Có lỗi xảy ra khi xóa bộ lọc tìm kiếm';
      toast.error(errorMessage);
    },
  });
}

/**
 * Hook to handle search navigation - gets query string and navigates to properties page
 */
export function useNavigateToSearch() {
  const getQueryStringMutation = useGetQueryString();

  const navigateToSearch = async (searchId: string, router: any) => {
    try {
      const response = await getQueryStringMutation.mutateAsync(searchId);
      if (response.status && response.data) {
        // Navigate to properties page with the query string
        router.push(`/properties?${response.data}`);
      } else {
        toast.error('Không thể tải thông tin tìm kiếm');
      }
    } catch (error) {
      console.error('Error navigating to search:', error);
      toast.error('Có lỗi xảy ra khi tải tìm kiếm');
    }
  };

  return {
    navigateToSearch,
    isLoading: getQueryStringMutation.isPending,
  };
}
