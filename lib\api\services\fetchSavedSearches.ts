import apiService, { RequestParams } from '../core';

// Types based on API documentation
export interface UserResponse {
  id: string;
  fullName: string;
  email: string;
  avatar: string | null;
  phoneNumber: string;
  gender: string;
}

export interface SearchCreateResponse {
  id: string;
  ownerSaving: UserResponse;
  searchTerm: string | null;
  isDescending: boolean;
  swLatitude: number | null;
  neLatitude: number | null;
  swLongitude: number | null;
  neLongitude: number | null;
  status: string[];
  transactionType: string[];
  type: string[];
  propertyDetailFilters: string[];
  amenityFilters: string[];
  bedrooms: number | null;
  minBedrooms: number | null;
  maxBedrooms: number | null;
  bathrooms: number | null;
  minBathrooms: number | null;
  maxBathrooms: number | null;
  livingRooms: number | null;
  minLivingRooms: number | null;
  maxLivingRooms: number | null;
  kitchens: number | null;
  minKitchens: number | null;
  maxKitchens: number | null;
  landArea: number | null;
  landWidth: number | null;
  landLength: number | null;
  buildingArea: number | null;
  numberOfFloors: number | null;
  floorNumber: number | null;
  minPrice: number | null;
  maxPrice: number | null;
  apartmentOrientation: string[];
  sortBy: string;
}

export interface SearchResponseWithPaging {
  searchCreateResponses: SearchCreateResponse[];
  page: number;
  limit: number;
  count: number;
  totalPages: number;
}

export interface SavedSearchesResponse {
  code: number;
  status: boolean;
  message: string;
  data: SearchResponseWithPaging;
}

export interface SaveSearchResponse {
  code: number;
  status: boolean;
  message: string;
  data: null;
}

export interface QueryStringResponse {
  code: number;
  status: boolean;
  message: string;
  data: string;
}

export interface DeleteSearchResponse {
  code: number;
  status: boolean;
  message: string;
  data: null;
}

// Search parameters for saving search
export interface SaveSearchParams {
  searchTerm?: string;
  isDescending?: boolean;
  sortBy?: string;
  swLatitude?: number;
  swLongitude?: number;
  neLatitude?: number;
  neLongitude?: number;
  status?: string[];
  transactionType?: string[];
  type?: string[];
  propertyDetailFilters?: string[];
  amenityFilters?: string[];
  bedrooms?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  bathrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  livingRooms?: number;
  minLivingRooms?: number;
  maxLivingRooms?: number;
  kitchens?: number;
  minKitchens?: number;
  maxKitchens?: number;
  landArea?: number;
  landWidth?: number;
  landLength?: number;
  buildingArea?: number;
  numberOfFloors?: number;
  floorNumber?: number;
  minPrice?: number;
  maxPrice?: number;
  apartmentOrientation?: string[];
}

// Pagination parameters
export interface SavedSearchesPagination {
  pageNumber?: number;
  pageSize?: number;
}

// Convert search params to query string parameters
const convertSaveSearchParams = (params?: SaveSearchParams): RequestParams => {
  if (!params) return {};

  const queryParams: RequestParams = {};

  // Basic parameters
  if (params.searchTerm) queryParams.searchTerm = params.searchTerm;
  if (params.isDescending !== undefined) queryParams.isDescending = params.isDescending;
  if (params.sortBy) queryParams.sortBy = params.sortBy;

  // Location parameters
  if (params.swLatitude !== undefined) queryParams.swLatitude = params.swLatitude;
  if (params.swLongitude !== undefined) queryParams.swLongitude = params.swLongitude;
  if (params.neLatitude !== undefined) queryParams.neLatitude = params.neLatitude;
  if (params.neLongitude !== undefined) queryParams.neLongitude = params.neLongitude;

  // Array parameters
  if (params.status?.length) queryParams.status = params.status;
  if (params.transactionType?.length) queryParams.transactionType = params.transactionType;
  if (params.type?.length) queryParams.type = params.type;
  if (params.propertyDetailFilters?.length)
    queryParams.propertyDetailFilters = params.propertyDetailFilters;
  if (params.amenityFilters?.length) queryParams.amenityFilters = params.amenityFilters;
  if (params.apartmentOrientation?.length)
    queryParams.apartmentOrientation = params.apartmentOrientation;

  // Room parameters
  if (params.bedrooms !== undefined) queryParams.bedrooms = params.bedrooms;
  if (params.minBedrooms !== undefined) queryParams.minBedrooms = params.minBedrooms;
  if (params.maxBedrooms !== undefined) queryParams.maxBedrooms = params.maxBedrooms;
  if (params.bathrooms !== undefined) queryParams.bathrooms = params.bathrooms;
  if (params.minBathrooms !== undefined) queryParams.minBathrooms = params.minBathrooms;
  if (params.maxBathrooms !== undefined) queryParams.maxBathrooms = params.maxBathrooms;
  if (params.livingRooms !== undefined) queryParams.livingRooms = params.livingRooms;
  if (params.minLivingRooms !== undefined) queryParams.minLivingRooms = params.minLivingRooms;
  if (params.maxLivingRooms !== undefined) queryParams.maxLivingRooms = params.maxLivingRooms;
  if (params.kitchens !== undefined) queryParams.kitchens = params.kitchens;
  if (params.minKitchens !== undefined) queryParams.minKitchens = params.minKitchens;
  if (params.maxKitchens !== undefined) queryParams.maxKitchens = params.maxKitchens;

  // Area parameters
  if (params.landArea !== undefined) queryParams.landArea = params.landArea;
  if (params.landWidth !== undefined) queryParams.landWidth = params.landWidth;
  if (params.landLength !== undefined) queryParams.landLength = params.landLength;
  if (params.buildingArea !== undefined) queryParams.buildingArea = params.buildingArea;

  // Floor parameters
  if (params.numberOfFloors !== undefined) queryParams.numberOfFloors = params.numberOfFloors;
  if (params.floorNumber !== undefined) queryParams.floorNumber = params.floorNumber;

  // Price parameters
  if (params.minPrice !== undefined) queryParams.minPrice = params.minPrice;
  if (params.maxPrice !== undefined) queryParams.maxPrice = params.maxPrice;

  return queryParams;
};

// Saved searches service
export const savedSearchesService = {
  // Save search filter
  saveSearch: async (params: SaveSearchParams): Promise<SaveSearchResponse> => {
    const queryParams = convertSaveSearchParams(params);
    // Create URL with query string for POST request
    const queryString = new URLSearchParams();
    Object.entries(queryParams).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        value.forEach(v => queryString.append(key, String(v)));
      } else if (value !== undefined && value !== null) {
        queryString.append(key, String(value));
      }
    });

    const url = `/api/searches${queryString.toString() ? `?${queryString.toString()}` : ''}`;
    const response = await apiService.post<SaveSearchResponse>(url);
    return response.data;
  },

  // Get saved searches with pagination
  getSavedSearches: async (
    pagination?: SavedSearchesPagination
  ): Promise<SavedSearchesResponse> => {
    const params: RequestParams = {};
    if (pagination?.pageNumber) params.pageNumber = pagination.pageNumber;
    if (pagination?.pageSize) params.pageSize = pagination.pageSize;

    const response = await apiService.get<SavedSearchesResponse>('/api/searches/saved', params);
    return response.data;
  },

  // Get query string for saved search
  getQueryString: async (searchId: string): Promise<QueryStringResponse> => {
    const response = await apiService.get<QueryStringResponse>(
      `/api/searches/${searchId}/query-string`
    );
    return response.data;
  },

  // Delete saved search
  deleteSearch: async (searchId: string): Promise<DeleteSearchResponse> => {
    const response = await apiService.delete<DeleteSearchResponse>(
      `/api/searches/${searchId}/delete-saved-search`
    );
    return response.data;
  },
};

export default savedSearchesService;
