'use client';

import React from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { useRecentlyViewed } from '@/hooks/useRecentlyViewed';
import { Skeleton } from '@/components/ui/skeleton';

export default function RecentlyViewedSection() {
  const { recentlyViewedWithTime, isLoading, hasRecentlyViewed, error } = useRecentlyViewed();

  // Không hiển thị section nếu không có dữ liệu hoặc có lỗi
  if (!isLoading && !hasRecentlyViewed && !error) {
    return null;
  }

  // Hiển thị error state nếu có lỗi
  if (error && !isLoading && !hasRecentlyViewed) {
    return (
      <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36">
        <div className="text-center py-8">
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
      <div className="text-left mb-8">
        <h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-4">Đã xem gần đây</h2>
        <p className="text-sm md:text-base text-gray-600 max-w-4xl">
          Xem lại những bất động sản bạn đã quan tâm để không bỏ lỡ cơ hội tốt.
        </p>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="h-48 w-full rounded-2xl" />
              <div className="space-y-2 px-1">
                <Skeleton className="h-6 w-3/4" />
                <div className="flex gap-4">
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                  <Skeleton className="h-4 w-12" />
                </div>
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Properties grid */}
      {!isLoading && hasRecentlyViewed && (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          {recentlyViewedWithTime.map(({ property, viewedAt, id }) => (
            <PropertyCard
              key={id}
              property={property}
              priority={false}
              size="md"
              viewedTime={viewedAt}
            />
          ))}
        </div>
      )}
    </div>
  );
}
