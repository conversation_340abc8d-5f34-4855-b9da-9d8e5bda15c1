'use client';

import { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PropertyCard } from '@/components/PropertyCard';
import { Home, Loader2 } from 'lucide-react';
import { TransactionType } from '@/lib/api/services/fetchProperty';
import { usePropertiesBySellerUpload } from '@/hooks/useProperty';

interface SellerProfileListingsProps {
  sellerId: string;
}

// Mock data removed - now using real API data

function SellerProfileListings({ sellerId }: SellerProfileListingsProps) {
  const [activeTab, setActiveTab] = useState('all');
  const [showAll, setShowAll] = useState(false);
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });

  // Fetch properties uploaded by the seller
  const {
    properties: fetchedProperties,
    isLoading,
    isError,
    error,
  } = usePropertiesBySellerUpload(sellerId);

  // Reset showAll when switching tabs
  useEffect(() => {
    setShowAll(false);
  }, [activeTab]);

  const forSaleProperties = fetchedProperties.filter(
    p => p.transactionType === TransactionType.FOR_SALE
  );
  const forRentProperties = fetchedProperties.filter(
    p => p.transactionType === TransactionType.FOR_RENT
  );

  // Tab data for easier management
  const tabs = [
    { name: `Tất cả (${fetchedProperties.length})`, value: 'all' },
    { name: `Bán (${forSaleProperties.length})`, value: 'sale' },
    { name: `Thuê (${forRentProperties.length})`, value: 'rent' },
  ];

  // Update underline position when active tab changes
  useLayoutEffect(() => {
    const activeIndex = tabs.findIndex(tab => tab.value === activeTab);
    const activeTabElement = tabRefs.current[activeIndex];

    if (activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement;

      setUnderlineStyle({
        left: offsetLeft,
        width: offsetWidth,
      });
    }
  }, [activeTab, fetchedProperties.length, forSaleProperties.length, forRentProperties.length]);

  // Get current tab properties and displayed properties
  const getCurrentTabProperties = () => {
    switch (activeTab) {
      case 'sale':
        return forSaleProperties;
      case 'rent':
        return forRentProperties;
      default:
        return fetchedProperties;
    }
  };

  const currentTabProperties = getCurrentTabProperties();
  const displayedProperties = showAll ? currentTabProperties : currentTabProperties.slice(0, 6);

  // Check if show more button should be displayed
  const shouldShowMoreButton = currentTabProperties.length > 6;

  // Loading state
  if (isLoading) {
    return (
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <Home className="w-5 h-5" />
            <span>Bất động sản</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Đang tải danh sách bất động sản...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (isError) {
    return (
      <Card className="mb-4 md:mb-8">
        <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
          <CardTitle className="flex items-center gap-2 text-base md:text-xl">
            <Home className="w-5 h-5" />
            <span>Bất động sản</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="col-span-full text-center py-8">
            <div className="text-red-400 mb-2">
              <Home className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-red-500">Không thể tải danh sách bất động sản</p>
            <p className="text-gray-500 text-sm mt-1">{error?.message || 'Đã xảy ra lỗi'}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4 md:mb-8">
      <CardHeader className="max-md:px-4 max-md:pb-2 max-md:pt-3">
        <CardTitle className="flex items-center gap-2 text-base md:text-xl">
          <Home className="w-5 h-5" />
          <span>Bất động sản ({fetchedProperties.length})</span>
        </CardTitle>
        <CardDescription className="text-xs md:text-sm">
          Danh sách bất động sản đã đăng bởi chuyên viên bất động sản
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-background relative rounded-none border-b p-0 grid w-full grid-cols-3">
            {tabs.map((tab, index) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                ref={el => {
                  tabRefs.current[index] = el;
                }}
                className="bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none"
              >
                {tab.name}
              </TabsTrigger>
            ))}

            <motion.div
              className="bg-primary absolute bottom-0 z-20 h-0.5"
              layoutId="underline"
              style={{
                left: underlineStyle.left,
                width: underlineStyle.width,
              }}
              transition={{
                type: 'spring',
                stiffness: 400,
                damping: 40,
              }}
            />
          </TabsList>

          <TabsContent value="all" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản nào</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sale" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản bán</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="rent" className="mt-6">
            {currentTabProperties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {displayedProperties.map(property => (
                  <PropertyCard key={property.id} property={property} priority={false} size="md" />
                ))}
              </div>
            ) : (
              <div className="col-span-full text-center py-8">
                <div className="text-gray-400 mb-2">
                  <Home className="w-12 h-12 mx-auto" />
                </div>
                <p className="text-gray-500">Chưa có bất động sản cho thuê</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Show More Button */}
        {shouldShowMoreButton && (
          <div className="text-center mt-8">
            <Button variant="outline" onClick={() => setShowAll(!showAll)} className="px-8">
              {showAll ? 'Ẩn bớt' : `Xem thêm ${currentTabProperties.length - 6} bất động sản`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default SellerProfileListings;
