# MyRevo Page Navigation

This document explains the URL-based navigation system implemented for the MyRevo page, allowing users to navigate directly to specific tabs and use browser back/forward buttons conveniently.

## Features

### 1. Direct Tab Navigation

Users can now navigate directly to specific tabs using URL parameters:

```
/myrevo?tab=saved-homes       # Nhà đã lưu
/myrevo?tab=collections       # Bộ sưu tập
/myrevo?tab=saved-searches    # Tìm kiếm đã lưu
/myrevo?tab=inbox            # Hộp thư
/myrevo?tab=your-homes       # Nhà của bạn
/myrevo?tab=account-settings # Cài đặt tài khoản
/myrevo?tab=recently-viewed  # Đã xem gần đây
/myrevo?tab=manage-tours     # Quản lý lịch hẹn
/myrevo?tab=appointments     # Quản lý lịch hẹn calendar
/myrevo?tab=tour-planner     # Lịch trình xem nhà
```

### 2. Browser Navigation Support

- **Back/Forward buttons**: Works seamlessly with browser navigation
- **URL sharing**: Users can share links to specific tabs
- **Bookmarking**: Specific tabs can be bookmarked
- **History tracking**: Browser history properly tracks tab changes

### 3. Responsive Design Support

The navigation system works across all device types:

- **Desktop**: Tab changes update URL and support browser navigation
- **Tablet**: Maintains mobile-like behavior with URL support
- **Mobile**: Back button and URL navigation work properly

## Usage Examples

### 1. Creating Links to Specific Tabs

```tsx
import Link from 'next/link';
import { getMyRevoTabUrl } from '@/utils/navigation/myRevoNavigation';

// Simple link
<Link href="/myrevo?tab=collections">
  View Collections
</Link>

// Using utility function
<Link href={getMyRevoTabUrl('saved-homes')}>
  View Saved Homes
</Link>
```

### 2. Programmatic Navigation

```tsx
import { useRouter } from 'next/navigation';
import { navigateToMyRevoTab } from '@/utils/navigation/myRevoNavigation';

function MyComponent() {
  const router = useRouter();

  const goToCollections = () => {
    navigateToMyRevoTab('collections', router);
  };

  return <button onClick={goToCollections}>Go to Collections</button>;
}
```

### 3. Using Pre-built Components

```tsx
import { MyRevoTabLink, MyRevoQuickNavigation } from '@/components/navigation/MyRevoTabLinks';

// Individual tab link
<MyRevoTabLink tab="inbox">
  Check Messages
</MyRevoTabLink>

// Quick navigation menu
<MyRevoQuickNavigation />
```

## Implementation Details

### URL Parameter Handling

- Invalid tab parameters redirect to the default tab (`saved-homes`)
- URL parameters are validated against allowed tab values
- Empty/missing tab parameters default to `saved-homes`

### State Management

- Desktop view: Uses Tabs component with URL synchronization
- Mobile/Tablet view: Maintains `selectedTab` state with URL support
- Browser back/forward buttons properly update component state

### Navigation Flow

1. User clicks tab or navigates to URL with tab parameter
2. URL is updated with new tab parameter
3. Component state is synchronized with URL
4. Appropriate tab content is displayed
5. Browser history is updated for back/forward navigation

## Error Handling

- Invalid tab values are automatically corrected
- Missing tab parameters default to safe fallback
- Type-safe tab values prevent runtime errors
- Graceful handling of navigation edge cases

## Best Practices

1. **Always use the utility functions** for tab navigation to ensure consistency
2. **Validate tab parameters** when accepting them from external sources
3. **Test on all device types** to ensure responsive behavior works correctly
4. **Use TypeScript types** to prevent invalid tab values at compile time

## Files Modified/Created

- `app/(user)/myrevo/page.tsx` - Main page with URL navigation
- `utils/navigation/myRevoNavigation.ts` - Navigation utilities
- `components/navigation/MyRevoTabLinks.tsx` - Reusable navigation components
- `docs/myrevo-navigation.md` - This documentation

The implementation ensures backward compatibility while adding powerful navigation features that improve user experience and SEO.
