import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { clause7To10Schema, Clause7To10Input } from '../schemas';

const DEFAULT_CLAUSE_7_TO_10_CONTENT = `Điều 7. Trách nhiệm do vi phạm hợp đồng

Hai bên thoả thuận cụ thể các trách nhiệm (như phạt; tính lãi, mức lãi suất; phương thức thực hiện trách nhiệm....) do vi phạm hợp đồng trong các trường hợp Bên mua chậm thanh toán tiền mua nhà hoặc chậm nhận bàn giao nh<PERSON> ở, Bên bán chậm bàn giao nhà ở....

Điều 8. Các trường hợp bất khả kháng

Bên mua hoặc Bên bán không bị coi là vi phạm hợp đồng và không bị phạt vi phạm hoặc không phải chịu trách nhiệm bồi thường thiệt hại nếu việc chậm thực hiện hoặc không thực hiện đúng các nghĩa vụ được các bên thỏa thuận trong hợp đồng này do có sự kiện bất khả kháng như thiên tai, chiến tranh, hỏa hoạn, sự thay đổi quy định pháp luật và các trường hợp khác mà không phải do lỗi của các Bên gây ra.

Điều 9. Chuyển giao quyền và nghĩa vụ

1. Bên mua có quyền thực hiện các giao dịch như chuyển nhượng, thế chấp, cho thuê để ở, tặng cho và các giao dịch khác theo quy định của pháp luật về nhà ở sau khi được cấp giấy chứng nhận quyền sở hữu đối với căn hộ đó.

2. Trong trường hợp chưa nhận bàn giao nhà ở từ Bên bán nhưng Bên mua thực hiện chuyển nhượng hợp đồng này cho bên thứ ba thì các bên phải thực hiện đúng thủ tục theo quy định tại …………………………………………………………………………………………………………………

3. Trong cả hai trường hợp nêu tại khoản 1 và 2 của Điều này, người mua nhà ở hoặc người nhận chuyển nhượng hợp đồng đều được hưởng quyền lợi và phải thực hiện các nghĩa vụ của Bên mua quy định trong hợp đồng này.

Điều 10. Cam kết của các Bên

1. Bên bán cam kết nhà ở nêu tại Điều 1 của hợp đồng này thuộc quyền sở hữu của Bên bán và không thuộc diện bị cấm giao dịch theo quy định của pháp luật.

2. Bên mua cam kết đã tìm hiểu, xem xét kỹ thông tin về nhà ở.

3. Việc ký kết hợp đồng này giữa các bên là hoàn toàn tự nguyện, không bị ép buộc, lừa dối. Trong quá trình thực hiện hợp đồng, nếu cần thay đổi hoặc bổ sung nội dung của hợp đồng này thì các bên thỏa thuận lập thêm phụ lục hợp đồng có chữ ký của hai bên và phụ lục hợp đồng có giá trị pháp lý như hợp đồng này.

4. Hai bên cam kết thực hiện đúng và đầy đủ các thỏa thuận đã quy định tại hợp đồng này.`;

interface Clause7To10FormProps {
  data: Clause7To10Input;
  onNext: (data: Clause7To10Input) => void;
  onBack?: () => void;
}

const Clause7To10Form: React.FC<Clause7To10FormProps> = ({ data, onNext, onBack }) => {
  const form = useForm<Clause7To10Input>({
    resolver: zodResolver(clause7To10Schema),
    defaultValues: {
      Clause7To10: data.Clause7To10 || DEFAULT_CLAUSE_7_TO_10_CONTENT,
    },
  });

  // Đồng bộ dữ liệu form với prop data khi data thay đổi
  React.useEffect(() => {
    form.reset({
      Clause7To10: data.Clause7To10 || DEFAULT_CLAUSE_7_TO_10_CONTENT,
    });
  }, [data, form]);

  const onSubmit = (formData: Clause7To10Input) => {
    onNext(formData);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Điều khoản 7-10</CardTitle>
        <CardDescription>
          Trách nhiệm vi phạm hợp đồng, bất khả kháng, chuyển giao quyền và cam kết
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="Clause7To10"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung điều khoản 7-10 *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung điều khoản 7-10: Điều kiện chuyển quyền sở hữu, giải quyết tranh chấp..."
                      rows={12}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-2">Gợi ý nội dung:</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Điều 7: Trách nhiệm do vi phạm hợp đồng</li>
                    <li>Điều 8: Các trường hợp bất khả kháng</li>
                    <li>Điều 9: Chuyển giao quyền và nghĩa vụ</li>
                    <li>Điều 10: Cam kết của các bên</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack}>
                  Quay lại
                </Button>
              )}
              <Button type="submit" className="ml-auto">
                Tiếp tục
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export { Clause7To10Form };
