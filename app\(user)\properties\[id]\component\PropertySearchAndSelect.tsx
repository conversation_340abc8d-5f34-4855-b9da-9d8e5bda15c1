'use client';

import { useState, useMemo, useEffect } from 'react';
import debounce from 'lodash.debounce';
import { useProperties } from '@/hooks/useProperty';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Loader2, PlusCircle, ChevronLeft, ChevronRight, Check } from 'lucide-react';
import type { Property } from '@/lib/api/services/fetchProperty';
import { cn } from '@/lib/utils';

interface PropertySearchAndSelectProps {
  onAddProperty: (property: Property) => void;
  existingPropertyIds: Set<string>;
  disabled: boolean;
}

export function PropertySearchAndSelect({
  onAddProperty,
  existingPropertyIds,
  disabled,
}: PropertySearchAndSelectProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 5;

  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce function using lodash.debounce
  const debouncedUpdate = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedSearchTerm(value);
        setPageNumber(1); // Reset page on search change
      }, 500),
    []
  );

  // Update searchTerm immediately, but debounce API call
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedUpdate(value);
  };

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedUpdate.cancel();
    };
  }, [debouncedUpdate]);

  const { properties, isLoading, isError, error, totalPages } = useProperties({
    searchTerm: debouncedSearchTerm,
    pageNumber,
    pageSize,
  });

  useEffect(() => {
    if (pageNumber > totalPages && totalPages > 0) {
      setPageNumber(totalPages);
    }
  }, [totalPages, pageNumber]);

  const handleAdd = (property: Property) => {
    if (existingPropertyIds.has(property.id)) return;
    onAddProperty(property);
  };

  const isEmpty = !isLoading && properties.length === 0;

  return (
    <div className="space-y-4">
      <Input
        placeholder="Tìm kiếm bất động sản theo tên..."
        value={searchTerm}
        onChange={handleSearchChange}
        disabled={disabled}
      />

      {isLoading ? (
        <div className="py-8">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Bất động sản</TableHead>
                <TableHead>Địa chỉ</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: pageSize }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse" />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="h-8 w-16 bg-gray-200 rounded animate-pulse ml-auto" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Đang tìm kiếm bất động sản...</span>
          </div>
        </div>
      ) : isError ? (
        <div className="text-center text-red-500 py-8">
          <p>{error?.message || 'Không thể tải danh sách bất động sản.'}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Thử lại
          </Button>
        </div>
      ) : isEmpty ? (
        <div className="text-center py-8 text-muted-foreground">
          {searchTerm
            ? 'Không tìm thấy bất động sản phù hợp.'
            : 'Bắt đầu tìm kiếm bất động sản để thêm vào tour.'}
        </div>
      ) : (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Bất động sản</TableHead>
                <TableHead>Địa chỉ</TableHead>
                <TableHead>Hành động</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {properties.map(property => {
                const isAdded = existingPropertyIds.has(property.id);
                return (
                  <TableRow
                    key={property.id}
                    className={cn(isAdded && 'bg-red-600 hover:bg-red-600 text-white')}
                  >
                    <TableCell className="font-medium">{property.title}</TableCell>
                    <TableCell className="max-w-[400px] truncate">
                      {property.location.address}
                    </TableCell>
                    <TableCell className="flex items-center">
                      <Button
                        type="button"
                        variant={isAdded ? 'secondary' : 'outline'}
                        size="sm"
                        onClick={() => handleAdd(property)}
                        disabled={isAdded || disabled}
                        className="flex items-center"
                      >
                        {isAdded ? (
                          <>
                            <Check className="h-4 w-4 mr-1" /> Đã Thêm
                          </>
                        ) : (
                          <>
                            <PlusCircle className="h-4 w-4 mr-2" /> Thêm
                          </>
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          <div className="flex justify-between items-center mt-4">
            <Button
              variant="outline"
              onClick={() => setPageNumber(prev => Math.max(1, prev - 1))}
              disabled={pageNumber <= 1 || disabled}
              type="button"
            >
              <ChevronLeft className="h-4 w-4 mr-2" /> Trước
            </Button>
            <span className="text-sm text-muted-foreground">
              Trang {pageNumber} / {totalPages}
            </span>
            <Button
              type="button"
              variant="outline"
              onClick={() => setPageNumber(prev => prev + 1)}
              disabled={pageNumber >= totalPages || disabled}
            >
              Tiếp <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
