'use client';

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { FormData } from '../types';
import { generateRentalContractHtml } from '../utils/rentalContractGenerator';

// Dynamic import for ReactQuill to avoid SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';

interface WordLikeEditorAdvancedProps {
  formData: FormData;
  contractContent: string;
  onContentChange: (content: string) => void;
  editable: boolean;
  onResetContract: () => void;
}

const WordLikeEditorAdvanced: React.FC<WordLikeEditorAdvancedProps> = ({
  formData,
  contractContent,
  onContentChange,
  editable,
  onResetContract,
}) => {
  const [localContent, setLocalContent] = useState(contractContent);

  useEffect(() => {
    if (!contractContent) {
      const generatedContent = generateRentalContractHtml(formData);
      setLocalContent(generatedContent);
      onContentChange(generatedContent);
    } else {
      setLocalContent(contractContent);
    }
  }, [contractContent, formData, onContentChange]);

  const handleContentChange = (content: string) => {
    setLocalContent(content);
    onContentChange(content);
  };

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['bold', 'italic', 'underline'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ align: [] }],
      ['clean'],
    ],
    clipboard: {
      // Allow HTML tags and classes to be preserved
      matchVisual: false,
    },
  };

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'list',
    'bullet',
    'align',
    'style',
    'class',
    'font',
    'table',
    'td',
    'tr',
  ];

  return (
    <div className="h-full flex flex-col">
      <style jsx global>{`
        .ql-editor {
          font-family: 'Times New Roman', serif !important;
        }
        .ql-editor .text-center {
          text-align: center !important;
        }
        .ql-editor .text-right {
          text-align: right !important;
        }
        .ql-editor .text-left {
          text-align: left !important;
        }
        .ql-editor .article-header {
          text-align: left !important;
          font-weight: bold !important;
          margin: 15px 0 8px 0 !important;
          font-family: 'Times New Roman', serif !important;
        }
        .ql-editor .section-header {
          text-align: left !important;
          font-weight: bold !important;
          margin: 15px 0 8px 0 !important;
          font-family: 'Times New Roman', serif !important;
        }
        .ql-editor .signature-table {
          width: 100% !important;
          border-collapse: collapse !important;
          margin-top: 30px !important;
        }
        .ql-editor .signature-cell {
          width: 50% !important;
          text-align: center !important;
          vertical-align: top !important;
          padding: 20px !important;
          border: none !important;
          font-family: 'Times New Roman', serif !important;
        }
        .ql-editor p,
        .ql-editor div,
        .ql-editor span,
        .ql-editor strong,
        .ql-editor em {
          font-family: 'Times New Roman', serif !important;
        }
      `}</style>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Xem trước hợp đồng</h3>
        {editable && (
          <Button
            onClick={onResetContract}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Khôi phục gốc
          </Button>
        )}
      </div>

      <div className="flex-1 min-h-0">
        <div style={{ height: '600px' }}>
          <ReactQuill
            value={localContent}
            onChange={handleContentChange}
            readOnly={!editable}
            theme="snow"
            style={{ height: '550px' }}
            modules={modules}
            formats={formats}
            placeholder="Nội dung hợp đồng sẽ được tạo tự động khi bạn điền thông tin..."
          />
        </div>
      </div>
    </div>
  );
};

export default WordLikeEditorAdvanced;
