'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useAuth } from '@/lib/providers/authProvider';
import { useSavedSearches, useDeleteSearch, useNavigateToSearch } from '@/hooks/useSavedSearches';
import { SearchCreateResponse } from '@/lib/api/services/fetchSavedSearches';
import { Search, Trash2, DollarSign, Bed, Bath, Car, ChevronRight, Filter } from 'lucide-react';
import { formatPrice } from '@/utils/numbers/formatCurrency';
import { TransactionType } from '@/lib/api/services/fetchProperty';

export default function SavedSearches() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteSearchId, setDeleteSearchId] = useState<string | null>(null);

  const pageSize = 10;
  const { searches, isLoading, isError, totalCount, totalPages, refetch } = useSavedSearches({
    pageNumber: currentPage,
    pageSize,
  });

  const deleteSearchMutation = useDeleteSearch();
  const { navigateToSearch, isLoading: isNavigating } = useNavigateToSearch();

  if (!isAuthenticated) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Vui lòng đăng nhập để xem tìm kiếm đã lưu</p>
      </div>
    );
  }

  const handleDeleteSearch = async (searchId: string) => {
    try {
      await deleteSearchMutation.mutateAsync(searchId);
      setDeleteSearchId(null);
    } catch (error) {
      console.error('Error deleting search:', error);
    }
  };

  const handleSearchClick = async (searchId: string) => {
    await navigateToSearch(searchId, router);
  };

  const formatSearchSummary = (search: SearchCreateResponse): string => {
    const parts: string[] = [];

    if (search.searchTerm) {
      parts.push(`"${search.searchTerm}"`);
    }

    if (search.transactionType?.length) {
      const typeMap: Record<string, string> = {
        ForRent: 'Cho thuê',
        ForSale: 'Bán',
        Project: 'Dự án',
      };
      parts.push(search.transactionType.map(t => typeMap[t] || t).join(', '));
    }

    if (search.type?.length) {
      const propertyTypeMap: Record<string, string> = {
        Apartment: 'Căn hộ',
        House: 'Nhà riêng',
        Townhouse: 'Nhà phố',
        Villa: 'Biệt thự',
        LandPlot: 'Đất nền',
      };
      parts.push(search.type.map(t => propertyTypeMap[t] || t).join(', '));
    }

    if (search.minPrice || search.maxPrice) {
      // Determine transaction type for price formatting
      const transactionType = search.transactionType?.includes('ForRent')
        ? TransactionType.FOR_RENT
        : TransactionType.FOR_SALE;

      if (search.minPrice && search.maxPrice) {
        parts.push(
          `${formatPrice(search.minPrice, transactionType)} - ${formatPrice(search.maxPrice, transactionType)}`
        );
      } else if (search.minPrice) {
        parts.push(`Từ ${formatPrice(search.minPrice, transactionType)}`);
      } else if (search.maxPrice) {
        parts.push(`Đến ${formatPrice(search.maxPrice, transactionType)}`);
      }
    }

    return parts.length > 0 ? parts.join(' • ') : 'Tìm kiếm tùy chỉnh';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-9" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">Có lỗi xảy ra khi tải danh sách tìm kiếm</p>
        <Button onClick={() => refetch()} variant="outline">
          Thử lại
        </Button>
      </div>
    );
  }

  if (searches.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Search className="w-12 h-12 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">Chưa có tìm kiếm nào được lưu</h3>
        <p className="text-muted-foreground mb-6">
          Khi bạn tìm kiếm bất động sản, hãy nhấn nút &quot;Lưu tìm kiếm&quot; để lưu lại các bộ lọc
          yêu thích
        </p>
        <Button onClick={() => router.push('/properties')} className="gap-2">
          <Search className="w-4 h-4" />
          Bắt đầu tìm kiếm
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Tìm kiếm đã lưu</h2>
          <p className="text-muted-foreground">Quản lý các bộ lọc tìm kiếm bất động sản đã lưu</p>
        </div>
        <div className="text-sm text-muted-foreground">{totalCount} tìm kiếm đã lưu</div>
      </div>

      {/* Search Cards */}
      <div className="space-y-4">
        {searches.map(search => (
          <Card key={search.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Filter className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      Lưu lúc {new Date(search.id).toLocaleDateString('vi-VN')}
                    </span>
                  </div>
                  <h3 className="font-semibold text-lg mb-1">
                    {search.searchTerm || 'Tìm kiếm tùy chỉnh'}
                  </h3>
                  <p className="text-muted-foreground text-sm">{formatSearchSummary(search)}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDeleteSearchId(search.id)}
                  className="text-muted-foreground hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Search Details */}
              <div className="flex flex-wrap gap-2 mb-4">
                {search.transactionType?.map(type => (
                  <Badge key={type} variant="secondary" className="text-xs">
                    {type === 'ForRent' ? 'Cho thuê' : type === 'ForSale' ? 'Bán' : type}
                  </Badge>
                ))}
                {search.type?.slice(0, 3).map(type => (
                  <Badge key={type} variant="outline" className="text-xs">
                    {type === 'Apartment'
                      ? 'Căn hộ'
                      : type === 'House'
                        ? 'Nhà riêng'
                        : type === 'Villa'
                          ? 'Biệt thự'
                          : type}
                  </Badge>
                ))}
                {search.type && search.type.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{search.type.length - 3} loại khác
                  </Badge>
                )}
              </div>

              {/* Property Details */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
                {search.bedrooms && (
                  <div className="flex items-center gap-1">
                    <Bed className="w-4 h-4 text-muted-foreground" />
                    <span>{search.bedrooms} phòng ngủ</span>
                  </div>
                )}
                {search.bathrooms && (
                  <div className="flex items-center gap-1">
                    <Bath className="w-4 h-4 text-muted-foreground" />
                    <span>{search.bathrooms} phòng tắm</span>
                  </div>
                )}
                {search.amenityFilters?.includes('Parking') && (
                  <div className="flex items-center gap-1">
                    <Car className="w-4 h-4 text-muted-foreground" />
                    <span>Có chỗ đậu xe</span>
                  </div>
                )}
                {(search.minPrice || search.maxPrice) && (
                  <div className="flex items-center gap-1">
                    <DollarSign className="w-4 h-4 text-muted-foreground" />
                    <span>
                      {search.minPrice && search.maxPrice
                        ? `${formatPrice(search.minPrice, search.transactionType?.includes('ForRent') ? TransactionType.FOR_RENT : TransactionType.FOR_SALE)} - ${formatPrice(search.maxPrice, search.transactionType?.includes('ForRent') ? TransactionType.FOR_RENT : TransactionType.FOR_SALE)}`
                        : search.minPrice
                          ? `Từ ${formatPrice(search.minPrice, search.transactionType?.includes('ForRent') ? TransactionType.FOR_RENT : TransactionType.FOR_SALE)}`
                          : `Đến ${formatPrice(search.maxPrice!, search.transactionType?.includes('ForRent') ? TransactionType.FOR_RENT : TransactionType.FOR_SALE)}`}
                    </span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <Button
                  onClick={() => handleSearchClick(search.id)}
                  disabled={isNavigating}
                  className="gap-2"
                >
                  <Search className="w-4 h-4" />
                  Xem kết quả
                  <ChevronRight className="w-4 h-4" />
                </Button>

                <div className="text-xs text-muted-foreground">
                  Bởi {search.ownerSaving.fullName}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Trước
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              );
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Sau
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteSearchId} onOpenChange={() => setDeleteSearchId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa tìm kiếm này? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteSearchId && handleDeleteSearch(deleteSearchId)}
              disabled={deleteSearchMutation.isPending}
            >
              {deleteSearchMutation.isPending ? 'Đang xóa...' : 'Xóa'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
