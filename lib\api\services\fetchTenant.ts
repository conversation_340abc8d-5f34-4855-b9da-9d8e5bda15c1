import apiService from '../core';

export interface Tenant {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  leaseStartDate: string;
  leaseEndDate: string;
  idVerificationFile?: string | null; // Assuming this is a URL or file path
}

export interface TenantCreateData {
  name: string;
  email: string;
  phone: string;
  address: string;
  leaseStartDate: string;
  leaseEndDate: string;
  idVerificationFile?: string[] | [];
}
export interface TenantCreateResponse {
  code: string;
  status: string;
  message: string;
  data: Tenant;
}

export interface CheckTenant {
  phone: string;
  email: string;
}

export const fetchTenant = {
  //get all tenant
  getAllTenant: async (): Promise<TenantCreateResponse[]> => {
    const response = await apiService.get<TenantCreateResponse[]>('/api/tenants');
    return response.data;
  },
  //create tenant
  createTenant: async (data: TenantCreateData): Promise<TenantCreateResponse> => {
    try {
      const currentTenant = await apiService.get<TenantCreateResponse>(
        `/api/tenants/phone-or-email?phone=${data.phone}&email=${data.email}`
      );
      return currentTenant?.data || null;
    } catch (error) {
      const response = await apiService.post<TenantCreateResponse, TenantCreateData>(
        '/api/tenants',
        data
      );
      return response.data;
    }
  },
  //get tenant by id
  getTenantById: async (id: string): Promise<TenantCreateResponse> => {
    const response = await apiService.get<TenantCreateResponse>(`/api/tenants/${id}`);
    return response.data;
  },
  //get tenant by phone
  getTenantByPhone: async (checkTenant: CheckTenant): Promise<TenantCreateResponse | null> => {
    const response = await apiService.get<TenantCreateResponse>(
      `/api/tenants/phone-or-email?phone=${checkTenant.phone}&email=${checkTenant.email}`
    );
    return response.data;
  },
  //update tenant by id
  updateTenantById: async (id: string, data: TenantCreateData): Promise<TenantCreateResponse> => {
    const response = await apiService.put<TenantCreateResponse, TenantCreateData>(
      `/api/tenants/${id}`,
      data
    );
    return response.data;
  },
};
