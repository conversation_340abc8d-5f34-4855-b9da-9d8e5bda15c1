'use client';

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { Quote } from 'lucide-react';

const testimonials = [
  {
    name: 'Trần Bảo Toà<PERSON>',
    location: 'Tây H<PERSON>, Phú Yên',
    image: '/profile1.png',
    title: '<PERSON><PERSON><PERSON> giá từ Trần Bảo Toà<PERSON>',
    content:
      'Thuê phòng ở RevoLand là một trải nghiệm tuyệt vời. Nhân viên tư vấn rất nhiệt tình và giải đáp thắc mắc của tôi một cách rất tốt. Tôi rất hài lòng về dịch vụ của họ.',
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    location: '<PERSON><PERSON> An, Bình Dương',
    image: '/profile2.png',
    title: '<PERSON><PERSON><PERSON> giá từ Nguyễn <PERSON>',
    content:
      'Tô<PERSON> đã tìm thấy một căn hộ ở RevoLand với giá cả phải chăng và địa chỉ rất tiện lợi. Tôi rất hài lòng về dịch vụ của họ.',
  },
  {
    name: 'Lâm Lê Nhân',
    location: 'Bình Tân, Hồ Chí Minh',
    image: '/profile3.png',
    title: 'Đánh giá từ Lâm Lê Nhân',
    content:
      'Tôi đã tìm thấy một căn hộ ở RevoLand với giá cả phải chăng và địa chỉ rất tiện lợi. Tôi rất hài lòng về dịch vụ của họ.',
  },
];

export default function TestimonialsSection() {
  return (
    <section className="w-full max-w-[1440px] px-8 md:px-8 xl:px-32 mx-auto py-12 sm:py-16 md:py-20 bg-background text-foreground">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8 md:mb-12">
        <div className="max-w-2xl">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 text-gray-900">
            Đánh giá khách hàng
          </h2>
          <p className="text-muted-foreground text-sm md:text-base">
            Những lời cảm ơn từ khách hàng
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {testimonials.map((testimonial, idx) => (
          <Card
            key={idx}
            className="group bg-white border border-red-100 hover:border-red-300 hover:shadow-lg transition-all duration-300"
          >
            <CardHeader className="pb-2">
              <div className="flex items-start gap-3">
                <Quote className="h-6 w-6 text-red-600 opacity-80" />
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-red-600 transition-colors duration-300">
                  {testimonial.title}
                </h3>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                {testimonial.content}
              </p>
            </CardContent>
            <CardFooter className="border-t border-red-50 pt-4">
              <div className="flex items-center">
                <Avatar className="h-10 w-10 mr-3 ring-2 ring-red-100">
                  <AvatarImage src={testimonial.image} alt={testimonial.name} />
                  <AvatarFallback className="bg-red-50 text-red-600">
                    {testimonial.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-gray-900">{testimonial.name}</p>
                  <p className="text-muted-foreground text-sm">{testimonial.location}</p>
                </div>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </section>
  );
}
