'use client';

interface StatsData {
  totalSales: number;
  totalListings: number;
  averageDaysOnMarket: number;
  clientSatisfaction: number;
  joinedAt?: string;
  yearsOfExperience?: number | null;
}

interface SellerProfileStatsProps {
  stats: StatsData;
}

function SellerProfileStats({ stats }: SellerProfileStatsProps) {
  // Format joined date to get year
  const getJoinedYear = (dateString?: string) => {
    if (!dateString) return new Date().getFullYear();
    return new Date(dateString).getFullYear();
  };

  const statsItems = [
    {
      year: getJoinedYear(stats.joinedAt),
      title: 'Thành Viên Revoland',
      subtitle: `Tham gia nền tảng từ năm ${getJoinedYear(stats.joinedAt)}`,
    },
    {
      year: stats.yearsOfExperience || 'N/A',
      title: '<PERSON><PERSON><PERSON>',
      subtitle: stats.yearsOfExperience
        ? `${stats.yearsOfExperience} năm kinh nghiệm trong lĩnh vực`
        : '<PERSON>ưa cập nhật thông tin kinh nghiệm',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12 py-6 lg:py-8">
      {/* Single container with left-right layout */}
      <div className="bg-gray-100 rounded-lg p-6 md:p-8">
        <div className="flex flex-col sm:flex-row sm:divide-x sm:divide-gray-300 divide-y sm:divide-y-0 divide-gray-300">
          {statsItems.map((item, index) => (
            <div
              key={index}
              className="flex-1 px-0 sm:px-4 lg:px-6 py-4 sm:py-0 first:sm:pl-0 last:sm:pr-0"
            >
              <div className="space-y-2">
                {/* Year - Large and bold */}
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900">{item.year}</h2>

                {/* Title */}
                <h3 className="text-xs md:text-sm font-semibold text-gray-800">{item.title}</h3>

                {/* Subtitle */}
                <p className="text-xs md:text-sm text-gray-600">{item.subtitle}</p>
              </div>
            </div>
          ))}
          <div className="flex-1 px-0 sm:px-4 lg:px-6 py-4 sm:py-0 first:sm:pl-0 last:sm:pr-0">
            <div className="space-y-2">
              {/* Total Sales - Large and bold */}
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
                {stats.totalSales || 'N/A'}
              </h2>

              {/* Title */}
              <h3 className="text-xs md:text-sm font-semibold text-gray-800">
                Giao dịch thành công
              </h3>

              {/* Subtitle */}
              <p className="text-xs md:text-sm text-gray-600">
                {stats.totalSales
                  ? `${stats.totalSales} giao dịch đã hoàn thành`
                  : 'Chưa có thông tin về giao dịch'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SellerProfileStats;
