'use client';

import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function PrivacyPolicyPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="flex items-center p-6">
        <Button variant="ghost" className="mr-4" onClick={() => router.back()}>
          <ArrowLeft className="w-6 h-6" />
        </Button>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 pb-12">
        <h1 className="text-4xl font-normal mb-4">Ch<PERSON>h Sách Bảo Mật Thông Tin REVOLAND</h1>
        <p className="mb-12 italic">Cập nhật: 25 tháng 7, 2025</p>

        <div className="prose prose-invert max-w-none">
          <h2 className="text-2xl font-semibold mt-12 mb-4">1. <PERSON> kết bảo mật từ Revoland</h2>

          <p className=" leading-relaxed mb-6">
            Chúng tôi coi dữ liệu của bạn là tài sản quý giá. Chính sách này thể hiện cam kết của
            Revoland trong việc bảo vệ quyền riêng tư và thông tin cá nhân của người dùng.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">2. Dữ liệu nào được thu thập</h2>

          <p className=" leading-relaxed mb-4">
            Chúng tôi thu thập các thông tin cần thiết để phục vụ bạn tốt hơn:
          </p>
          <p className=" leading-relaxed mb-6">
            Thông tin tài khoản, liên hệ, tin đăng, lịch sử tìm kiếm, thiết bị, hành vi sử dụng,
            v.v.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            3. Vì sao chúng tôi thu thập dữ liệu
          </h2>

          <p className=" leading-relaxed mb-4">
            Mọi dữ liệu đều được sử dụng với mục đích chính đáng:
          </p>
          <p className=" leading-relaxed mb-6">
            Hỗ trợ đăng tin – giao dịch – tư vấn – bảo mật – cá nhân hóa trải nghiệm của bạn.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            4. Cách Revoland bảo vệ dữ liệu của bạn
          </h2>

          <p className=" leading-relaxed mb-6">
            Chúng tôi áp dụng các biện pháp kỹ thuật (mã hóa, tường lửa, kiểm soát truy cập) để bảo
            vệ thông tin khỏi rò rỉ, mất mát hoặc truy cập trái phép.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">5. Bạn có quyền kiểm soát dữ liệu</h2>

          <p className=" leading-relaxed mb-4">Bạn có thể:</p>
          <ul className=" leading-relaxed mb-6 list-disc ml-6 space-y-2">
            <li>Truy cập – Chỉnh sửa – Xóa thông tin cá nhân</li>
            <li>Rút lại sự đồng ý</li>
            <li>Yêu cầu chúng tôi không sử dụng dữ liệu vì mục đích marketing</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-12 mb-4">6. Lưu trữ bao lâu? Xóa khi nào?</h2>

          <p className=" leading-relaxed mb-6">
            Thông tin của bạn chỉ được lưu khi còn cần thiết cho mục đích sử dụng. Khi không còn lý
            do hợp pháp, chúng tôi sẽ xóa hoặc ẩn danh toàn bộ dữ liệu.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            7. Chia sẻ dữ liệu với bên thứ ba – Có nhưng minh bạch
          </h2>

          <p className=" leading-relaxed mb-4">Chúng tôi chỉ chia sẻ dữ liệu với:</p>
          <ul className=" leading-relaxed mb-6 list-disc ml-6 space-y-2">
            <li>Đối tác dịch vụ (ví dụ: đơn vị xác minh, gửi email, chăm sóc khách hàng)</li>
            <li>Cơ quan chức năng khi có yêu cầu hợp pháp</li>
            <li>Không bao giờ bán dữ liệu.</li>
          </ul>

          <h2 className="text-2xl font-semibold mt-12 mb-4">8. Trang web của bên thứ 3</h2>

          <p className=" leading-relaxed mb-6">
            Đôi khi, Website và Ứng dụng có thể chứa các liên kết đến và từ các trang web của các
            mạng lưới đối tác và nhà cung cấp…..
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">9. Bảo mật khi đăng tin & tương tác</h2>

          <p className=" leading-relaxed mb-6">
            Revoland đảm bảo các thao tác đăng tin, nhắn tin, liên hệ, lưu tin đều được mã hóa và xử
            lý qua hệ thống bảo mật riêng.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            10. Bạn cần làm gì để bảo vệ chính mình
          </h2>

          <p className=" leading-relaxed mb-6">
            Giữ kín thông tin đăng nhập, tránh chia sẻ OTP hoặc mã xác minh. Revoland không bao giờ
            yêu cầu bạn cung cấp mật khẩu qua bất kỳ kênh nào.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            11. Cách thức xử lý thông tin & Khiếu nại bảo mật
          </h2>

          <p className=" leading-relaxed mb-6">
            Nếu bạn nghi ngờ thông tin bị sử dụng sai mục đích, hãy liên hệ ngay. Revoland luôn có
            đội ngũ xử lý riêng cho các vấn đề về bảo mật.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">
            12. Thời gian lưu trữ và thời gian bắt đầu, kết thúc xử lý dữ liệu
          </h2>

          <p className=" leading-relaxed mb-6">
            Dữ liệu có thể được lưu trữ tại Việt Nam hoặc quốc gia có hệ thống lưu trữ an toàn cao.
            Mọi vị trí đều phải tuân thủ tiêu chuẩn bảo mật.
          </p>

          <h2 className="text-2xl font-semibold mt-12 mb-4">13. Tuyên bố từ chối trách nhiệm</h2>

          <p className=" leading-relaxed mb-6">
            Chúng tôi không chịu trách nhiệm đối với bất kỳ thiệt hại, tổn thất hay tranh chấp nào
            phát sinh từ việc người dùng tự nguyện chia sẻ thông tin cá nhân, liên hệ, tài sản hoặc
            nội dung khác với các bên thứ ba thông qua nền tảng. Người dùng cần tự cân nhắc và chịu
            trách nhiệm với các hành vi tương tác của mình.
          </p>

          <p className=" leading-relaxed mb-6">
            Chúng tôi cũng không chịu trách nhiệm trong trường hợp không nhận được thông báo, yêu
            cầu hoặc phản hồi từ phía người dùng do lỗi kỹ thuật, trục trặc hệ thống, gián đoạn
            đường truyền, sự cố phần mềm, hoặc bất kỳ nguyên nhân nào nằm ngoài sự kiểm soát hợp lý
            của chúng tôi.
          </p>

          <p className=" leading-relaxed mb-6">
            Mọi dữ liệu hiển thị và tương tác trên nền tảng có thể bị gián đoạn hoặc sai lệch trong
            một số tình huống bất khả kháng. Chúng tôi không cam kết rằng hệ thống sẽ hoạt động liên
            tục, không có lỗi hoặc không có nguy cơ bị tấn công mạng.
          </p>
        </div>
      </div>
    </div>
  );
}
