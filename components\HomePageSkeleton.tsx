'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { useEffect, useState } from 'react';

export default function HomepageSkeleton() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 300);
    return () => clearTimeout(timer);
  }, []);

  if (!loading) return null;

  return (
    <div className="fixed inset-0 z-50 bg-background text-white overflow-y-auto">
      {/* Hero Section */}
      <div className="max-w-screen-xl mx-auto px-6 grid grid-cols-1 md:grid-cols-2 items-center gap-12 py-24">
        <div className="space-y-6 text-center md:text-left max-w-md mx-auto md:mx-0">
          <Skeleton className="h-[36px] md:h-[48px] w-4/5 mx-auto md:mx-0" />
          <Skeleton className="h-[36px] md:h-[48px] w-3/5 mx-auto md:mx-0" />
          <Skeleton className="h-5 w-full max-w-md mx-auto md:mx-0" />
          <div className="flex flex-col sm:flex-row justify-center md:justify-start gap-4 max-w-sm mx-auto md:mx-0">
            <Skeleton className="h-10 w-[120px]" />
            <Skeleton className="h-10 w-[160px]" />
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-10 max-w-md mx-auto md:mx-0">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-muted/30 p-4 rounded-xl">
                <Skeleton className="h-6 w-1/2 mx-auto mb-2" />
                <Skeleton className="h-4 w-3/4 mx-auto" />
              </div>
            ))}
          </div>
        </div>

        <div className="hidden md:block h-[400px] w-full">
          <Skeleton className="w-full h-full rounded-xl" />
        </div>

        <div className="block md:hidden w-full h-[300px]">
          <Skeleton className="w-full h-full rounded-xl" />
        </div>
      </div>

      {/* Featured Properties Section */}
      <div className="container px-6 py-16 mx-auto">
        <Skeleton className="h-8 w-48 mx-auto mb-12" />
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-muted/30 p-4 rounded-xl">
              <Skeleton className="w-full h-[200px] rounded-lg mb-4" />
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>

      {/* Footer Skeleton */}
      <div className="bg-muted py-16 px-6">
        <div className="max-w-screen-xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="space-y-4">
            <Skeleton className="h-10 w-10" />
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-10 w-full max-w-sm" />
          </div>
          {[...Array(2)].map((_, col) => (
            <div key={col} className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-4 w-3/4" />
              ))}
            </div>
          ))}
        </div>
        <div className="flex flex-col sm:flex-row justify-between items-center mt-12 gap-6">
          <Skeleton className="h-4 w-48" />
          <div className="flex gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-6 w-6 rounded-full" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
