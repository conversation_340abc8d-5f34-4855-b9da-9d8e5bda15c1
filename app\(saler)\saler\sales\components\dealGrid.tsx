'use client';

import { DealCard } from './DealCard';
import { useState, useEffect } from 'react';
import { Alert } from '@/components/ui/alert';
import { AlertCircle, TrendingUp, Star, Clock, Phone, CheckCircle, XCircle } from 'lucide-react';
import { AlertTitle } from '@/components/ui/alert';
import { AlertDescription } from '@/components/ui/alert';
import { Deal, DealStatus, DealSearchParams } from '@/lib/api/services/fetchDeal';
import { useDeals, useUpdateDeal } from '@/hooks/useDeals';
import { LeadCardSkeleton } from '@/components/leadCardSeketon';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const DEAL_COLUMNS = [
  {
    id: 'New',
    title: '<PERSON>ớ<PERSON>',
    description: '<PERSON>iao dịch vừa được tạo',
    icon: Star,
    priority: 'Mớ<PERSON>',
  },
  {
    id: 'Contacted',
    title: 'Đ<PERSON> liên hệ',
    description: 'Đã liên hệ với khách hàng',
    icon: Phone,
    priority: 'Đang xử lý',
  },
  {
    id: 'Negotiation',
    title: 'Đàm phán',
    description: 'Đang trong quá trình đàm phán',
    icon: TrendingUp,
    priority: 'Quan trọng',
  },
  {
    id: 'Closing',
    title: 'Sắp chốt',
    description: 'Gần như chốt được giao dịch',
    icon: Clock,
    priority: 'Ưu tiên cao',
  },
  {
    id: 'Won',
    title: 'Thành công',
    description: 'Giao dịch đã thành công',
    icon: CheckCircle,
    priority: 'Hoàn thành',
  },
  {
    id: 'Lost',
    title: 'Thất bại',
    description: 'Giao dịch không thành công',
    icon: XCircle,
    priority: 'Đã đóng',
  },
];

export function DealGrid() {
  const [searchParams] = useState<DealSearchParams>({
    pageNumber: 1,
    pageSize: 100,
  });

  const { data: deals, isLoading, isError, error, isFetching } = useDeals(searchParams);
  const updateDealStatus = useUpdateDeal();

  const [localDeals, setLocalDeals] = useState<Deal[]>([]);

  useEffect(() => {
    if (deals?.deals) {
      setLocalDeals(deals.deals);
    }
  }, [deals]);

  const dealsArray = localDeals;

  const groupedDeals = {
    New: dealsArray.filter(deal => deal.status === DealStatus.New),
    Contacted: dealsArray.filter(deal => deal.status === DealStatus.Contacted),
    Negotiation: dealsArray.filter(deal => deal.status === DealStatus.Negotiation),
    Closing: dealsArray.filter(deal => deal.status === DealStatus.Closing),
    Won: dealsArray.filter(deal => deal.status === DealStatus.Won),
    Lost: dealsArray.filter(deal => deal.status === DealStatus.Lost),
  };

  // Handle drag and drop event to update deal status
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }
    setLocalDeals(prevDeals =>
      prevDeals.map(deal =>
        deal.id === draggableId ? { ...deal, status: destination.droppableId as DealStatus } : deal
      )
    );
    updateDealStatus.mutate(
      {
        id: draggableId,
        status: destination.droppableId as DealStatus,
      },
      {
        onError: () => {
          setLocalDeals(prevDeals =>
            prevDeals.map(deal =>
              deal.id === draggableId ? { ...deal, status: source.droppableId as DealStatus } : deal
            )
          );
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {DEAL_COLUMNS.map(column => (
            <Card key={column.id} className="border border-border/40 shadow-sm bg-muted">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center">
                      <column.icon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-sm font-semibold">{column.title}</CardTitle>
                      <p className="text-xs text-muted-foreground mt-0.5">{column.description}</p>
                    </div>
                  </div>
                  <div className="h-5 w-8 bg-muted/30 rounded animate-pulse" />
                </div>
              </CardHeader>
              <CardContent className="p-3 space-y-2">
                {Array.from({ length: 2 }).map((_, index) => (
                  <LeadCardSkeleton key={index} />
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error?.message || 'Failed to load deals'}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {DEAL_COLUMNS.map(column => {
            const columnDeals = groupedDeals[column.id as DealStatus];
            return (
              <Card key={column.id} className="border border-border/40 shadow-sm bg-muted">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 rounded-lg bg-muted/50 flex items-center justify-center">
                        <column.icon className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div>
                        <CardTitle className="text-sm font-semibold">{column.title}</CardTitle>
                        <p className="text-xs text-muted-foreground mt-0.5">{column.description}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs bg-muted/30">
                      {columnDeals.length}
                    </Badge>
                  </div>
                </CardHeader>
                <Droppable droppableId={column.id}>
                  {provided => (
                    <CardContent
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="p-3 space-y-2 min-h-[300px]"
                    >
                      {columnDeals.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-8 text-center">
                          <div className="w-12 h-12 rounded-lg bg-muted/30 flex items-center justify-center mb-3">
                            <column.icon className="h-6 w-6 text-muted-foreground/60" />
                          </div>
                          <p className="text-xs font-medium text-muted-foreground">
                            Không có giao dịch nào
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Kéo giao dịch vào đây để thay đổi trạng thái
                          </p>
                        </div>
                      ) : (
                        columnDeals.map((deal, index) => (
                          <Draggable key={deal.id} draggableId={deal.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                style={provided.draggableProps.style}
                              >
                                <DealCard
                                  deal={deal}
                                  status={column.id as DealStatus}
                                  isDragging={snapshot.isDragging}
                                  isLoading={isFetching}
                                />
                              </div>
                            )}
                          </Draggable>
                        ))
                      )}
                      {provided.placeholder}
                    </CardContent>
                  )}
                </Droppable>
              </Card>
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
}
