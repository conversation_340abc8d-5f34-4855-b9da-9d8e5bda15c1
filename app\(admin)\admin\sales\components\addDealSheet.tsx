'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Plus, Loader2, FileText, Flag, Users } from 'lucide-react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateDeal } from '@/hooks/useDeals';
import { useLeads } from '@/hooks/useLead';
import { DealPriority } from '@/lib/api/services/fetchDeal';
import { cn } from '@/lib/utils';
import { Lead } from '@/lib/api/services/fetchLead';

const formSchema = z.object({
  leadId: z.string().min(1, 'Please select a lead'),
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  description: z
    .string()
    .min(1, 'Description is required')
    .max(500, 'Description must be less than 500 characters'),
  priority: z.nativeEnum(DealPriority, {
    errorMap: () => ({ message: 'Please select a priority level' }),
  }),
});

type FormData = z.infer<typeof formSchema>;

interface AddDealSheetProps {
  children?: React.ReactNode;
  className?: string;
}

export function AddDealSheet({ children, className }: AddDealSheetProps) {
  const [open, setOpen] = useState(false);
  const createDeal = useCreateDeal();

  // Fetch leads for selection
  const { data: leads, isLoading: leadsLoading } = useLeads(
    {
      pageNumber: 1,
      pageSize: 100,
      createdAt: new Date().toISOString().split('T')[0],
    },
    { enabled: open }
  );

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      leadId: '',
      title: '',
      description: '',
      priority: DealPriority.Medium,
    },
  });

  const onSubmit = async (data: FormData) => {
    createDeal.mutate(data, {
      onSuccess: response => {
        if (response.status) {
          setOpen(false);
          form.reset();
        }
      },
    });
  };

  const getPriorityLabel = (priority: DealPriority) => {
    switch (priority) {
      case DealPriority.High:
        return 'Cao';
      case DealPriority.Medium:
        return 'Trung bình';
      case DealPriority.Low:
        return 'Thấp';
      default:
        return priority;
    }
  };

  const getPriorityColor = (priority: DealPriority) => {
    switch (priority) {
      case DealPriority.High:
        return 'text-red-600';
      case DealPriority.Medium:
        return 'text-orange-600';
      case DealPriority.Low:
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        {children || (
          <Button className={cn('gap-2', className)}>
            <Plus className="h-4 w-4" />
            Thêm một giao dịch mới
          </Button>
        )}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="text-xl font-bold flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Tạo giao dịch mới
          </SheetTitle>
          <SheetDescription>
            Tạo giao dịch mới từ khách hàng tiềm năng hiện có. Tất cả các trường đều bắt buộc.
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="mt-6 space-y-6">
            {/* Lead Selection */}
            <FormField
              control={form.control}
              name="leadId"
              render={({ field }) => (
                <FormItem className="px-4">
                  <FormLabel className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Khách hàng
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={leadsLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={leadsLoading ? 'Đang tải...' : 'Chọn khách hàng'}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {leads?.leads?.data?.map((lead: Lead) => (
                        <SelectItem key={lead.id} value={lead.id}>
                          <div className="flex flex-col items-start">
                            <span className="font-medium">{lead.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {lead.email || lead.phone || 'Không có thông tin liên hệ'}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Chọn khách hàng để tạo deal mới</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="px-4">
                  <FormLabel className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Tiêu đề giao dịch
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Ví dụ: Bán căn hộ 2PN tại Q1" {...field} />
                  </FormControl>
                  <FormDescription>Nhập tiêu đề mô tả ngắn gọn về deal này</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="px-4">
                  <FormLabel className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Mô tả
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Mô tả chi tiết về deal, yêu cầu của khách hàng, ghi chú quan trọng..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Mô tả chi tiết về deal và các yêu cầu cụ thể</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Priority */}
            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem className="px-4">
                  <FormLabel className="flex items-center gap-2">
                    <Flag className="h-4 w-4" />
                    Mức độ ưu tiên
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn mức độ ưu tiên" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(DealPriority).map(priority => (
                        <SelectItem key={priority} value={priority}>
                          <div className="flex items-center gap-2">
                            <Flag className={cn('h-3 w-3', getPriorityColor(priority))} />
                            <span className="font-medium">{getPriorityLabel(priority)}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Đánh giá mức độ quan trọng của deal này</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-3 pt-4 px-4">
              <Button type="submit" disabled={createDeal.isPending} className="flex-1 gap-2">
                {createDeal.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                {createDeal.isPending ? 'Đang tạo...' : 'Tạo giao dịch'}
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
