'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useState } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { useAuthStore } from '@/lib/store/authStore';
import { RegisterRequest, Roles } from '@/lib/api/services/fetchAuth';
import { GoogleAuthButton } from '@/components/auth/googleAuthButton';
import { toast } from 'sonner';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { OTPVerification } from '@/components/auth/OTPVerification';
import { z } from 'zod';
import { Separator } from '@/components/ui/separator';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FacebookAuthButton } from '@/components/auth/facebookAuthButton';

const registerSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Tên phải có ít nhất 2 ký tự')
    .max(50, 'Tên không được vượt quá 50 ký tự'),
  phoneNumber: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .refine(
      value => {
        // Loại bỏ khoảng trắng và dấu gạch ngang
        const cleanPhone = value.replace(/[\s-]/g, '');
        // Regex cho số điện thoại Việt Nam
        const phoneRegex = /^(\+84|84|0)[35789][0-9]{8}$/;
        return phoneRegex.test(cleanPhone);
      },
      {
        message: 'Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam hợp lệ',
      }
    ),
  password: z
    .string()
    .min(8, 'Mật khẩu phải có ít nhất 8 ký tự')
    .regex(/[A-Z]/, 'Mật khẩu phải có ít nhất 1 chữ cái viết hoa')
    .regex(/[a-z]/, 'Mật khẩu phải có ít nhất 1 chữ cái viết thường')
    .regex(/[0-9]/, 'Mật khẩu phải có ít nhất 1 số')
    .regex(/[^A-Za-z0-9]/, 'Mật khẩu phải có ít nhất 1 ký tự đặc biệt'),
  terms: z.boolean().refine(val => val, 'Bạn phải đồng ý với điều khoản và điều kiện'),
});

type RegisterFormData = z.infer<typeof registerSchema>;

export function RegisterForm() {
  const {
    register: registerUser,
    isLoading,
    registerSuccess,
    keyVariable,
    resetRegisterState,
  } = useAuth();
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const [showPassword, setShowPassword] = useState(false);
  const [googleError, setGoogleError] = useState<string | null>(null);
  const [facebookError, setFacebookError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    mode: 'onChange',
  });

  // Format phone number while typing
  const formatPhoneNumber = (value: string) => {
    // Loại bỏ tất cả ký tự không phải số
    const numbersOnly = value.replace(/[^\d]/g, '');

    // Nếu bắt đầu bằng 84, thêm +
    if (numbersOnly.startsWith('84')) {
      return '+' + numbersOnly;
    }

    // Nếu bắt đầu bằng 0, giữ nguyên
    if (numbersOnly.startsWith('0')) {
      return numbersOnly;
    }

    // Nếu không bắt đầu bằng 0 hoặc 84, thêm 0 vào đầu
    if (numbersOnly.length > 0 && !numbersOnly.startsWith('0') && !numbersOnly.startsWith('84')) {
      return '0' + numbersOnly;
    }

    return numbersOnly;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phoneNumber', formatted, { shouldValidate: true });
  };

  const onSubmit = async (data: RegisterFormData) => {
    try {
      // Chuẩn hóa số điện thoại trước khi gửi
      const cleanPhone = data.phoneNumber.replace(/[\s-]/g, '');

      const payload: RegisterRequest = {
        userName: cleanPhone,
        fullName: data.fullName,
        keyRegister: cleanPhone,
        password: data.password,
        about: '',
        birthDate: new Date().toISOString(),
        role: Roles.User,
      };
      await registerUser(payload);
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Đã có lỗi xảy ra khi đăng ký. Vui lòng thử lại sau.');
    }
  };

  if (isAuthenticated) return null;

  return (
    <>
      <div className="flex flex-col items-center justify-center">
        <div className="w-full space-y-8 rounded-xl">
          <div className="flex flex-col items-center gap-2 text-center">
            <h1 className="text-xl md:text-2xl font-bold tracking-tight text-red-500">
              Tạo tài khoản
            </h1>
            <p className="text-balance text-xs md:text-sm text-muted-foreground">
              Nhập thông tin của bạn để tạo tài khoản bằng số điện thoại
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName" className="text-xs md:text-sm">
                    Tên đầy đủ
                    <span className="text-destructive"> *</span>
                  </Label>
                  <Input
                    id="fullName"
                    {...register('fullName')}
                    placeholder="Nhập tên đầy đủ"
                    className={cn(
                      errors.fullName && 'border-destructive',
                      'placeholder:text-xs md:placeholder:text-sm text-xs md:text-sm'
                    )}
                    aria-describedby="fullName-error"
                  />
                  {errors.fullName && (
                    <p className="text-xs text-destructive" id="fullName-error">
                      {errors.fullName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-xs md:text-sm">
                    Số điện thoại
                    <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="phoneNumber"
                    {...register('phoneNumber')}
                    onChange={handlePhoneChange}
                    placeholder="Nhập số điện thoại (VD: 0901234567)"
                    className={cn(
                      errors.phoneNumber && 'border-destructive',
                      'placeholder:text-xs md:placeholder:text-sm text-xs md:text-sm'
                    )}
                    aria-describedby="phoneNumber-error"
                  />
                  {errors.phoneNumber && (
                    <p className="text-xs text-destructive" id="phoneNumber-error">
                      {errors.phoneNumber.message}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Hỗ trợ định dạng: 0901234567, +84901234567, 84901234567
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-xs md:text-sm">
                  Mật khẩu
                  <span className="text-destructive"> *</span>
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Nhập mật khẩu"
                    className={cn(
                      errors.password && 'border-destructive pr-10',
                      'placeholder:text-xs md:placeholder:text-sm text-xs md:text-sm'
                    )}
                    aria-describedby="password-error"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1 h-8 w-8 px-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                    <span className="sr-only">
                      {showPassword ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
                    </span>
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-xs text-destructive" id="password-error">
                    {errors.password.message}
                  </p>
                )}
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>Mật khẩu phải có:</p>
                  <ul className="list-disc list-inside space-y-0.5 ml-2">
                    <li>Ít nhất 8 ký tự</li>
                    <li>1 chữ cái viết hoa</li>
                    <li>1 chữ cái viết thường</li>
                    <li>1 số</li>
                    <li>1 ký tự đặc biệt</li>
                  </ul>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Controller
                  control={control}
                  name="terms"
                  render={({ field }) => (
                    <Checkbox
                      id="terms"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className={cn(errors.terms && 'border-destructive')}
                    />
                  )}
                />
                <Label
                  htmlFor="terms"
                  className={cn(
                    'text-xs text-muted-foreground md:text-sm leading-none',
                    errors.terms && 'text-destructive'
                  )}
                >
                  Tôi đồng ý với{' '}
                  <Link href="/legal/terms-of-agreement" className="text-primary hover:underline">
                    Điều khoản
                  </Link>{' '}
                  và{' '}
                  <Link href="/legal/privacy-policy" className="text-primary hover:underline">
                    Chính sách bảo mật
                  </Link>
                </Label>
              </div>
              {errors.terms && (
                <p className="text-xs text-destructive" id="terms-error">
                  {errors.terms.message}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full bg-red-500 hover:bg-red-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isLoading ? 'Đang tạo tài khoản...' : 'Tạo tài khoản'}
                </>
              ) : (
                'Tạo tài khoản bằng số điện thoại'
              )}
            </Button>
          </form>

          <div className="relative flex items-center justify-center">
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
            <span className="px-2 text-xs md:text-sm text-muted-foreground">Hoặc đăng ký với</span>
            <Separator className="flex-1 border-t border-dashed bg-transparent" />
          </div>

          <div className="flex justify-center w-full">
            <div className="space-y-4 w-full max-w-[400px]">
              <GoogleAuthButton mode="register" onError={setGoogleError} />
              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                <FacebookAuthButton mode="register" onError={setFacebookError} />
              </div>
              {googleError && (
                <div className="rounded-md bg-destructive/10 p-2 text-center text-xs md:text-sm text-destructive">
                  {googleError}
                </div>
              )}
              {facebookError && (
                <div className="rounded-md bg-destructive/10 p-2 text-center text-xs md:text-sm text-destructive">
                  {facebookError}
                </div>
              )}
            </div>
          </div>

          <div className="text-center">
            <p className="text-xs md:text-sm text-muted-foreground">
              Đã có tài khoản?{' '}
              <Link href="/login" className="text-primary text-red-500 hover:underline">
                Đăng nhập
              </Link>
            </p>
          </div>

          <div className="text-balance text-center text-xs md:text-sm text-muted-foreground">
            Bằng cách tiếp tục, bạn đồng ý với{' '}
            <Link href="/legal/terms-of-agreement" className="hover:text-primary hover:underline">
              Điều khoản, điều kiện
            </Link>{' '}
            và{' '}
            <Link href="/legal/privacy-policy" className="hover:text-primary hover:underline">
              Chính sách bảo mật
            </Link>
            .
          </div>
        </div>
      </div>

      <Dialog
        open={registerSuccess}
        onOpenChange={open => {
          if (!open) {
            resetRegisterState();
          }
        }}
      >
        <DialogContent
          className="max-w-lg w-full [&>button]:hidden"
          onInteractOutside={e => e.preventDefault()}
        >
          {keyVariable && (
            <OTPVerification keyRegister={keyVariable} onClose={() => resetRegisterState()} />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
