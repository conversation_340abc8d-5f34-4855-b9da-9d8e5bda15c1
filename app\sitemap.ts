import { propertyService, PropertyStatus } from '@/lib/api/services/fetchProperty';

export type SitemapChangeFrequency =
  | 'always'
  | 'hourly'
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'yearly'
  | 'never';

/**
 * Generate sitemap for Revoland website
 * Includes all static routes and dynamic property routes for optimal SEO
 *
 * Route priorities:
 * - 1.0: Homepage
 * - 0.9: Main feature pages (properties, landing)
 * - 0.8: Important legal/info pages, individual properties
 * - 0.7: User functionality, legal sections
 * - 0.6: Secondary features
 * - 0.5: User-specific pages
 * - 0.4: Auth pages
 * - 0.3: Demo/utility pages
 */
export default async function sitemap() {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://www.revoland.vn';

  // Add static pages with appropriate priorities and change frequencies
  const staticRoutes = [
    // Main pages - highest priority
    { route: '', priority: 1.0, changeFrequency: 'daily' as const },
    { route: '/properties', priority: 0.9, changeFrequency: 'daily' as const },
    { route: '/landingpage', priority: 0.9, changeFrequency: 'weekly' as const },

    // User functionality pages
    { route: '/appointments', priority: 0.7, changeFrequency: 'weekly' as const },
    { route: '/comparison', priority: 0.6, changeFrequency: 'weekly' as const },
    { route: '/myrevo', priority: 0.5, changeFrequency: 'weekly' as const },

    // Legal and information pages - important for SEO
    { route: '/legal', priority: 0.7, changeFrequency: 'monthly' as const },
    { route: '/legal/privacy-policy', priority: 0.8, changeFrequency: 'monthly' as const },
    { route: '/legal/terms-of-agreement', priority: 0.8, changeFrequency: 'monthly' as const },
    { route: '/legal/regulations', priority: 0.7, changeFrequency: 'monthly' as const },
    { route: '/legal/faq', priority: 0.7, changeFrequency: 'weekly' as const },
    { route: '/legal/pricing', priority: 0.8, changeFrequency: 'weekly' as const },
    { route: '/legal/complaint-settlement', priority: 0.6, changeFrequency: 'monthly' as const },

    // Authentication pages - lower priority but still indexed
    { route: '/login', priority: 0.4, changeFrequency: 'yearly' as const },
    { route: '/register', priority: 0.4, changeFrequency: 'yearly' as const },
    { route: '/forgot-password', priority: 0.3, changeFrequency: 'yearly' as const },
    { route: '/renew-password', priority: 0.3, changeFrequency: 'yearly' as const },

    // Demo and feature pages
    // { route: '/navigation-demo', priority: 0.3, changeFrequency: 'monthly' as const },
    // { route: '/notification-demo', priority: 0.3, changeFrequency: 'monthly' as const },

    // Future internationalization routes (commented for now)
    // { route: '/vi', priority: 0.9, changeFrequency: 'daily' as const },
    // { route: '/en', priority: 0.9, changeFrequency: 'daily' as const },
    // { route: '/vi/tin-tuc', priority: 0.7, changeFrequency: 'daily' as const },
    // { route: '/en/tin-tuc', priority: 0.7, changeFrequency: 'daily' as const },
  ];

  const routes = staticRoutes.map(({ route, priority, changeFrequency }) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date().toISOString(),
    changeFrequency,
    priority,
  }));

  // Add property pages
  let propertyRoutes: Array<{
    url: string;
    lastModified: string;
    changeFrequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    priority: number;
  }> = [];

  try {
    const propertiesResponse = await propertyService.getProperties({
      pageSize: 1000, // Get all properties for sitemap
      status: PropertyStatus.AVAILABLE,
    });

    if (propertiesResponse.data?.properties) {
      propertyRoutes = propertiesResponse.data.properties.map(property => ({
        url: `${baseUrl}/properties/${property.id}`,
        lastModified: property.updatedAt,
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      }));
    }
  } catch (error) {
    console.error('Error fetching properties for sitemap:', error);
  }

  // TODO: Add other dynamic routes for SEO when needed:
  // - Featured collections (if any public collections exist)
  // - Agent profiles (if public agent pages exist)
  // - Blog posts or news articles (if blog feature is added)
  // - Property search result pages for popular search terms
  // - Location-based property pages (e.g., /properties/hanoi, /properties/hcm)

  // Combine all routes
  const allRoutes = [...routes, ...propertyRoutes];

  // Log sitemap generation info for monitoring
  console.log(
    `Sitemap generated with ${allRoutes.length} URLs (${routes.length} static, ${propertyRoutes.length} properties)`
  );

  return allRoutes;
}
