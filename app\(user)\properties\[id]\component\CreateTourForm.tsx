'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Loader2, Trash2, Bed, Bath, Maximize, MapPin, Save } from 'lucide-react';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { type Property, TransactionType } from '@/lib/api/services/fetchProperty';
import type { Tour, UpdateTourPropertyRequest } from '@/lib/api/services/fetchTour';
import {
  useCreateTour,
  useUpdateTour,
  useAddPropertyToTour,
  useRemovePropertyFromTour,
  useUserTours,
  useUpdateTourStatus,
  useUpdateTourProperty,
} from '@/hooks/useTour';
import { getPropertyTypeText, PropertyCard } from '@/components/PropertyCard';
import { formatPrice } from '@/utils/numbers/formatCurrency';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useState, useEffect } from 'react';
import { getTourStatusDisplay } from '@/utils/tour';
import { PropertySearchAndSelect } from './PropertySearchAndSelect';
import { Calendar } from '@/components/calendar';

interface CreateTourFormProps {
  onSuccess: () => void;
  onBack: () => void;
  property?: Property;
  tour?: Tour;
  isDialog?: boolean;
  initialProperties?: Property[];
}

// Helper function to extract time from UTC string without timezone conversion
export const extractTimeFromUTC = (utcString: string): string => {
  // Ép UTC thành timestamp gốc
  const utcDate = new Date(utcString);

  // Tính toán giờ GMT+7 thủ công
  const utcTimestamp = utcDate.getTime(); // milliseconds
  const offset7h = 7 * 60 * 60 * 1000; // cộng thêm 7 giờ
  const gmt7Date = new Date(utcTimestamp + offset7h);

  const hours = String(gmt7Date.getUTCHours()).padStart(2, '0');
  const minutes = String(gmt7Date.getUTCMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};
// Helper function to add one hour to a time string (HH:mm)
const addOneHour = (time: string): string => {
  const [hours, minutes] = time.split(':').map(Number);
  const date = new Date(2000, 0, 1, hours, minutes);
  date.setHours(date.getHours() + 1);
  if (date.getHours() >= 24) {
    return '09:00';
  }
  const newHours = date.getHours().toString().padStart(2, '0');
  const newMinutes = date.getMinutes().toString().padStart(2, '0');
  return `${newHours}:${newMinutes}`;
};

const singlePropertySchema = z
  .object({
    property: z.any(),
    propertyId: z.string().min(1, 'ID bất động sản là bắt buộc.'),
    title: z.string().optional(),
    startTime: z
      .string()
      .regex(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Định dạng thời gian không hợp lệ (HH:mm).'),
    endTime: z
      .string()
      .regex(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Định dạng thời gian không hợp lệ (HH:mm).'),
    tourPropertyId: z.string().optional(),
    status: z.enum(['Pending', 'Approved', 'Completed', 'Rejected']).default('Pending'),
  })
  .superRefine((data, ctx) => {
    const start = new Date(`2000-01-01T${data.startTime}:00`);
    const end = new Date(`2000-01-01T${data.endTime}:00`);
    if (start >= end) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Thời gian kết thúc phải sau thời gian bắt đầu.',
        path: ['endTime'],
      });
    }
    const oneHourInMs = 60 * 60 * 1000;
    if (end.getTime() - start.getTime() < oneHourInMs) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Thời gian xem phải kéo dài ít nhất 1 giờ.',
        path: ['endTime'],
      });
    }
  });

const formSchema = z
  .object({
    name: z.string().min(1, 'Tên lịch trình là bắt buộc.'),
    startDateTime: z.date({
      required_error: 'Ngày giờ bắt đầu lịch trình là bắt buộc.',
    }),
    note: z.string().optional(),
    status: z.enum(['Pending', 'Approved', 'Completed']).default('Pending'),
    properties: z
      .array(singlePropertySchema)
      .min(1, 'Phải có ít nhất một bất động sản trong lịch trình.'),
  })
  .superRefine((values, ctx) => {
    const seenTimeSlots = new Set<string>();
    values.properties.forEach((prop, index) => {
      if (prop.startTime && prop.endTime) {
        const timeSlotKey = `${prop.startTime}-${prop.endTime}`;
        if (seenTimeSlots.has(timeSlotKey)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Bất động sản ${index + 1} có thời gian xem trùng lặp với một bất động sản khác.`,
            path: [`properties.${index}.startTime`],
          });
        } else {
          seenTimeSlots.add(timeSlotKey);
        }
      }
    });

    for (let i = 1; i < values.properties.length; i++) {
      const prevProperty = values.properties[i - 1];
      const currentProperty = values.properties[i];
      if (prevProperty.endTime && currentProperty.startTime) {
        const prevEndTime = new Date(`2000-01-01T${prevProperty.endTime}:00`);
        const currentStartTime = new Date(`2000-01-01T${currentProperty.startTime}:00`);
        const oneHourInMs = 60 * 60 * 1000;
        if (currentStartTime.getTime() < prevEndTime.getTime() + oneHourInMs) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Bất động sản ${i + 1} phải bắt đầu ít nhất 1 giờ sau khi bất động sản trước kết thúc.`,
            path: [`properties.${i}.startTime`],
          });
        }
      }
    }
  });

type TourFormValues = z.infer<typeof formSchema>;

export function CreateTourForm({
  onSuccess,
  onBack,
  property,
  isDialog,
  tour,
  initialProperties,
}: CreateTourFormProps) {
  const { refetch } = useUserTours();
  const { mutate: createTour, isPending: isCreatingTour } = useCreateTour();
  const { mutate: updateTour, isPending: isUpdatingTour } = useUpdateTour();
  const { mutate: addPropertyToTour, isPending: isAddingProperty } = useAddPropertyToTour();
  const { mutate: removePropertyFromTour, isPending: isRemovingProperty } =
    useRemovePropertyFromTour();
  const { mutate: updateTourProperty, isPending: isUpdatingProperty } = useUpdateTourProperty();
  const updateTourStatus = useUpdateTourStatus();

  const form = useForm<TourFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: tour
      ? {
          name: tour.name,
          startDateTime: new Date(tour.startDateTime),
          note: tour.note || '',
          status: tour.status,
          properties: tour.tourProperties.map(tp => ({
            status: tp.status || 'Pending',
            property: tp.response,
            propertyId: tp.response.id,
            title: tp.response.title,
            startTime: extractTimeFromUTC(tp.visitTimeStart),
            endTime: extractTimeFromUTC(tp.visitTimeEnd),
            tourPropertyId: tp.id,
          })),
        }
      : {
          name: '',
          startDateTime: undefined,
          note: '',
          status: 'Pending',
          properties: initialProperties
            ? initialProperties.map(p => ({
                property: p,
                propertyId: p.id,
                title: p.title,
                startTime: '09:00',
                endTime: '10:00',
                status: 'Pending',
                tourPropertyId: undefined,
              }))
            : property
              ? [
                  {
                    property: property,
                    propertyId: property.id,
                    title: property.title,
                    startTime: '09:00',
                    endTime: '10:00',
                    status: 'Pending',
                    tourPropertyId: undefined,
                  },
                ]
              : [],
        },
  });

  const [savedPropertyIds, setSavedPropertyIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (tour) {
      const initialSavedIds = new Set(tour.tourProperties.map(tp => tp.response.id));
      setSavedPropertyIds(initialSavedIds);
    }
  }, [tour]);

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'properties',
  });

  const validatePropertyTimeSequence = (
    properties: TourFormValues['properties'],
    currentIndex: number,
    formInstance: UseFormReturn<TourFormValues>
  ): boolean => {
    formInstance.clearErrors(`properties.${currentIndex}.startTime`);
    formInstance.clearErrors(`properties.${currentIndex}.endTime`);

    const currentProperty = properties[currentIndex];
    const oneHourInMs = 60 * 60 * 1000;

    const singlePropValidationResult = singlePropertySchema.safeParse(currentProperty);
    if (!singlePropValidationResult.success) {
      singlePropValidationResult.error.errors.forEach(err => {
        const path = `properties.${currentIndex}.${err.path[0]}`;
        formInstance.setError(path as any, { type: err.code, message: err.message });
      });
      return false;
    }

    const currentTimeKey = `${currentProperty.startTime}-${currentProperty.endTime}`;
    for (let i = 0; i < properties.length; i++) {
      if (i === currentIndex) continue;
      const otherProp = properties[i];
      if (otherProp.startTime && otherProp.endTime) {
        const otherTimeKey = `${otherProp.startTime}-${otherProp.endTime}`;
        if (currentTimeKey === otherTimeKey) {
          formInstance.setError(`properties.${currentIndex}.startTime`, {
            type: 'manual',
            message: `Thời gian xem trùng lặp với bất động sản khác trong lịch trình.`,
          });
          return false;
        }
      }
    }

    if (currentIndex > 0) {
      const prevProperty = properties[currentIndex - 1];
      if (prevProperty.endTime && currentProperty.startTime) {
        const prevEndTime = new Date(`2000-01-01T${prevProperty.endTime}:00`);
        const currentStartTime = new Date(`2000-01-01T${currentProperty.startTime}:00`);
        if (currentStartTime.getTime() < prevEndTime.getTime() + oneHourInMs) {
          formInstance.setError(`properties.${currentIndex}.startTime`, {
            type: 'manual',
            message: `Bất động sản này phải bắt đầu ít nhất 1 giờ sau khi bất động sản trước kết thúc.`,
          });
          return false;
        }
      }
    }

    if (currentIndex < properties.length - 1) {
      const nextProperty = properties[currentIndex + 1];
      if (currentProperty.endTime && nextProperty.startTime) {
        const currentEndTime = new Date(`2000-01-01T${currentProperty.endTime}:00`);
        const nextStartTime = new Date(`2000-01-01T${nextProperty.startTime}:00`);
        if (nextStartTime.getTime() < currentEndTime.getTime() + oneHourInMs) {
          formInstance.setError(`properties.${currentIndex}.endTime`, {
            type: 'manual',
            message: `Thời gian kết thúc của bất động sản này phải trước ít nhất 1 giờ so với thời gian bắt đầu của bất động sản tiếp theo.`,
          });
          return false;
        }
      }
    }

    return true;
  };

  const handleSavePropertyToTour = (index: number) => {
    if (!tour) return;

    const tourStartDateTime = form.getValues('startDateTime');
    if (!tourStartDateTime) {
      form.setError('startDateTime', {
        message: 'Ngày bắt đầu lịch trình là bắt buộc để lưu bất động sản.',
      });
      return;
    }

    const currentProperties = form.getValues('properties');
    const isValid = validatePropertyTimeSequence(currentProperties, index, form);
    if (!isValid) {
      return;
    }

    const propertyData = currentProperties[index];

    const [startHour, startMinute] = propertyData.startTime.split(':').map(Number);
    const [endHour, endMinute] = propertyData.endTime.split(':').map(Number);

    // Tạo local date với giờ = 00:00:00
    const localDate = new Date(
      tourStartDateTime.getFullYear(),
      tourStartDateTime.getMonth(),
      tourStartDateTime.getDate()
    );

    const visitStart = new Date(localDate);
    visitStart.setHours(startHour, startMinute, 0, 0);

    const visitEnd = new Date(localDate);
    visitEnd.setHours(endHour, endMinute, 0, 0);

    const addPropertyPayload = {
      propertyId: propertyData.propertyId,
      visitTimeStart: visitStart.toISOString(),
      visitTimeEnd: visitEnd.toISOString(),
    };

    addPropertyToTour(
      {
        tourId: tour.id,
        property: addPropertyPayload,
      },
      {
        onSuccess: data => {
          setSavedPropertyIds(prev => new Set(Array.from(prev).concat(propertyData.propertyId)));
          form.setValue(`properties.${index}.tourPropertyId`, data.data);
          onSuccess();
          refetch();
          window.location.reload();
        },
      }
    );
  };

  const handleUpdateProperty = (index: number, tourPropertyId: string) => {
    if (!tour) return;

    const tourStartDateTime = form.getValues('startDateTime');
    if (!tourStartDateTime) {
      form.setError('startDateTime', {
        message: 'Ngày bắt đầu lịch trình là bắt buộc để cập nhật bất động sản.',
      });
      return;
    }

    const currentProperties = form.getValues('properties');
    const isValid = validatePropertyTimeSequence(currentProperties, index, form);
    if (!isValid) {
      return;
    }

    const propertyData = currentProperties[index];

    const [startHour, startMinute] = propertyData.startTime.split(':').map(Number);
    const [endHour, endMinute] = propertyData.endTime.split(':').map(Number);

    // Tạo local date với giờ = 00:00:00
    const localDate = new Date(
      tourStartDateTime.getFullYear(),
      tourStartDateTime.getMonth(),
      tourStartDateTime.getDate()
    );

    const visitStart = new Date(localDate);
    visitStart.setHours(startHour, startMinute, 0, 0);

    const visitEnd = new Date(localDate);
    visitEnd.setHours(endHour, endMinute, 0, 0);

    const updatePropertyPayload: Partial<UpdateTourPropertyRequest> = {
      visitTimeStart: visitStart.toISOString(),
      visitTimeEnd: visitEnd.toISOString(),
      propertyId: propertyData.propertyId,
    };

    updateTourProperty(
      {
        id: tourPropertyId,
        tour: updatePropertyPayload,
      },
      {
        onSuccess: () => {
          onSuccess();
          refetch();
        },
        onError: error => {
          console.error('Error updating property in tour backend:', error);
        },
      }
    );
  };

  const handleRemoveProperty = (index: number) => {
    const propertyData = form.getValues(`properties.${index}`);
    const isPropertyInTour = savedPropertyIds.has(propertyData.propertyId);

    if (tour && isPropertyInTour && propertyData.tourPropertyId) {
      removePropertyFromTour(
        {
          tourId: tour.id,
          propertyId: propertyData.tourPropertyId,
        },
        {
          onSuccess: () => {
            setSavedPropertyIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(propertyData.propertyId);
              return newSet;
            });
            remove(index);
            onSuccess();
            refetch();
          },
          onError: error => {
            console.error('Error removing property from tour backend:', error);
          },
        }
      );
    } else {
      remove(index);
    }
  };

  const handleCompleteTour = (tourPropertyId: string) => {
    updateTourStatus.mutate({ id: tourPropertyId, status: 'Completed' });
    window.location.reload();
  };

  const handleAddProperty = (prop: Property) => {
    let startTime = '09:00';
    let endTime = '10:00';

    const currentProperties = form.getValues('properties');
    console.log('Current properties in handleAddProperty:', currentProperties);

    // Tìm property cuối cùng có endTime hợp lệ
    const lastValidProperty = [...currentProperties].reverse().find(p => p.endTime);

    if (lastValidProperty?.endTime) {
      startTime = addOneHour(lastValidProperty.endTime);
      endTime = addOneHour(startTime); // Đảm bảo kéo dài 1 tiếng
    }

    console.log('Calculated startTime:', startTime, 'endTime:', endTime);

    append({
      property: prop,
      propertyId: prop.id,
      title: prop.title,
      startTime,
      endTime,
      status: 'Pending',
      tourPropertyId: undefined,
    });

    // Trigger validation để form re-render lại
    form.trigger('properties');
  };

  const onSubmit = async (values: TourFormValues) => {
    if (values.properties.length === 0) {
      form.setError('properties', {
        type: 'manual',
        message: 'Phải có ít nhất một bất động sản trong lịch trình.',
      });
      return;
    }

    try {
      const tourPropertiesPayload = values.properties.map(prop => {
        const [startHour, startMinute] = prop.startTime.split(':').map(Number);
        const [endHour, endMinute] = prop.endTime.split(':').map(Number);

        const startDate = values.startDateTime;

        // 🔧 Build visitStart local
        const visitStart = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
          startHour,
          startMinute,
          0
        );

        const visitEnd = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
          endHour,
          endMinute,
          0
        );

        return {
          propertyId: prop.property.id,
          visitTimeStart: visitStart.toISOString(), // 🟢 Vẫn dùng toISOString nhưng đã build chuẩn local
          visitTimeEnd: visitEnd.toISOString(),
          assignTo: prop.property.saler?.id || undefined,
        };
      });

      const rawDate = values.startDateTime;
      const startDateTime = new Date(
        Date.UTC(
          rawDate.getFullYear(),
          rawDate.getMonth(),
          rawDate.getDate(),
          0,
          0,
          0 // Hoặc bạn set 9, 0, 0 nếu muốn 09:00
        )
      ).toISOString();
      if (tour) {
        updateTour(
          {
            id: tour.id,
            tour: {
              name: values.name,
              startDateTime: startDateTime,
              note: values.note,
            },
          },
          {
            onSuccess: () => {
              onSuccess();
              refetch();
            },
          }
        );
      } else {
        createTour(
          {
            ...values,
            startDateTime: startDateTime,
            tourProperties: tourPropertiesPayload,
          },
          {
            onSuccess: () => {
              onSuccess();
              refetch();
            },
          }
        );
      }
    } catch (error) {
      console.error('Failed to submit tour:', error);
    }
  };

  const isLoading = isCreatingTour || isUpdatingTour;
  const submitButtonText = tour ? 'Cập nhật Lịch Trình' : 'Tạo Lịch Trình';
  const backButtonText = tour ? 'Quay lại chi tiết' : 'Quay lại danh sách';

  const existingPropertyIds = new Set(fields.map(field => field.propertyId));

  const isFormDisabled = isLoading || isAddingProperty || isRemovingProperty || isUpdatingProperty;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={onBack}
            disabled={isFormDisabled}
            className="flex items-center gap-2 bg-red-600 text-white hover:text-white hover:bg-red-700"
          >
            <ArrowLeft className="h-4 w-4" />
            {backButtonText}
          </Button>
        </div>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên lịch trình</FormLabel>
              <FormControl>
                <Input placeholder="Lịch trình xem nhà" {...field} disabled={isFormDisabled} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="startDateTime"
          render={({ field }) => (
            <FormItem className="flex flex-col items-left text-left">
              <FormLabel>Ngày bắt đầu lịch trình</FormLabel>
              <FormControl>
                <div className="flex justify-center">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={isFormDisabled}
                    className="rounded-md border shadow"
                    fromYear={2023}
                    toYear={2030}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="properties"
          render={({ fieldState }) => (
            <FormItem>
              <h3 className="mb-4 text-lg font-medium">Danh sách bất động sản trong lịch trình</h3>
              {fields.length === 0 && (
                <p className="text-muted-foreground mb-4 text-red-600">
                  Chưa có bất động sản nào được thêm vào lịch trình này.
                </p>
              )}
              {fields.map((item, index) => {
                const isPropertyInTour = savedPropertyIds.has(item.propertyId);
                const statusDisplay = getTourStatusDisplay(item.status);
                return (
                  <div
                    key={item.id}
                    className="mb-4 flex flex-col md:flex-row gap-4 rounded-md border p-4"
                  >
                    <div className="flex flex-1 flex-col md:flex-row gap-4">
                      <div className={`w-full ${!isDialog ? 'md:w-1/3' : 'md:w-2/3'}`}>
                        <PropertyCard property={item.property} size="sm" isHiddenDetail={true} />
                      </div>
                      <div className="flex-1 flex flex-col p-2">
                        <div>
                          <p className="font-semibold text-lg">
                            {item.title || `Bất động sản ID: ${item.propertyId}`}
                          </p>
                          <div className="space-y-1 mt-1"></div>
                        </div>
                        <div className="mt-4">
                          <p className="text-xl font-semibold">
                            {formatPrice(
                              item.property.transactionType === TransactionType.FOR_SALE
                                ? item.property.priceDetails.salePrice || 0
                                : item.property.priceDetails.rentalPrice || 0,
                              item.property.transactionType
                            )}
                          </p>
                          <div className="flex flex-col mt-2 text-foreground text-sm">
                            <div className="flex gap-4">
                              <div className="flex items-center">
                                <Bed className="size-4 mr-1" />
                                <span>{item.property.propertyDetails.bedrooms}</span>
                              </div>
                              <div className="flex items-center">
                                <Bath className="size-4 mr-1" />
                                <span>{item.property.propertyDetails.bathrooms}</span>
                              </div>
                              <div className="flex items-center">
                                <Maximize className="size-4 mr-1" />
                                <span>{item.property.propertyDetails.buildingArea} m²</span>
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground my-2">
                              {getPropertyTypeText(
                                item.property.type,
                                item.property.transactionType
                              )}
                            </div>
                          </div>
                          <div className="mt-1">
                            <div className="flex items-center text-xs text-muted-foreground">
                              <MapPin className="size-4 mr-1" />
                              <span>
                                {item.property.location?.city || 'Location unavailable'},{' '}
                                {item.property.location?.district}
                              </span>
                            </div>
                          </div>
                          <div className="mt-4">
                            <span className={statusDisplay.colorClass}>{statusDisplay.text}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-4 md:w-1/3">
                      <FormField
                        control={form.control}
                        name={`properties.${index}.startTime`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Thời gian bắt đầu</FormLabel>
                            <FormControl>
                              <Input
                                type="time"
                                {...field}
                                disabled={
                                  item.status === 'Approved' ||
                                  item.status === 'Completed' ||
                                  item.status === 'Rejected' ||
                                  isFormDisabled
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`properties.${index}.endTime`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Thời gian kết thúc</FormLabel>
                            <FormControl>
                              <Input
                                type="time"
                                {...field}
                                disabled={
                                  item.status === 'Approved' ||
                                  item.status === 'Completed' ||
                                  item.status === 'Rejected' ||
                                  isFormDisabled
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="flex gap-2">
                        {item.status === 'Approved' && (
                          <Button
                            type="button"
                            variant="default"
                            size="sm"
                            onClick={() => handleCompleteTour(item.tourPropertyId || '')}
                            className={`bg-blue-200 text-blue-700 hover:bg-blue-200 disabled:opacity-50 cursor-pointer`}
                          >
                            Hoàn thành
                          </Button>
                        )}
                        {tour && item.status !== 'Completed' && item.status !== 'Rejected' && (
                          <>
                            {!isPropertyInTour && (
                              <Button
                                type="button"
                                variant="default"
                                size="sm"
                                onClick={() => handleSavePropertyToTour(index)}
                                disabled={isFormDisabled}
                                className={`flex items-center gap-1 bg-blue-600 text-white hover:bg-blue-700`}
                              >
                                {isAddingProperty ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Save className="h-4 w-4" />
                                )}
                                Lưu vào lịch trình
                              </Button>
                            )}
                            {isPropertyInTour && item.status === 'Pending' && (
                              <Button
                                type="button"
                                variant="default"
                                size="sm"
                                onClick={() =>
                                  handleUpdateProperty(index, item.tourPropertyId || '')
                                }
                                disabled={isFormDisabled || isUpdatingProperty}
                                className={`flex items-center gap-1 bg-yellow-600 text-white hover:bg-yellow-700`}
                              >
                                {isUpdatingProperty ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Save className="h-4 w-4" />
                                )}
                                Cập nhậtt
                              </Button>
                            )}
                          </>
                        )}
                        {fields.length > 0 &&
                          item.status !== 'Completed' &&
                          item.status !== 'Rejected' && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  disabled={isFormDisabled}
                                  aria-label={`Xóa bất động sản #${index + 1}`}
                                  className="flex items-center gap-1"
                                >
                                  {isRemovingProperty ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                  Xóa
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Xác nhận xóa bất động sản</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Bạn có chắc chắn muốn xóa bất động sản {item.title} khỏi lịch
                                    trình?{' '}
                                    {isPropertyInTour && tour && (
                                      <span className="font-medium text-red-600">
                                        Bất động sản này sẽ được xóa khỏi tour và không thể hoàn
                                        tác.
                                      </span>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel disabled={isRemovingProperty}>
                                    Hủy
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleRemoveProperty(index)}
                                    disabled={isRemovingProperty}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    {isRemovingProperty ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Đang xóa...
                                      </>
                                    ) : (
                                      'Xóa'
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                      </div>
                    </div>
                  </div>
                );
              })}
              <div className="mt-6">
                <h3 className="mb-4 text-lg font-medium">Thêm bất động sản mới</h3>
                <PropertySearchAndSelect
                  onAddProperty={handleAddProperty}
                  existingPropertyIds={existingPropertyIds}
                  disabled={isFormDisabled}
                />
              </div>
              {fieldState.error && <FormMessage>{fieldState.error.message}</FormMessage>}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ghi chú (Tùy chọn)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Ghi chú thêm về lịch trình..."
                  {...field}
                  disabled={isFormDisabled}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          className="bg-red-600 text-white hover:bg-red-700"
          type="submit"
          disabled={isFormDisabled}
        >
          {isFormDisabled && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {submitButtonText}
        </Button>
      </form>
    </Form>
  );
}
