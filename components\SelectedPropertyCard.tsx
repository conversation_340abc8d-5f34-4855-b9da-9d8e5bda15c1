import { X } from 'lucide-react';
import Image from 'next/image';
import { Property } from '@/lib/api/services/fetchProperty';

interface SelectedPropertyCardProps {
  property: Property;
  onRemove: (id: string) => void;
}

export default function SelectedPropertyCard({ property, onRemove }: SelectedPropertyCardProps) {
  return (
    <div
      key={property.id}
      className="relative flex-shrink-0 w-32 h-24 p-2 border-2 border-gray-300 rounded-lg bg-white flex flex-col items-center justify-center"
    >
      <button
        onClick={() => onRemove(property.id)}
        className="absolute -top-1 -right-1 bg-gray-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-gray-600"
      >
        <X className="w-3 h-3" />
      </button>
      <div className="flex flex-col items-center gap-2">
        <div className="relative w-16 h-16">
          <Image
            src={property.imageUrls[0] || '/placeholder.svg'}
            alt={property.title}
            fill
            className="object-cover rounded"
          />
        </div>
        <p className="text-xs text-center line-clamp-1 leading-tight">{property.title}</p>
      </div>
    </div>
  );
}
