'use client';

import * as React from 'react';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { autocomplete } from '@/lib/google';
import { PlaceData } from '@googlemaps/google-maps-services-js';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

type TransactionType = 'ForSale' | 'ForRent';

export default function SearchAutocompleteLocation() {
  const [open, setOpen] = useState(false);
  const [predictions, setPredictions] = useState<PlaceData[]>([]);
  const [input, setInput] = useState('');
  const [transactionType, setTransactionType] = useState<TransactionType>('ForSale');
  const router = useRouter();

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(open => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  useEffect(() => {
    const fetchPredictions = async () => {
      if (input.trim()) {
        const predictions = await autocomplete(input);
        setPredictions(predictions as PlaceData[]);
      } else {
        setPredictions([]);
      }
    };

    const debounceTimer = setTimeout(() => {
      fetchPredictions();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [input]);

  const handleSelect = async (prediction: PlaceData) => {
    const searchTerm = prediction.formatted_address || '';
    setInput(searchTerm);
    setOpen(false);

    // Use coordinates directly from the prediction since they're already available
    if (prediction.geometry?.location) {
      const location = prediction.geometry.location;

      // Try to extract lat and lng safely
      let lat: number | undefined;
      let lng: number | undefined;

      if (location.lat !== undefined) {
        lat = typeof location.lat === 'number' ? location.lat : parseFloat(location.lat);
      }
      if (location.lng !== undefined) {
        lng = typeof location.lng === 'number' ? location.lng : parseFloat(location.lng);
      }

      // Ensure lat and lng are valid numbers
      if (lat !== undefined && lng !== undefined && !isNaN(lat) && !isNaN(lng)) {
        // For autocomplete selection, we only set the center coordinates
        // The map will create its own bounds based on the zoom level
        const params = new URLSearchParams({
          lat: lat.toString(),
          lng: lng.toString(),
          zoom: '14', // Set a reasonable default zoom for search results
          transactionType,
        });

        router.push(`/properties?${params.toString()}`);
      } else {
        router.push(
          `/properties?searchTerm=${encodeURIComponent(searchTerm)}&transactionType=${transactionType}`
        );
      }
    } else {
      router.push(
        `/properties?searchTerm=${encodeURIComponent(searchTerm)}&transactionType=${transactionType}`
      );
    }
  };

  return (
    <>
      <div className="relative">
        <Input
          placeholder="Tìm kiếm địa điểm..."
          value={input}
          onChange={e => setInput(e.target.value)}
          onClick={() => setOpen(true)}
          className="cursor-pointer md:placeholder:text-base placeholder:text-sm md:text-base text-sm"
        />
        {/* <kbd className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
          <span className="text-xs">⌘</span>K
        </kbd> */}
      </div>
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput placeholder="Tìm kiếm địa điểm..." value={input} onValueChange={setInput} />

        {/* Transaction Type Buttons */}
        <div className="flex gap-2 p-3 border-b">
          <Button
            variant={transactionType === 'ForSale' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTransactionType('ForSale')}
            className={cn(
              'flex-1 text-sm',
              transactionType === 'ForSale'
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'border-gray-300 hover:bg-gray-50'
            )}
          >
            Mua
          </Button>
          <Button
            variant={transactionType === 'ForRent' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTransactionType('ForRent')}
            className={cn(
              'flex-1 text-sm',
              transactionType === 'ForRent'
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'border-gray-300 hover:bg-gray-50'
            )}
          >
            Thuê
          </Button>
        </div>

        <CommandList className="">
          <CommandEmpty>Hãy nhập địa điểm bạn muốn tìm kiếm.</CommandEmpty>
          {predictions.length > 0 && (
            <CommandGroup heading="Địa điểm">
              {predictions.map(prediction => (
                <CommandItem key={prediction.place_id} onSelect={() => handleSelect(prediction)}>
                  {prediction.formatted_address}
                </CommandItem>
              ))}
            </CommandGroup>
          )}
        </CommandList>
      </CommandDialog>
    </>
  );
}
