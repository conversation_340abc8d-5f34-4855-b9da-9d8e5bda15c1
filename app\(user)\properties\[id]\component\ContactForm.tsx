import React from 'react';
import { Property, ApartmentOrientation } from '@/lib/api/services/fetchProperty';
import ContactFormComponent from '@/components/ContactFormComponent';

interface ContactFormProps {
  property: Property;
}

export default function ContactForm({ property }: ContactFormProps) {
  const propertyInfo = {
    id: property.id,
    name: property.title,
    code: property.code,
    address: property.location.address,
    contactName: property.contactName,
    contactPhone: property.contactPhone,
    contactEmail: property.contactEmail,
    propertyDetails: {
      ...property.propertyDetails,
      bedrooms: property.propertyDetails?.bedrooms || 0,
      bathrooms: property.propertyDetails?.bathrooms || 0,
      livingRooms: property.propertyDetails?.livingRooms || 0,
      kitchens: property.propertyDetails?.kitchens || 0,
      landArea: property.propertyDetails?.landArea || 0,
      landWidth: property.propertyDetails?.landWidth || 0,
      landLength: property.propertyDetails?.landLength || 0,
      buildingArea: property.propertyDetails?.buildingArea || 0,
      numberOfFloors: property.propertyDetails?.numberOfFloors || 0,
      hasBasement: property.propertyDetails?.hasBasement || false,
      floorNumber: property.propertyDetails?.floorNumber || 0,
      apartmentOrientation:
        property.propertyDetails?.apartmentOrientation || ApartmentOrientation.NORTH,
      furnished: property.propertyDetails?.furnished || false,
    },
    type: property.type,
    bedrooms: property.propertyDetails?.bedrooms || 0,
    bathrooms: property.propertyDetails?.bathrooms || 0,
    landArea: property.propertyDetails?.landArea || 0,
    imageUrls: property.imageUrls,
    yearBuilt: property.yearBuilt,
    status: property.status,
  };

  return <ContactFormComponent propertyInfo={propertyInfo} isPropertyDetail={true} />;
}
