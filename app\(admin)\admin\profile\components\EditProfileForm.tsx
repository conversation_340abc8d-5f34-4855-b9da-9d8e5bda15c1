import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateProfile } from '@/hooks/useUsers';
import { Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ProfileFormData {
  fullName: string;
  email: string;
  phoneNumber: string;
  about: string;
  userName: string;
  birthdate?: string;
  status?: string;
  avatarFile?: File;
}

interface EditProfileFormProps {
  profile: ProfileFormData;
  onSuccess: () => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Form validation schema
const profileFormSchema = z.object({
  fullName: z
    .string()
    .min(2, { message: 'Name must be at least 2 characters.' })
    .max(50, { message: 'Name must not be longer than 50 characters.' }),
  userName: z
    .string()
    .min(3, { message: 'Username must be at least 3 characters.' })
    .max(30, { message: 'Username must not be longer than 30 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  phoneNumber: z
    .string()
    .min(5, { message: 'Phone number must be at least 5 characters.' })
    .max(15, { message: 'Phone number must not be longer than 15 characters.' })
    .optional()
    .or(z.literal('')),
  about: z
    .string()
    .max(1000, { message: 'Bio must not be longer than 500 characters.' })
    .optional()
    .or(z.literal('')),
  birthDay: z.string().optional(),
  birthMonth: z.string().optional(),
  birthYear: z.string().optional(),
  status: z.enum(['Online', 'Idle', 'DoNotDisturb', 'Invisible']).optional(),
});

export function EditProfileForm({
  profile,
  onSuccess,
  onCancel,
  isSubmitting,
}: EditProfileFormProps) {
  const { mutate: updateProfile, isPending } = useUpdateProfile();

  // Parse birthdate string to day, month, year
  let birthDay = '';
  let birthMonth = '';
  let birthYear = '';

  if (profile.birthdate) {
    try {
      const date = new Date(profile.birthdate);
      if (!isNaN(date.getTime())) {
        birthDay = date.getDate().toString();
        birthMonth = (date.getMonth() + 1).toString();
        birthYear = date.getFullYear().toString();
      }
    } catch (error) {
      // Keep empty values if parsing fails
    }
  }

  // Create form
  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: profile.fullName || '',
      userName: profile.userName || '',
      email: profile.email || '',
      phoneNumber: profile.phoneNumber || '',
      about: profile.about || '',
      birthDay: birthDay,
      birthMonth: birthMonth,
      birthYear: birthYear,
      status: (profile.status as 'Online' | 'Idle' | 'DoNotDisturb' | 'Invisible') || 'Online',
    },
  });

  // Form submission handler
  const onSubmit = async (values: z.infer<typeof profileFormSchema>) => {
    // Create birthDate from separate fields
    let birthDate = '';
    if (values.birthDay && values.birthMonth && values.birthYear) {
      try {
        const date = new Date(
          parseInt(values.birthYear),
          parseInt(values.birthMonth) - 1,
          parseInt(values.birthDay)
        );
        if (!isNaN(date.getTime())) {
          birthDate = date.toISOString();
        }
      } catch (error) {
        // Keep empty if invalid date
      }
    }

    // Create profile data object for API
    const profileData = {
      userName: values.userName,
      fullName: values.fullName,
      email: values.email,
      phoneNumber: values.phoneNumber || '',
      about: values.about || '',
      birthdate: birthDate,
      status: values.status || 'Online',
    };

    updateProfile(profileData, {
      onSuccess: data => {
        if (data.status) {
          // Force delay để đảm bảo server đã lưu dữ liệu
          setTimeout(() => {
            onSuccess();
          }, 1000);
        }
      },
      onError: () => {},
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="User" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="user" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="+****************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Ngày sinh - 3 select đơn giản */}
        <div className="space-y-2">
          <FormLabel>Ngày sinh</FormLabel>
          <div className="grid grid-cols-3 gap-3">
            <FormField
              control={form.control}
              name="birthDay"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Ngày" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <SelectItem key={day} value={day.toString()}>
                          {day}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="birthMonth"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Tháng" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {[
                        'Tháng 1',
                        'Tháng 2',
                        'Tháng 3',
                        'Tháng 4',
                        'Tháng 5',
                        'Tháng 6',
                        'Tháng 7',
                        'Tháng 8',
                        'Tháng 9',
                        'Tháng 10',
                        'Tháng 11',
                        'Tháng 12',
                      ].map((month, index) => (
                        <SelectItem key={index + 1} value={(index + 1).toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="birthYear"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Năm" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i).map(
                        year => (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <FormField
          control={form.control}
          name="about"
          render={({ field }) => (
            <FormItem>
              <FormLabel>About</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Tell us a bit about yourself"
                  className="min-h-32"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-3 justify-end pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isPending}
          >
            Hủy
          </Button>
          <Button type="submit" disabled={isSubmitting || isPending}>
            {isSubmitting && isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang lưu
              </>
            ) : (
              'Lưu thay đổi'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
