'use client';

import { ContractItem } from '@/lib/api/services/fetchContract';
import { ColumnDef } from '@tanstack/react-table';
import ContractActionCell from './ContractActionCell'; // Adjust the import path as necessary

export const columns: ColumnDef<ContractItem>[] = [
  {
    accessorKey: 'property.name',
    header: () => <div className="text-left font-semibold ">Tên bất động sản</div>,
    cell: ({ row }) => <div className="text-left ">{row.original.property.name}</div>,
  },
  {
    accessorKey: 'contractType',
    header: () => <div className="text-left font-semibold "><PERSON><PERSON><PERSON> hợp đồng</div>,
    cell: ({ row }) => <div className="text-left ">{row.original.contractType}</div>,
  },
  {
    accessorKey: 'pdfContractUrls',
    header: () => <div className="text-left font-semibold ">PDF URLs</div>,
    cell: ({ row }) => {
      const urls = row.original.pdfContractUrls;
      if (!urls || urls.length === 0) {
        return <div className=" italic">N/A</div>;
      }
      return (
        <div className="flex flex-col gap-1">
          {urls.map((url, index) => (
            <a
              key={index}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className=" hover:underline truncate max-w-[200px] block"
              title={url} // Shows full URL on hover
            >
              {'View PDF'} {/* Display filename or a default "View PDF" */}
            </a>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left font-semibold ">Trạng thái</div>,
    cell: ({ row }) => <div className="text-left ">{row.original.status}</div>,
  },
  {
    id: 'actions',
    header: () => <div className="text-center font-semibold ">Hành động</div>,
    cell: ({ row }) => {
      const contract = row.original;
      return (
        <div className="flex justify-center items-center">
          <ContractActionCell contract={contract} />
        </div>
      );
    },
  },
];
