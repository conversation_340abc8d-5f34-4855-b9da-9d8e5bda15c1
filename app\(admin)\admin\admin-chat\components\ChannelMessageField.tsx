import { Search, X, Filter } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
// import { Status } from './StatusBadge';
import { Select, SelectContent, SelectTrigger } from '@/components/ui/select';
import { getCookie } from 'cookies-next';
import { jwtDecode } from 'jwt-decode';
import { signalRService } from '@/lib/realtime/signalR';
import MessageFieldCard from './MessageFieldCard';

interface CustomJwtPayload {
  UserID: string;
  [key: string]: string | number | boolean;
}

interface Props {
  platform: string;
  conversations: {
    id: string;
    lastMessage: string;
    lastUpdated: string;
    platform: string;
    platformUser: {
      id: string;
      name: string;
      avatarUrl: string;
    } | null;
  }[];

  setChatUser: React.Dispatch<
    React.SetStateAction<{ id: string; name: string; avatarUrl: string }>
  >;
  setSelectedConversation: React.Dispatch<React.SetStateAction<string>>;
  selectedConversation: string;
}

export default function ChannelMessageField(props: Props) {
  const [tag, setTag] = useState<string>('all');
  const [sort, setSort] = useState<string>('all');
  const [userId, setUserId] = useState<string>('');
  const { conversations, setChatUser, setSelectedConversation, selectedConversation } = props;

  useEffect(() => {
    const token = getCookie('auth-token') + '';
    const decodedToken = jwtDecode<CustomJwtPayload>(token);
    if (decodedToken) {
      setUserId(decodedToken.UserID);
    }
  }, []);

  const getActiveFiltersCount = () => {
    let count = 0;
    if (tag !== 'all') count++;
    if (sort !== 'all') count++;
    return count;
  };

  const handleJoinConversation = async (sessionId: string, userId: string) => {
    if (!sessionId || !userId) {
      console.error('Invalid sessionId or userId');
      return;
    }

    try {
      setSelectedConversation(sessionId);
      await signalRService.joinConversation(sessionId, userId);
      // Leave current conversation if exists
      if (selectedConversation) {
        try {
          await signalRService.leaveConversation(selectedConversation);
        } catch (error) {
          console.error('Failed to leave conversation:', error);
        }
      }

      // Join new conversation
      setSelectedConversation(sessionId);
      await signalRService.joinConversation(sessionId, userId);
    } catch (error) {
      console.error('Failed to join conversation:', error);
      // Reset selected conversation on error
      setSelectedConversation('');
      throw error;
    }
  };

  return (
    <div className="w-full h-full border rounded-lg shadow-md bg-white/100 hover:shadow-lg transition-all duration-300">
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center gap-1">
          <h2 className="text-[20px] font-semibold text-blue-800">Messages</h2>
        </div>
      </div>

      <div className="px-4 pb-4">
        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search name"
              className="pl-9 bg-gray-50/50 border-gray-200 focus-visible:ring-blue-500 rounded-full h-9 text-sm"
            />
          </div>

          {/* Combined Filter */}
          <div className="relative flex-shrink-0">
            <Select>
              <SelectTrigger className="h-9 bg-gray-50/50 border-gray-200 rounded-full text-sm pr-8 pl-3 flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span>Filter</span>
                {getActiveFiltersCount() > 0 && (
                  <span className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full">
                    {getActiveFiltersCount()}
                  </span>
                )}
              </SelectTrigger>
              <SelectContent>
                <div className="p-2">
                  <div className="mb-2">
                    <div className="text-xs font-medium text-gray-500 mb-1 px-2">Tag</div>
                    <div className="space-y-1">
                      <button
                        onClick={() => setTag('all')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          tag === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        All tag
                      </button>
                      <button
                        onClick={() => setTag('hot')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          tag === 'hot' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Hot
                      </button>
                      <button
                        onClick={() => setTag('warm')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          tag === 'warm' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Warm
                      </button>
                      <button
                        onClick={() => setTag('cold')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          tag === 'cold' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Cold
                      </button>
                    </div>
                  </div>
                  <div className="border-t pt-2">
                    <div className="text-xs font-medium text-gray-500 mb-1 px-2">Sort by</div>
                    <div className="space-y-1">
                      <button
                        onClick={() => setSort('all')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          sort === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        All time
                      </button>
                      <button
                        onClick={() => setSort('newest')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          sort === 'newest' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Newest first
                      </button>
                      <button
                        onClick={() => setSort('oldest')}
                        className={`w-full text-left px-2 py-1.5 text-sm rounded-md hover:bg-gray-100 ${
                          sort === 'oldest' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                        }`}
                      >
                        Oldest first
                      </button>
                    </div>
                  </div>
                </div>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {(tag !== 'all' || sort !== 'all') && (
          <div className="flex flex-wrap gap-2 mt-3">
            {tag !== 'all' && (
              <div className="flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm">
                <span>{tag}</span>
                <button
                  onClick={() => setTag('all')}
                  className="hover:bg-blue-100 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
            {sort !== 'all' && (
              <div className="flex items-center gap-1 px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm">
                <span>{sort === 'newest' ? 'Newest first' : 'Oldest first'}</span>
                <button
                  onClick={() => setSort('all')}
                  className="hover:bg-blue-100 rounded-full p-0.5"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="max-h-[calc(100vh-185px)] overflow-y-auto px-4 w-full">
        <div className="w-full">
          {conversations.map(msg => (
            <div
              key={msg.id}
              onClick={() => {
                handleJoinConversation(msg.id, userId);
                setChatUser({
                  id: msg.platformUser?.id || 'anonymous',
                  name: msg.platformUser?.name || 'Anonymous',
                  avatarUrl: msg.platformUser?.avatarUrl || '',
                });
              }}
              className="w-full"
            >
              <MessageFieldCard
                key={msg.id}
                id={msg.id}
                lastMessage={msg.lastMessage}
                lastUpdated={msg.lastUpdated}
                platform={msg.platform}
                platformUser={
                  msg.platformUser || { id: 'anonymous', name: 'Anonymous', avatarUrl: '' }
                }
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
