import apiService, { RequestParams } from '../core';

export enum PropertyStatus {
  AVAILABLE = 'Available',
  PENDING = 'Pending',
  SOLD = 'Sold',
  RENTED = 'Rented',
}

export enum PropertyType {
  APARTMENT = 'Apartment',
  MINI_SERVICE_APARTMENT = 'MiniServiceApartment',
  COMMERCIAL_TOWNHOUSE = 'CommercialTownhouse',
  MOTEL = 'Motel',
  AIRBNB = 'Airbnb',
  HOUSE = 'House',
  TOWNHOUSE = 'Townhouse',
  VILLA = 'Villa',
  SHOP_HOUSE = 'ShopHouse',
  LAND_PLOT = 'LandPlot',
  PROJECT_LAND = 'ProjectLand',
  OFFICE = 'Office',
  WAREHOUSE = 'Warehouse',
  FACTORY = 'Factory',
  INDUSTRIAL = 'Industrial',
  HOTEL = 'Hotel',
  SOCIAL_HOUSING = 'SocialHousing',
  NEW_URBAN_AREA = 'NewUrbanArea',
  ECO_RESORT = 'EcoResort',
  OTHER = 'Other',
}

export enum TransactionType {
  FOR_RENT = 'ForRent',
  FOR_SALE = 'ForSale',
  PROJECT = 'Project',
}

export enum PropertyDetailFilters {
  hasBasement = 'hasBasement',
  furnished = 'furnished',
}

// export interface Owner {
//   id: string;
//   name: string;
//   phone: string;
//   email: string;
//   address: string;
//   gender: string;
//   nationality: string;
// }

export interface Location {
  address: string;
  city: string;
  district: string;
  ward: string;
  latitude: number;
  longitude: number;
}

export enum ApartmentOrientation {
  NORTH = 'North',
  SOUTH = 'South',
  EAST = 'East',
  WEST = 'West',
  NORTHEAST = 'NorthEast',
  NORTHWEST = 'NorthWest',
  SOUTHEAST = 'SouthEast',
  SOUTHWEST = 'SouthWest',
}

export interface PropertyDetail {
  bedrooms?: number;
  bathrooms?: number;
  livingRooms?: number;
  kitchens?: number;
  landArea?: number;
  landWidth?: number;
  landLength?: number;
  buildingArea?: number;
  numberOfFloors?: number;
  hasBasement?: boolean;
  floorNumber?: number;
  apartmentOrientation?: ApartmentOrientation;
  furnished?: boolean;
}

export interface PropertyInfo {
  id: string;
  name: string;
  code: string;
  address: string;
  imageUrls: string[];
  yearBuilt: number;
  propertyDetails?: {
    bedrooms?: number;
    bathrooms?: number;
    landArea?: number;
    livingRooms?: number;
    kitchens?: number;
    landWidth?: number;
    landLength?: number;
    buildingArea?: number;
    numberOfFloors?: number;
    floorNumber?: number;
  };
  type?: string;

  status: PropertyStatus;
}

export enum Currency {
  VND = 'VND',
  USD = 'USD',
}

export enum PaymentMethod {
  CASH = 'Cash',
  BANK_TRANSFER = 'Bank Transfer',
  CREDIT_CARD = 'Credit Card',
  PAYPAL = 'Paypal',
}

export interface PriceDetail {
  salePrice?: number;
  rentalPrice?: number;
  pricePerSquareMeter?: number;
  currency: Currency;
  depositAmount?: number;
  maintenanceFee: number;
  paymentMethods: string[];
}

export interface Amenity {
  parking: boolean;
  elevator: boolean;
  swimmingPool: boolean;
  gym: boolean;
  securitySystem: boolean;
  airConditioning: boolean;
  balcony: boolean;
  garden: boolean;
  playground: boolean;
  backupGenerator: boolean;
}

//TODO: fixin field transaction history
export interface TransactionHistory {
  id: string;
  transactionDate: string;
  transactionType: string;
  price: number;
  buyer: Buyer;
  seller: Saler;
}

export interface Buyer {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
}

export interface Saler {
  id: string;
  fullName: string;
  email: string;
  avatar?: string;
  phoneNumber: string;
}

export interface Video {
  videoUrl: string;
  title: string;
  description: string;
}

export interface Property {
  id: string;
  saler: Saler;
  title: string;
  name: string;
  description: string;
  transactionType: TransactionType;
  type: PropertyType;
  status: PropertyStatus;
  adminNote?: string;
  code: string;
  // owner: Owner;
  location: Location;
  propertyDetails: PropertyDetail;
  priceDetails: PriceDetail;
  amenities: Amenity;
  imageUrls: string[];
  floorPlanUrls: string[];
  videoUrls?: string[];
  video?: Video;
  yearBuilt: number;
  legalDocumentUrls: string[];
  transactionHistory?: TransactionHistory[];
  isFeatured: boolean;
  isVerified: boolean;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  createdAt: string;
  updatedAt: string;
  isFavorite: boolean;
  isViewed: boolean;
}

// Property search parameters
export interface PropertySearchParams {
  searchTerm?: string;
  pageNumber?: number;
  pageSize?: number;
  isDescending?: boolean;
  status?: PropertyStatus;
  transactionType?: TransactionType;
  type?: PropertyType;
  isFeatured?: boolean;
  isVerified?: boolean;

  // Geographic filters
  latitude?: number;
  longitude?: number;
  maxRadius?: number;
  hasLocationCoordinates?: boolean;
  swLatitude?: number;
  neLatitude?: number;
  swLongitude?: number;
  neLongitude?: number;

  // Property detail filters
  propertyDetailFilters?: PropertyDetailFilters | PropertyDetailFilters[];
  amenityFilters?: string | string[];

  // Bedroom filters
  bedrooms?: number;
  minBedrooms?: number;
  maxBedrooms?: number;

  // Bathroom filters
  bathrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;

  // Living room filters
  livingRooms?: number;
  minLivingRooms?: number;
  maxLivingRooms?: number;

  // Kitchen filters
  kitchens?: number;
  minKitchens?: number;
  maxKitchens?: number;

  // Land area filters
  minLandArea?: number;
  maxLandArea?: number;

  // Land width filters
  landWidth?: number;
  minLandWidth?: number;
  maxLandWidth?: number;

  // Land length filters
  landLength?: number;
  minLandLength?: number;
  maxLandLength?: number;

  // Building area filters
  buildingArea?: number;
  minBuildingArea?: number;
  maxBuildingArea?: number;

  // Floor filters
  numberOfFloors?: number;
  minNumberOfFloors?: number;
  maxNumberOfFloors?: number;

  // Floor number filters
  floorNumber?: number;
  minFloorNumber?: number;
  maxFloorNumber?: number;

  // Orientation and features
  apartmentOrientation?: ApartmentOrientation;

  // Price filters
  minPrice?: number;
  maxPrice?: number;

  // Sorting
  sortBy?: string;
}

export interface PropertyDetailRequest {
  title: string;
  name: string;
  description: string;
  transactionType: TransactionType;
  type: PropertyType;
  status: PropertyStatus;
  adminNote?: string;
  code: string;
  // ownerId: string;
  location: Location;
  propertyDetails: PropertyDetail;
  priceDetails: PriceDetail;
  amenities: Amenity;
  images: string[];
  yearBuilt: number;
  floorPlans: string[];
  video?: Video;
  legalDocuments: string[];
  transactionId?: string;
}

// API response types
export interface PropertyResponse {
  code: string;
  status: boolean;
  message: string;
  data: {
    count: number;
    limit: number;
    page: number;
    totalPages: number;
    properties: Property[];
  };
}

export interface PropertyDetailResponse {
  code: string;
  status: boolean;
  message: string;
  data: Property;
}

export interface ActionPropertyResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

export interface VerifyPropertyRequest {
  propertyId: string;
}

export interface VerifyPropertyResponse {
  code: string;
  status: boolean;
  message: string;
  data?: string;
}

// Convert PropertySearchParams to RequestParams
const convertPropertyFilters = (filters?: PropertySearchParams): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};

  // Basic parameters
  if (filters.searchTerm) params.searchTerm = filters.searchTerm;
  if (filters.pageNumber !== undefined) params.pageNumber = filters.pageNumber;
  if (filters.pageSize !== undefined) params.pageSize = filters.pageSize;
  if (filters.isDescending !== undefined) params.isDescending = filters.isDescending;
  if (filters.sortBy) params.sortBy = filters.sortBy;
  if (filters.status) params.status = filters.status;
  if (filters.type) params.type = filters.type;
  if (filters.transactionType) params.transactionType = filters.transactionType;
  if (filters.isFeatured !== undefined) params.isFeatured = filters.isFeatured;
  if (filters.isVerified !== undefined) params.isVerified = filters.isVerified;

  // Geographic filters
  if (filters.latitude !== undefined) params.latitude = filters.latitude;
  if (filters.longitude !== undefined) params.longitude = filters.longitude;
  if (filters.maxRadius !== undefined) params.maxRadius = filters.maxRadius;
  if (filters.hasLocationCoordinates !== undefined)
    params.hasLocationCoordinates = filters.hasLocationCoordinates;
  if (filters.swLatitude !== undefined) params.swLatitude = filters.swLatitude;
  if (filters.neLatitude !== undefined) params.neLatitude = filters.neLatitude;
  if (filters.swLongitude !== undefined) params.swLongitude = filters.swLongitude;
  if (filters.neLongitude !== undefined) params.neLongitude = filters.neLongitude;

  // Price filters
  if (filters.minPrice !== undefined) params.minPrice = filters.minPrice;
  if (filters.maxPrice !== undefined) params.maxPrice = filters.maxPrice;

  // Bedroom filters
  if (filters.bedrooms !== undefined) params.bedrooms = filters.bedrooms;
  if (filters.minBedrooms !== undefined) params.minBedrooms = filters.minBedrooms;
  if (filters.maxBedrooms !== undefined) params.maxBedrooms = filters.maxBedrooms;

  // Bathroom filters
  if (filters.bathrooms !== undefined) params.bathrooms = filters.bathrooms;
  if (filters.minBathrooms !== undefined) params.minBathrooms = filters.minBathrooms;
  if (filters.maxBathrooms !== undefined) params.maxBathrooms = filters.maxBathrooms;

  // Living room filters
  if (filters.livingRooms !== undefined) params.livingRooms = filters.livingRooms;
  if (filters.minLivingRooms !== undefined) params.minLivingRooms = filters.minLivingRooms;
  if (filters.maxLivingRooms !== undefined) params.maxLivingRooms = filters.maxLivingRooms;

  // Kitchen filters
  if (filters.kitchens !== undefined) params.kitchens = filters.kitchens;
  if (filters.minKitchens !== undefined) params.minKitchens = filters.minKitchens;
  if (filters.maxKitchens !== undefined) params.maxKitchens = filters.maxKitchens;

  // Land area filters
  if (filters.minLandArea !== undefined) params.minLandArea = filters.minLandArea;
  if (filters.maxLandArea !== undefined) params.maxLandArea = filters.maxLandArea;

  // Land width filters
  if (filters.landWidth !== undefined) params.landWidth = filters.landWidth;
  if (filters.minLandWidth !== undefined) params.minLandWidth = filters.minLandWidth;
  if (filters.maxLandWidth !== undefined) params.maxLandWidth = filters.maxLandWidth;

  // Land length filters
  if (filters.landLength !== undefined) params.landLength = filters.landLength;
  if (filters.minLandLength !== undefined) params.minLandLength = filters.minLandLength;
  if (filters.maxLandLength !== undefined) params.maxLandLength = filters.maxLandLength;

  // Building area filters
  if (filters.buildingArea !== undefined) params.buildingArea = filters.buildingArea;
  if (filters.minBuildingArea !== undefined) params.minBuildingArea = filters.minBuildingArea;
  if (filters.maxBuildingArea !== undefined) params.maxBuildingArea = filters.maxBuildingArea;

  // Floor filters
  if (filters.numberOfFloors !== undefined) params.numberOfFloors = filters.numberOfFloors;
  if (filters.minNumberOfFloors !== undefined) params.minNumberOfFloors = filters.minNumberOfFloors;
  if (filters.maxNumberOfFloors !== undefined) params.maxNumberOfFloors = filters.maxNumberOfFloors;

  // Floor number filters
  if (filters.floorNumber !== undefined) params.floorNumber = filters.floorNumber;
  if (filters.minFloorNumber !== undefined) params.minFloorNumber = filters.minFloorNumber;
  if (filters.maxFloorNumber !== undefined) params.maxFloorNumber = filters.maxFloorNumber;

  // Orientation
  if (filters.apartmentOrientation) params.apartmentOrientation = filters.apartmentOrientation;

  // Handle array parameters
  if (filters.propertyDetailFilters) {
    params.propertyDetailFilters = Array.isArray(filters.propertyDetailFilters)
      ? filters.propertyDetailFilters
      : [filters.propertyDetailFilters];
  }
  if (filters.amenityFilters) {
    params.amenityFilters = Array.isArray(filters.amenityFilters)
      ? filters.amenityFilters
      : [filters.amenityFilters];
  }

  return params;
};

// Property service with typed API methods
export const propertyService = {
  // Get all properties with filters
  getProperties: async (filters?: PropertySearchParams): Promise<PropertyResponse> => {
    const params = convertPropertyFilters(filters);
    const response = await apiService.get<PropertyResponse>('/api/properties', params);
    return response.data;
  },

  getPropertiesBySaler: async (filters?: PropertySearchParams): Promise<PropertyResponse> => {
    const params = convertPropertyFilters(filters);
    const response = await apiService.get<PropertyResponse>(`/api/properties/saler`, params);
    return response.data;
  },

  // Get properties uploaded by a specific seller
  getPropertiesBySellerUpload: async (
    sellerId: string,
    filters?: PropertySearchParams
  ): Promise<PropertyResponse> => {
    const params = convertPropertyFilters(filters);
    const response = await apiService.get<PropertyResponse>(
      `/api/users/seller/${sellerId}/uploaded-properties`,
      params
    );
    return response.data;
  },

  // Get a single property by ID
  getProperty: async (id: string): Promise<PropertyDetailResponse> => {
    const response = await apiService.get<PropertyDetailResponse>(`/api/properties/${id}`);
    return response.data;
  },

  // Create a new property
  createProperty: async (
    property: Partial<PropertyDetailRequest>
  ): Promise<ActionPropertyResponse> => {
    const response = await apiService.post<ActionPropertyResponse, Partial<PropertyDetailRequest>>(
      '/api/properties',
      property
    );
    return response.data;
  },

  // Update an existing property
  updateProperty: async (
    id: string,
    property: Partial<PropertyDetailRequest>
  ): Promise<ActionPropertyResponse> => {
    const response = await apiService.put<ActionPropertyResponse, Partial<PropertyDetailRequest>>(
      `/api/properties/${id}`,
      property
    );
    return response.data;
  },

  // Delete a property
  deleteProperty: async (id: string): Promise<ActionPropertyResponse> => {
    const response = await apiService.delete<ActionPropertyResponse>(`/api/properties/${id}`);
    return response.data;
  },

  // Verify a property
  verifyProperty: async (propertyId: string): Promise<VerifyPropertyResponse> => {
    const response = await apiService.patch<VerifyPropertyResponse, VerifyPropertyRequest>(
      `/api/properties/${propertyId}/verify`
    );
    return response.data;
  },

  // Unverify a property
  unverifyProperty: async (propertyId: string): Promise<VerifyPropertyResponse> => {
    const response = await apiService.patch<VerifyPropertyResponse, VerifyPropertyRequest>(
      `/api/properties/${propertyId}/unverify`
    );
    return response.data;
  },

  // Download Excel file of properties
  downloadExcel: async (p0: {
    LoanAmount: number;
    LoanTerm: number;
    InterestRate: number;
  }): Promise<Blob> => {
    const blob = await apiService.getBlob('/api/properties/download-excel', p0);
    return blob;
  },
};

export default propertyService;
