'use client';

import * as React from 'react';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { autocomplete } from '@/lib/google';
import { PlaceData } from '@googlemaps/google-maps-services-js';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { HomeIcon, Search } from 'lucide-react';
import { motion } from 'framer-motion';

enum TransactionType {
  ForSale = 'ForSale',
  ForRent = 'ForRent',
  ForSell = 'ForSell',
}

interface PredefinedLocation {
  id: string;
  name: string;
  lat: number;
  lng: number;
}

interface CityGroup {
  city: string;
  locations: PredefinedLocation[];
}

const tabs = [
  {
    name: '<PERSON><PERSON>',
    value: TransactionType.ForSale,
  },
  {
    name: 'Thu<PERSON>',
    value: TransactionType.ForRent,
  },
  {
    name: '<PERSON><PERSON>',
    value: TransactionType.ForSell,
  },
];

// Predefined cities and districts with hardcoded coordinates
const cityGroups: CityGroup[] = [
  {
    city: 'TP. Hồ Chí Minh',
    locations: [
      { id: 'hcm-quan1', name: 'Quận 1', lat: 10.7769, lng: 106.7009 },
      { id: 'hcm-quan3', name: 'Quận 3', lat: 10.7787, lng: 106.6923 },
      { id: 'hcm-quan7', name: 'Quận 7', lat: 10.7378, lng: 106.723 },
      { id: 'hcm-thuduc', name: 'Quận Thủ Đức', lat: 10.8506, lng: 106.7713 },
      { id: 'hcm-binhtan', name: 'Quận Bình Tân', lat: 10.7403, lng: 106.6245 },
    ],
  },
  {
    city: 'Hà Nội',
    locations: [
      { id: 'hanoi-hoankiem', name: 'Quận Hoàn Kiếm', lat: 21.0285, lng: 105.8542 },
      { id: 'hanoi-badinh', name: 'Quận Ba Đình', lat: 21.0341, lng: 105.8372 },
      { id: 'hanoi-dongda', name: 'Quận Đống Đa', lat: 21.0144, lng: 105.8342 },
      { id: 'hanoi-haibatrung', name: 'Quận Hai Bà Trưng', lat: 21.0122, lng: 105.8673 },
      { id: 'hanoi-caugiay', name: 'Quận Cầu Giấy', lat: 21.0331, lng: 105.8019 },
    ],
  },
  {
    city: 'Đà Nẵng',
    locations: [
      { id: 'danang-haichau', name: 'Quận Hải Châu', lat: 16.0544, lng: 108.2022 },
      { id: 'danang-sontra', name: 'Quận Sơn Trà', lat: 16.0842, lng: 108.25 },
      { id: 'danang-thanhkhe', name: 'Quận Thanh Khê', lat: 16.0583, lng: 108.1717 },
      { id: 'danang-ngu-hanh-son', name: 'Quận Ngũ Hành Sơn', lat: 15.9756, lng: 108.2625 },
      { id: 'danang-lien-chieu', name: 'Quận Liên Chiểu', lat: 16.0756, lng: 108.1453 },
    ],
  },
  {
    city: 'Hải Phòng',
    locations: [
      { id: 'haiphong-hongbang', name: 'Quận Hồng Bàng', lat: 20.8525, lng: 106.6886 },
      { id: 'haiphong-ngoquyen', name: 'Quận Ngô Quyền', lat: 20.858, lng: 106.6816 },
      { id: 'haiphong-lechan', name: 'Quận Lê Chân', lat: 20.8447, lng: 106.6842 },
      { id: 'haiphong-haian', name: 'Quận Hải An', lat: 20.8597, lng: 106.7969 },
      { id: 'haiphong-kienan', name: 'Quận Kiến An', lat: 20.8453, lng: 106.7639 },
    ],
  },
  {
    city: 'Cần Thơ',
    locations: [
      { id: 'cantho-ninhkieu', name: 'Quận Ninh Kiều', lat: 10.0452, lng: 105.7469 },
      { id: 'cantho-binhthuy', name: 'Quận Bình Thủy', lat: 10.0833, lng: 105.75 },
      { id: 'cantho-cairang', name: 'Quận Cái Răng', lat: 10.0333, lng: 105.7833 },
      { id: 'cantho-omon', name: 'Quận Ô Môn', lat: 10.1167, lng: 105.6333 },
      { id: 'cantho-thotnot', name: 'Quận Thốt Nốt', lat: 10.2167, lng: 105.5 },
    ],
  },
];

export default function EnhancedSearchAutocomplete() {
  const [open, setOpen] = useState(false);
  const [predictions, setPredictions] = useState<PlaceData[]>([]);
  const [input, setInput] = useState('');
  const [transactionType, setTransactionType] = useState<TransactionType>(TransactionType.ForSale);
  const router = useRouter();

  // Animated underline state
  const tabRefs = React.useRef<(HTMLButtonElement | null)[]>([]);
  const [underlineStyle, setUnderlineStyle] = React.useState({ left: 0, width: 0 });

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen(open => !open);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  // Update underline position when active tab changes
  React.useLayoutEffect(() => {
    const activeIndex = tabs.findIndex(tab => tab.value === transactionType);
    const activeTabElement = tabRefs.current[activeIndex];

    if (activeTabElement) {
      const { offsetLeft, offsetWidth } = activeTabElement;

      setUnderlineStyle({
        left: offsetLeft,
        width: offsetWidth,
      });
    }
  }, [transactionType]);

  // Initialize underline position when dialog opens
  React.useLayoutEffect(() => {
    if (open) {
      const timer = setTimeout(() => {
        const activeIndex = tabs.findIndex(tab => tab.value === transactionType);
        const activeTabElement = tabRefs.current[activeIndex];

        if (activeTabElement) {
          const { offsetLeft, offsetWidth } = activeTabElement;

          setUnderlineStyle({
            left: offsetLeft,
            width: offsetWidth,
          });
        }
      }, 0);

      return () => clearTimeout(timer);
    }
  }, [open, transactionType]);

  useEffect(() => {
    // Clear predictions immediately when input is empty
    if (!input.trim()) {
      setPredictions([]);
      return;
    }

    const fetchPredictions = async () => {
      const predictions = await autocomplete(input);
      setPredictions(predictions as PlaceData[]);
    };

    const debounceTimer = setTimeout(() => {
      fetchPredictions();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [input]);

  const handleSelect = async (prediction: PlaceData) => {
    const searchTerm = prediction.formatted_address || '';
    const inputItem = prediction.name || '';
    setInput(inputItem);
    setOpen(false);

    // Use coordinates directly from the prediction since they're already available
    if (prediction.geometry?.location) {
      const location = prediction.geometry.location;

      // Try to extract lat and lng safely
      let lat: number | undefined;
      let lng: number | undefined;

      if (location.lat !== undefined) {
        lat = typeof location.lat === 'number' ? location.lat : parseFloat(location.lat);
      }
      if (location.lng !== undefined) {
        lng = typeof location.lng === 'number' ? location.lng : parseFloat(location.lng);
      }

      // Ensure lat and lng are valid numbers
      if (lat !== undefined && lng !== undefined && !isNaN(lat) && !isNaN(lng)) {
        // For autocomplete selection, we only set the center coordinates
        // The map will create its own bounds based on the zoom level
        const params = new URLSearchParams({
          lat: lat.toString(),
          lng: lng.toString(),
          zoom: '14', // Set a reasonable default zoom for search results
          transactionType,
        });

        router.push(`/properties?${params.toString()}`);
      } else {
        router.push(
          `/properties?searchTerm=${encodeURIComponent(searchTerm)}&transactionType=${transactionType}`
        );
      }
    } else {
      router.push(
        `/properties?searchTerm=${encodeURIComponent(searchTerm)}&transactionType=${transactionType}`
      );
    }
  };

  const handlePredefinedLocationSelect = (location: PredefinedLocation) => {
    setInput(location.name);
    setOpen(false);

    // Navigate with hardcoded coordinates
    const params = new URLSearchParams({
      lat: location.lat.toString(),
      lng: location.lng.toString(),
      zoom: '14',
      transactionType,
    });

    router.push(`/properties?${params.toString()}`);
  };

  const handleSearchClick = () => {
    // if (input.trim()) {
    //     router.push(
    //         `/properties?searchTerm=${encodeURIComponent(input)}&transactionType=${transactionType}`
    //     );
    // } else {
    //     setOpen(true);
    // }
    setOpen(true);
  };

  return (
    <>
      <div className="w-full space-y-4">
        {/* Search Button Container */}
        <div className="flex justify-center">
          <Button
            onClick={handleSearchClick}
            className="min-w-fit w-auto h-auto min-h-12 rounded-full flex items-center bg-white hover:bg-gray-50 border md:border-2 shadow-sm hover:text-gray-800 px-6 py-3 gap-3 font-normal"
            variant="outline"
          >
            {input ? (
              <HomeIcon className="h-5 w-5 flex-shrink-0" />
            ) : (
              <Search className="h-5 w-5 flex-shrink-0" />
            )}
            <span className="hidden md:block font-semibold whitespace-nowrap">
              {input
                ? `Tìm ${transactionType === 'ForSale' ? 'mua' : transactionType === 'ForRent' ? 'thuê' : 'bán'} bất động sản tại ${input}`
                : 'Bắt đầu tìm kiếm bất động sản'}
            </span>
            <span className="block md:hidden font-semibold whitespace-nowrap">
              {input ? `${input}` : 'Bắt đầu tìm kiếm bất động sản'}
            </span>
          </Button>
        </div>
        {/* Animated Tabs */}
        {/* <Tabs value={transactionType} onValueChange={(value) => setTransactionType(value as TransactionType)}>
                    <TabsList className="bg-background relative rounded-none p-0 h-auto grid grid-cols-3 w-full">
                        {tabs.map((tab, index) => (
                            <TabsTrigger
                                key={tab.value}
                                value={tab.value}
                                ref={el => {
                                    tabRefs.current[index] = el;
                                }}
                                className="text-black bg-background dark:data-[state=active]:bg-background relative z-10 rounded-none border-0 data-[state=active]:shadow-none py-3 text-sm font-semibold data-[state=active]:text-red-600 hover:text-red-600 transition-colors"
                            >
                                {tab.name}
                            </TabsTrigger>
                        ))}

                        <motion.div
                            className="bg-red-600 absolute bottom-0 z-20 h-0.5"
                            layoutId="underline"
                            style={{
                                left: underlineStyle.left,
                                width: underlineStyle.width,
                            }}
                            transition={{
                                type: 'spring',
                                stiffness: 400,
                                damping: 40,
                            }}
                        />
                    </TabsList>
                </Tabs> */}
      </div>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Tìm kiếm địa điểm..."
            value={input}
            onValueChange={setInput}
            className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
          />
          {/* Transaction Type Tabs */}
          <div className="border-b bg-white">
            <Tabs
              value={transactionType}
              onValueChange={value => setTransactionType(value as TransactionType)}
            >
              <TabsList className="w-full h-auto grid grid-cols-3 bg-background rounded-none border-0 relative p-0">
                {tabs.map((tab, index) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    ref={el => {
                      tabRefs.current[index] = el;
                    }}
                    className="rounded-none border-0 data-[state=active]:bg-transparent data-[state=active]:text-red-600 data-[state=active]:shadow-none py-3 text-sm font-medium relative z-10 bg-background"
                  >
                    {tab.name}
                  </TabsTrigger>
                ))}

                <motion.div
                  className="bg-red-600 absolute bottom-0 z-20 h-0.5"
                  layoutId="underline"
                  style={{
                    left: underlineStyle.left,
                    width: underlineStyle.width,
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 40,
                  }}
                />
              </TabsList>
            </Tabs>
          </div>

          <CommandList>
            {/* Show predefined locations when no input */}
            {input.trim() === '' ? (
              cityGroups.map(cityGroup => (
                <CommandGroup key={cityGroup.city} heading={cityGroup.city}>
                  {cityGroup.locations.map(location => (
                    <CommandItem
                      key={location.id}
                      onSelect={() => handlePredefinedLocationSelect(location)}
                      className="cursor-pointer hover:bg-gray-50"
                    >
                      <HomeIcon className="mr-2 h-4 w-4 text-zinc-500" />
                      {location.name}
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))
            ) : predictions.length > 0 ? (
              <CommandGroup heading="Địa điểm">
                {predictions.map(prediction => (
                  <CommandItem
                    key={prediction.place_id}
                    onSelect={() => handleSelect(prediction)}
                    className="cursor-pointer hover:bg-gray-50"
                  >
                    <Search className="mr-2 h-4 w-4" />
                    {prediction.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            ) : (
              <CommandEmpty>Hãy nhập địa điểm bạn muốn tìm kiếm.</CommandEmpty>
            )}
          </CommandList>
        </Command>
      </CommandDialog>
    </>
  );
}
