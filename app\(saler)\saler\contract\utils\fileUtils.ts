/**
 * Utility functions for file operations
 */

/**
 * Convert blob to File object
 */
export function blobToFile(blob: Blob, fileName: string): File {
  return new File([blob], fileName, { type: blob.type });
}

/**
 * Generate unique filename for contract PDF
 */
export function generateContractPDFFileName(
  contractType: string = 'rental',
  propertyId?: string,
  timestamp?: number
): string {
  const ts = timestamp || Date.now();
  const pid = propertyId ? `_${propertyId}` : '';
  return `contract_${contractType}${pid}_${ts}.pdf`;
}
