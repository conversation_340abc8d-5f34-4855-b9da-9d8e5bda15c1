import { z } from 'zod';

// Schema cho Bên A (<PERSON><PERSON><PERSON> cho thuê)
export const partyASchema = z.object({
  companyName: z.string().optional(),
  fullName: z.string().min(1, '<PERSON><PERSON> và tên là bắt buộc'),
  address: z.string().min(1, 'Đ<PERSON><PERSON> chỉ là bắt buộc'),
  phone: z.string().min(1, 'Số điện thoại là bắt buộc'),
  fax: z.string().optional(),
  email: z.string().email('<PERSON>ail không hợp lệ'),
  taxId: z.string().optional(),
  accountNumber: z.string().optional(),
  representative: z.string().optional(),
  birthYear: z.string().optional(),
  position: z.string().optional(),
  idNumber: z.string().min(1, 'Số CMND/CCCD là bắt buộc'),
  idIssueDate: z.string().min(1, '<PERSON><PERSON><PERSON> cấp là bắt buộc'),
  idIssuePlace: z.string().min(1, '<PERSON><PERSON><PERSON> cấp là bắt buộc'),
  householdRegistration: z.string().optional(),
  propertyOwnership: z.string().optional(),
});

// Schema cho Bên B (Bên thuê)
export const partyBSchema = z.object({
  fullName: z.string().min(1, 'Họ và tên là bắt buộc'),
  birthDate: z.string().min(1, 'Ngày sinh là bắt buộc'),
  address: z.string().min(1, 'Địa chỉ là bắt buộc'),
  phone: z.string().min(1, 'Số điện thoại là bắt buộc'),
  email: z.string().email('Email không hợp lệ'),
  fax: z.string().optional(),
  taxId: z.string().optional(),
  accountNumber: z.string().optional(),
  representative: z.string().optional(),
  birthYear: z.string().optional(),
  position: z.string().optional(),
  leaseStartDate: z.string().min(1, 'Ngày bắt đầu là bắt buộc'),
  leaseEndDate: z.string().min(1, 'Ngày kết thúc là bắt buộc'),
  idNumber: z.string().min(1, 'Số CMND/CCCD là bắt buộc'),
  idIssueDate: z.string().min(1, 'Ngày cấp là bắt buộc'),
  idIssuePlace: z.string().min(1, 'Nơi cấp là bắt buộc'),
});

// Schema cho điều khoản hợp đồng
export const contractTermsSchema = z.object({
  propertyAddress: z.string().min(1, 'Địa chỉ căn hộ là bắt buộc'),
  apartmentNumber: z.string().min(1, 'Số căn hộ là bắt buộc'),
  floor: z.string().min(1, 'Tầng là bắt buộc'),
  totalArea: z.number().min(1, 'Diện tích phải lớn hơn 0'),
  landArea: z.number().optional(),
  sharedArea: z.number().optional(),
  privateArea: z.number().optional(),
  equipment: z.string().optional(),
  monthlyRent: z.number().min(1, 'Tiền thuê hàng tháng phải lớn hơn 0'),
  monthlyRentWords: z.string().min(1, 'Tiền thuê bằng chữ là bắt buộc'),
  paymentMethod: z.string().min(1, 'Phương thức thanh toán là bắt buộc'),
  paymentDate: z.number().min(1).max(31, 'Ngày thanh toán phải từ 1-31'),
  leaseStartDate: z.string().min(1, 'Ngày bắt đầu là bắt buộc'),
  leaseEndDate: z.string().min(1, 'Ngày kết thúc là bắt buộc'),
  contractDuration: z.string().min(1, 'Thời hạn hợp đồng là bắt buộc'),
  deposit: z.number().min(0, 'Tiền đặt cọc không được âm'),
  purpose: z.string().optional(),
});

// Types
export type PartyAFormData = z.infer<typeof partyASchema>;
export type PartyBFormData = z.infer<typeof partyBSchema>;
export type ContractTermsFormData = z.infer<typeof contractTermsSchema>;
