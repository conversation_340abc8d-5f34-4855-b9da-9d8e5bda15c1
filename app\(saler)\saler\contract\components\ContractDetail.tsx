'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/utils/numbers/formatCurrency';

// Dynamically import react-quill to prevent SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.bubble.css';
import { ContractItem } from '@/lib/api/services/fetchContract';
import { numberToVietnameseMoney } from '@/utils/numbers/numberToVietnameseMoney';

interface ContractDetailModalProps {
  contract: ContractItem;
}

const ContractDetailModal: React.FC<ContractDetailModalProps> = ({ contract }) => {
  if (!contract) {
    return (
      <div className="flex justify-center items-center h-full text-gray-500 text-lg">
        <PERSON>h<PERSON>ng có dữ liệu hợp đồng.
      </div>
    );
  }

  const isForSale = contract.contractType.toLowerCase() === 'sale';

  return (
    <div className="p-6 max-w-4xl mx-auto rounded-lg shadow-lg">
      <div className="grid grid-cols-1 gap-6">
        {/* Contract Information */}
        <Card className="shadow-md border-none">
          <CardHeader className="bg-gradient-to-r  rounded-t-lg">
            <CardTitle className="text-xl font-bold">Thông tin hợp đồng</CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div className="flex flex-col sm:flex-row sm:justify-between">
              <span className="font-semibold ">Tên bất động sản:</span>
              <span className="">{contract.property?.name || 'Không có'}</span>
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between">
              <span className="font-semibold ">Loại hợp đồng:</span>
              <Badge variant={isForSale ? 'default' : 'secondary'} className="mt-1 sm:mt-0">
                {contract.contractType}
              </Badge>
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-between">
              <span className="font-semibold ">Trạng thái:</span>
              <Badge
                variant={
                  contract.status === 'Active'
                    ? 'default'
                    : contract.status === 'Inactive'
                      ? 'destructive'
                      : 'secondary'
                }
                className="mt-1 sm:mt-0"
              >
                {contract.status}
              </Badge>
            </div>

            {isForSale ? (
              <>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Giá bán:</span>
                  <span className="">
                    {formatCurrency(
                      contract.paymentSchedule?.reduce((sum, item) => sum + item.amount, 0) || 0,
                      contract.property?.priceDetails?.currency || 'VND'
                    )}
                  </span>
                </div>
                <div className="flex flex-col sm:flex-col sm:justify-between">
                  <span className="font-semibold ">Lịch thanh toán:</span>
                  <div className="mt-1 sm:mt-0">
                    {contract.paymentSchedule && contract.paymentSchedule.length > 0 ? (
                      <ul className="list-disc list-inside space-y-1">
                        {contract.paymentSchedule.map((payment, index) => (
                          <li key={index} className="">
                            {formatCurrency(
                              payment.amount,
                              contract.property?.priceDetails?.currency || 'VND'
                            )}{' '}
                            - {numberToVietnameseMoney(payment.amount)} -{' '}
                            {new Date(payment.paymentDate).toLocaleDateString('vi-VN')} (
                            {payment.note})
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <span className="text-gray-500 italic">Không có lịch thanh toán</span>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Ngày bắt đầu:</span>
                  <span className="">
                    {contract.contractStartDate
                      ? new Date(contract.contractStartDate).toLocaleDateString('vi-VN')
                      : 'Không có'}
                  </span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Ngày kết thúc:</span>
                  <span className="">
                    {contract.contractEndDate
                      ? new Date(contract.contractEndDate).toLocaleDateString('vi-VN')
                      : 'Không có'}
                  </span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Tiền thuê định kỳ:</span>
                  <span className="">
                    {formatCurrency(
                      contract.recurringRent || 0,
                      contract.property?.priceDetails?.currency || 'VND'
                    )}{' '}
                    {'('}
                    {numberToVietnameseMoney(contract.recurringRent || 0)}
                    {')'}
                  </span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Tiền đặt cọc:</span>
                  <span className="">
                    {formatCurrency(
                      contract.depositAmount || 0,
                      contract.property?.priceDetails?.currency || 'VND'
                    )}{' '}
                    {'('}
                    {numberToVietnameseMoney(contract.depositAmount || 0)}
                    {')'}
                  </span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <span className="font-semibold ">Điều khoản thanh toán:</span>
                  <span className="">{contract.paymentTerms || 'Không có'}</span>
                </div>
              </>
            )}

            <div className="flex flex-col sm:flex-row sm:justify-between">
              <span className="font-semibold ">Tệp PDF hợp đồng:</span>
              <div className="mt-1 sm:mt-0">
                {contract.pdfContractUrls && contract.pdfContractUrls.length > 0 ? (
                  <ul className="list-disc list-inside space-y-1">
                    {contract.pdfContractUrls.map((url, index) => (
                      <li key={index}>
                        <a
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className=" hover:underline truncate max-w-[calc(100%-20px)] inline-block align-middle"
                          title={url}
                        >
                          {`Tài liệu ${index + 1}`}
                        </a>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <span className=" italic">Không có tệp PDF</span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contract Content */}
        <Card className="lg:col-span-2 shadow-md border-none ">
          <CardHeader className="bg-gradient-to-r  rounded-t-lg">
            <CardTitle className="text-xl font-bold">Nội dung hợp đồng</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="border rounded-md p-4 max-h-[600px] overflow-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              <ReactQuill
                value={contract.content || '<p>Không có nội dung hợp đồng.</p>'}
                readOnly={true}
                theme="snow"
                modules={{ toolbar: false }}
                className="bg-transparent"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContractDetailModal;
