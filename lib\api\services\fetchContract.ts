import apiService, { RequestParams } from '../core';

export interface PriceDetails {
  salePrice: number;
  rentalPrice: number;
  pricePerSquareMeter: number;
  currency: string;
  depositAmount: number;
  maintenanceFee: number;
  paymentMethods: string[];
}

export interface Property {
  id: string;
  ownerId: string;
  salerId: string;
  name: string;
  transactionType: string;
  type: string;
  status: string;
  code: string;
  priceDetails: PriceDetails;
  image: string;
  yearBuilt: number;
  legalDocuments: string[];
}

export interface RenewalLog {}

export interface ContractItem {
  id: string;
  property: Property;
  tenantId: string | null;
  buyerId: string | null;
  contractType: string;
  contractStartDate: string | null;
  contractEndDate: string | null;
  paymentTerms: string | null;
  status: string;
  recurringRent: number | null;
  depositAmount: number | null;
  pdfContractUrls: string[];
  paymentSchedule: PaymentSchedule[] | null;
  renewalLogs: RenewalLog[];
  rentDueDate: string | null;
  content: string | null;
}

export interface ContractListData {
  data: ContractItem[];
  totalCount: number;
  totalPages: number | null;
  currentPage: number;
  pageSize: number;
  total: number;
}

export interface ContractListResponse {
  code: number;
  status: boolean;
  message: string;
  data: ContractListData;
}

export interface ContractDetailResponse {
  code: number;
  status: boolean;
  message: string;
  data: ContractItem;
}

export interface PaymentSchedule {
  paymentDate: string;
  amount: number;
  note: string;
}

export interface HouseSaleContract {
  id: string;
  propertyId: string;
  buyerId: string;
  paymentSchedule: PaymentSchedule[];
  pdfContractUrls: string[];
  createdAt: string;
  updatedAt: string;
}

export interface HouseSaleContractRequest {
  propertyId: string;
  buyerId: string;
  paymentSchedule: PaymentSchedule[];
  pdfContractUrls: string[];
  content: string;
}

export interface HouseSaleContractResponse {
  code: number;
  status: boolean;
  message: string;
  data: HouseSaleContract;
}

export interface ContractFilterParams {
  pageNumber?: number; // Use pageNumber for consistency with other services
  pageSize?: number;
  propertyId?: string;
  ownerId?: string;
  tenantId?: string;
  buyerId?: string;
  contractType?: string;
  total?: number; // Total number of contracts, if needed for pagination
}

//renatal contract

export interface createRentalContractState {
  propertyId?: string;
  tenantId?: string;
  contractStartDate?: string;
  contractEndDate?: string;
  paymentTerms?: string;
  curringRent?: number;
  rentDueDate?: string;
  depositAmount?: number;
  pdfContractUrls?: string[];
  content?: string;
}

export interface RentalContractResponse {
  id: string;
  property: Property;
  tenantId: string;
  contractStartDate: string;
  contractEndDate: string;
  paymentTerms: string;
  curringRent: number;
  rentDueDate: string;
  depositAmount: number;
  pdfContractUrls: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
  content: string;
}

export interface RequestSendContract {
  email: string;
  contractLink: string;
}

export const convertContractFilters = (filters?: ContractFilterParams): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};

  if (filters.pageNumber !== undefined) params.pageNumber = filters.pageNumber;
  if (filters.pageSize !== undefined) params.pageSize = filters.pageSize;
  if (filters.propertyId) params.propertyId = filters.propertyId;
  if (filters.ownerId) params.ownerId = filters.ownerId;
  if (filters.tenantId) params.tenantId = filters.tenantId;
  if (filters.buyerId) params.buyerId = filters.buyerId;
  if (filters.contractType) params.contractType = filters.contractType;

  return params;
};

export const contractService = {
  // Create a new house sale contract
  createHouseSaleContract: async (
    data: HouseSaleContractRequest
  ): Promise<HouseSaleContractResponse> => {
    const response = await apiService.post<HouseSaleContractResponse, HouseSaleContractRequest>(
      '/api/contracts/sale',
      data
    );
    return response.data;
  },

  //Get all house sale contracts
  getAllContractsBySaler: async (filters?: ContractFilterParams): Promise<ContractListResponse> => {
    const params = convertContractFilters(filters);
    const response = await apiService.get<ContractListResponse>('/api/contracts/saler', params);
    return response.data;
  },

  // Get house sale contract by ID
  getContractById: async (id: string): Promise<ContractDetailResponse> => {
    const response = await apiService.get<ContractDetailResponse>(`/api/contracts/${id}`);
    return response.data;
  },

  //send contract to related parties
  sendContractToRelatedParties: async (
    data: RequestSendContract
  ): Promise<HouseSaleContractResponse> => {
    const response = await apiService.post<HouseSaleContractResponse, RequestSendContract>(
      `/api/contracts/send-contract-direct`,
      data
    );
    return response.data;
  },
  // Update a rental contract by ID
  updateSaleContractById: async (
    id: string,
    data: HouseSaleContractRequest
  ): Promise<HouseSaleContractResponse> => {
    const response = await apiService.put<HouseSaleContractResponse, HouseSaleContractRequest>(
      `/api/contracts/sale/${id}`,
      data
    );
    return response.data;
  },

  // Delete house sale contract by ID
  deleteContractById: async (id: string): Promise<HouseSaleContractResponse> => {
    const res = await apiService.delete<HouseSaleContractResponse>(`/api/contracts/${id}`);
    return res.data;
  },

  // -----------------------------------------------------------------
  // Get all active rental contracts
  // -----------------------------------------------------------------
  createRentalContract: async (
    data: createRentalContractState
  ): Promise<RentalContractResponse> => {
    const response = await apiService.post<RentalContractResponse, createRentalContractState>(
      '/api/contracts/rental',
      data
    );
    return response.data;
  },

  getAllActiveRentalContracts: async (): Promise<RentalContractResponse[]> => {
    const response = await apiService.get<RentalContractResponse[]>('/api/rental-contracts/active');
    return response.data;
  },
  // Update a rental contract by ID
  updateRentalContractById: async (
    id: string,
    data: createRentalContractState
  ): Promise<RentalContractResponse> => {
    const response = await apiService.put<RentalContractResponse, createRentalContractState>(
      `/api/contracts/rental/${id}`,
      data
    );
    return response.data;
  },

  getAllActive: async (): Promise<RentalContractResponse[]> => {
    const response = await apiService.get<RentalContractResponse[]>('/api/rental-contracts/active');
    return response.data;
  },
  //renew a rental contract by ID
  renewById: async (id: string): Promise<RentalContractResponse> => {
    const response = await apiService.post<RentalContractResponse>(
      `/api/rental-contracts/${id}/renew`
    );
    return response.data;
  },
  //restore a rental contract by ID
  restoreById: async (id: string): Promise<RentalContractResponse> => {
    const response = await apiService.post<RentalContractResponse>(
      `/api/rental-contracts/${id}/restore`
    );
    return response.data;
  },
};
