import apiService, { RequestParams } from '../core';

export interface TodayTaskResponse {
  code: number;
  status: true;
  message: string;
  data: {
    dealValueByProperty?: [];
    appointmentsToday?: [];
    upcomingAppointments?: [];
  };
}

export interface TopKpisResponse {
  code: number;
  status: boolean;
  message: string;
  data: {
    leadsAssignedToday: number;
    totalLeadsAssigned: number;
    dealsInProgress: number;
    dealsClosedThisMonth: number;
    dealsWonThisMonth: number;
    dealsLostThisMonth: number;
    conversionRate: number;
    dealsByStatus: {
      [StatusOfDeal.New]: number;
      [StatusOfDeal.Contacted]: number;
      [StatusOfDeal.Negotiation]: number;
      [StatusOfDeal.Closing]: number;
      [StatusOfDeal.Won]: number;
      [StatusOfDeal.Lost]: number;
    };
    followUpsDueToday: number;
  };
}

export enum StatusOfDeal {
  New = 'New',
  Contacted = 'Contacted',
  Negotiation = 'Negotiation',
  Closing = 'Closing',
  Won = 'Won',
  Lost = 'Lost',
}

export interface TopKpisFilters {
  from: string;
  to: string;
}

const convertStaticFilters = (filters?: TopKpisFilters): RequestParams => {
  if (!filters) return {};

  const params: RequestParams = {};
  if (filters.from) params.from = filters.from;
  if (filters.to) params.to = filters.to;

  return params;
};

export const getStaticService = {
  getStatic: async (filters?: TopKpisFilters): Promise<TopKpisResponse> => {
    const params = convertStaticFilters(filters);
    const response = await apiService.get<TopKpisResponse>('/api/statistics/top-kpis', params);
    return response.data;
  },

  getTodayTask: async (): Promise<TodayTaskResponse> => {
    const response = await apiService.get<TodayTaskResponse>('/api/statistics/today-tasks');
    return response.data;
  },
};
