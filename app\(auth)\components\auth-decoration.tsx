'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';

const AuthDecoration = () => {
  const pathname = usePathname();
  const isRegister = pathname === '/register';

  return (
    <div className="absolute inset-0 overflow-hidden">
      <motion.div
        initial={{ x: 0, y: 0 }}
        animate={{
          x: isRegister ? '40%' : '0%',
          y: isRegister ? '-25%' : '0%',
        }}
        transition={{ type: 'spring', stiffness: 100, damping: 20 }}
        className="absolute -left-32 top-36 h-96 w-96 rounded-full bg-red-500/30 blur-2xl"
      />

      <motion.div
        initial={{ x: 0, y: 0 }}
        animate={{
          x: isRegister ? '-30%' : '0%',
          y: isRegister ? '40%' : '0%',
        }}
        transition={{ type: 'spring', stiffness: 100, damping: 20 }}
        className="absolute right-12 top-12 h-44 w-44 rounded-full bg-red-400/20 blur-xl"
      />
      <motion.div
        initial={{ x: 0, y: 0 }}
        animate={{
          x: isRegister ? '40%' : '0%',
          y: isRegister ? '-20%' : '0%',
        }}
        transition={{ type: 'spring', stiffness: 100, damping: 20 }}
        className="absolute bottom-0 left-96 h-64 w-96 rounded-full bg-red-600/20 blur-2xl"
      />

      <div className="absolute inset-0 bg-gradient-to-br from-red-50/50 via-transparent to-transparent dark:from-red-950/50" />
    </div>
  );
};

export default AuthDecoration;
