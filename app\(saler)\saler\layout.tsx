import { ThemeProvider } from '@/lib/providers/themeProvider';
import type { Metadata } from 'next';
import '../../globals.css';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/common/appSidebar';

export const metadata: Metadata = {
  title: 'RevoLand Dashboard',
  icons: {
    icon: '/LOGO_RV_red-01-01.png',
    apple: '/LOGO_RV_red-01-01.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <SidebarProvider
        defaultOpen={true}
        className="flex h-screen overflow-hidden  "
        style={
          {
            '--sidebar-width': '300px',
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset className="overflow-auto scrollbar-hide">{children}</SidebarInset>
      </SidebarProvider>
    </ThemeProvider>
  );
}
