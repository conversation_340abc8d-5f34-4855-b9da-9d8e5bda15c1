import { Dot } from 'lucide-react';
import Image from 'next/image';
import React from 'react';
// import StatusBadge, { Status } from './StatusBadge'; waiting for new api

type MessageFieldCardProps = {
  id: string;
  lastMessage: string;
  lastUpdated: string;
  platform: string;
  platformUser: {
    id: string;
    name: string;
    avatarUrl: string;
  };
};

const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();

  // Convert to Vietnam timezone (+7)
  const vietnamDate = new Date(date.getTime() + 7 * 60 * 60 * 1000);
  const vietnamNow = new Date(now.getTime() + 7 * 60 * 60 * 1000);

  const diffInSeconds = Math.floor((vietnamNow.getTime() - vietnamDate.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '1m';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays}d`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths}mo`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears}y`;
};

export default function MessageFieldCard(props: MessageFieldCardProps) {
  const { platformUser, lastMessage, lastUpdated } = props;
  const relativeTime = formatRelativeTime(lastUpdated);

  return (
    <div className="hover:bg-gray-100 p-3 rounded-[5px] cursor-pointer mx-2 flex justify-between items-center group w-full">
      <div className="flex gap-2 min-w-0 w-full">
        <Image
          src={
            platformUser.avatarUrl ||
            `https://ui-avatars.com/api/?name=${encodeURIComponent(platformUser.name || 'Anonymous')}&background=F3F4F6&color=000000`
          }
          alt="logo"
          width={50}
          height={50}
          unoptimized
          className="rounded-full cursor-pointer flex-shrink-0"
        />

        <div className="flex flex-col items-start min-w-0 w-full">
          <div className="flex items-center gap-1 w-full">
            <p className="font-semibold truncate text-sm">{platformUser.name || 'Anonymous'}</p>
            {/* <StatusBadge status={status} />  waiting for new api */}
          </div>
          <div className="flex items-center w-full gap-1">
            <p
              // className={`text-[12px] ${
              //   hasNewMessage ? 'font-semibold' : 'text-gray-500'
              // } truncate max-w-[82%]`}
              className="text-[12px] text-gray-500 truncate w-[calc(100%-60px)]"
              title={platformUser.name}
            >
              {lastMessage}
            </p>
            <Dot className="text-gray-500 flex-shrink-0" width={10} height={10} />
            <p className="text-[12px] text-gray-500 flex-shrink-0">{relativeTime}</p>
          </div>
        </div>
      </div>

      {/* {hasNewMessage && (
        <div className="w-[10px] h-[10px] bg-blue-600 rounded-full flex-shrink-0 ml-2"></div>
      )} */}
    </div>
  );
}
