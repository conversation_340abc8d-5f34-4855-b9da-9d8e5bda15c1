'use client';
import type React from 'react';
import { useState, useMemo, useEffect } from 'react';
import debounce from 'lodash.debounce';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Loader2, Trash2, Eye } from 'lucide-react';
import { useUserTours, useDeleteTour } from '@/hooks/useTour';
import { formatDateWithLocale } from '@/utils/dates/formatDate';
import type { Tour } from '@/lib/api/services/fetchTour';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
} from '@/components/ui/pagination';

interface TourTableProps {
  disabled: boolean;
  isDialog?: boolean;
  onEditTour?: (tour: Tour) => void;
}

export function TourTable({ disabled, onEditTour, isDialog }: TourTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const pageSize = 5; // Number of tours per page
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce function using lodash.debounce
  const debouncedUpdate = useMemo(
    () =>
      debounce((value: string) => {
        setDebouncedSearchTerm(value);
        setPageNumber(1); // Reset page on search change
      }, 500), // 500ms debounce delay
    []
  );

  // Update searchTerm immediately, but debounce API call
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedUpdate(value);
  };

  // Clean up debounce on unmount
  useEffect(() => {
    return () => {
      debouncedUpdate.cancel();
    };
  }, [debouncedUpdate]);

  // Use the useUserTours hook with the debounced search term
  const { tours, isLoading, isError, error, totalPages, refetch } = useUserTours({
    searchTerm: debouncedSearchTerm,
    pageNumber,
    pageSize,
  });

  // Use the delete tour hook
  const { mutate: deleteTour, isPending: isDeletingTour } = useDeleteTour();

  // Adjust page number if it exceeds total pages after a search/filter
  useEffect(() => {
    if (pageNumber > totalPages && totalPages > 0) {
      setPageNumber(totalPages);
    }
  }, [totalPages, pageNumber]);

  const isEmpty = !isLoading && tours.length === 0;

  const handleAction = (tour: Tour) => {
    // For user role, call the onEditTour callback to open the CreateTourForm with tour data
    if (onEditTour) {
      onEditTour(tour);
    }
  };

  const handleDeleteTour = (tourId: string) => {
    deleteTour(tourId, {
      onSuccess: () => {
        refetch();
        console.log('Tour deleted successfully');
      },
      onError: error => {
        console.error('Failed to delete tour:', error);
      },
    });
  };

  const isTableDisabled = disabled || isDeletingTour;

  return (
    <div className="space-y-4">
      <Input
        placeholder="Tìm kiếm lịch trình theo tên..."
        value={searchTerm}
        onChange={handleSearchChange}
        disabled={isTableDisabled}
      />

      {isLoading ? (
        // Skeleton loading state for the table
        <div className="py-8">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Lịch Trình</TableHead>
                <TableHead>Ngày Bắt Đầu</TableHead>
                <TableHead>Ghi Chú</TableHead>
                {!isDialog && <TableHead className="text-right">Hành động</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: pageSize }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse" />
                  </TableCell>
                  <TableCell>
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse" />
                  </TableCell>
                  {!isDialog && (
                    <TableCell className="text-right">
                      <div className="h-8 w-24 bg-gray-200 rounded animate-pulse ml-auto" />
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">Đang tìm kiếm lịch trình...</span>
          </div>
        </div>
      ) : isError ? (
        <div className="text-center text-red-500 py-8">
          <p>{error?.message || 'Không thể tải danh sách lịch trình.'}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Thử lại
          </Button>
        </div>
      ) : isEmpty ? (
        <div className="text-center py-8 text-muted-foreground">
          {searchTerm ? 'Không tìm thấy lịch trình phù hợp.' : 'Bắt đầu tìm kiếm lịch trình.'}
        </div>
      ) : (
        <>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Lịch Trình</TableHead>
                <TableHead>Ngày Bắt Đầu</TableHead>
                <TableHead>Ghi Chú</TableHead>
                {!isDialog && <TableHead>Hành động</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {tours.map(tour => {
                return (
                  <TableRow key={tour.id}>
                    <TableCell className="font-medium">{tour.name}</TableCell>
                    <TableCell>{formatDateWithLocale(tour.startDateTime, 'vi')}</TableCell>
                    <TableCell className="max-w-[300px] truncate">
                      {tour.note || 'Không có ghi chú'}
                    </TableCell>
                    {!isDialog && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleAction(tour)}
                            disabled={isTableDisabled}
                            className="flex items-center gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            Xem chi tiết
                          </Button>
                          {/* Delete Button with Confirmation Dialog */}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                disabled={isTableDisabled}
                                className="flex items-center gap-1"
                              >
                                {isDeletingTour ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Trash2 className="h-4 w-4" />
                                )}
                                Xóa
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Xác nhận xóa lịch trình</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Bạn có chắc chắn muốn xóa lịch trình {tour.name}? Hành động này
                                  không thể hoàn tác.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel disabled={isDeletingTour}>Hủy</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteTour(tour.id)}
                                  disabled={isDeletingTour}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  {isDeletingTour ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Đang xóa...
                                    </>
                                  ) : (
                                    'Xóa'
                                  )}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {/*Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setPageNumber(p => Math.max(1, p - 1))}
                      className={pageNumber === 1 ? 'pointer-events-none text-gray-400' : ''}
                    >
                      Trước
                    </PaginationPrevious>
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(page => {
                      if (totalPages <= 5) return true;
                      return (
                        page === 1 ||
                        page === totalPages ||
                        (page >= pageNumber - 1 && page <= pageNumber + 1)
                      );
                    })
                    .reduce<number[]>((acc, page, i, arr) => {
                      if (i > 0 && page - arr[i - 1] > 1) {
                        acc.push(-1); // Dấu ...
                      }
                      acc.push(page);
                      return acc;
                    }, [])
                    .map((page, i) =>
                      page === -1 ? (
                        <PaginationItem key={`ellipsis-${i}`}>
                          <span className="px-2 text-muted-foreground">...</span>
                        </PaginationItem>
                      ) : (
                        <PaginationItem key={page}>
                          <Button
                            variant={pageNumber === page ? 'outline' : 'ghost'}
                            className={`px-3 py-1 text-sm ${pageNumber === page ? 'border border-gray-300' : ''}`}
                            onClick={() => setPageNumber(page)}
                          >
                            {page}
                          </Button>
                        </PaginationItem>
                      )
                    )}

                  {/* Nút Tiếp */}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setPageNumber(p => Math.min(totalPages, p + 1))}
                      className={
                        pageNumber === totalPages ? 'pointer-events-none text-gray-400' : ''
                      }
                    >
                      Tiếp
                    </PaginationNext>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      )}
    </div>
  );
}
