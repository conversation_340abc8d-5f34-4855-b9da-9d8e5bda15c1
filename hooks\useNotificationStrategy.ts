// hooks/useNotificationStrategy.ts
import { useEffect, useState } from 'react';
import { useAuth } from './useAuth';
import { useNotificationConnection } from './useNotification';

type ConnectionStrategy = 'immediate' | 'lazy' | 'manual';

export const useNotificationStrategy = (strategy: ConnectionStrategy = 'immediate') => {
  const { token, isAuthenticated } = useAuth();
  const { connect, disconnect, isConnected } = useNotificationConnection();
  const [connectionAttempts, setConnectionAttempts] = useState(0);
  const [lastConnectionTime, setLastConnectionTime] = useState<number | null>(null);

  // ✅ Immediate Strategy - Connect ngay sau login
  useEffect(() => {
    if (strategy !== 'immediate') return;

    const connectImmediately = async () => {
      if (isAuthenticated && token && !isConnected && connectionAttempts < 3) {
        try {
          await connect(token);
          setLastConnectionTime(Date.now());
          setConnectionAttempts(0);
          console.log('🔔 Immediate connection established');
        } catch (error) {
          setConnectionAttempts(prev => prev + 1);
          console.error('Connection attempt failed:', error);

          // Retry after delay
          if (connectionAttempts < 2) {
            setTimeout(() => connectImmediately(), 5000 * (connectionAttempts + 1));
          }
        }
      }
    };

    connectImmediately();
  }, [isAuthenticated, token, isConnected, strategy, connectionAttempts, connect]);

  // ✅ Auto-reconnect khi mất kết nối
  useEffect(() => {
    if (!isAuthenticated || !token) return;

    const handleReconnect = async () => {
      if (!isConnected && lastConnectionTime) {
        const timeSinceLastConnection = Date.now() - lastConnectionTime;

        // Chỉ reconnect nếu đã mất kết nối trong vòng 30 phút
        if (timeSinceLastConnection < 30 * 60 * 1000) {
          try {
            await connect(token);
            console.log('🔄 Auto-reconnected');
          } catch (error) {
            console.error('Auto-reconnect failed:', error);
          }
        }
      }
    };

    // Check reconnection every 30 seconds
    const interval = setInterval(handleReconnect, 30000);
    return () => clearInterval(interval);
  }, [isAuthenticated, token, isConnected, lastConnectionTime, connect]);

  // ✅ Disconnect khi logout
  useEffect(() => {
    if (!isAuthenticated && isConnected) {
      disconnect();
      setLastConnectionTime(null);
      setConnectionAttempts(0);
      console.log('🔌 Disconnected due to logout');
    }
  }, [isAuthenticated, isConnected, disconnect]);

  // Manual connection method
  const connectManually = async () => {
    if (!token) throw new Error('No authentication token');

    try {
      await connect(token);
      setLastConnectionTime(Date.now());
      setConnectionAttempts(0);
      return { success: true };
    } catch (error) {
      setConnectionAttempts(prev => prev + 1);
      throw error;
    }
  };

  return {
    connectManually,
    isConnected,
    connectionAttempts,
    lastConnectionTime: lastConnectionTime ? new Date(lastConnectionTime) : null,
  };
};
