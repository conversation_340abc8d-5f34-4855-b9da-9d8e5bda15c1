import { ThemeProvider } from '@/lib/providers/themeProvider';
import type { Metadata } from 'next';
import '../../globals.css';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebarAdmin } from '@/components/common/appSidebarAdmin';

export const metadata: Metadata = {
  title: 'RevoLand Dashboard',
  icons: {
    icon: '/LOGO_RV_red-01-01.png',
    apple: '/LOGO_RV_red-01-01.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
      <SidebarProvider>
        <AppSidebarAdmin variant="inset" collapsible="icon" />
        {children}
      </SidebarProvider>
    </ThemeProvider>
  );
}
