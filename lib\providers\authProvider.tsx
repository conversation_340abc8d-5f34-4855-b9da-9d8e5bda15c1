'use client';

import { createContext, useContext, useEffect, ReactNode, useState } from 'react';
import { useAuthStore } from '@/lib/store/authStore';
import { useAuth as useAuthHook } from '@/hooks/useAuth';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { LoginForm } from '@/app/(auth)/components/login-form';
import type { Property } from '@/lib/api/services/fetchProperty';

// Use the actual return type from the useAuth hook and extend it
type AuthContextType = ReturnType<typeof useAuthHook> & {
  showAuthDialog: () => void;
  AuthDialog: React.ReactNode;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
  property?: Property;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { syncAuthState } = useAuthStore();
  const authHook = useAuthHook();
  const [showAuthDialog, setShowAuthDialog] = useState(false);

  useEffect(() => {
    syncAuthState();
  }, [syncAuthState]);

  useEffect(() => {
    if (authHook.isAuthenticated && showAuthDialog) {
      setShowAuthDialog(false);
    }
  }, [authHook.isAuthenticated, showAuthDialog]);

  // Listen for logout events to close auth dialog
  useEffect(() => {
    const handleLogout = () => {
      setShowAuthDialog(false);
    };

    window.addEventListener('logout', handleLogout);
    return () => window.removeEventListener('logout', handleLogout);
  }, []);

  const showAuthDialogForFeature = () => {
    if (!authHook.isAuthenticated) {
      setShowAuthDialog(true);
    }
  };

  const AuthDialog = (
    <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
      <DialogContent className="sm:max-w-md max-w-lg p-0 gap-0 max-h-[90vh] overflow-y-auto">
        <div className="p-10">
          <LoginForm />
        </div>
      </DialogContent>
    </Dialog>
  );

  const contextValue: AuthContextType = {
    ...authHook,
    showAuthDialog: showAuthDialogForFeature,
    AuthDialog,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
      {AuthDialog}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook that requires authentication
export function useRequireAuth() {
  const { isAuthenticated, isLoading, showAuthDialog } = useAuth();

  const requireAuth = () => {
    if (!isLoading && !isAuthenticated) {
      showAuthDialog();
      return false;
    }
    return isAuthenticated;
  };

  return {
    isAuthenticated,
    isLoading,
    requireAuth,
  };
}

// Export the auth hook directly for backward compatibility
export { useAuth as useAuthHook } from '@/hooks/useAuth';
