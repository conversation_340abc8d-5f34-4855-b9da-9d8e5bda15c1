/**
 * Secure Authentication Utilities
 *
 * This file provides utilities for implementing a more secure auth system
 * using httpOnly cookies for tokens and client-side flags for UI state
 */

import { getCookie, setCookie, deleteCookie } from 'cookies-next';
import { getSecureCookieConfig } from './cookieConfig';

/**
 * Dual-token approach for maximum security:
 * 1. Access token in httpOnly cookie (not accessible to JavaScript)
 * 2. Auth flag in regular cookie (for client-side auth state)
 */

export interface SecureAuthTokens {
  accessToken: string;
  refreshToken?: string;
}

/**
 * Store tokens securely using httpOnly cookies
 * This prevents XSS attacks from stealing tokens
 */
export function setSecureAuthTokens(tokens: SecureAuthTokens): void {
  // Store access token in httpOnly cookie (most secure)
  setCookie('secure-auth-token', tokens.accessToken, {
    ...getSecureCookieConfig(),
    httpOnly: true, // Cannot be accessed by JavaScript
    maxAge: 60 * 60 * 2, // 2 hours (shorter for access tokens)
  });

  // Store refresh token if provided
  if (tokens.refreshToken) {
    setCookie('secure-refresh-token', tokens.refreshToken, {
      ...getSecureCookieConfig(),
      httpOnly: true,
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });
  }

  // Store auth state flag for client-side usage (not httpOnly)
  setCookie('auth-state', 'authenticated', {
    ...getSecureCookieConfig(),
    httpOnly: false, // Accessible to JavaScript
    maxAge: 60 * 60 * 2, // Same as access token
  });
}

/**
 * Check if user is authenticated (client-side)
 * This only checks the auth state flag, not the actual token
 */
export function isAuthenticated(): boolean {
  return getCookie('auth-state') === 'authenticated';
}

/**
 * Clear all authentication data
 */
export function clearSecureAuthTokens(): void {
  deleteCookie('secure-auth-token', { path: '/' });
  deleteCookie('secure-refresh-token', { path: '/' });
  deleteCookie('auth-state', { path: '/' });
}

/**
 * Get auth state for client-side components
 * Use this instead of checking tokens directly
 */
export function getAuthState(): { isAuthenticated: boolean; needsRefresh: boolean } {
  const authState = getCookie('auth-state');
  const isAuth = authState === 'authenticated';

  // If auth state exists but we're close to expiration, signal refresh needed
  // This is a simplified check - in real implementation, you'd check token expiry
  const needsRefresh = isAuth; // Implement proper token expiry check here

  return {
    isAuthenticated: isAuth,
    needsRefresh,
  };
}

/**
 * Middleware helper to extract token from httpOnly cookie
 * Use this in your middleware.ts or API routes
 */
export function getTokenFromRequest(request: Request): string | null {
  // This would be implemented in middleware/API routes
  // where you have access to the httpOnly cookie
  // Example: request.cookies.get('secure-auth-token')?.value
  console.log(request);
  return null; // Placeholder - implement based on your framework
}

/**
 * Configuration for implementing secure auth
 */
export const SECURE_AUTH_CONFIG = {
  ACCESS_TOKEN_EXPIRY: 60 * 60 * 2, // 2 hours
  REFRESH_TOKEN_EXPIRY: 60 * 60 * 24 * 30, // 30 days
  AUTH_STATE_EXPIRY: 60 * 60 * 2, // Same as access token

  // Cookie names
  COOKIES: {
    ACCESS_TOKEN: 'secure-auth-token',
    REFRESH_TOKEN: 'secure-refresh-token',
    AUTH_STATE: 'auth-state',
  },
} as const;
