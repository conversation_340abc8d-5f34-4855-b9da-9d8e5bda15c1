'use client';

import { Property, PropertyType } from '@/lib/api/services/fetchProperty';
import { useProperties } from '@/hooks/useProperty';
import { useEffect, useState } from 'react';
import { PropertyCard } from '@/components/PropertyCard';

interface TopPropertiesSectionProps {
  currentProperty: Property;
}

// Utility function to get Vietnamese property type name
function getPropertyTypeName(type: PropertyType): string {
  switch (type) {
    case PropertyType.APARTMENT:
      return 'Căn hộ chung cư';
    case PropertyType.VILLA:
      return 'Biệt thự';
    case PropertyType.SHOP_HOUSE:
      return 'Nhà phố';
    case PropertyType.LAND_PLOT:
      return 'Đất nền';
    case PropertyType.HOUSE:
      return 'Nhà riêng';
    case PropertyType.TOWNHOUSE:
      return 'Nhà phố thương mại';
    case PropertyType.MINI_SERVICE_APARTMENT:
      return '<PERSON>ăn hộ mini';
    case PropertyType.COMMERCIAL_TOWNHOUSE:
      return '<PERSON>h<PERSON> phố thương mại';
    case PropertyType.MOTEL:
      return 'Nhà trọ';
    case PropertyType.AIRBNB:
      return '<PERSON>ăn hộ Airbnb';
    case PropertyType.PROJECT_LAND:
      return 'Đất dự án';
    case PropertyType.OFFICE:
      return 'Văn phòng';
    case PropertyType.WAREHOUSE:
      return 'Kho bãi';
    case PropertyType.FACTORY:
      return 'Nhà xưởng';
    case PropertyType.INDUSTRIAL:
      return 'Khu công nghiệp';
    case PropertyType.HOTEL:
      return 'Khách sạn';
    case PropertyType.SOCIAL_HOUSING:
      return 'Nhà ở xã hội';
    case PropertyType.NEW_URBAN_AREA:
      return 'Khu đô thị mới';
    case PropertyType.ECO_RESORT:
      return 'Khu nghỉ dưỡng sinh thái';
    case PropertyType.OTHER:
      return 'Khác';
    default:
      return type;
  }
}

export default function TopPropertiesSection({ currentProperty }: TopPropertiesSectionProps) {
  const [topProperties, setTopProperties] = useState<Property[]>([]);
  const [similarTypeProperties, setSimilarTypeProperties] = useState<Property[]>([]);

  // Get all properties for the first section
  const { properties, isLoading } = useProperties({ pageSize: 8 });

  // Get properties of the same type for the second section
  const { properties: sameTypeProperties, isLoading: isLoadingSameType } = useProperties({
    pageSize: 8,
    type: currentProperty.type,
  });

  useEffect(() => {
    if (properties.length > 0) {
      const filteredProperties = properties
        .filter(property => property.id !== currentProperty.id)
        .slice(0, 8);
      setTopProperties(filteredProperties);
    }
  }, [properties, currentProperty]);

  useEffect(() => {
    if (sameTypeProperties.length > 0) {
      const filteredSameTypeProperties = sameTypeProperties
        .filter(property => property.id !== currentProperty.id)
        .slice(0, 8);
      setSimilarTypeProperties(filteredSameTypeProperties);
    }
  }, [sameTypeProperties, currentProperty]);

  const propertyTypeName = getPropertyTypeName(currentProperty.type);

  return (
    <div className="mt-16 mb-8 space-y-16">
      {/* Same Type Properties Section */}
      {!isLoadingSameType && similarTypeProperties.length > 0 && (
        <div>
          <h2 className="text-xl md:text-2xl font-semibold mb-6">{propertyTypeName} tương tự</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {similarTypeProperties.map(property => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        </div>
      )}
      {/* Similar Properties Section */}
      {!isLoading && topProperties.length > 0 && (
        <div>
          <h2 className="text-xl md:text-2xl font-semibold mb-6">Gợi ý cho bạn</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {topProperties.map(property => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
