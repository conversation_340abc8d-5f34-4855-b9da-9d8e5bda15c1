import apiService from '../core';

export interface SyncRecentlyViewedResponse {
  code: number;
  status: boolean;
  message: string;
  data?: string;
}

export interface SyncRecentlyViewedRequest {
  id: string;
  viewAt: string;
}

export interface FetchRecentlyViewedResponse {
  code: number;
  status: boolean;
  message: string;
  data?: PropertyViewHistory[];
}

export interface PropertyViewHistory {
  id: string;
  userId: string;
  propertyId: string;
  viewAt: string;
}

export const recentlyViewedService = {
  syncRecentlyViewed: async (
    items: SyncRecentlyViewedRequest[]
  ): Promise<SyncRecentlyViewedResponse> => {
    const response = await apiService.post<SyncRecentlyViewedResponse, SyncRecentlyViewedRequest[]>(
      '/api/PropertyViewHistory',
      items
    );
    return response.data;
  },

  fetchRecentlyViewed: async (): Promise<FetchRecentlyViewedResponse> => {
    const response = await apiService.get<FetchRecentlyViewedResponse>('/api/PropertyViewHistory');
    return response.data;
  },
};
