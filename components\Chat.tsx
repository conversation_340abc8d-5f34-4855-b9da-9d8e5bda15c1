'use client';
import { useState, KeyboardEvent } from 'react';
import { PaperClipIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { CalendarIcon, BarChartIcon, HouseIcon, UserIcon, Send } from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from './ui/badge';

const Chat = () => {
  const [message, setMessage] = useState('');

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // Handle send message
      if (message.trim()) {
        toast.info('Tính năng đang được phát triển');
        setMessage('');
      }
    }
  };

  const actionButtons = [
    { label: 'Tì<PERSON> kiếm bất động sản', icon: HouseIcon },
    { label: 'Thống kê thị trường', icon: BarChartIcon },
    { label: 'Lịch hẹn xem nhà', icon: CalendarIcon },
    // { label: 'Hỏi về tài chính', icon: CreditCardIcon },
    { label: 'Liên hệ tư vấn viên', icon: UserIcon },
  ];

  return (
    <div className="h-full bg-background flex flex-col items-center">
      {/* Logo at the top */}
      {/* <div className="w-full flex justify-center py-6">
              
            </div> */}

      {/* Chat Interface */}
      <div className="flex-1 w-full max-w-5xl flex items-center justify-center mb-56 md:px-4 sm:px-6">
        <motion.div
          className="w-full flex flex-col p-4 sm:p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Link href="/" className="flex items-center justify-center">
            <Image
              src="/logo_revoland_red.png"
              alt="Revoland icon"
              width={80}
              height={80}
              priority
              className="rounded-md w-[50px] h-auto sm:w-[100px]"
            />
          </Link>
          {/* Welcome Text */}
          <motion.div
            className="mb-6 sm:mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.h1
              className="text-xl sm:text-2xl font-medium text-black mb-2 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Chào mừng đến <span className="text-red-500">Revoland</span>
            </motion.h1>
            <motion.p
              className="text-xs sm:text-base text-gray-600 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Hãy hỏi tôi bất cứ điều gì liên quan đến bất động sản
            </motion.p>
          </motion.div>

          {/* Chat Messages Area */}
          {/* <ScrollArea className="flex-1 mb-4 min-h-[200px] max-h-[400px] rounded-md border p-4">
                        Messages will be rendered here
                    </ScrollArea> */}

          {/* Input Area */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <Textarea
              value={message}
              onChange={e => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Đặt câu hỏi về bất động sản..."
              className="w-full p-4 pr-32 rounded-2xl resize-none min-h-[120px] focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 [&::placeholder]:text-sm [&::placeholder]:text-muted-foreground"
            />
            <Badge className="absolute -top-2 right-4 border-transparent bg-gradient-to-r from-indigo-500 to-pink-500 [background-size:105%] bg-center text-white">
              Sắp ra mắt
            </Badge>
            {/* Action Buttons */}
            <div className="absolute bottom-3 left-3 flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="rounded-full h-8 w-8 sm:h-10 sm:w-10"
              >
                <PaperClipIcon className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
              <Button
                variant="outline"
                className="rounded-full text-xs sm:text-sm whitespace-nowrap"
              >
                Tìm kiếm sâu
                <ChevronDownIcon className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
              </Button>
            </div>

            {/* Send Button */}
            <Button
              size="icon"
              className="absolute bg-red-500 hover:bg-red-600 bottom-3 right-3 rounded-full h-8 w-8 sm:h-10 sm:w-10"
              onClick={() => {
                if (message.trim()) {
                  toast.info('Tính năng đang được phát triển');
                  setMessage('');
                }
              }}
            >
              <Send className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </motion.div>

          {/* Quick Action Buttons */}
          <motion.div
            className="flex flex-wrap justify-center gap-2 mt-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            {actionButtons.map((button, index) => (
              <Button
                key={index}
                variant="outline"
                className="rounded-full text-xs sm:text-sm whitespace-nowrap"
              >
                <button.icon className="h-4 w-4 sm:h-5 sm:w-5 mr-1" />
                <span className="hidden sm:inline">{button.label}</span>
              </Button>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default Chat;
