import apiService from '../core';
import { Property } from './fetchProperty';

export interface ChatNotification {
  title: string;
  body: string;
}

export interface ChatMessage {
  direction: string;
  conversationId: string;
  content: string;
  senderId: string;
  timestamp: string;
  platform: string;
  id?: string;
  createdAt?: string;
  recipientId: string;
  platformUserId: string;
  replyToMessageId: string;
  updatedAt: string;
  attachments: string[];
  isDeleted: boolean;
  groupId: string;
  localId?: string;
  messageId?: string;
}

export type RawAttachment =
  MessageResponse['attachments'] extends Array<infer U>
    ? U
    :
        | never
        | {
            id?: string;
            type?: string;
            url?: string;
            name?: string;
            fileType?: string;
            fileUrl?: string;
            fileName?: string;
          };

export interface SendMessageRequest {
  senderId?: string;
  recipientId?: string;
  platformUserId?: string;
  content: string;
  messageType?: string;
  direction?: string;
  platform?: string;
  conversationId?: string;
  replyToMessageId?: string;
  createdAt?: string;
  attachmentIds?: string[];
}

export interface PinMessage {
  id: string;
  messageId: string;
  senderId: string;
  content: string;
}

export interface Message {
  messageId: string;
  id: string;
  senderId: string | undefined;
  content: string;
  timestamp: Date;
  createdAt: Date;
  direction: string;
  senderType?: SenderType;
  isRead?: boolean;
  isAnonymous?: boolean;
  isChannelSelection?: boolean;
  replyToMessageId?: string;
  senderName?: string;
  attachments?: Array<{
    id: string;
    type: string;
    url: string;
    fileName: string;
  }>;
  pending?: boolean;
  localId?: string;
}

export type MessageStructure = {
  id: string;
  senderId: string;
  direction: string;
  createdAt: string;
  content: string;
  replyToMessageId: string;
  attachments: Array<{
    id: string;
    type: string;
    url: string;
    name: string;
  }>;
  isDeleted: boolean;
  isPinned?: boolean;
  replyToMessage?: MessageStructure;
};

// Extended message structure to support new features
// export interface ExtendedMessageStructure {
//   id: string;
//   senderId: string;
//   direction: string;
//   createdAt: string;
//   content: string;
//   replyToMessageId: string;
//   attachments: Array<{
//     id: string;
//     type: string;
//     url: string;
//     name: string;
//   }>;
//   isDeleted: boolean;
//   isPinned?: boolean;
//   replyToMessage?: ExtendedMessageStructure;
// }

export interface MessageResponse extends Message {
  direction: string;
  createdAt: Date;
  isNew?: boolean;
  conversationId?: string;
  userId?: string;
  isDeleted?: boolean;
}

export interface Conversation {
  data: {
    id: string;
    lastUpdated: string;
    lastMessage: string;
    platform: string;
    direction?: string;
    pinMessages: ChatMessage[];
    platformUser: {
      id: string;
      avatarUrl: string;
      name: string;
      email: string;
      phone: string;
      address: string;
      city: string;
      state: string;
      zip: string;
    };
  };
}

export interface PropertyCardMessage {
  type: 'property-card';
  id: string;
  messageId: string;
  timestamp: Date;
  property: Property;
}

export enum SenderType {
  User = 'user',
  Admin = 'admin',
}

class ChatService {
  private getStoredConversation() {
    if (typeof window === 'undefined') return null;
    const stored = localStorage.getItem('chat_conversation');
    return stored ? JSON.parse(stored) : null;
  }

  private storeConversation(userId: string, conversationId: string) {
    if (typeof window === 'undefined') return;
    localStorage.setItem('chat_conversation', JSON.stringify({ userId, conversationId }));
  }

  async getUserConversation(): Promise<Conversation | null> {
    try {
      const response = await apiService.get('/api/conversations/user');
      return response.data as Conversation;
    } catch (error) {
      console.error('Failed to fetch current user conversation:', error);
      return null;
    }
  }

  async getSellerConversation(sellerId: string): Promise<Conversation | null> {
    try {
      const response = await apiService.get(`/api/conversations/seller/${sellerId}`);
      return response.data as Conversation;
    } catch (error) {
      console.error('Failed to fetch seller conversation:', error);
      return null;
    }
  }

  async sendMessage(request: SendMessageRequest): Promise<MessageResponse> {
    const endpoint = '/api/chat';

    const response = await apiService.post(endpoint, {
      ...request,
      content: request.content,
      recipientId: request.recipientId,
      direction: request.direction || 'inbound',
    });
    const data = response.data as MessageResponse;
    if (data.isNew && data.conversationId && data.userId) {
      this.storeConversation(data.userId, data.conversationId);
    }

    return data;
  }

  async pinMessage(messageId: string, conversationId: string): Promise<void> {
    await apiService.post('/api/chat/pin', {
      messageId,
      conversationId,
    });
  }

  async unpinMessage(messageId: string, conversationId: string): Promise<void> {
    await apiService.post('/api/chat/unpin', {
      messageId,
      conversationId,
    });
  }

  async deleteMessage(messageId: string): Promise<void> {
    await apiService.delete(`/api/chat/${messageId}`);
  }
}

export const chatService = new ChatService();
