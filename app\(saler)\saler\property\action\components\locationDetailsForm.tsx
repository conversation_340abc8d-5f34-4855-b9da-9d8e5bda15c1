'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { MapPin, Map, Building, Info, Navigation, Search, Crosshair } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  APIProvider,
  Map as GoogleMap,
  AdvancedMarker,
  Pin,
  useMap,
  useMapsLibrary,
} from '@vis.gl/react-google-maps';
import { useProvinces, useWards } from '@/hooks/useProvinces';
interface LocationFormData {
  location: {
    address: string;
    latitude: number;
    longitude: number;
    city: string;
    ward: string;
  };
}

interface LocationDetailsFormProps {
  formData: LocationFormData;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (id: string, value: string) => void;
  handleNext: () => void;
}

export default function LocationDetailsForm({
  formData,
  handleChange,
  handleSelectChange,
  handleNext,
}: LocationDetailsFormProps) {
  // Use custom hooks for data fetching
  const { data: provinces = [], isLoading: provincesLoading } = useProvinces();
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const { data: wards = [], isLoading: wardsLoading } = useWards(selectedProvince);

  const [selectedWard, setSelectedWard] = useState<string>('');
  const [locationTab, setLocationTab] = useState<string>('province');

  // Google Maps state
  const [mapCenter, setMapCenter] = useState({
    lat: formData.location.latitude || 10.842935416604869,
    lng: formData.location.longitude || 106.84182012230411,
  });
  const [markerPosition, setMarkerPosition] = useState({
    lat: formData.location.latitude || 10.842935416604869,
    lng: formData.location.longitude || 106.84182012230411,
  });
  const [searchAddress, setSearchAddress] = useState('');
  const [mapError, setMapError] = useState<string | null>(null);
  const [placeSelected, setPlaceSelected] = useState(false);
  const [markerKey, setMarkerKey] = useState(0);
  const [autocompleteReady, setAutocompleteReady] = useState(false);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const searchInputRef = useRef<HTMLInputElement | null>(null);
  const autocompleteInitializedRef = useRef(false);
  const defaultCoordinatesSetRef = useRef(false);

  // Update map center when form data changes
  useEffect(() => {
    if (formData.location.latitude && formData.location.longitude) {
      const newCenter = {
        lat: formData.location.latitude,
        lng: formData.location.longitude,
      };
      setMapCenter(newCenter);
      setMarkerPosition(newCenter);
      setMarkerKey(prev => prev + 1);
    } else if (!defaultCoordinatesSetRef.current) {
      const defaultLat = 10.842935416604869;
      const defaultLng = 106.84182012230411;

      handleSelectChange('location.latitude', defaultLat.toString());
      handleSelectChange('location.longitude', defaultLng.toString());

      const newCenter = {
        lat: defaultLat,
        lng: defaultLng,
      };
      setMapCenter(newCenter);
      setMarkerPosition(newCenter);
      defaultCoordinatesSetRef.current = true;
    }
  }, [formData.location.latitude, formData.location.longitude, handleSelectChange]);

  // Reset default coordinates flag when formData changes
  useEffect(() => {
    if (formData.location.latitude && formData.location.longitude) {
      defaultCoordinatesSetRef.current = true;
    }
  }, [formData.location.latitude, formData.location.longitude]);

  // Set initial values from formData if present
  useEffect(() => {
    if (formData.location.city && provinces.length > 0) {
      const province = provinces.find(p => p.name === formData.location.city);
      if (province) {
        setSelectedProvince(province.code.toString());
      }
    }
  }, [formData.location.city, provinces]);

  // Set initial ward value from formData if present
  useEffect(() => {
    if (formData.location.ward && wards.length > 0) {
      const ward = wards.find(w => w.name === formData.location.ward);
      if (ward) {
        setSelectedWard(ward.code.toString());
      }
    }
  }, [formData.location.ward, wards]);

  const handleLocationChange = (type: 'province' | 'ward', value: string) => {
    if (type === 'province') {
      const province = provinces.find(p => p.code.toString() === value);
      if (province) {
        setSelectedProvince(value);
        handleSelectChange('location.city', province.name);
        // Reset ward when province changes
        setSelectedWard('');
        handleSelectChange('location.ward', '');
      }
    } else if (type === 'ward') {
      const ward = wards.find(w => w.code.toString() === value);
      if (ward) {
        setSelectedWard(value);
        handleSelectChange('location.ward', ward.name);
      }
    }
  };

  const LocationPath = () => (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Navigation className="h-4 w-4" />
      <span>
        {formData.location.city && (
          <>
            {formData.location.city}
            {formData.location.ward && (
              <>
                {' → '}
                {formData.location.ward}
              </>
            )}
          </>
        )}
      </span>
    </div>
  );

  // Google Maps component with autocomplete
  function MapContent() {
    const map = useMap();
    const placesLib = useMapsLibrary('places');
    const geocodingLib = useMapsLibrary('geocoding');

    // Memoized callback for place selection
    const handlePlaceSelected = useCallback(
      (place: google.maps.places.PlaceResult) => {
        if (place.geometry && place.geometry.location) {
          const newLat = place.geometry.location.lat();
          const newLng = place.geometry.location.lng();

          // Update form data first
          handleSelectChange('location.latitude', newLat.toString());
          handleSelectChange('location.longitude', newLng.toString());

          // Update map center and marker position
          setMapCenter({ lat: newLat, lng: newLng });
          setMarkerPosition({ lat: newLat, lng: newLng });
          setMarkerKey(prev => prev + 1);

          if (place.formatted_address) {
            setSearchAddress(place.formatted_address);
            const addressEvent = {
              target: { id: 'location.address', value: place.formatted_address },
            } as React.ChangeEvent<HTMLInputElement>;
            handleChange(addressEvent);
          }

          if (map) {
            map.panTo({ lat: newLat, lng: newLng });
            map.setZoom(16);
          }

          setMapError(null);
          setPlaceSelected(true);

          setTimeout(() => {
            if (searchInputRef.current) {
              searchInputRef.current.value = '';
            }
          }, 100);
        } else {
          console.error('Place has no geometry:', place);
          setMapError('Không thể lấy tọa độ từ địa chỉ đã chọn. Vui lòng thử lại.');
        }
      },
      [map]
    );

    // Set up Autocomplete when places library is loaded
    useEffect(() => {
      if (!placesLib || !searchInputRef.current || autocompleteInitializedRef.current) {
        return;
      }

      const setupAutocomplete = () => {
        try {
          if (autocompleteRef.current) {
            google.maps.event.clearInstanceListeners(autocompleteRef.current);
            autocompleteRef.current = null;
          }

          if (!searchInputRef.current) {
            return;
          }

          autocompleteRef.current = new google.maps.places.Autocomplete(searchInputRef.current, {
            fields: ['address_components', 'geometry', 'formatted_address'],
            componentRestrictions: { country: 'vn' },
          });

          autocompleteRef.current.addListener('place_changed', () => {
            const place = autocompleteRef.current!.getPlace();
            handlePlaceSelected(place);
          });

          setAutocompleteReady(true);
          autocompleteInitializedRef.current = true;
        } catch (error) {
          console.error('Error setting up autocomplete:', error);
          setMapError('Không thể tải tính năng tìm kiếm địa chỉ. Vui lòng thử lại.');
          setAutocompleteReady(false);
        }
      };

      setupAutocomplete();

      return () => {
        if (autocompleteRef.current) {
          google.maps.event.clearInstanceListeners(autocompleteRef.current);
          autocompleteRef.current = null;
        }
        setAutocompleteReady(false);
        autocompleteInitializedRef.current = false;
      };
    }, [placesLib, handlePlaceSelected]);

    // Handle marker drag end to update address and coordinates
    const handleMarkerDragEnd = useCallback(
      (event: google.maps.MapMouseEvent) => {
        if (!event.latLng) return;

        const newLat = event.latLng.lat();
        const newLng = event.latLng.lng();

        handleSelectChange('location.latitude', newLat.toString());
        handleSelectChange('location.longitude', newLng.toString());

        setMarkerPosition({ lat: newLat, lng: newLng });
        setMapCenter({ lat: newLat, lng: newLng });
        setMarkerKey(prev => prev + 1);

        if (geocodingLib) {
          const geocoder = new geocodingLib.Geocoder();
          geocoder.geocode(
            { location: { lat: newLat, lng: newLng } },
            (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
              if (status === 'OK' && results && results[0]) {
                const formattedAddress = results[0].formatted_address;
                setSearchAddress(formattedAddress);

                const addressEvent = {
                  target: { id: 'location.address', value: formattedAddress },
                } as React.ChangeEvent<HTMLInputElement>;
                handleChange(addressEvent);

                setMapError(null);
              } else {
                console.error('Geocode failed:', status);
                setMapError('Không thể lấy địa chỉ từ tọa độ. Vui lòng thử lại.');
              }
            }
          );
        } else {
          console.warn('Geocoding library not available');
          setMapError(
            'Tính năng lấy địa chỉ tạm thời không khả dụng. Vui lòng nhập địa chỉ thủ công.'
          );
        }
      },
      [geocodingLib]
    );

    return (
      <AdvancedMarker
        key={`marker-${markerKey}-${placeSelected ? 'selected' : 'default'}`}
        position={markerPosition}
        draggable={true}
        onDragEnd={handleMarkerDragEnd}
      >
        <Pin
          background={placeSelected ? '#00ff00' : '#ff0000'}
          glyphColor={'#ffffff'}
          borderColor={'#ffffff'}
        />
      </AdvancedMarker>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6 md:space-y-8">
      {/* Header Section */}
      <div className="space-y-3 px-4 md:px-0">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <MapPin className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl md:text-2xl font-semibold tracking-tight">Thông tin địa chỉ</h1>
            <p className="text-sm md:text-base text-muted-foreground">
              Cung cấp địa chỉ cụ thể và tọa độ
            </p>
          </div>
        </div>
      </div>

      {/* Location Selection Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Building className="h-5 w-5" />
            Địa chỉ hành chính
          </CardTitle>
          <CardDescription>Chọn địa chỉ hành chính cụ thể cho bất động sản của bạn</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Location Path */}
          {(formData.location.city || formData.location.ward) && (
            <div className="p-4 bg-muted/30 rounded-lg">
              <LocationPath />
            </div>
          )}

          <Tabs
            value={locationTab}
            onValueChange={value => {
              setLocationTab(value);
            }}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="province" className="relative">
                Tỉnh/Thành phố
                {formData.location.city && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    ✓
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="ward" disabled={!selectedProvince} className="relative">
                Phường/Xã
                {formData.location.ward && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                    ✓
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Province Tab */}
            <TabsContent value="province" className="space-y-4">
              {provincesLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[60vh] md:max-h-80 overflow-y-auto">
                  {[
                    'Thành phố Hồ Chí Minh',
                    'Thành phố Hà Nội',
                    'Thành phố Đà Nẵng',
                    'Thành phố Cần Thơ',
                    'Thành phố Hải Phòng',
                    'Tây Ninh',
                    'Lâm Đồng',
                    'Thành phố Huế',
                    'Nghệ An',
                    'Quảng Ninh',
                    ...provinces
                      .filter(
                        province =>
                          ![
                            'Thành phố Hồ Chí Minh',
                            'Thành phố Hà Nội',
                            'Thành phố Đà Nẵng',
                            'Thành phố Cần Thơ',
                            'Thành phố Hải Phòng',
                            'Tây Ninh',
                            'Lâm Đồng',
                            'Thành phố Huế',
                            'Nghệ An',
                            'Quảng Ninh',
                          ].includes(province.name)
                      )
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map(province => province.name),
                  ]
                    .map(name => provinces.find(province => province.name === name))
                    .filter((province): province is (typeof provinces)[number] => Boolean(province))
                    .map(province => (
                      <button
                        key={province.code}
                        onClick={() => {
                          handleLocationChange('province', province.code.toString());
                          setLocationTab('ward');
                        }}
                        className={`p-3 text-left rounded-lg border hover:border-primary transition-colors ${
                          selectedProvince === province.code.toString()
                            ? 'border-primary bg-primary/5'
                            : 'border-border'
                        }`}
                      >
                        <span className="font-medium text-sm">{province.name}</span>
                      </button>
                    ))}
                </div>
              )}
            </TabsContent>

            {/* Ward Tab */}
            <TabsContent value="ward" className="space-y-4">
              {wardsLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-[60vh] md:max-h-80 overflow-y-auto">
                  {wards.map(ward => (
                    <button
                      key={ward.code}
                      onClick={() => handleLocationChange('ward', ward.code.toString())}
                      className={`p-3 text-left rounded-lg border hover:border-primary transition-colors ${
                        selectedWard === ward.code.toString()
                          ? 'border-primary bg-primary/5'
                          : 'border-border'
                      }`}
                    >
                      <span className="font-medium text-sm">{ward.name}</span>
                    </button>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Google Maps Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Crosshair className="h-5 w-5" />
            Định vị chính xác trên bản đồ
          </CardTitle>
          <CardDescription>
            Tìm kiếm địa chỉ và kéo thả pin để định vị chính xác tọa độ bất động sản
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search Input */}
          <div className="space-y-3">
            <Label htmlFor="search-address" className="text-sm font-medium">
              Tìm kiếm địa chỉ
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search-address"
                ref={searchInputRef}
                placeholder={
                  autocompleteReady
                    ? 'Nhập địa chỉ để tìm kiếm...'
                    : 'Đang tải tính năng tìm kiếm...'
                }
                value={searchAddress}
                onChange={e => {
                  setSearchAddress(e.target.value);
                  setPlaceSelected(false);
                }}
                className="h-11 pl-10"
                disabled={true}
              />
              {!autocompleteReady && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
            {placeSelected && (
              <div className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <p className="text-xs text-green-700">
                  Đã chọn địa chỉ thành công! Pin đã được cập nhật.
                </p>
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              {autocompleteReady
                ? 'Nhập địa chỉ và chọn từ danh sách gợi ý để di chuyển bản đồ'
                : 'Vui lòng đợi tính năng tìm kiếm được tải xong...'}
            </p>
          </div>

          {/* Map */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Bản đồ tương tác</Label>
            <div className="relative h-[500px] w-full rounded-lg border overflow-hidden">
              {mapError && (
                <div className="absolute inset-0 bg-red-50 border-2 border-red-200 rounded-lg flex items-center justify-center z-10">
                  <div className="text-center p-4">
                    <p className="text-sm text-red-600 font-medium">Lỗi bản đồ</p>
                    <p className="text-xs text-red-500 mt-1">{mapError}</p>
                  </div>
                </div>
              )}

              <APIProvider
                apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ''}
                libraries={['places', 'geocoding']}
              >
                <GoogleMap
                  defaultZoom={15}
                  center={mapCenter}
                  gestureHandling="greedy"
                  disableDefaultUI={false}
                  mapId={process.env.NEXT_PUBLIC_GOOGLE_MAPS_ID}
                  className="w-full h-full"
                >
                  <MapContent />
                </GoogleMap>
              </APIProvider>

              {/* Map Instructions Overlay */}
              <div className="absolute bottom-8 left-2 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm border">
                <p className="text-xs text-muted-foreground">💡 Kéo thả pin để định vị chính xác</p>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Kéo thả pin để định vị chính xác vị trí bất động sản. Tọa độ sẽ được tự động cập nhật.
              Pin màu đỏ = vị trí mặc định, Pin màu xanh = đã chọn từ tìm kiếm.
            </p>
          </div>

          {/* Current Coordinates Display */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="space-y-1">
              <Label className="text-xs font-medium text-muted-foreground">Vĩ độ hiện tại</Label>
              <p className="text-sm font-mono">{markerPosition.lat.toFixed(6)}</p>
            </div>
            <div className="space-y-1">
              <Label className="text-xs font-medium text-muted-foreground">Kinh độ hiện tại</Label>
              <p className="text-sm font-mono">{markerPosition.lng.toFixed(6)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Address Details Card */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-6">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Map className="h-5 w-5" />
            Thông tin địa chỉ
          </CardTitle>
          <CardDescription>
            Cung cấp địa chỉ cụ thể và tọa độ cho bất động sản của bạn
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Label htmlFor="location.address" className="text-sm font-medium">
                Địa chỉ
              </Label>
              <span className="text-destructive text-sm">*</span>
            </div>
            <Input
              id="location.address"
              placeholder="Nhập địa chỉ cụ thể của bất động sản"
              value={formData.location.address}
              onChange={handleChange}
              className="h-11"
              required
            />
            <p className="text-xs text-muted-foreground">Nhập địa chỉ cụ thể của bất động sản</p>
          </div>

          <Separator />
        </CardContent>
      </Card>

      {/* Help Card */}
      <Card className="border-dashed bg-muted/30">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Hướng dẫn sử dụng bản đồ</p>
              <p className="text-xs text-muted-foreground">
                1. <strong>Chọn địa điểm:</strong> Chọn tỉnh/thành phố và phường/xã từ các tab để
                xác định khu vực hành chính.
                <br />
                2. <strong>Tìm kiếm địa chỉ:</strong> Nhập địa chỉ vào ô tìm kiếm và chọn từ danh
                sách gợi ý để di chuyển bản đồ đến vị trí đó. Pin sẽ chuyển sang màu xanh khi đã
                chọn.
                <br />
                3. <strong>Định vị chính xác:</strong> Kéo thả pin để định vị chính xác vị trí bất
                động sản. Tọa độ GPS sẽ được tự động cập nhật.
                <br />
                4. <strong>Thông tin địa chỉ:</strong> Sau khi định vị, địa chỉ chi tiết sẽ được tự
                động điền vào form. Bạn có thể chỉnh sửa thêm nếu cần thiết.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-6 border-t">
        <Button onClick={handleNext} size="lg" className="px-8">
          Tiếp theo
        </Button>
      </div>
    </div>
  );
}
