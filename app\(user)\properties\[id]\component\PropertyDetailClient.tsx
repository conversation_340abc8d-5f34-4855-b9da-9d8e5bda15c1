'use client';

import { useProperty } from '@/hooks/useProperty';
import PropertyDetailWrapper from './PropertyDetailWrapper';
import Loading from '../loading';
import { notFound } from 'next/navigation';

interface PropertyDetailClientProps {
  propertyId: string;
}

export default function PropertyDetailClient({ propertyId }: PropertyDetailClientProps) {
  const { data, isLoading, isError } = useProperty(propertyId);

  if (isLoading) {
    return <Loading />;
  }

  if (isError || !data?.data) {
    notFound();
  }

  return <PropertyDetailWrapper property={data.data} />;
}
