import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  appointmentService,
  AppointmentRequest,
  AppointmentResponse,
  Appointment,
  RescheduleAppointmentRequest,
  UserAppointmentsResponse,
  CancelAppointmentRequest,
} from '@/lib/api/services/fetchAppointment';
import { toast } from 'sonner';

// Query Keys
export const appointmentKeys = {
  all: ['appointments'] as const,
  user: () => [...appointmentKeys.all, 'user'] as const,
  byProperty: (propertyId: string) => [...appointmentKeys.all, 'property', propertyId] as const,
};

/**
 * Hook to create a new appointment
 * Implements optimistic updates and proper error handling
 */
export function useCreateAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (request: AppointmentRequest): Promise<AppointmentResponse> => {
      const response = await appointmentService.createAppointment(request);

      if (!response.status) {
        throw new Error(response.message || 'Không thể tạo lịch hẹn');
      }

      return response;
    },
    onSuccess: data => {
      // Show success toast
      toast.success('Lịch hẹn đã được tạo thành công');

      // Invalidate and refetch user appointments
      queryClient.invalidateQueries({
        queryKey: appointmentKeys.user(),
      });

      // Optionally add the new appointment to cache
      if (data.data) {
        queryClient.setQueryData<Appointment[]>(appointmentKeys.user(), (oldData = []) => [
          data.data!,
          ...oldData,
        ]);
      }
    },
    onError: error => {
      console.error('Create appointment error:', error);

      toast.error(error.message || 'Không thể tạo lịch hẹn. Vui lòng thử lại sau.');
    },
  });
}

/**
 * Hook to get all appointments for the current user
 * Implements proper caching and error handling
 */
export function useGetUserAppointments() {
  return useQuery({
    queryKey: appointmentKeys.user(),
    queryFn: async (): Promise<Appointment[]> => {
      const response = await appointmentService.getUserAppointments();

      if (!response.status) {
        throw new Error(response.message || 'Không thể tải danh sách lịch hẹn');
      }

      return response.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

/**
 * Hook to get appointments for a specific property
 * Useful for property-specific appointment views
 */
export function useGetPropertyAppointments(propertyId: string, enabled = true) {
  return useQuery({
    queryKey: appointmentKeys.byProperty(propertyId),
    queryFn: async (): Promise<Appointment[]> => {
      const response = await appointmentService.getUserAppointments();

      if (!response.status) {
        throw new Error(response.message || 'Không thể tải danh sách lịch hẹn');
      }

      // Filter appointments by property ID
      return (response.data || []).filter(appointment => appointment.propertyId === propertyId);
    },
    enabled: enabled && !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2,
  });
}

/**
 * Hook to prefetch user appointments
 * Useful for improving perceived performance
 */
export function usePrefetchUserAppointments() {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: appointmentKeys.user(),
      queryFn: async (): Promise<Appointment[]> => {
        const response = await appointmentService.getUserAppointments();
        return response.status ? response.data || [] : [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
}

/**
 * Hook to reschedule an appointment
 * Implements optimistic updates and proper error handling
 */
export function useRescheduleAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, request }: { id: string; request: RescheduleAppointmentRequest }) =>
      appointmentService.rescheduleAppointment(id, request),

    // Thêm optimistic update
    onMutate: async ({ id, request }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: appointmentKeys.user() });

      // Snapshot the previous value
      const previousAppointments = queryClient.getQueryData(appointmentKeys.user());

      // Optimistically update to the new value
      queryClient.setQueryData(
        appointmentKeys.user(),
        (old: UserAppointmentsResponse | undefined) => {
          if (!old?.data) return old;

          return {
            ...old,
            data: old.data.map(appointment =>
              appointment.id === id
                ? { ...appointment, date: request.date, updatedAt: new Date().toISOString() }
                : appointment
            ),
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousAppointments };
    },

    onSuccess: data => {
      if (data.status) {
        // Invalidate để đảm bảo data fresh từ server
        queryClient.invalidateQueries({
          queryKey: appointmentKeys.user(),
        });
        toast.success('Lịch hẹn đã được chuyển lịch thành công');
      } else {
        toast.error(data.message || 'Không thể chuyển lịch hẹn');
      }
    },

    onError: (error, variables, context) => {
      // Rollback optimistic update on error
      if (context?.previousAppointments) {
        queryClient.setQueryData(appointmentKeys.user(), context.previousAppointments);
      }

      console.error('Reschedule appointment error:', error);
      toast.error(error.message || 'Không thể chuyển lịch hẹn. Vui lòng thử lại sau.');
    },
  });
}

export const useCancelAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, request }: { id: string; request: CancelAppointmentRequest }) =>
      appointmentService.cancelAppointment(id, request),

    onMutate: async ({ id }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['appointments'] });

      // Snapshot the previous value
      const previousAppointments = queryClient.getQueryData(['appointments']);

      // Optimistically update to the new value
      queryClient.setQueryData(['appointments'], (old: UserAppointmentsResponse) => {
        if (!old?.data) return old;

        return {
          ...old,
          data: old.data.map((appointment: Appointment) =>
            appointment.id === id ? { ...appointment, status: 'cancelled' } : appointment
          ),
        };
      });

      // Return a context object with the snapshotted value
      return { previousAppointments };
    },

    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousAppointments) {
        queryClient.setQueryData(['appointments'], context.previousAppointments);
      }

      toast.error(error.message || 'Có lỗi xảy ra khi hủy cuộc hẹn');
    },

    onSuccess: (data, variables) => {
      toast.success('Đã hủy cuộc hẹn thành công');

      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.invalidateQueries({ queryKey: ['appointments', variables.id] });
    },
  });
};

export const useAppointmentDetail = (id: string) => {
  return useQuery({
    queryKey: ['appointment', id],
    queryFn: () => appointmentService.getAppointmentById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 or 403
      if (
        error?.message?.includes('Không tìm thấy') ||
        error?.message?.includes('không có quyền')
      ) {
        return false;
      }
      return failureCount < 3;
    },
  });
};
