import { ImageResponse } from 'next/og';
// App router includes @vercel/og.
// No need to install it.

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);

  // Get image URLs and filter out empty ones
  const images = [
    searchParams.get('img1'),
    searchParams.get('img2'),
    searchParams.get('img3'),
    searchParams.get('img4'),
  ].filter(Boolean) as string[];

  const imageCount = images.length;

  // Check if we have at least one image
  if (imageCount === 0) {
    return new ImageResponse(
      <>Visit with &quot;?img1=...&amp;img2=...&amp;img3=...&amp;img4=...&quot;</>,
      {
        width: 1200,
        height: 630,
      }
    );
  }

  // Create layout function based on image count
  const createLayout = () => {
    const baseStyle = {
      display: 'flex' as const,
      backgroundColor: '#f8fafc',
      width: '100%',
      height: '100%',
    };

    const imageStyle = {
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const,
    };

    if (imageCount === 1) {
      // Single image: Full size
      return (
        <div style={{ ...baseStyle, display: 'flex' }}>
          <img src={images[0]} style={imageStyle} alt="Property" />
        </div>
      );
    }

    if (imageCount === 2) {
      // Two images: Side by side
      return (
        <div style={{ ...baseStyle, display: 'flex' }}>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[0]} style={imageStyle} alt="Property 1" />
          </div>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[1]} style={imageStyle} alt="Property 2" />
          </div>
        </div>
      );
    }

    if (imageCount === 3) {
      // Three images: One large on left, two stacked on right
      return (
        <div style={{ ...baseStyle, display: 'flex' }}>
          {/* Large image on left */}
          <div style={{ flex: 2, overflow: 'hidden', display: 'flex' }}>
            <img src={images[0]} style={imageStyle} alt="Property 1" />
          </div>
          {/* Two images stacked on right */}
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
              <img src={images[1]} style={imageStyle} alt="Property 2" />
            </div>
            <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
              <img src={images[2]} style={imageStyle} alt="Property 3" />
            </div>
          </div>
        </div>
      );
    }

    // Four images: 2x2 grid
    return (
      <div style={{ ...baseStyle, display: 'flex', flexDirection: 'column' }}>
        {/* Top Row */}
        <div style={{ display: 'flex', width: '100%', height: '50%' }}>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[0]} style={imageStyle} alt="Property 1" />
          </div>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[1]} style={imageStyle} alt="Property 2" />
          </div>
        </div>
        {/* Bottom Row */}
        <div style={{ display: 'flex', width: '100%', height: '50%' }}>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[2]} style={imageStyle} alt="Property 3" />
          </div>
          <div style={{ flex: 1, overflow: 'hidden', display: 'flex' }}>
            <img src={images[3]} style={imageStyle} alt="Property 4" />
          </div>
        </div>
      </div>
    );
  };

  return new ImageResponse(createLayout(), {
    width: 1200,
    height: 630,
  });
}
