import React, { useState, useEffect } from 'react';
import { PropertyCard } from '@/components/PropertyCard';
import { TransactionType, PropertyType } from '@/lib/api/services/fetchProperty';
import { useProperties } from '@/hooks/useProperty';
import { useIsMobile } from '@/hooks/useMobile';
// import RecentlyViewedSection from '../RecentlyViewedSection';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from '@/components/ui/carousel';

// Skeleton component for loading state
const PropertySkeleton = () => (
  <div className="overflow-hidden transition-all duration-300 flex flex-col">
    {/* Image Section with Badges */}
    <div className="relative aspect-square size-full mb-2 group">
      <Skeleton className="w-full h-full rounded-2xl" />
      {/* Badge Skeletons */}
      <div className="absolute top-4 left-4 flex gap-2">
        <Skeleton className="h-6 w-12 rounded-full bg-white/20" />
        <Skeleton className="h-6 w-16 rounded-full bg-white/20" />
      </div>
    </div>

    <div className="px-1">
      {/* Price and Actions Section */}
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-8 w-8 rounded-md" />
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </div>

      {/* Property Details Section */}
      <div className="flex justify-between items-center mb-2 mt-2">
        <div className="flex gap-4">
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded" />
            <Skeleton className="h-4 w-3" />
          </div>
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded" />
            <Skeleton className="h-4 w-3" />
          </div>
          <div className="flex items-center">
            <Skeleton className="h-4 w-4 mr-1 rounded" />
            <Skeleton className="h-4 w-8" />
          </div>
        </div>
        <Skeleton className="h-3 w-16" />
      </div>

      {/* Location Section */}
      <div className="mb-2">
        <div className="flex items-center">
          <Skeleton className="h-4 w-4 mr-1 rounded" />
          <Skeleton className="h-3 w-32" />
        </div>
      </div>
    </div>
  </div>
);

export default function BuyAbilitySection() {
  // Mobile detection
  const isMobile = useIsMobile();

  // Carousel API states
  const [saleCarouselApi, setSaleCarouselApi] = useState<CarouselApi>();
  const [rentCarouselApi, setRentCarouselApi] = useState<CarouselApi>();
  const [apartmentSaleCarouselApi, setApartmentSaleCarouselApi] = useState<CarouselApi>();
  const [landPlotSaleCarouselApi, setLandPlotSaleCarouselApi] = useState<CarouselApi>();
  const [apartmentRentCarouselApi, setApartmentRentCarouselApi] = useState<CarouselApi>();

  // Carousel scroll states for sale properties
  const [saleCanScrollPrev, setSaleCanScrollPrev] = useState(false);
  const [saleCanScrollNext, setSaleCanScrollNext] = useState(false);

  // Carousel scroll states for rent properties
  const [rentCanScrollPrev, setRentCanScrollPrev] = useState(false);
  const [rentCanScrollNext, setRentCanScrollNext] = useState(false);

  // Carousel scroll states for specific property types - Sale
  const [apartmentSaleCanScrollPrev, setApartmentSaleCanScrollPrev] = useState(false);
  const [apartmentSaleCanScrollNext, setApartmentSaleCanScrollNext] = useState(false);
  const [landPlotSaleCanScrollPrev, setLandPlotSaleCanScrollPrev] = useState(false);
  const [landPlotSaleCanScrollNext, setLandPlotSaleCanScrollNext] = useState(false);

  // Carousel scroll states for specific property types - Rent
  const [apartmentRentCanScrollPrev, setApartmentRentCanScrollPrev] = useState(false);
  const [apartmentRentCanScrollNext, setApartmentRentCanScrollNext] = useState(false);

  // Fetch newest properties for sale
  const { properties: saleProperties, isLoading: isLoadingSale } = useProperties({
    transactionType: TransactionType.FOR_SALE,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  // Fetch newest properties for rent
  const { properties: rentProperties, isLoading: isLoadingRent } = useProperties({
    transactionType: TransactionType.FOR_RENT,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  // Fetch specific property types for sale
  const { properties: apartmentSaleProperties, isLoading: isLoadingApartmentSale } = useProperties({
    transactionType: TransactionType.FOR_SALE,
    type: PropertyType.APARTMENT,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  const { properties: landPlotSaleProperties, isLoading: isLoadingLandPlotSale } = useProperties({
    transactionType: TransactionType.FOR_SALE,
    type: PropertyType.LAND_PLOT,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });

  // Fetch specific property types for rent
  const { properties: apartmentRentProperties, isLoading: isLoadingApartmentRent } = useProperties({
    transactionType: TransactionType.FOR_RENT,
    type: PropertyType.APARTMENT,
    pageSize: 8,
    pageNumber: 1,
    sortBy: 'createdAt',
    isDescending: true,
  });
  // Monitor sale carousel scroll state
  useEffect(() => {
    if (!saleCarouselApi) {
      return;
    }

    const updateScrollState = () => {
      setSaleCanScrollPrev(saleCarouselApi.canScrollPrev());
      setSaleCanScrollNext(saleCarouselApi.canScrollNext());
    };

    // Set initial state
    updateScrollState();

    // Listen for scroll events
    saleCarouselApi.on('select', updateScrollState);
    saleCarouselApi.on('reInit', updateScrollState);

    return () => {
      saleCarouselApi.off('select', updateScrollState);
      saleCarouselApi.off('reInit', updateScrollState);
    };
  }, [saleCarouselApi]);

  // Monitor rent carousel scroll state
  useEffect(() => {
    if (!rentCarouselApi) {
      return;
    }

    const updateScrollState = () => {
      setRentCanScrollPrev(rentCarouselApi.canScrollPrev());
      setRentCanScrollNext(rentCarouselApi.canScrollNext());
    };

    // Set initial state
    updateScrollState();

    // Listen for scroll events
    rentCarouselApi.on('select', updateScrollState);
    rentCarouselApi.on('reInit', updateScrollState);

    return () => {
      rentCarouselApi.off('select', updateScrollState);
      rentCarouselApi.off('reInit', updateScrollState);
    };
  }, [rentCarouselApi]);

  // Monitor apartment sale carousel scroll state
  useEffect(() => {
    if (!apartmentSaleCarouselApi) {
      return;
    }

    const updateScrollState = () => {
      setApartmentSaleCanScrollPrev(apartmentSaleCarouselApi.canScrollPrev());
      setApartmentSaleCanScrollNext(apartmentSaleCarouselApi.canScrollNext());
    };

    updateScrollState();
    apartmentSaleCarouselApi.on('select', updateScrollState);
    apartmentSaleCarouselApi.on('reInit', updateScrollState);

    return () => {
      apartmentSaleCarouselApi.off('select', updateScrollState);
      apartmentSaleCarouselApi.off('reInit', updateScrollState);
    };
  }, [apartmentSaleCarouselApi]);

  // Monitor land plot sale carousel scroll state
  useEffect(() => {
    if (!landPlotSaleCarouselApi) {
      return;
    }

    const updateScrollState = () => {
      setLandPlotSaleCanScrollPrev(landPlotSaleCarouselApi.canScrollPrev());
      setLandPlotSaleCanScrollNext(landPlotSaleCarouselApi.canScrollNext());
    };

    updateScrollState();
    landPlotSaleCarouselApi.on('select', updateScrollState);
    landPlotSaleCarouselApi.on('reInit', updateScrollState);

    return () => {
      landPlotSaleCarouselApi.off('select', updateScrollState);
      landPlotSaleCarouselApi.off('reInit', updateScrollState);
    };
  }, [landPlotSaleCarouselApi]);

  // Monitor apartment rent carousel scroll state
  useEffect(() => {
    if (!apartmentRentCarouselApi) {
      return;
    }

    const updateScrollState = () => {
      setApartmentRentCanScrollPrev(apartmentRentCarouselApi.canScrollPrev());
      setApartmentRentCanScrollNext(apartmentRentCarouselApi.canScrollNext());
    };

    updateScrollState();
    apartmentRentCarouselApi.on('select', updateScrollState);
    apartmentRentCarouselApi.on('reInit', updateScrollState);

    return () => {
      apartmentRentCarouselApi.off('select', updateScrollState);
      apartmentRentCarouselApi.off('reInit', updateScrollState);
    };
  }, [apartmentRentCarouselApi]);

  return (
    <section className="w-full flex flex-col items-center justify-center pt-6 pb-16 bg-white mt-0 md:mt-2">
      {/* Newest Properties for Sale Section */}
      <div className="w-full max-w-8xl mx-auto  mb-4 md:mb-12">
        <div className="text-left mb-2 md:mb-4">
          <div className="flex justify-between items-center mb-2 md:mb-4 px-4 sm:px-16 lg:px-24">
            <Link
              href="/properties?transactionType=ForSale"
              className="flex items-center gap-2 text-gray-900 hover:text-red-600 transition-colors group"
            >
              <h2 className="text-base md:text-xl font-semibold">Bất động sản mới nhất - Bán</h2>
              <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-red-600" />
            </Link>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => saleCarouselApi?.scrollPrev()}
                disabled={!saleCarouselApi || !saleCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => saleCarouselApi?.scrollNext()}
                disabled={!saleCarouselApi || !saleCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel
          setApi={setSaleCarouselApi}
          className="w-full sm:px-4 md:px-8 lg:px-12 xl:px-24"
          opts={{
            align: 'start',
          }}
        >
          <CarouselContent className="ml-2 mr-2 md:-ml-4">
            {isLoadingSale
              ? // Loading skeleton for sale properties
                Array.from({ length: 8 }).map((_, i) => (
                  <CarouselItem
                    key={i}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : saleProperties.map(property => (
                  <CarouselItem
                    key={property.id}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertyCard
                      property={property}
                      priority={true}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>

      {/* Newest Properties for Rent Section */}
      <div className="w-full max-w-8xl mx-auto  mb-8 md:mb-12">
        <div className="text-left mb-2 md:mb-4">
          <div className="flex justify-between items-center mb-2 md:mb-4 px-4 sm:px-16 lg:px-24">
            <Link
              href="/properties?transactionType=ForRent"
              className="flex items-center gap-2 text-gray-900 hover:text-red-600 transition-colors group"
            >
              <h2 className="text-base md:text-xl font-semibold">
                Bất động sản mới nhất - Cho thuê
              </h2>
              <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-red-600" />
            </Link>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => rentCarouselApi?.scrollPrev()}
                disabled={!rentCarouselApi || !rentCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => rentCarouselApi?.scrollNext()}
                disabled={!rentCarouselApi || !rentCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel
          setApi={setRentCarouselApi}
          className="w-full sm:px-4 md:px-8 lg:px-12 xl:px-24"
          opts={{
            align: 'start',
          }}
        >
          <CarouselContent className="ml-2 mr-2 md:-ml-4">
            {isLoadingRent
              ? // Loading skeleton for rent properties
                Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem
                    key={`rent-skeleton-${index}`}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : rentProperties.map(property => (
                  <CarouselItem
                    key={property.id}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertyCard
                      property={property}
                      priority={false}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>

      {/* Apartment for Sale Section */}
      <div className="w-full max-w-8xl mx-auto  mb-8 md:mb-12">
        <div className="text-left mb-2 md:mb-4">
          <div className="flex justify-between items-center mb-2 md:mb-4 px-4 sm:px-16 lg:px-24">
            <Link
              href="/properties?propertyType=Apartment&transactionType=ForSale"
              className="flex items-center gap-2 text-gray-900 hover:text-red-600 transition-colors group"
            >
              <h2 className="text-base md:text-xl font-semibold">Chung cư bán</h2>
              <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-red-600" />
            </Link>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => apartmentSaleCarouselApi?.scrollPrev()}
                disabled={!apartmentSaleCarouselApi || !apartmentSaleCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => apartmentSaleCarouselApi?.scrollNext()}
                disabled={!apartmentSaleCarouselApi || !apartmentSaleCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel
          setApi={setApartmentSaleCarouselApi}
          className="w-full sm:px-4 md:px-8 lg:px-12 xl:px-24"
          opts={{
            align: 'start',
          }}
        >
          <CarouselContent className="ml-2 mr-2 md:-ml-4">
            {isLoadingApartmentSale
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem
                    key={`apartment-sale-skeleton-${index}`}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : apartmentSaleProperties.map(property => (
                  <CarouselItem
                    key={property.id}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertyCard
                      property={property}
                      priority={false}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>

      {/* Land Plot for Sale Section */}
      <div className="w-full max-w-8xl mx-auto  mb-8 md:mb-12">
        <div className="text-left mb-2 md:mb-4">
          <div className="flex justify-between items-center mb-2 md:mb-4 px-4 sm:px-16 lg:px-24">
            <Link
              href="/properties?propertyType=LandPlot&transactionType=ForSale"
              className="flex items-center gap-2 text-gray-900 hover:text-red-600 transition-colors group"
            >
              <h2 className="text-base md:text-xl font-semibold">Đất bán</h2>
              <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-red-600" />
            </Link>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => landPlotSaleCarouselApi?.scrollPrev()}
                disabled={!landPlotSaleCarouselApi || !landPlotSaleCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => landPlotSaleCarouselApi?.scrollNext()}
                disabled={!landPlotSaleCarouselApi || !landPlotSaleCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel
          setApi={setLandPlotSaleCarouselApi}
          className="w-full sm:px-4 md:px-8 lg:px-12 xl:px-24"
          opts={{
            align: 'start',
          }}
        >
          <CarouselContent className="ml-2 mr-2 md:-ml-4">
            {isLoadingLandPlotSale
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem
                    key={`land-plot-sale-skeleton-${index}`}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : landPlotSaleProperties.map(property => (
                  <CarouselItem
                    key={property.id}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertyCard
                      property={property}
                      priority={false}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>

      {/* Townhouse for Sale Section */}
      {/* <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900">
              Nhà liền kề bán
            </h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => townhouseSaleCarouselApi?.scrollPrev()}
                disabled={!townhouseSaleCarouselApi || !townhouseSaleCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => townhouseSaleCarouselApi?.scrollNext()}
                disabled={!townhouseSaleCarouselApi || !townhouseSaleCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel 
          setApi={setTownhouseSaleCarouselApi}
          className="w-full"
          opts={{
            align: "start",
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {isLoadingTownhouseSale
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem key={`townhouse-sale-skeleton-${index}`} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : townhouseSaleProperties.map(property => (
                  <CarouselItem key={property.id} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertyCard property={property} priority={false} size={isMobile ? "sm" : "md"} />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div> */}

      {/* Apartment for Rent Section */}
      <div className="w-full max-w-8xl mx-auto  mb-8 md:mb-12">
        <div className="text-left mb-2 md:mb-4">
          <div className="flex justify-between items-center mb-2 md:mb-4 px-4 sm:px-16 lg:px-24">
            <Link
              href="/properties?propertyType=Apartment&transactionType=ForRent"
              className="flex items-center gap-2 text-gray-900 hover:text-red-600 transition-colors group"
            >
              <h2 className="text-base md:text-xl font-semibold">Chung cư cho thuê</h2>
              <ChevronRight className="h-4 w-4 text-gray-600 group-hover:text-red-600" />
            </Link>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => apartmentRentCarouselApi?.scrollPrev()}
                disabled={!apartmentRentCarouselApi || !apartmentRentCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => apartmentRentCarouselApi?.scrollNext()}
                disabled={!apartmentRentCarouselApi || !apartmentRentCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel
          setApi={setApartmentRentCarouselApi}
          className="w-full sm:px-4 md:px-8 lg:px-12 xl:px-24"
          opts={{
            align: 'start',
          }}
        >
          <CarouselContent className="ml-2 mr-2 md:-ml-4">
            {isLoadingApartmentRent
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem
                    key={`apartment-rent-skeleton-${index}`}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : apartmentRentProperties.map(property => (
                  <CarouselItem
                    key={property.id}
                    className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/2 lg:basis-1/3 xl:basis-1/4 2xl:basis-1/5"
                  >
                    <PropertyCard
                      property={property}
                      priority={false}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div>

      {/* Villa for Rent Section */}
      {/* <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900">
              Biệt thự cho thuê
            </h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => villaRentCarouselApi?.scrollPrev()}
                disabled={!villaRentCarouselApi || !villaRentCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => villaRentCarouselApi?.scrollNext()}
                disabled={!villaRentCarouselApi || !villaRentCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel 
          setApi={setVillaRentCarouselApi}
          className="w-full"
          opts={{
            align: "start",
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {isLoadingVillaRent
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem key={`villa-rent-skeleton-${index}`} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : villaRentProperties.map(property => (
                  <CarouselItem key={property.id} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertyCard property={property} priority={false} size={isMobile ? "sm" : "md"} />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div> */}

      {/* Office for Rent Section */}
      {/* <div className="w-full max-w-8xl mx-auto px-4 sm:px-16 lg:px-36 mb-12">
        <div className="text-left mb-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900">
              Văn phòng cho thuê
            </h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => officeRentCarouselApi?.scrollPrev()}
                disabled={!officeRentCarouselApi || !officeRentCanScrollPrev}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 rounded-full border-2"
                onClick={() => officeRentCarouselApi?.scrollNext()}
                disabled={!officeRentCarouselApi || !officeRentCanScrollNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <Carousel 
          setApi={setOfficeRentCarouselApi}
          className="w-full"
          opts={{
            align: "start",
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {isLoadingOfficeRent
              ? Array.from({ length: 8 }).map((_, index) => (
                  <CarouselItem key={`office-rent-skeleton-${index}`} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertySkeleton />
                  </CarouselItem>
                ))
              : officeRentProperties.map(property => (
                  <CarouselItem key={property.id} className="pl-2 md:pl-4 basis-1/1 sm:basis-1/2 md:basis-1/3 lg:basis-1/5">
                    <PropertyCard property={property} priority={false} size={isMobile ? "sm" : "md"} />
                  </CarouselItem>
                ))}
          </CarouselContent>
        </Carousel>
      </div> */}

      {/* Recently Viewed Section */}
      {/* <RecentlyViewedSection /> */}
    </section>
  );
}
