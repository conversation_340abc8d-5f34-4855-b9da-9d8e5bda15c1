'use client';

import { useEffect, useState } from 'react';
import ChannelSideBar from './ChannelSideBar';

import Channel<PERSON>essageField from './ChannelMessageField';
import { useMediaQuery } from '@/utils/useMediaQuery';
import { ConversationResponse } from '@/lib/api/services/fetchConversation';
import { useUserProfile } from '@/hooks/useUsers';
import { useGetConversation } from '@/hooks/useConversation';
import ChannelChatContent from './ChannelChatContent';

const pagingRequest = {
  pageNumber: 1,
  pageSize: 10,
};

export default function UnifiedChatLayout() {
  const [isMounted, setIsMounted] = useState(false);
  const [response, setResponse] = useState<ConversationResponse>({
    code: 0,
    status: false,
    message: '',
    data: [
      {
        platform: '',
        conversations: [],
      },
    ],
  });
  const { data, isLoading } = useGetConversation(pagingRequest);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('system');
  const [chatUser, setChatUser] = useState<{ id: string; name: string; avatarUrl: string }>({
    id: '',
    name: '',
    avatarUrl: '',
  });
  const [selectedConversation, setSelectedConversation] = useState<string>(''); //Zalo chat handler
  const { data: userLoggedIn } = useUserProfile();

  useEffect(() => {
    if (data && !isLoading) {
      setResponse(data);
    }
  }, [data, isLoading]);

  useEffect(() => {
    setIsMounted(true);
  }, [selectedPlatform, setSelectedPlatform]);

  const isMobile = useMediaQuery('(max-width: 723px)');
  const isTablet = useMediaQuery('(min-width: 724px) and (max-width: 1345px)');

  const [isMobileChatOpen, setIsMobileChatOpen] = useState(true);

  let widthLength: { sidebar: string; messageField: string; chatContent: string };
  switch (true) {
    case isMobile:
      widthLength = {
        sidebar: '50px',
        messageField: '0px',
        chatContent: 'calc(91vw - 50px)',
      };
      break;
    case isTablet:
      widthLength = {
        sidebar: '56px',
        messageField: '360px',
        chatContent: 'calc(100% - 56px - 360px)',
      };
      break;
    default:
      widthLength = {
        sidebar: '72px',
        messageField: '480px',
        chatContent: 'calc(100% - 56px - 360px)',
      };
      break;
  }

  if (!isMounted) {
    // Render a skeleton, spinner, or just null to avoid hydration mismatch
    return null;
  }

  return (
    <div className="flex w-full justify-between h-[100vh] overflow-hidden bg-background/20 gap-4 px-4">
      <div className="bg-background/40 flex-shrink-0" style={{ width: widthLength.sidebar }}>
        <ChannelSideBar
          isMobile={isMobile}
          channels={response.data}
          setSelectedPlatform={setSelectedPlatform}
          platform={selectedPlatform}
          adminProfile={{
            name: userLoggedIn?.profile.fullName || 'Unknown',
            avatar: userLoggedIn?.profile.avatar || '',
          }}
        />
      </div>

      {/* On mobile, if isMobileChatOpen is false, show ChannelMessageField at full width and hide ChannelChatContent */}
      {isMobile && !isMobileChatOpen ? (
        <div className="my-3 bg-background/40 w-[83vw]">
          <ChannelMessageField
            setChatUser={setChatUser}
            platform={selectedPlatform}
            conversations={
              response.data.find(item => item.platform === selectedPlatform)?.conversations || []
            }
            setSelectedConversation={setSelectedConversation}
            selectedConversation={selectedConversation}
          />
        </div>
      ) : (
        <div className="flex gap-3 w-full">
          <div
            className="my-3 bg-background/40 flex-shrink-0"
            style={{
              width: widthLength.messageField,
              display: widthLength.messageField === '0px' ? 'none' : 'block',
            }}
          >
            <ChannelMessageField
              setSelectedConversation={setSelectedConversation}
              platform={selectedPlatform}
              setChatUser={setChatUser}
              conversations={
                response.data.find(item => item.platform === selectedPlatform)?.conversations || []
              }
              selectedConversation={selectedConversation}
            />
          </div>

          <div
            className="flex-1 my-3 bg-background/40 min-w-0"
            style={{ width: widthLength.chatContent }}
          >
            <ChannelChatContent
              isMobile={isMobile}
              setIsMobileChatOpen={setIsMobileChatOpen}
              chatUser={chatUser}
              selectedConversation={selectedConversation}
            />
          </div>
        </div>
      )}
    </div>
  );
}
