import apiService from '../core';

export interface Saler {
  id: string;
  fullName: string;
  email: string;
  avatar: string;
  phoneNumber: string;
  gender: string;
}
export interface Location {
  address: string;
  city: string;
  district: string;
  ward: string;
  latitude: number;
  longitude: number;
}

export interface PropertyDetails {
  bedrooms: number;
  bathrooms: number;
  livingRooms: number;
  kitchens: number;
  landArea: number;
  landWidth: number;
  landLength: number;
  buildingArea: number;
  numberOfFloors: number;
  hasBasement: boolean;
  floorNumber: number;
  apartmentOrientation: string;
  furnished: boolean;
}

export interface PriceDetails {
  salePrice: number;
  rentalPrice: number;
  pricePerSquareMeter: number;
  currency: string;
  depositAmount: number;
  maintenanceFee: number;
  paymentMethods: string[];
}

export interface Amenities {
  parking?: boolean;
  elevator?: boolean;
  swimmingPool?: boolean;
  gym?: boolean;
  securitySystem?: boolean;
  airConditioning?: boolean;
  balcony?: boolean;
  garden?: boolean;
  playground?: boolean;
  backupGenerator?: boolean;
  [key: string]: unknown;
}

export interface Video {
  videoUrl: string;
  title: string;
  description: string;
}

export interface PropertyLandingPage {
  id: string;
  saler?: Saler;
  title: string;
  name: string;
  description: string;
  transactionType: string;
  type: string;
  status: string;
  adminNote: string;
  code: string;
  owner?: unknown;
  location?: Location;
  propertyDetails?: PropertyDetails;
  priceDetails?: PriceDetails;
  amenities?: Amenities;
  imageUrls: string[];
  floorPlanUrls?: string[];
  video?: Video;
  yearBuilt: number;
  legalDocumentUrls?: string[];
  transactionHistory?: unknown[];
  isFeatured?: boolean;
  isVerified?: boolean;
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string | null;
  createdAt?: string;
  updatedAt?: string;
  isFavorite?: boolean;
  isViewed?: boolean;
}

export interface PropertyLandingPageResponse {
  code: number;
  status: boolean;
  message: string;
  data: {
    properties: PropertyLandingPage[];
    page: number;
    limit: number;
    count: number;
    totalPages: number;
  };
}
export async function fetchPropertyLandingPage(
  pageNumber = 1,
  pageSize = 6
): Promise<PropertyLandingPageResponse> {
  const res = await apiService.get<PropertyLandingPageResponse>(
    `https://dev.revoland.vn/api/properties?pageNumber=${pageNumber}&pageSize=${pageSize}`
  );
  return res.data as PropertyLandingPageResponse;
}
