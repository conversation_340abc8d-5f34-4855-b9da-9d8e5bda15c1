'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Save, Download, Send } from 'lucide-react';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Progress } from '@/components/ui/progress';

// Dynamic import cho Quill Editor để tránh SSR issues
const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });
import 'react-quill/dist/quill.snow.css';
import { generateSaleContractHtml } from './utils/saleContractGenerator';
import { blobToFile, generateContractPDFFileName } from '../../utils/fileUtils';
import { exportQuillContentToPDFWithSignatures } from '../../utils/pdfMakeExporter';
import {
  Location,
  PriceDetail,
  PropertyDetail,
  PropertyType,
} from '@/lib/api/services/fetchProperty';
import { useUploadPropertyLegalDocument } from '@/hooks/useAttachment';
import {
  SaleContractInput,
  PartyAInput,
  PartyBInput,
  Clause1To2Input,
  Clause3To6Input,
  Clause7To10Input,
  Clause11To14Input,
  saleContractDataSchema,
} from './schemas';
import PartyAForm from './Components/PartyAForm';
import PartyBForm from './Components/PartyBForm';
import PropertyDetailsForm from './Components/PropertyDetailsForm';
import { Clause3To6Form } from './Components/Clause3To6Form';
import { Clause7To10Form } from './Components/Clause7To10Form';
import { Clause11To14Form } from './Components/Clause11To14Form';
import { useBuyer } from '@/hooks/useBuyer';
import { Buyer } from '@/lib/api/services/fetchBuyer';
import { useContract } from '@/hooks/useContract';
import { useRouter } from 'next/navigation';

interface SaleContractPageProps {
  propertyLocation: Location;
  propertyDetails?: PropertyDetail;
  propertyType?: PropertyType;
  priceDetails?: PriceDetail;
  legalDocumentUrls?: string[];
  propertyId?: string;
}

const HouseSaleContract: React.FC<SaleContractPageProps> = ({
  propertyLocation,
  propertyDetails,
  priceDetails,
  propertyType,
  legalDocumentUrls,
  propertyId,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [contractContent, setContractContent] = useState('');
  const [isContractEdited, setIsContractEdited] = useState(false);
  const [pdfUrls, setPdfUrls] = useState<string[]>([]);
  const [lastGeneratedContent, setLastGeneratedContent] = useState('');
  const [isUploadingPDF, setIsUploadingPDF] = useState(false);
  const [isLoadingSendContract, setIsLoadingSendContract] = useState(false);
  const SaleContract = useContract();
  const uploadContractPDFApi = useUploadPropertyLegalDocument();
  const apiBuyer = useBuyer();
  const router = useRouter();

  const initialFormData: SaleContractInput = {
    partyA: {
      name: '',
      idNumber: '',
      idIssuedDate: '',
      idIssuedPlace: '',
      permanentAddress: '',
      contactAddress: '',
      email: '',
      phone: '',
      fax: '',
      accountNumber: '',
      bankName: '',
      taxCode: '',
      bankCode: '',
    },
    partyB: {
      name: '',
      idNumber: '',
      idIssuedDate: '',
      idIssuedPlace: '',
      permanentAddress: '',
      contactAddress: '',
      email: '',
      phone: '',
      fax: '',
      accountNumber: '',
      bankName: '',
      taxCode: '',
      bankCode: '',
      idVerifications: [],
    },
    clause1To2Input: {
      propertyType: propertyType || '',
      address:
        propertyLocation?.address +
          ', ' +
          propertyLocation?.ward +
          ', ' +
          propertyLocation?.district +
          ', ' +
          propertyLocation?.city || '',
      totalFloorArea: propertyDetails?.floorNumber || 0,
      totalLandArea: propertyDetails?.landArea || 0,
      privateArea: 0,
      sharedArea: 0,
      landOrigin: '',
      equipmentDetails: '',
      legalDocuments: legalDocumentUrls || [],
      salePrice: priceDetails?.salePrice || 0,
      paymentMethod: '',
      paymentSchedule: [{ amount: 0, paymentDate: '', note: '' }],
    },
    clause3To6: { Clause3To6: '' },
    clause7To10: { Clause7To10: '' },
    clause11To14: { Clause11To14: '' },
  };

  const form = useForm<SaleContractInput>({
    resolver: zodResolver(saleContractDataSchema),
    defaultValues: initialFormData,
  });

  const { watch } = form;
  const watchedData = watch();

  // Auto-generate contract content when form data changes
  useEffect(() => {
    if (!isContractEdited && watchedData) {
      const newContent = generateSaleContractHtml(watchedData);
      setContractContent(newContent);
    }
  }, [watchedData, isContractEdited]);

  const handleStep1Next = useCallback(
    (data: PartyAInput) => {
      form.setValue('partyA', data);
      setIsContractEdited(false);
      setCurrentStep(2);
    },
    [form]
  );

  const handleStep2Next = useCallback(
    (data: PartyBInput) => {
      form.setValue('partyB', data);
      setIsContractEdited(false);
      setCurrentStep(3);
    },
    [form]
  );

  const handleStep3Next = useCallback(
    (data: Clause1To2Input) => {
      form.setValue('clause1To2Input', data);
      setIsContractEdited(false);
      setCurrentStep(4);
    },
    [form]
  );

  const handleStep4Next = useCallback(
    (data: Clause3To6Input) => {
      form.setValue('clause3To6', data);
      setIsContractEdited(false);
      setCurrentStep(5);
    },
    [form]
  );

  const handleStep5Next = useCallback(
    (data: Clause7To10Input) => {
      form.setValue('clause7To10', data);
      setIsContractEdited(false);
      setCurrentStep(6);
    },
    [form]
  );

  const handleStep6Next = useCallback(
    (data: Clause11To14Input) => {
      form.setValue('clause11To14', data);
      setIsContractEdited(false);
      setCurrentStep(7);
    },
    [form]
  );

  const handleBackStep = useCallback(() => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  }, []);

  const handleContractContentChange = useCallback((content: string) => {
    setContractContent(content);
    setIsContractEdited(true);
  }, []);

  const handleResetContract = useCallback(() => {
    const newContent = generateSaleContractHtml(watchedData);
    setContractContent(newContent);
    setIsContractEdited(false);
    setPdfUrls([]);
    setLastGeneratedContent('');
    toast.success('Đã khôi phục nội dung hợp đồng gốc');
  }, [watchedData]);

  const generateAndUploadPDF = useCallback(async (): Promise<string[]> => {
    try {
      setIsUploadingPDF(true);

      const currentContent = isContractEdited
        ? contractContent
        : generateSaleContractHtml(watchedData);

      const pdfBlob = (await exportQuillContentToPDFWithSignatures(currentContent, true)) as Blob;

      if (!pdfBlob) {
        throw new Error('Failed to generate PDF blob');
      }

      const fileName = generateContractPDFFileName('sale', propertyId);
      const pdfFile = blobToFile(pdfBlob, fileName);

      const uploadResponse = await uploadContractPDFApi.mutateAsync({
        files: [pdfFile],
      });

      if (uploadResponse.status && uploadResponse.data && uploadResponse.data.length > 0) {
        const urls = uploadResponse.data.map(attachment => attachment.fileUrl);
        setLastGeneratedContent(currentContent);
        setPdfUrls(urls);
        return urls;
      } else {
        throw new Error('Upload response không hợp lệ');
      }
    } catch (error) {
      console.error('Error generating and uploading PDF:', error);
      toast.error('Có lỗi xảy ra khi upload PDF hợp đồng');
      return [];
    } finally {
      setIsUploadingPDF(false);
    }
  }, [contractContent, watchedData, isContractEdited, propertyId, uploadContractPDFApi]);

  const getPdfUrls = useCallback(async (): Promise<string[]> => {
    const currentContent = isContractEdited
      ? contractContent
      : generateSaleContractHtml(watchedData);
    if (pdfUrls.length > 0 && lastGeneratedContent === currentContent) {
      return pdfUrls;
    }
    return await generateAndUploadPDF();
  }, [
    pdfUrls,
    lastGeneratedContent,
    isContractEdited,
    contractContent,
    watchedData,
    generateAndUploadPDF,
  ]);

  const handleExportPDF = useCallback(async () => {
    try {
      const currentContent = isContractEdited
        ? contractContent
        : generateSaleContractHtml(watchedData);
      await exportQuillContentToPDFWithSignatures(currentContent, false);
      toast.success('Đã xuất PDF thành công!');
      // await getPdfUrls(); // Update pdfUrls if needed
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Có lỗi xảy ra khi xuất PDF');
    }
  }, [contractContent, watchedData, isContractEdited]);

  const sendToParties = useCallback(async () => {
    setIsLoadingSendContract(true);
    try {
      const urls = await getPdfUrls();
      if (urls.length === 0) {
        toast.error('Không có file PDF để gửi');
        return;
      }
      // Giả sử có API để gửi hợp đồng
      // await sendContractToParties({ pdfUrls: urls, ...watchedData });
      const resOwner = await SaleContract.sendContractMutation.mutateAsync({
        contractLink: urls[0], // Giả sử chỉ gửi 1 link PDF
        email: watchedData.partyA.email,
      });
      const resTenant = await SaleContract.sendContractMutation.mutateAsync({
        contractLink: urls[0], // Giả sử chỉ gửi 1 link PDF
        email: watchedData.partyB.email,
      });
      if (!resOwner.status || !resTenant.status) {
        toast.error('Không thể gửi hợp đồng đến các bên');
        return;
      }
      toast.success('Đã gửi hợp đồng đến các bên thành công');
    } catch (error) {
      console.error('Error sending contract:', error);
      toast.error('Có lỗi xảy ra khi gửi hợp đồng');
    } finally {
      setIsLoadingSendContract(false);
    }
  }, [getPdfUrls]);

  const handleSave = useCallback(async () => {
    try {
      const pdfUrls = await getPdfUrls();
      if (pdfUrls.length === 0) {
        throw new Error('Không có URL PDF để lưu hợp đồng');
      }

      let buyer: Buyer | null = null;

      const buyerData = {
        name: watchedData.partyB.name,
        phone: watchedData.partyB.phone,
        email: watchedData.partyB.email,
        address: watchedData.partyB.permanentAddress,
        idVerifications: watchedData.partyB.idVerifications,
        bankCode: watchedData.partyB.bankCode,
        bankName: watchedData.partyB.bankName,
        taxCode: watchedData.partyB.taxCode,
        faxCode: watchedData.partyB.fax || '',
      };

      const createBuyerRes = await apiBuyer.createBuyer(buyerData);
      if (createBuyerRes && createBuyerRes.status && createBuyerRes.data) {
        buyer = createBuyerRes.data;
      }

      const contractData = {
        propertyId: propertyId || '',
        buyerId: buyer?.id || '',
        content: contractContent || generateSaleContractHtml(watchedData),
        pdfContractUrls: pdfUrls,
        paymentSchedule: watchedData.clause1To2Input.paymentSchedule.map(schedule => ({
          ...schedule,
          note: schedule.note || '',
        })),
      };

      // TODO: Implement actual save API call
      await SaleContract.createMutationSale.mutateAsync(contractData);
      toast.success('Đã lưu hợp đồng thành công!');
      router.push('/saler/contract'); // Redirect to contract list after saving
    } catch (error) {
      console.error('Error saving contract:', error);
      toast.error('Có lỗi xảy ra khi lưu hợp đồng');
    }
  }, [contractContent, watchedData, propertyId, getPdfUrls, apiBuyer]);

  const getStepProgress = () => {
    return (currentStep / 7) * 100;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <PartyAForm data={watchedData.partyA} onNext={handleStep1Next} />;
      case 2:
        return (
          <PartyBForm data={watchedData.partyB} onNext={handleStep2Next} onBack={handleBackStep} />
        );
      case 3:
        return (
          <PropertyDetailsForm
            data={watchedData.clause1To2Input}
            onNext={handleStep3Next}
            onBack={handleBackStep}
          />
        );
      case 4:
        return (
          <Clause3To6Form
            data={watchedData.clause3To6}
            onNext={handleStep4Next}
            onBack={handleBackStep}
          />
        );
      case 5:
        return (
          <Clause7To10Form
            data={watchedData.clause7To10}
            onNext={handleStep5Next}
            onBack={handleBackStep}
          />
        );
      case 6:
        return (
          <Clause11To14Form
            data={watchedData.clause11To14}
            onNext={handleStep6Next}
            onBack={handleBackStep}
          />
        );
      case 7:
        return (
          <Card className="w-full">
            <div className="p-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-green-600 mb-2">Hoàn tất tạo hợp đồng</h2>
                <p className="text-gray-600">
                  Hợp đồng mua bán nhà ở đã được tạo thành công. Bạn có thể xem lại, chỉnh sửa hoặc
                  thực hiện các thao tác bên dưới.
                </p>
                {isContractEdited && (
                  <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <p className="text-sm text-amber-800">
                      Bạn đã chỉnh sửa nội dung hợp đồng. Khi xuất PDF hoặc lưu hợp đồng, nội dung
                      đã chỉnh sửa sẽ được sử dụng.
                    </p>
                  </div>
                )}
              </div>

              <div className="flex flex-col gap-4 justify-center">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={handleExportPDF}
                    className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700"
                    disabled={isUploadingPDF}
                  >
                    <Download className="w-4 h-4" />
                    {isUploadingPDF ? 'Đang xuất PDF...' : 'Xuất PDF'}
                  </Button>
                  <Button
                    onClick={handleSave}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                    disabled={isUploadingPDF}
                  >
                    <Save className="w-4 h-4" />
                    {isUploadingPDF ? 'Đang lưu...' : 'Lưu hợp đồng'}
                  </Button>
                  <Button
                    onClick={sendToParties}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                    disabled={isLoadingSendContract}
                  >
                    <Send className="w-4 h-4" />
                    Gửi đến các bên
                  </Button>
                </div>
                {isContractEdited && (
                  <Button
                    onClick={handleResetContract}
                    variant="outline"
                    className="flex items-center gap-2 border-amber-300 text-amber-700 hover:bg-amber-100"
                  >
                    Khôi phục gốc
                  </Button>
                )}
                <div className="flex justify-between mt-6">
                  <Button onClick={handleBackStep} variant="outline" className="px-8 py-2">
                    Quay lại
                  </Button>
                  <Button
                    onClick={() => {
                      setCurrentStep(1);
                      form.reset(initialFormData);
                      setContractContent('');
                      setIsContractEdited(false);
                      setPdfUrls([]);
                      setLastGeneratedContent('');
                      toast.success('Đã tạo hợp đồng mới');
                    }}
                    variant="outline"
                    className="px-8 py-2"
                  >
                    Tạo hợp đồng mới
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Progress Bar */}
      <div className="flex-shrink-0 p-4 lg:p-6 border-b bg-background">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Bước {currentStep} / 7</span>
            <span className="text-sm">{Math.round(getStepProgress())}% hoàn thành</span>
          </div>
          <Progress value={getStepProgress()} className="h-2" />
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        <div className="w-full h-full">
          <ResizablePanelGroup direction="horizontal" className="h-full w-full">
            {/* Form Panel */}
            <ResizablePanel minSize={40} className="flex-1">
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-auto scrollbar-hide">
                  <div className="p-4 lg:p-6">
                    <div className="max-w-4xl mx-auto">{renderStepContent()}</div>
                  </div>
                </div>
              </div>
            </ResizablePanel>

            {/* Contract Preview Panel */}
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={50} minSize={30} maxSize={70}>
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-auto scrollbar-hide">
                  <div className="p-4 lg:p-6">
                    <Card className="h-full">
                      <div className="p-4 h-full">
                        <ReactQuill
                          value={contractContent}
                          onChange={handleContractContentChange}
                          theme="snow"
                          readOnly={currentStep !== 7}
                          modules={{
                            toolbar: [
                              [{ header: [1, 2, 3, false] }],
                              ['bold', 'italic', 'underline', 'strike'],
                              [{ align: [] }],
                              [{ list: 'ordered' }, { list: 'bullet' }],
                              ['clean'],
                            ],
                          }}
                          formats={[
                            'header',
                            'bold',
                            'italic',
                            'underline',
                            'strike',
                            'align',
                            'list',
                            'bullet',
                          ]}
                          className="h-full"
                        />
                      </div>
                    </Card>
                  </div>
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </div>
  );
};

export default HouseSaleContract;
