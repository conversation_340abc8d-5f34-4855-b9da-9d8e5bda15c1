# SEO Structured Data Components

This directory contains TypeScript components for adding JSON-LD structured data to your website pages, improving SEO and search engine understanding.

## Components Overview

### 1. OrganizationStructuredData

**Purpose**: Adds organization, website, and local business schema to help display "Revoland" beside your logo in search results.
**Usage**: Already added to `app/layout.tsx` for site-wide coverage.

```tsx
import OrganizationStructuredData from '@/components/seo/OrganizationStructuredData';

// In your layout or page
<OrganizationStructuredData url="https://www.revoland.vn" />;
```

### 2. BreadcrumbStructuredData

**Purpose**: Helps search engines understand page hierarchy and navigation structure.
**Usage**: Add to pages with navigation breadcrumbs.

```tsx
import { BreadcrumbStructuredData } from '@/components/seo';

const breadcrumbItems = [
  { name: 'Trang chủ', url: '/' },
  { name: '<PERSON><PERSON><PERSON> động sản', url: '/properties' },
  { name: '<PERSON> tiết căn hộ', url: '/properties/123' },
];

<BreadcrumbStructuredData items={breadcrumbItems} />;
```

### 3. FAQStructuredData

**Purpose**: Enables featured snippets in search results for FAQ content.
**Usage**: Add to FAQ pages or pages with question-answer content.

```tsx
import { FAQStructuredData } from '@/components/seo';

const faqs = [
  {
    question: 'Làm sao để đăng ký tài khoản Revoland?',
    answer:
      'Bạn có thể đăng ký tài khoản bằng cách click vào nút "Đăng ký" và điền thông tin cá nhân.',
  },
  {
    question: 'Chi phí sử dụng dịch vụ Revoland là bao nhiều?',
    answer: 'Revoland cung cấp nhiều gói dịch vụ phù hợp với nhu cầu của bạn.',
  },
];

<FAQStructuredData faqs={faqs} />;
```

### 4. ServiceStructuredData

**Purpose**: Helps with local SEO and service discovery for your real estate services.
**Usage**: Add to service description pages.

```tsx
import { ServiceStructuredData } from '@/components/seo';

<ServiceStructuredData
  serviceName="Dịch vụ mua bán bất động sản"
  description="Chuyên cung cấp dịch vụ mua bán nhà đất, căn hộ, biệt thự"
  serviceType="Real Estate Brokerage"
  provider="Revoland"
  areaServed={[
    { name: 'Hồ Chí Minh', type: 'City' },
    { name: 'Hà Nội', type: 'City' },
  ]}
  offers={[
    {
      name: 'Tư vấn mua nhà',
      description: 'Dịch vụ tư vấn chuyên nghiệp cho người mua nhà',
      priceRange: '$$',
    },
  ]}
/>;
```

## Implementation Examples

### Property Detail Page

Your existing `PropertyStructuredData.tsx` already handles individual property listings.

### Homepage

```tsx
// app/page.tsx
import { ServiceStructuredData, FAQStructuredData } from '@/components/seo';

export default function HomePage() {
  return (
    <>
      <ServiceStructuredData
        serviceName="Nền tảng bất động sản Revoland"
        description="Giải pháp bất động sản toàn diện"
        serviceType="Real Estate Platform"
        provider="Revoland"
        areaServed={[{ name: 'Vietnam', type: 'Country' }]}
      />
      {/* Your page content */}
    </>
  );
}
```

### FAQ Page

```tsx
// app/legal/faq/page.tsx
import { FAQStructuredData, BreadcrumbStructuredData } from '@/components/seo';

const faqs = [
  // Your FAQ data
];

const breadcrumbs = [
  { name: 'Trang chủ', url: '/' },
  { name: 'Pháp lý', url: '/legal' },
  { name: 'Câu hỏi thường gặp', url: '/legal/faq' },
];

export default function FAQPage() {
  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbs} />
      <FAQStructuredData faqs={faqs} />
      {/* Your page content */}
    </>
  );
}
```

## SEO Benefits

1. **Brand Recognition**: Organization schema helps display "Revoland" instead of URL
2. **Rich Snippets**: FAQ schema can generate featured snippets
3. **Navigation Understanding**: Breadcrumb schema improves site structure understanding
4. **Local SEO**: Service and LocalBusiness schemas improve local search visibility
5. **Search Features**: Enables various Google search features like site search box

## Best Practices

1. **Validate Schema**: Use Google's Rich Results Test tool to validate your structured data
2. **Monitor Performance**: Check Google Search Console for rich snippet performance
3. **Keep Updated**: Ensure structured data matches your actual content
4. **Test Regularly**: Structured data should be tested after major content changes

## Testing Tools

- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Schema.org Validator](https://validator.schema.org/)
- [Google Search Console](https://search.google.com/search-console)
