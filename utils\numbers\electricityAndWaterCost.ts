/**
 * Calculate electricity cost based on EVN tiered pricing and 8% VAT.
 * @param kwh - Number of kilowatt-hours consumed
 * @returns Total cost in VND (rounded)
 */
export function calculateElectricityCost(kwh: number): number {
  const tiers = [
    { limit: 50, price: 1984 },
    { limit: 50, price: 2050 },
    { limit: 100, price: 2380 },
    { limit: 100, price: 2998 },
    { limit: 100, price: 3350 },
    { limit: Infinity, price: 3460 },
  ];
  let remaining = kwh;
  let total = 0;

  for (const tier of tiers) {
    if (remaining <= 0) break;

    const consumedUnits = Math.min(remaining, tier.limit);
    total += consumedUnits * tier.price;
    remaining -= consumedUnits;
  }

  return Math.round(total * 1.08); // VAT 8%
}

/**
 * Calculate water cost based on tiered pricing, 8% VAT, and 10% environment fee.
 * @param m3 - Number of cubic meters consumed
 * @returns Total cost in VND (rounded)
 */
export function calculateWaterCost(m3: number): number {
  const tiers = [
    { limit: 10, price: 5973 },
    { limit: 10, price: 7052 },
    { limit: 10, price: 8669 },
    { limit: Infinity, price: 15929 },
  ];
  let remaining = m3;
  let total = 0;

  for (const tier of tiers) {
    if (remaining <= 0) break;

    const consumedUnits = Math.min(remaining, tier.limit);
    total += consumedUnits * tier.price;
    remaining -= consumedUnits;
  }

  const vat = total * 0.08; // VAT 8%
  const envFee = total * 0.1; // Environment fee 10%
  return Math.round(total + vat + envFee);
}
